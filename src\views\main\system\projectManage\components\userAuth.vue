<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="{ position: 'end' }"
    :title="$t('staffAllocation')"
    width="800px"
    @cancel="carDrawerCancel"
    @confirm="carDrawerConfirm"
  >
    <x-transfer
      :titles="carDrawer.transfer.titles"
      :dataSource="carDrawer.transfer.dataSource"
      :targetSource="carDrawer.transfer.targetSource"
      :locale="carDrawer.transfer.locale"
      showSearch
    />
  </x-drawer>
</template>
<script lang="ts" setup>
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import type { GetProDetailResponse } from "@/services/type";
import { reactive, watch } from "vue";
import { saveProUserRel, getProDetail, getUserListInEnt } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xTransfer from "@/components/x-transfer.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("projectManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 动态数据
 */
const carDrawer = reactive({
  transfer: {
    locale: {
      itemUnit: $t("person"),
      searchPlaceholder: $t("PEnterStaff"),
    },
    titles: [$t("allStaffs"), $t("assigned")],
    dataSource: [] as TreeItemType[],
    targetSource: [] as TreeItemType[],
  },
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const detail: GetProDetailResponse = await getProDetail(props.id);

      const { userList } = await getUserListInEnt(detail.entId);

      const selectedIds = detail.relUserList.map((item) => item.userId);
      const selected = detail.relUserList.map((item) => ({
        title: item.userName,
        value: item.userId,
        disabled: false,
        checked: false,
      }));

      const selectable = userList.map((entity) => {
        const item = {
          title: entity.userName,
          value: entity.userId,
          disabled: false,
          checked: undefined as undefined | true | false,
        };
        if (selectedIds.includes(entity.userId) || entity.hasPro) {
          item.checked = false;
          item.disabled = true;
        }
        return item;
      });

      carDrawer.transfer.dataSource = selectable;
      carDrawer.transfer.targetSource = selected;
    }
  }
);

/**
 * 取消
 */
const carDrawerCancel = () => {
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
};

/**
 * 提交
 */
const carDrawerConfirm = async () => {
  await saveProUserRel({
    id: props.id,
    userIdList: carDrawer.transfer.targetSource.map((v) => v.value),
  });
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
  emits("update:show", false);
  Message("success", $t("saveSuccess"));
  emits("confirm");
};
</script>

<style lang="scss" scoped></style>
