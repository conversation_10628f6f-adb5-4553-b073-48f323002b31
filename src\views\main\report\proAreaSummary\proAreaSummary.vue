<template>
  <section class="pro-area-summary" ref="proAreaSummaryRef">
    <div class="pro-area-summary-top">
      <template v-for="(item, index) in tabs.config" :key="index">
        <div class="top-title" :class="{ active: tabs.active === item.value }">
          <span @click="changeTitle(item)">{{ item.label }}</span>
        </div>
      </template>
    </div>
    <div class="pro-area-summary-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.entId"
          :options="formOptions.company"
          :popupContainer="proAreaSummaryRef"
          :placeholder="$t('PEntEntName')"
          showPopSearch
          allowClear
          class="middle-left-select"
        />
        <x-select
          v-model:value="searchForm.proId"
          :options="formOptions.project"
          :popupContainer="proAreaSummaryRef"
          :placeholder="$t('allProjects')"
          showPopSearch
          allowClear
          class="middle-left-select"
        />
        <x-select
          v-if="tabs.active === 'area'"
          v-model:value="searchForm.areaId"
          :options="formOptions.area"
          :popupContainer="proAreaSummaryRef"
          :placeholder="$t('allAreas')"
          showPopSearch
          allowClear
          class="middle-left-select"
        />
        <DatePicker
          v-model:value="searchForm.date"
          :popupContainer="proAreaSummaryRef"
          style="width: 300px"
          :allowClear="false"
          @changeSelect="toggleDateType"
          showSelect
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:proDataCalculate:proCalculateList')"
          @click="exportExcel"
          icon="export_blue"
          type="paleBlue"
          :text="$t('export')"
          style="margin-right: 12px"
        />
        <x-button
          v-if="
            permitList.includes('sys:proDataCalculate:proCalculateListDownload')
          "
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="pro-area-summary-bottom">
      <x-table
        :cols="filteredTableCols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
      </x-table>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import { useMainStore } from "@/stores/main";
import {
  compList,
  getProjectAndArea,
  getProCalculateList,
  exportProCalculateList,
} from "@/services/api";
import { download, i18nSimpleKey, formatRoundNum } from "@/assets/ts/utils";
import type { PageSizeType } from "@/components/types";
import {
  formatDateTime,
  getDaysInMonth,
  getWeekInDay,
} from "@/assets/ts/dateTime";
import { DatePicker } from "@/components/x-date-picker";
import xButton from "@/components/x-button.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";

const {
  userInfo: { permitList },
} = useMainStore();

const $t = i18nSimpleKey("proAreaSummary");

const proAreaSummaryRef = ref<any>();

const tabs = reactive({
  config: [
    {
      label: $t("proSummary"),
      value: "pro",
    },
    {
      label: $t("areaSummary"),
      value: "area",
    },
  ],
  active: "pro",
});

const changeTitle = (item: any) => {
  tabs.active = item.value;
  resetSearchForm();
};

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "20",
    },
    {
      key: "areaName",
      title: $t("area"),
      width: "40",
      hover: true,
    },
    {
      key: "projectName",
      title: $t("project"),
      width: "40",
      hover: true,
    },
    {
      key: "entName",
      title: $t("company"),
      width: "50",
      hover: true,
    },
    {
      key: "dateText",
      title: $t("taskDate"),
      width: "50",
      hover: true,
    },
    {
      key: "taskTimes",
      title: `${$t("taskTimes")}(${$t("times")})`,
      width: "30",
    },
    {
      key: "formatDuration",
      title: `${$t("taskDuration")}(h)`,
      width: "30",
    },
    {
      key: "formatMileage",
      title: `${$t("traveMiles")}(km)`,
      width: "30",
    },
    {
      key: "formatWorkArea",
      title: `${$t("cleanArea")}(m²)`,
      width: "30",
    },
    {
      key: "formatUsedWater",
      title: `${$t("useWater")}(L)`,
      width: "30",
    },
    {
      key: "formatUsedElectricity",
      title: `${$t("useElectric")}(kW·h)`,
      width: "30",
    },
    {
      key: "formatLitter",
      title: `${$t("garbageCleanNumber")}(L)`,
      width: "30",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const filteredTableCols = computed(() => {
  if (tabs.active === "area") {
    return table.cols;
  }
  return table.cols.filter((col) => col.key !== "areaName");
});

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchList();
};

const formOptions = reactive({
  company: [] as any,
  project: [] as any,
  area: [] as any,
});

// 查询
const searchForm = reactive({
  entId: "",
  proId: "",
  areaId: "",
  date: formatDateTime(Date.now(), "YYYY-MM-DD"),
  dateType: 1,
});

const nowInfo = {
  1: formatDateTime(Date.now(), "YYYY-MM-DD"),
  2: getWeekInDay(Date.now()).toString(),
  3: formatDateTime(Date.now(), "YYYY-MM"),
};
const toggleDateType = (value: "day" | "week" | "month") => {
  searchForm.dateType = { day: 1, week: 2, month: 3 }[value];
  searchForm.date = nowInfo[searchForm.dateType];
};
const reqDate = computed(
  () =>
    ({
      1: {
        workStart: `${searchForm.date} 00:00:00`,
        workEnd: `${searchForm.date} 23:59:59`,
      },
      2: (() => {
        const [start, end] = searchForm.date.split(",");
        return {
          workStart: `${start} 00:00:00`,
          workEnd: `${end} 23:59:59`,
          weekNo: Number(getWeekInDay(start, "weekIndex")) + 1,
        };
      })(),
      3: (() => {
        const days = getDaysInMonth(
          Number(searchForm.date.slice(0, 4)),
          Number(searchForm.date.slice(5, 7)) - 1
        );
        return {
          workStart: `${searchForm.date}-01 00:00:00`,
          workEnd: `${searchForm.date}-${days} 23:59:59`,
        };
      })(),
    }[searchForm.dateType])
);

const searchList = async () => {
  table.loading = true;
  const param = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    proOrArea: tabs.active === "pro",
    dateType: searchForm.dateType,
    ...reqDate.value,
    ...(searchForm.entId ? { entId: searchForm.entId } : {}),
    ...(searchForm.proId ? { proId: searchForm.proId } : {}),
    ...(searchForm.areaId ? { areaId: searchForm.areaId } : {}),
  };
  const { totalCount, list } = await getProCalculateList(param);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderNumber: (index + 1).toString().padStart(3, "0"),
            formatDuration: (item.taskDuration / 60).toFixed(1),
            formatMileage: formatRoundNum(item.mileage, 1),
            formatWorkArea: formatRoundNum(item.finishedWorkArea, 1),
            formatUsedWater: formatRoundNum(item.usedWaterAmount, 1),
            formatUsedElectricity: formatRoundNum(
              item.usedElectricityAmount,
              1
            ),
            formatLitter: formatRoundNum(item.litter, 1),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

// 重新搜索
const reSearch = () => {
  table.pagination["current"] = 1;
  searchList();
};

// 重置
const resetSearchForm = () => {
  searchForm.entId = "";
  searchForm.proId = "";
  searchForm.areaId = "";
  formOptions.area = [];
  searchForm.date = nowInfo[searchForm.dateType];
  reSearch();
};

// 导出
const exportExcel = async () => {
  const param = {
    proOrArea: tabs.active === "pro",
    dateType: searchForm.dateType,
    ...reqDate.value,
    ...(searchForm.entId ? { entId: searchForm.entId } : {}),
    ...(searchForm.proId ? { proId: searchForm.proId } : {}),
    ...(searchForm.areaId ? { areaId: searchForm.areaId } : {}),
  };
  try {
    const response = await exportProCalculateList(param);
    download(response);
    Message("success", $t("exportSuccess"));
  } catch (error) {
    Message("error", $t("exportFail"));
  }
};

// 数据获取
const getCompanyList = async () => {
  formOptions.company = (await compList()).map((item) => ({
    label: item.entName,
    value: item.entId,
  }));
};

const getProjectList = async () => {
  formOptions.project = await getProjectAndAreaList(null);
};

const getProjectAndAreaList = async (proId: any) => {
  const param = proId ? [proId] : [];
  const list = (await getProjectAndArea(param)).map((item) => ({
    label: item.name,
    value: String(item.id),
  }));
  return list;
};

onMounted(() => {
  getCompanyList();
  getProjectList();
  reSearch();
});

watch(
  () => searchForm.proId,
  async (newV) => {
    if (newV) {
      searchForm.areaId = "";
      formOptions.area = await getProjectAndAreaList(newV);
    }
  }
);
</script>

<style lang="scss" scoped>
.pro-area-summary {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include ct-f(y);
    @include wh(100%, 36px);
    line-height: 36px;
    .top-title {
      position: relative;
      display: flex;
      margin-right: 20px;
      @include sc(16px, #9f9fa4);
      font-weight: bold;
      cursor: pointer;
      &.active {
        @include sc(18px, rgb(36, 40, 89));
        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 50%;
          @include wh(49px, 3px);
          transform: translateX(-50%);
          background-color: #5964fb;
        }
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .date-type-selector {
      width: 50px;
      :deep(.x-select-normal-selector) {
        background-color: #e9eafe;
        border-right: none;
        border-radius: 4px 0 0 4px;
      }
    }
    .date-range-selector {
      width: 320px;
      :deep(.x-date-picker-trigger) {
        border-left: none;
        border-radius: 0 4px 4px 0;
      }
    }
    .middle-left,
    .middle-right {
      display: flex;
      &-select {
        width: 200px;
        margin-right: 10px;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
  }
}
</style>
