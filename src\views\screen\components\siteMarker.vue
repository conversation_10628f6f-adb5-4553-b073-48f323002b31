<template>
  <section class="site-marker">
    <div v-if="props.focus" class="site-marker-radar">
      <div :class="`radar-focus-ripple ${props.icon}`"></div>
      <div :class="`radar-focus-ripple ${props.icon}`"></div>
    </div>
    <div
      :class="[
        'site-marker-icon',
        props.focus
          ? ''
          : props.icon === 'map_ployline_end_disable'
          ? 'blur-line'
          : 'blur',
      ]"
    >
      <x-icon
        :name="props.icon"
        :width="props.focus ? '2.7vw' : '1.8vw'"
        :height="props.focus ? '2.7vw' : '1.8vw'"
      />
      <div
        v-if="props.number"
        :class="['site-marker-number', { nofocus: !props.focus }]"
      >
        {{ fixZeroToStr(props.number) }}
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import xIcon from "@/components/x-icon.vue";
import { fixZeroToStr } from "@/assets/ts/dateTime.ts";
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  focus: {
    type: Boolean,
    default: () => false,
  },
  number: {
    type: Number,
    default: () => 0,
  },
});
</script>

<style lang="scss" scoped>
.site-marker {
  position: relative;
  &-radar {
    @include ct-p(x) {
      bottom: -1vw;
    }
    @include wh(100%) {
      border-radius: 50%;
    }
    .radar-focus-ripple {
      @include ct-p;
      @include wh(1.3vw) {
        border-radius: 50%;
      }
      &.map_ployline_end_enable {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 0.13vw 0.27vw rgba(89, 100, 251, 0.4);
      }
      &.map_garbage {
        background-color: rgba(39, 212, 161, 0.2);
        box-shadow: 0px 0px 0.13vw 0.27vw rgba(39, 212, 161, 0.2);
      }
      &.map_battery {
        background-color: rgba(116, 34, 254, 0.2);
        box-shadow: 0px 0px 0.13vw 0.27vw rgba(116, 34, 254, 0.2);
      }
      &.map_water {
        background-color: rgba(77, 174, 255, 0.2);
        box-shadow: 0px 0px 0.13vw 0.27vw rgba(77, 174, 255, 0.2);
      }
      &.map_park {
        background-color: rgba(249, 133, 46, 0.2);
        box-shadow: 0px 0px 0.13vw 0.27vw rgba(249, 133, 46, 0.2);
      }
      animation: focusRipple 2.4s linear infinite;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1.2s;
      }
    }
    @keyframes focusRipple {
      80% {
        width: 4vw;
        height: 4vw;
        opacity: 0.3;
      }
      100% {
        width: 2.7vw;
        height: 2.7vw;
        opacity: 0.3;
      }
    }
  }
  &-icon {
    position: relative;
    transition: 0.3s padding linear;
    &.blur {
      padding: 0.8vw 0 0 0.05vw;
    }
    &.blur-line {
      padding: 0.12vw 0 0 0.04vw;
    }
  }
  &-number {
    position: absolute;
    transition: 0.3s all linear;
    left: 50%;
    top: 0.5vw;
    transform: translate(-50%);
    @include sc(0.8vw, #fff);
    &.nofocus {
      left: 0.8vw;
      top: 0.13vw;
      transform: none;
    }
  }
}
</style>
