<template>
  <section class="car-live-marker">
    <div
      v-show="props.status.online && props.status.warn"
      @click="!disabledClick && (socket.alarmStatusPop = props.status.deviceId)"
      class="marker-warn-icon"
    >
      <x-icon
        class="flicker-animate"
        name="map_warn"
        width="25px"
        height="22.5px"
      />
    </div>
    <div class="marker-radar">
      <template v-if="props.focus">
        <div :class="['marker-radar-focus-ripple', { disable: !props.status.online }]"></div>
        <div :class="['marker-radar-focus-ripple', { disable: !props.status.online }]"></div>
      </template>
      <template v-else-if="props.status.online">
        <div :class="['marker-radar-ripple', { disable: !props.status.online }]"></div>
      </template>
    </div>
    <div :class="['marker-car-num', { disable: !props.status.online }]">
      {{ props.status.deviceId }}
    </div>
    <div
      :style="markerExtraStyle"
      @click="!disabledClick && (socket.statusPop = props.status.deviceId)"
    >
      <div style="transform: rotate(-180deg)">
        <x-icon
          :name="getIconName"
          :width="getIconWidth"
          :height="getIconHeight"
        />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { computed, PropType } from "vue";
import { ref, watch, onMounted } from "vue";
import { useMainStore } from "@/stores/main";
import xIcon from "@/components/x-icon.vue";

const props = defineProps({
  status: {
    type: Object as PropType<{
      online: boolean;
      warn: boolean;
      deviceId: string;
    }>,
    required: true,
  },
  focus: {
    type: Boolean,
    default: () => false,
  },
  /** 角度值 */
  direction: {
    type: Number,
    default: 0,
  },
  /** 禁用点击 */
  disabledClick: Boolean,
  /** 设备类型 1,car 2,uav */
  deviceType: {
    type: Number,
    default: 1,
  },
});

const { socket } = useMainStore();

const getIconName = computed(() => {
  if (props.deviceType === 1) {
    return props.status.online ? "map_car" : "map_car_disable";
  } else {
    return props.status.online ? "map_uav" : "map_uav_disable";
  }
});

const getIconWidth = computed(() => {
  switch (props.deviceType) {
    case 1:
      return props.focus ? "28px" : "24px";
    case 2:
      return props.focus ? "80px" : "70px";
    default:
      return "24px";
  }
});

const getIconHeight = computed(() => {
  switch (props.deviceType) {
    case 1:
      return props.focus ? "41.7px" : "35.4px";
    case 2:
      return props.focus ? "40px" : "30px";
    default:
      return "35.4px";
  }
});

// 设置初始旋转角度
onMounted(() => {
  markerExtraStyle.value = {
    transform: `rotate(${props.direction}deg)`,
    transition: `all 1s ease`,
  };
});

// 设置旋转角度
const markerExtraStyle = ref<Record<string, any>>({});
// const transformAngle = ref(0);
watch(
  () => socket.baseStatus,
  (newV) => {
    const deviceId = props.status.deviceId;
    const target = newV?.find((item) => item.deviceId === deviceId);
    if (target) {
      // const currentDirection = props.direction || 0;
      // const newDirection = target.direction || 0;

      // // 顺时针角度
      // const clockwiseAngle = (newDirection - currentDirection + 360) % 360;
      // // 逆时针角度
      // const counterclockwiseAngle = (currentDirection - newDirection + 360) % 360;

      // let angle; // 旋转角度
      // let direction; // 1:顺时针 -1:逆时针

      // if (clockwiseAngle < counterclockwiseAngle) {
      //   angle = clockwiseAngle;
      //   direction = 1;
      // } else {
      //   angle = counterclockwiseAngle;
      //   direction = -1;
      // }
      // transformAngle.value += angle * direction;

      markerExtraStyle.value = {
        transform: `rotate(${target.direction}deg)`, // direction为逆时针旋转度数 transform取反
        transition: `all 1s ease`,
      };
    }
  }
);
</script>

<style lang="scss" scoped>
.car-live-marker {
  position: relative;
  .marker-radar {
    @include wh(100%) {
      border-radius: 50%;
    }
    &-ripple {
      @include ct-p;
      @include wh(10px) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: Ripple 1s linear infinite;
      &.disable {
        background-color: rgba(140, 140, 140, 0.55);
        box-shadow: 0px 0px 2px 4px rgba(140, 140, 140, 0.55);
        border: 1px solid rgba(140, 140, 140, 0.55);
      }
    }
    @keyframes Ripple {
      to {
        width: 40px;
        height: 40px;
        opacity: 0.3;
      }
    }
    &-focus-ripple {
      @include ct-p;
      @include wh(20px) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: focusRipple 2.4s linear infinite;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1.2s;
      }
      &.disable {
        background-color: rgba(140, 140, 140, 0.55);
        box-shadow: 0px 0px 2px 4px rgba(140, 140, 140, 0.55);
        border: 1px solid rgba(140, 140, 140, 0.55);
      }
    }
    @keyframes focusRipple {
      80% {
        width: 60px;
        height: 60px;
        opacity: 0.3;
      }
      100% {
        width: 40px;
        height: 40px;
        opacity: 0.3;
      }
    }
  }
  .marker-car-num {
    @include ct-p(x) {
      top: -20px;
    }
    height: 16px;
    padding: 0 4px;
    background-color: rgba(89, 100, 251, 0.8);

    border-radius: 2px;
    @include sc(12px, #fff) {
      line-height: 16px;
    }
    white-space: nowrap;
    &.disable {
      background-color: rgba(90, 90, 90, 0.8);
    }
  }
  .marker-warn-icon {
    @include ct-p(x) {
      top: -52px;
    }
    .flicker-animate {
      animation-duration: 0.8s;
      animation-name: flicker;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
      @keyframes flicker {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0.4;
        }
      }
    }
  }
}
</style>
