const getUnit = (value: string): string => {
  if (typeof value === "number") {
    return "px";
  }
  return value.match(/[a-z%]+$/)?.[0] || "";
};

const parseValue = (value: string): number => {
  return parseFloat(value) || 0;
};

type AnimationMode = "linear" | "ease" | "ease-in" | "ease-out" | "ease-in-out";

/**
 * 使用动画效果处理指定元素的属性变化
 * @param {HTMLElement} element   运动对象，必选
 * @param {JSON}        target    属性：目标值，必选
 * @param {number}      duration  运动时间，可选
 * @param {string}      mode      运动模式，可选
 * @return {Promise}    返回 Promise 对象，当动画完成时 Promise 执行成功
 */
export const animate = (
  element: HTMLElement,
  target: Record<string, number | string>,
  duration: number = 400,
  mode: AnimationMode = "linear"
): Promise<void> => {
  return new Promise<void>((resolve) => {
    // 存储属性初始样式和单位
    const initialStyles: Record<string, number | string> = {};
    const unit: Record<string, string> = {};
    for (const prop in target) {
      initialStyles[prop] =
        prop === "scrollTop" ? element[prop] : element.style[prop] || "";
      unit[prop] = getUnit(target[prop]);
    }
    // 获取起始时间戳
    const startTime = performance.now();
    let flag = false;
    requestAnimationFrame(function animateStep(timestamp) {
      const time = timestamp - startTime;
      const elapsed = time > duration ? duration : time;
      if (elapsed == duration) {
        flag = true;
      }
      // 更新目标属性
      for (const prop in target) {
        const startValue = parseValue(initialStyles[prop]);
        const endValue = parseValue(target[prop]);
        const currentValue = calculateCurrentValue(
          startValue,
          endValue,
          duration,
          elapsed,
          mode
        );
        if (prop === "scrollTop") {
          element[prop] = currentValue;
        } else {
          element.style[prop] = currentValue + unit[prop];
        }
      }
      // 动画未完成继续动画 否则结束
      if (!flag) {
        requestAnimationFrame(animateStep);
      } else {
        resolve();
      }
    });
  });
};

// 根据动画模式和经过的时间计算当前值
const calculateCurrentValue = (
  startValue: number,
  endValue: number,
  duration: number,
  elapsed: number,
  mode: AnimationMode
): number => {
  switch (mode) {
    case "linear":
    case "ease":
      return ease(elapsed / duration) * (endValue - startValue) + startValue;
    case "ease-in":
      return easeIn(elapsed / duration) * (endValue - startValue) + startValue;
    case "ease-out":
      return easeOut(elapsed / duration) * (endValue - startValue) + startValue;
    case "ease-in-out":
      return (
        easeInOut(elapsed / duration) * (endValue - startValue) + startValue
      );
    default:
      return linear(elapsed / duration) * (endValue - startValue) + startValue;
  }
};

// 不同的缓动函数，用于不同的动画模式
const linear = (t: number): number => {
  return t;
};

const ease = (t: number): number => {
  return t * t * t;
};

const easeIn = (t: number): number => {
  return t * t * t * t;
};

const easeOut = (t: number): number => {
  return 1 - easeIn(1 - t);
};

const easeInOut = (t: number): number => {
  return t < 0.5 ? 2 * t * t : 1 - Math.pow(-2 * t + 2, 2) / 2;
};

export const animateTime = (callback: Function, interval: number = 0) => {
  let startTime: number | null = null;
  let requestId: number;
  let shouldStop = false; // 在callback中无法取消，因为cancelAnimationFrame只能取消未执行的
  function loop(timestamp: number) {
    if (!startTime) startTime = timestamp;
    const elapsed = timestamp - startTime;

    if (elapsed >= interval) {
      startTime = timestamp;
      if (shouldStop) return;
      callback();
      if (shouldStop) return;
    }
    requestId = requestAnimationFrame(loop);
  }
  requestId = requestAnimationFrame(loop);
  // 返回一个停止函数，允许外部停止动画
  return () => {
    shouldStop = true;
    cancelAnimationFrame(requestId);
  };
};
