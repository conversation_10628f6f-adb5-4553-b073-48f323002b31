<template>
  <section
    ref="carStatsRef"
    class="car-stats"
    v-xloading="!socket.enableCarTree"
  >
    <div class="car-stats-top">
      <div class="top-left">{{ $t("carRealTimeStatus") }}</div>
      <div class="top-right"><span></span><span></span><span></span></div>
    </div>
    <div class="car-stats-center">
      <div
        class="center-item"
        v-for="(item, index) in stats.base"
        :key="index"
      >
        <div
          :class="['center-item-inner', { enable: stats.baseActIndex === index }]"
          @click="toggleBase(index)"
        >
          <div class="center-item-number">{{ item.number }}</div>
          <div
            class="center-item-color"
            :style="{ backgroundColor: item.color }"
          ></div>
          <div class="center-item-type">{{ item.type }}</div>
        </div>
      </div>
    </div>
    <div class="car-stats-bottom">
      <div class="bottom-filter">
        <x-select
          v-model:value="stats.carId"
          :options="stats.carOptions"
          placeholder="请输入车牌名搜索"
          showSearch
          @itemClick="selCarHandle"
        />
        <x-icon
          style="cursor: pointer"
          name="map_filter"
          width="20px"
          height="20px"
          @click="handelFilter"
        />
      </div>
      <x-tree
        class="bottom-tree"
        :treeData="filterTreeData"
        :selectedItems="socket.selectedCarItem"
        @update:selectedItems="updateSelectedCar"
        :clickHandle="clickCarHandle"
        checkable
        autoExpandParent
      >
        <template #title="{ item }">
          <template v-if="item._type === 'car'">
            <div class="car-stats-bottom-tree-item">
              <div :class="['item-online', { disable: !item.online }]"></div>
              <div :class="['item-title', { disable: !item.online }]">
                {{ item.title }}
              </div>
              <div
                v-show="item.warnLevel !== 4 && item.online"
                class="item-warn"
              >
                <x-icon
                  :name="`alarm_level_${item.warnLevel}`"
                  width="12px"
                  height="12px"
                />
              </div>
              <div
                v-show="item.warnWater && item.online"
                class="item-warn"
              >
                <x-icon
                  name="alarm_water"
                  width="12px"
                  height="12px"
                />
              </div>
              <div
                v-show="item.warnElec && item.online"
                class="item-warn"
              >
                <x-icon
                  name="alarm_battery"
                  width="12px"
                  height="12px"
                />
              </div>
            </div>
          </template>
          <template v-else> {{ item.title }} </template>
        </template>
      </x-tree>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch } from "vue";
import type { PropType } from "vue";
import { liveCarTree } from "@/services/api";
import { addCar, delCar } from "@/services/wsapi";
import { useMainStore } from "@/stores/main";
import { resTreeToXTree, fixXTreeChecked, treeBfsParse, isProject } from "@/assets/ts/utils";
import { mapZoom } from "@/services/wsconfig";
import xTree from "@/components/x-tree";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xSelect from "@/components/x-select.vue";
import type { OptionsType } from "@/components/x-select.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { animate } from "@/assets/ts/animate";
import XIcon from "@/components/x-icon.vue";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  map: {
    type: Object as PropType<AMap.Map>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
  carFilterShow: Boolean,
  carFilterActiveId: String,
});
const emit = defineEmits(["update:carFilterShow"]);

const { socket } = useMainStore();
const carStatsRef = ref<HTMLElement>();
type CarOptionType = OptionsType[0] & TreeItemType;
const stats = reactive({
  base: computed(() => [
    {
      type: "全部",
      number: (socket.statsStatus?.online || 0) + (socket.statsStatus?.offline || 0),
      color: "#28E09F",
    },
    {
      type: $t("online"),
      number: socket.statsStatus?.online || 0,
      color: "#5964FB",
    },
    {
      type: $t("offline"),
      number: socket.statsStatus?.offline || 0,
      color: "#DCE3FB",
    },
    {
      type: $t("alarm"),
      number: socket.statsStatus?.warnNum || 0,
      color: "#E24562",
    },
  ]),
  baseActIndex: 0,
  allCars: {} as any,
  carId: "",
  carOptions: [] as CarOptionType[],
});

const toggleBase = (index: number) => {
  stats.baseActIndex = index;
  stats.carId = "";
  updateCarOptions();
  clearAllCars();
};

const handelFilter = () => {
  emit("update:carFilterShow", true);
};

const updateCarOptions = () => {
  const newCarOptions: CarOptionType[] = [];
  treeBfsParse(filterTreeData.value, "children", (item: TreeItemType) => {
    if (item.vehicleNo) {
      newCarOptions.push({
        ...item,
        label: item.vehicleNo,
        value: item.vehicleNo,
      });
    }
  });
  stats.carOptions = newCarOptions;
};

const clearAllCars = () => {
  delCar(socket.enableCarIds);
  // 由于x-tree的props.treeData未使用深度监听(性能优化)，需要手动更改
  socket.selectedCarItem = [];
  treeBfsParse(socket.enableCarTree || [], "children", (item: TreeItemType) => {
    item.checked = false;
    item.indeterminate = false;
  });
  useMainStore().updateEnableCarIds([]);
};

const filterTreeData = computed<any>(() => {
  const filterHandle = [
    () => true,
    (carItem: CarOptionType) => stats.allCars[carItem.vehicleNo]?.online,
    (carItem: CarOptionType) => !stats.allCars[carItem.vehicleNo]?.online,
    (carItem: CarOptionType) => stats.allCars[carItem.vehicleNo]?.warn,
  ][stats.baseActIndex];
  return socket.enableCarTree?.reduce((acc, proItem) => {
    const allowItems =
      proItem._children?.filter(filterHandle).map((item: CarOptionType) => ({
        ...item,
        checked: socket.enableCarIds.some((v) => v === item.title),
        online: stats.allCars[item.vehicleNo]?.online || false,
        warn: stats.allCars[item.vehicleNo]?.warn || false,
        warnLevel: stats.allCars[item.vehicleNo]?.warnLevel || undefined,
        warnWater: stats.allCars[item.vehicleNo]?.warnWater || undefined,
        warnElec: stats.allCars[item.vehicleNo]?.warnElec || undefined,
      })) || [];
    if (allowItems.length) {
      proItem.children = allowItems;
      acc.push(proItem);
    }
    return acc;
  }, []);
});

const clickCarHandle = (item: TreeItemType | CarOptionType) => {
  const carIds = isProject(item) ? item.children?.map((v) => v.title) : [item.title];
  const carBaseStatus = socket.baseStatus?.filter((v) => carIds?.includes(v.deviceId))!;
  props.map.clearInfoWindow();
  props.map.setZoomAndCenter(mapZoom, [carBaseStatus[0].longitude, carBaseStatus[0].latitude]);
  props.map
    .getAllOverlays()
    .filter((v) => v.getExtData()?.key)
    .forEach((v: AMap.Marker) => {
      if (v.getExtData()?.key === item.title) {
        v.setzIndex(20);
      } else {
        v.setzIndex(12);
      }
    });
};
const updateSelectedCar = (selectedItems: TreeItemType[]) => {
  let selectCars = selectedItems.filter((v) => v.vehicleNo).map((v) => v.vehicleNo);
  if (selectCars.length > socket.enableCarIds.length) {
    // 解决carlist中删除车辆后再次勾选 车辆按原顺序插入而未有序排列的问题
    selectCars = [...socket.enableCarIds, ...selectCars.filter((id) => !socket.enableCarIds.includes(id))];
    addCar(selectCars);
  } else {
    delCar(socket.enableCarIds.filter((id) => !selectCars.includes(id)));
  }
  useMainStore().updateEnableCarIds(selectCars);
};

const selCarHandle = (carItem: CarOptionType) => {
  clickCarHandle(carItem);
  const itemHeight = 32;
  let scrollTop = 0;
  outerLoop: for (const proItem of filterTreeData.value) {
    scrollTop += itemHeight;
    const expanded = proItem.expanded;
    for (const [index, _carItem] of proItem.children.entries()) {
      if (_carItem.vehicleNo === carItem.vehicleNo) {
        proItem.expanded = true;
        scrollTop += index * itemHeight;
        break outerLoop;
      }
    }
    expanded && (scrollTop += proItem.children.length * itemHeight);
  }
  animate(carStatsRef.value!.querySelector(".bottom-tree")!, { scrollTop }, 500);
};

watch(
  () => socket.baseStatus,
  (newV) => {
    newV?.forEach((v) => {
      stats.allCars[v.deviceId] = {
        online: v.online,
        warn: v.warn,
        warnLevel: v.warnLevel,
        warnWater: v.warnWater,
        warnElec: v.warnElec,
      };
    });
    // filterTreeData; // 不会自动触发，只能手动触发 filterTreeData computed
  },
  { immediate: true }
);
watch(
  () => props.carFilterActiveId,
  (newV) => {
    if (newV) {
      const carItem = filterTreeData.value.find((v: TreeItemType) => v.title === newV);
      selCarHandle(carItem);
    }
  }
);

(async () => {
  socket.enableCarTree = fixXTreeChecked(resTreeToXTree((await liveCarTree(socket.enableCarIds)).proList));

  treeBfsParse(socket.enableCarTree, "children", (item: TreeItemType) => {
    if (!item.vehicleNo) {
      item._children = item.children; // 给项目的所有车保存，用于筛选功能
    }
  });

  updateCarOptions();
})();
</script>

<style lang="scss" scoped>
.car-stats {
  position: absolute;
  top: 1px;
  right: 0;
  min-width: 240px;
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 42px) {
      padding: 0 18px 0 9px;
      background-color: rgb(247, 249, 254);
    }
    .top-left {
      @include sc(12px, #242859) {
        font-weight: bolder;
      }
    }
    .top-right {
      @include ct-f(y);
      span {
        display: block;
        background-color: rgb(89, 100, 251);
        border-radius: 50%;
      }
      span:nth-child(1) {
        @include wh(3px);
      }
      span:nth-child(2) {
        @include wh(4px) {
          margin-left: 2px;
        }
      }
      span:nth-child(3) {
        @include wh(6px) {
          margin-left: 2px;
        }
      }
    }
  }
  &-center {
    display: flex;
    @include wh(100%, 90px) {
      background-color: rgb(247, 249, 254);
    }
    .center-item {
      height: 100%;
      padding: 6px 8px;
      flex: 1;
      &-inner {
        cursor: pointer;
        @include wh(100%) {
          padding-top: 10px;
        }

        &.enable {
          position: relative;
          background-color: rgb(235, 239, 250);
          border-radius: 2px;
          &::before {
            @include ct-p(x) {
              bottom: 0;
            }
            @include triangle(4px, 6px, rgb(255, 255, 255), top);
          }
        }
      }
      &-number {
        @include sc(16px, #242859) {
          line-height: 24px;
          text-align: center;
          font-weight: bolder;
        }
      }
      &-color {
        @include wh(8px, 2px) {
          border-radius: 4px;
          margin: 6px auto 0 auto;
        }
      }
      &-type {
        margin-top: 5px;
        @include sc(12px, #9f9fa4) {
          line-height: 18px;
          text-align: center;
        }
      }
    }
  }
  &-bottom {
    @include fj {
      flex-direction: column;
    }
    width: 100%;
    height: 320px;
    // min-height: 194px;
    // max-height: 290px;
    padding: 0 10px 10px 10px;
    .bottom-filter {
      @include fj {
        align-items: center;
        column-gap: 5px;
      }
      @include wh(100%, 52px);
    }
    .bottom-tree {
      width: 100%;
      flex: 1;
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    }

    &-tree-item {
      @include ct-f(y);
      .item-online {
        position: relative;
        @include wh(10px) {
          background-color: #5964fb;
          border-radius: 50%;
          margin-right: 2px;
        }
        &.disable {
          background-color: #dce3fb;
        }
        &::after {
          content: "";
          @include ct-p;
          @include wh(5px) {
            border-radius: 50%;
            background-color: #fff;
          }
        }
      }
      .item-title {
        &.disable {
          color: #9f9fa4;
        }
      }
      .item-warn {
        @include ct-f;
        margin-left: 8px;
      }
    }
  }
}
</style>
