// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 默认快照 1`] = `
"<section class="x-transfer" data-v-ccaa7f0a="">
  <div class="x-transfer-left" data-v-ccaa7f0a="">
    <div class="content-top" data-v-ccaa7f0a=""><span class="content-top-title" data-v-ccaa7f0a="">Source</span><span class="content-top-number" data-v-ccaa7f0a=""> （3项） </span></div>
    <div class="content-search" data-v-ccaa7f0a="">
      <x-input-stub placeholder="请输入搜索内容" suffix="input_search" type="text" allowclear="true" disabled="false" value="" data-v-ccaa7f0a=""></x-input-stub>
    </div>
    <div class="content-list" data-v-ccaa7f0a="">
      <x-tree-stub treedata="[object Object],[object Object]" autoexpandparent="false" selecteditems="" selectedtitles="" selectedkeys="" expandedkeys="" checkable="true" checkedkeys="" multiple="false" data-v-ccaa7f0a=""></x-tree-stub>
    </div>
    <div class="content-bottom" data-v-ccaa7f0a="">
      <div class="content-bottom-select-all" data-v-ccaa7f0a="">
        <x-checkbox-stub text="全选" checked="false" indeterminate="false" disabled="false" stoppropagation="true" class="selectAll" data-v-ccaa7f0a=""></x-checkbox-stub>
      </div>
      <div class="content-bottom-selected" data-v-ccaa7f0a=""> 已选0项</div>
    </div>
  </div>
  <div class="x-transfer-center" data-v-ccaa7f0a="">
    <div class="x-transfer-center-center" data-v-ccaa7f0a="">
      <div class="center-right-arrow" data-v-ccaa7f0a=""></div>
      <div class="center-left-arrow" data-v-ccaa7f0a=""></div>
    </div>
  </div>
  <div class="x-transfer-right" data-v-ccaa7f0a="">
    <div class="content-top" data-v-ccaa7f0a=""><span class="content-top-title" data-v-ccaa7f0a="">Target</span><span class="content-top-number" data-v-ccaa7f0a="">（1项）</span></div>
    <div class="content-search" data-v-ccaa7f0a="">
      <x-input-stub placeholder="请输入搜索内容" suffix="input_search" type="text" allowclear="true" disabled="false" value="" data-v-ccaa7f0a=""></x-input-stub>
    </div>
    <div class="content-list" data-v-ccaa7f0a="">
      <x-tree-stub treedata="[object Object]" autoexpandparent="false" selecteditems="" selectedtitles="" selectedkeys="" expandedkeys="" checkable="true" checkedkeys="" multiple="false" data-v-ccaa7f0a=""></x-tree-stub>
    </div>
    <div class="content-bottom" data-v-ccaa7f0a="">
      <div class="content-bottom-select-all" data-v-ccaa7f0a="">
        <x-checkbox-stub text="全选" checked="false" indeterminate="false" disabled="false" stoppropagation="true" class="selectAll" data-v-ccaa7f0a=""></x-checkbox-stub>
      </div>
      <div class="content-bottom-selected" data-v-ccaa7f0a=""> 已选0项</div>
    </div>
  </div>
</section>"
`;
