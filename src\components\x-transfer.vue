<template>
  <section class="x-transfer">
    <div class="x-transfer-left">
      <div class="content-top">
        <span class="content-top-title">{{ props.titles[0] }}</span>
        <span class="content-top-number">
          （{{ totalNum }}{{ _locale.itemUnit }}）
        </span>
      </div>
      <div class="content-search">
        <x-input
          :value="left.searchValue"
          @update:value="leftSearchValueUpdate"
          :placeholder="_locale.searchPlaceholder"
          suffix="input_search"
          allowClear
        />
      </div>
      <div class="content-list">
        <x-tree
          :treeData="leftSource"
          v-model:selectedItems="left.selectedItems"
          checkable
        >
          <template #title="{ title }">
            <template
              v-for="(item, index) in title.split(
                new RegExp(
                  `(?<=${left.searchValue})|(?=${left.searchValue})`,
                  'i'
                )
              )"
              :key="index"
            >
              <template
                v-if="
                  left.searchValue.toLowerCase().includes(item.toLowerCase())
                "
              >
                <span style="color: #5964fb">{{ item }}</span>
              </template>
              <template v-else>{{ item }}</template>
            </template>
          </template>
        </x-tree>
      </div>
      <div class="content-bottom">
        <div class="content-bottom-select-all">
          <x-checkbox
            v-if="props.showSelectAll"
            :text="$t('fullSelect')"
            :checked="Boolean(leftSelectAll)"
            :class="{
              selectAll: leftSelectAll || rightSource.length !== totalNum,
            }"
            @update:checked="leftSelectAllUpdate"
          />
        </div>
        <div class="content-bottom-selected">
          {{ $t("alreadySelect") }}{{ leftSelectedCount }}{{ _locale.itemUnit }}
        </div>
      </div>
    </div>
    <div class="x-transfer-center">
      <div class="x-transfer-center-center">
        <div
          :class="['center-right-arrow', { enable: canLeftToRight }]"
          @click="leftToRight"
        ></div>
        <div
          :class="['center-left-arrow', { enable: canRightToLeft }]"
          @click="rightToLeft"
        ></div>
      </div>
    </div>
    <div class="x-transfer-right">
      <div class="content-top">
        <span class="content-top-title">{{ props.titles[1] }}</span>
        <span class="content-top-number"
          >（{{ rightSource.length }}{{ _locale.itemUnit }}）</span
        >
      </div>
      <div class="content-search">
        <x-input
          :value="right.searchValue"
          @update:value="rightSearchValueUpdate"
          :placeholder="_locale.searchPlaceholder"
          suffix="input_search"
          allowClear
        />
      </div>
      <div class="content-list">
        <x-tree
          :treeData="rightSource"
          v-model:selectedItems="right.selectedItems"
          checkable
        >
          <template #title="{ title }">
            <template
              v-for="(item, index) in title.split(
                new RegExp(
                  `(?<=${right.searchValue})|(?=${right.searchValue})`,
                  'i'
                )
              )"
              :key="index"
            >
              <template
                v-if="
                  right.searchValue.toLowerCase().includes(item.toLowerCase())
                "
              >
                <span style="color: #5964fb">{{ item }}</span>
              </template>
              <template v-else>{{ item }}</template>
            </template>
          </template>
        </x-tree>
      </div>
      <div class="content-bottom">
        <div class="content-bottom-select-all">
          <x-checkbox
            v-if="props.showSelectAll"
            :text="$t('fullSelect')"
            :checked="Boolean(rightSelectAll)"
            :class="{
              selectAll: rightSelectAll || rightSource.length > 0,
            }"
            @update:checked="rightSelectAllUpdate"
          />
        </div>
        <div class="content-bottom-selected">
          {{ $t("alreadySelect") }}{{ right.selectedItems.length
          }}{{ _locale.itemUnit }}
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { reactive, computed, watch, toRef } from "vue";
import type { PropType } from "vue";
import { treeBfsParse, treePostDfs } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xTree from "@/components/x-tree";
import xCheckbox from "@/components/x-checkbox.vue";
import xInput from "@/components/x-input.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");
const props = defineProps({
  titles: {
    // 标题集合，顺序从左至右
    type: Array as PropType<string[]>,
    default: () => ["Source", "Target"],
  },
  dataSource: {
    // 渲染到左侧框的数据源 (targetKeys除外)
    type: Array as PropType<TreeItemType[]>,
    required: true,
  },
  targetSource: {
    // 渲染到右侧框的列表数据的集合
    type: Array as PropType<TreeItemType[]>,
    required: true,
  },
  showSearch: {
    type: Boolean,
    default: () => false,
  },
  showSelectAll: {
    type: Boolean,
    default: () => true,
  },
  locale: {
    type: Object as PropType<Partial<typeof defaultLocale>>,
  },
});

const defaultLocale = {
  itemUnit: $t("item"),
  // itemsUnit: "项",
  notFoundContent: $t("listEmpty"),
  searchPlaceholder: $t("PEntSearchContent"),
};
const _locale = { ...defaultLocale, ...props.locale };
const emits = defineEmits(["update:selectedKeys", "update:targetSource"]);

// toRef：创建不可变引用，解决传入嵌套数据时丢失响应性的问题
const leftSource = toRef(props, "dataSource");
const totalNum = computed({
  get() {
    let count = 0;
    treeBfsParse(leftSource.value, "children", (item: TreeItemType) => {
      if (!(item.children && item.children.length > 0)) count += 1;
    });
    return count;
  },
  set() {},
});

const left = reactive({
  searchValue: "",
  selectAll: props.dataSource.every((v) => v.checked),
  selectedItems: [] as TreeItemType[],
});

const leftDfs = (tree: TreeItemType[]) => {
  treePostDfs(tree, "children", (item: TreeItemType) => {
    if (item.children && item.children.length > 0) {
      item.expanded = item.children.some(
        (v) => v.title.includes(left.searchValue) || v.expanded
      );
    }
  });
};

watch(
  () => left.searchValue,
  (newV) => {
    newV && leftDfs(leftSource.value);
  }
);

watch(
  () => leftSource.value,
  () => {
    treeBfsParse(leftSource.value, "children", (item: TreeItemType) => {
      if (!(item.children && item.children.length > 0)) totalNum.value += 1;
    });
  }
);

const leftSearchValueUpdate = (value: string) => {
  left.searchValue = value;
};
const leftSelectAllUpdate = (bool: boolean) => {
  treeBfsParse(props.dataSource, "children", (item: TreeItemType) => {
    if (!item.disabled) {
      item.checked = bool;
    }
    item.indeterminate = false;
    const index = left.selectedItems.findIndex((v) => v.value === item.value);
    // 不存在 && 选中
    if (!~index && item.checked) {
      left.selectedItems.push(item);
      // 存在 && 没选中
    } else if (~index && !item.checked) {
      left.selectedItems.splice(index, 1);
    }
  });
  left.selectAll = bool;
};
const canLeftToRight = computed(() =>
  leftSource.value.some((item) => item.checked || item.indeterminate)
);
const leftToRight = () => {
  left.selectedItems.forEach((item) => {
    item.checked = false;
    item.indeterminate = false;
  });
  const childItems = left.selectedItems.filter(
    (item) => !(item.children && item.children.length > 0)
  );
  // 添加右侧
  rightSource.value.unshift(...JSON.parse(JSON.stringify(childItems)));
  // 处理左侧
  childItems.forEach((item) => (item.disabled = true));
  treePostDfs(leftSource.value, "children", (item: TreeItemType) => {
    if (item.children && item.children.length > 0) {
      item.checked = false;
      item.indeterminate = false;
      item.disabled = item.children.every((v) => v.disabled);
    }
  });

  left.selectedItems = [];
  left.selectAll = false;
  right.selectAll = false;
};

const rightSource = toRef(props, "targetSource");
const right = reactive({
  searchValue: "",
  selectAll: false,
  selectedItems: [] as TreeItemType[],
});
const rightSearchValueUpdate = (value: string) => {
  right.searchValue = value;
};
const rightSelectAllUpdate = (bool: boolean) => {
  rightSource.value
    .filter((v) => v.checked !== bool)
    .forEach((v) => {
      v.checked = bool;
      if (bool) {
        right.selectedItems.push(v);
      } else {
        right.selectedItems.splice(
          right.selectedItems.findIndex((v) => v.value === v.value),
          1
        );
      }
    });
  right.selectAll = bool;
};
const canRightToLeft = computed(() =>
  rightSource.value.some((item) => item.checked)
);
const rightToLeft = () => {
  right.selectedItems.forEach((item) => (item.checked = false));
  // 删除右侧
  const needDelIndexs = rightSource.value
    .map((v, i) => ({ index: i, item: v }))
    .filter((v) => right.selectedItems.includes(v.item))
    .map((v) => v.index);
  for (let i = needDelIndexs.length - 1; i > -1; i--) {
    rightSource.value.splice(needDelIndexs[i], 1);
  }
  // 处理左侧
  treePostDfs(leftSource.value, "children", (item: TreeItemType) => {
    // 父辈节点 && 禁用
    if (item.children && item.children.length > 0) {
      item.disabled = item.children.every((v) => v.disabled);
    } else if (right.selectedItems.some((v) => v.value === item.value)) {
      item.disabled = false;
    }
  });
  emits("update:targetSource", rightSource);
  right.selectedItems = [];
  right.selectAll = false;
  left.selectAll = false;
};
const leftSelectedCount = computed(
  () =>
    left.selectedItems.filter(
      (item) => !(item.children && item.children.length > 0)
    ).length
);
const leftSelectAll = computed(
  () =>
    leftSelectedCount.value &&
    (left.selectAll ||
      leftSelectedCount.value + rightSource.value.length === totalNum.value)
);
const rightSelectAll = computed(
  () =>
    right.selectedItems.length &&
    (right.selectAll || right.selectedItems.length === rightSource.value.length)
);
</script>

<style lang="scss" scoped>
.x-transfer {
  display: flex;
  @include wh(100%, 100%);
  &-left,
  &-right {
    height: calc(100% - 56px);
    flex: 1;
    width: 0;
    padding: 10px;
    background-color: #fff;
    .content-top {
      @include wh(100%, 22px);
      &-title {
        color: #383838;
      }
      &-number {
        color: rgb(159, 159, 164);
      }
    }
    .content-search {
      margin-top: 8px;
    }
    .content-list {
      width: 100%;
      height: calc(100% - 22px - 8px - 32px - 22px);
      padding-top: 16px;
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    }
    .content-bottom {
      @include fj;
      @include wh(100%, 22px);
    }
    .selectAll {
      :deep(.x-checkbox-text) {
        color: #000;
      }
    }
  }
  &-center {
    @include ct-f;
    @include wh(56px, 100%);
    &-center {
      @include fj {
        flex-direction: column;
      }
      @include wh(32px, 82px);
      .center-right-arrow,
      .center-left-arrow {
        cursor: pointer;
        position: relative;
        @include wh(32px) {
          background: rgb(255, 255, 255);
          border-radius: 4px;
          color: rgb(153, 153, 153);
        }
        &::before {
          content: "";
          position: absolute;
          top: 11px;
        }
        transition: all 0.4s ease-out;
        &.enable {
          background-color: #5964fb;
          color: #fff;
        }
      }
      .center-right-arrow {
        &::before {
          @include v-arrow(right, 2px, 8px);
          left: 9px;
        }
      }
      .center-left-arrow {
        &::before {
          @include v-arrow(left, 2px, 8px);
          left: 13px;
        }
      }
    }
  }
}
</style>
