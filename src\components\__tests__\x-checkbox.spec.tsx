import { describe, it, expect, afterEach, vi } from "vitest";
import { mount } from '@vue/test-utils';
import XCheckbox from '@/components/x-checkbox.vue';

const baseProps = { checked: false, text: 'checkbox文案' }

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = mount(XCheckbox,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = mount(XCheckbox,{
      props: {
        ...baseProps,
        indeterminate: true,
        disabled: true,
      },
    });
    expect(wrapper.find('.x-checkbox-text span').text()).toBe('checkbox文案');
    expect(wrapper.find('input').attributes().value).toBe('checkbox文案');

    expect(wrapper.find('.content-inner').classes().includes('indeterminate')).toBe(true);

    expect(wrapper.find('.content-inner').classes().includes('disabled')).toBe(true);
    expect(wrapper.find('input').attributes().disabled).toBeDefined();
    expect(wrapper.find('input').classes().includes('disabled')).toBe(true);
  });
  
});

describe('Events', () => {
  it('验证stopPropagation: false时可以冒泡', () => {
    const onOuterClick = vi.fn();
    const wrapper = mount({
      setup() {
        return () => <div onClick={onOuterClick}><XCheckbox {...baseProps} stopPropagation={false}/></div>
      }
    });
    wrapper.find('input').trigger('click');
    expect(onOuterClick).toHaveBeenCalled();
  });

  it('验证stopPropagation: true时无法冒泡', () => {
    const onOuterClick = vi.fn();
    const wrapper = mount({
      setup() {
        return () => <div onClick={onOuterClick}><XCheckbox {...baseProps} stopPropagation={true}/></div>
      }
    });
    wrapper.find('input').trigger('click');
    expect(onOuterClick).not.toHaveBeenCalled();
  });
});

describe('Slots', () => {
  it('label', () => {
    const wrapper = mount({
      setup() {
        return () => <XCheckbox {...baseProps}
          v-slots={{
            label: () => <span>测试label插槽</span>
          }}
        />
      }
    });
    expect(wrapper.find('.x-checkbox-text span').html()).toBe('<span>测试label插槽</span>');
  });
});
