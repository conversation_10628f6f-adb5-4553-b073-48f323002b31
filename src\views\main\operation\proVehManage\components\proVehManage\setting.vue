<template>
  <x-drawer
    :title="$t('setting')"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="20px 0 20px 20px"
    width="858px"
  >
    <div class="content">
      <div class="content-top">
        <span
          class="info"
          :class="{ active: isOnline }"
          >{{ props.vehicleNo }}</span
        >
        <div
          class="badge"
          :class="{ active: isOnline }"
        >
          {{ isOnline ? $t("online") : $t("offline") }}
        </div>
      </div>
      <div class="content-middle">
        <x-table
          :cols="table.cols"
          :dataSource="table.dataSource"
          :loading="table.loading"
        >
          <!-- 阀值（电池电量小于或等于） -->
          <template #threshold="{ record }"> {{ record.threshold }}% </template>
          <!-- 状态 -->
          <template #status="{ record }">
            <template v-if="record.settingKey === 'automaticRecharge'">
              <x-switch
                :checkedValue="true"
                :unCheckedValue="false"
                :unCheckedChildren="$t('off')"
                :checkedChildren="$t('on')"
                :checked="record.status"
                :disabled="record.settingKey === 'forcedShutdown'"
                @mousedown="beforeChangeSchedual(record)"
              />
            </template>
            <span v-else>{{ record.status ? "开启" : "关闭" }}</span>
          </template>
          <!-- 操作 -->
          <template #opera="{ record }">
            <div class="table-opera">
              <div class="table-opera-text">
                <span @click="openEdit(record)">{{ $t("edit") }}</span>
              </div>
            </div>
          </template>
        </x-table>
      </div>
      <div
        class="content-bottom"
        v-if="!table.loading"
      >
        <div>{{ $t("hmiSetting") }}</div>
        <x-button
          type="blue"
          size="small"
          :text="$t('resetPWD')"
          @click="resetPWD"
          style="padding: 5px"
        />
      </div>
      <div
        class="content-bottom"
        v-if="!table.loading"
      >
        <div>
          <span>{{ "HMI定时任务锁定" }}</span>
          <x-switch
            :checkedValue="true"
            :unCheckedValue="false"
            :unCheckedChildren="$t('off')"
            :checkedChildren="$t('on')"
            :checked="editModal.taskLocking"
            style="margin-left: 10px"
            @mousedown="beforeTaskLocking"
          />
        </div>
      </div>
      <div
        class="content-bottom"
        v-if="!table.loading"
      >
        <div style="margin-bottom: 5px; width: 100%; font-weight: bold">{{ "垃圾识别设置" }}</div>
        <div style="display: flex; column-gap: 30px; width: 100%">
          <div>
            <span>{{ "车辆垃圾识别" }}</span>
            <x-switch
              :checkedValue="true"
              :unCheckedValue="false"
              :unCheckedChildren="$t('off')"
              :checkedChildren="$t('on')"
              :checked="editModal.garbageIdentify"
              style="margin-left: 10px"
              @mousedown="beforeGarbageIdentify"
            />
          </div>
          <div>
            <span>{{ "对接第三方平台" }}</span>
            <x-switch
              :checkedValue="true"
              :unCheckedValue="false"
              :unCheckedChildren="$t('off')"
              :checkedChildren="$t('on')"
              :checked="editModal.dockingPlatform"
              style="margin-left: 10px"
              @mousedown="beforeDockingPlatform"
            />
          </div>
        </div>
      </div>
    </div>
    <edit
      v-model:show="editModal.show"
      :vehicleNo="props.vehicleNo"
      :settingName="editModal.settingName"
      :settingKey="editModal.settingKey"
      :threshold="editModal.threshold"
      :status="editModal.status"
      :station-id="editModal.stationId"
      @confirm="editModal.confirm"
    />
  </x-drawer>
</template>

<script lang="tsx" setup>
import { reactive, watch, createVNode, ref } from "vue";
import {
  getVehicleSetup,
  setVehicleSetup,
  resetHMIPassword,
  vehManageSetScheduleChange,
  vehicleInfoUpdateSwitch,
} from "@/services/api";
import { onBaseStatus, offBaseStatus } from "@/services/wsapi";
import Message from "@/components/x-message";
import XDrawer from "@/components/x-drawer.vue";
import xTable from "@/components/x-table.vue";
import xSwitch from "@/components/x-switch.vue";
import Edit from "./edit.vue";
import xButton from "@/components/x-button.vue";
import xModal from "@/components/x-modal";
import xIcon from "@/components/x-icon.vue";
import { useMainStore } from "@/stores/main";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

const { socket } = useMainStore();

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  vehicleNo: {
    type: String,
    required: true,
  },
  vehicleId: {
    type: String,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "30",
    },
    {
      key: "settingName",
      title: $t("settingItem"),
      width: "60",
    },
    {
      key: "threshold",
      title: $t("thresholdBattery"),
      width: "80",
      slots: "threshold",
    },
    {
      key: "status",
      title: $t("status"),
      width: "40",
      slots: "status",
    },
    {
      key: "litter",
      title: $t("opera"),
      width: "40",
      slots: "opera",
    },
  ],
  dataSource: [] as any[],
  loading: true,
});

const getSettingInfo = async () => {
  table.loading = true;
  try {
    const data = await getVehicleSetup({ vehNo: props.vehicleNo });
    table.dataSource = [
      {
        settingName: $t("automaticCharging"),
        settingKey: "automaticRecharge",
        threshold: data.automaticRechargeElectricity,
        status: data.automaticRecharge,
      },
      {
        settingName: $t("automaticShutdown"),
        settingKey: "forcedShutdown",
        threshold: data.forcedShutdownElectricity,
        status: true,
      },
      {
        settingName: $t("lowBatteryReminder"),
        settingKey: "powerReminder",
        threshold: data.powerReminderElectricity,
        status: data.powerReminder,
      },
      // TODO: 待后端加接口字段[自动加水]
      // {
      //   settingName: $t("automaticWater"),
      //   settingKey: "automaticWater",
      //   threshold: 20,
      //   status: false,
      // },
    ].map((item, index) => ({
      orderId: (index + 1).toString().padStart(3, "0"),
      ...item,
    }));
    editModal.garbageIdentify = data.snapSwitch;
    editModal.dockingPlatform = data.xinheSwitch;
  } catch (error) {
    console.error(error);
  } finally {
    table.loading = false;
  }
};

/** 更改状态 */
const beforeChangeSchedual = async (record: any) => {
  const params = {
    vehNo: props.vehicleNo,
    [record.settingKey]: !record.status,
  };
  try {
    await setVehicleSetup(params);
    Message("success", `${record.settingName ? $t("close") : $t("open")}${$t("success")}`);
  } finally {
    getSettingInfo();
  }
};

const taskLocking = ref(false);
/** HMI定时任务锁定 */
const beforeTaskLocking = async () => {
  if (taskLocking.value) return;
  taskLocking.value = true;
  try {
    await vehManageSetScheduleChange({ id: props.id, scheduleChange: !editModal.taskLocking ? 0 : 1 });
    Message("success", `HMI定时任务锁定${editModal.taskLocking ? $t("close") : $t("open")}${$t("success")}`);
  } finally {
    // getSettingInfo();
    taskLocking.value = false;
  }
};

/** 车辆垃圾识别 */
const beforeGarbageIdentify = async () => {
  try {
    await vehicleInfoUpdateSwitch({ id: props.vehicleId, snapSwitch: !editModal.garbageIdentify });
    Message("success", `车辆垃圾识别${!editModal.garbageIdentify ? $t("open") : $t("close")}${$t("success")}`);
  } finally {
    getSettingInfo();
  }
};

/** 对接第三方平台 */
const beforeDockingPlatform = async () => {
  try {
    await vehicleInfoUpdateSwitch({ id: props.vehicleId, xinheSwitch: !editModal.dockingPlatform });
    Message("success", `对接第三方平台${!editModal.dockingPlatform ? $t("open") : $t("close")}${$t("success")}`);
  } finally {
    getSettingInfo();
  }
};
// 编辑
const editModal = reactive({
  show: false,
  status: false,
  settingKey: "",
  settingName: "",
  threshold: 0,
  stationId: 0,
  /** HMI定时任务锁定 */
  taskLocking: false,
  /** 车辆垃圾识别 */
  garbageIdentify: false,
  /** 对接第三方平台 */
  dockingPlatform: false,
  confirm: () => getSettingInfo(),
});
const openEdit = (record: any) => {
  const { status, settingName, settingKey, threshold, stationId } = record;
  editModal.status = status;
  editModal.settingName = settingName;
  editModal.settingKey = settingKey;
  editModal.threshold = threshold;
  editModal.stationId = stationId; //TODO: 因后端与车端协议未对接，暂无该字段
  editModal.show = true;
};

// 重置密码
const resetPWD = () => {
  xModal.confirm({
    title: $t("sureToResetHMIPWD"),
    content: (
      <div>
        {$t("car")}：{props.vehicleNo}
      </div>
    ),
    confirm() {
      return resetHMIPassword(props.vehicleNo).then((res) => {
        xModal.success({
          width: "345px",
          title: `${$t("car")}${props.vehicleNo}${$t("resetPWDSuccess")}！`,
          content: (
            <div>
              <span style="margin-right: 10px;">
                {" "}
                {$t("newPWD")}：{res.pwd}
              </span>
              <div
                style="cursor: pointer;display:inline-block;"
                onClick={() => copyPassword(res.pwd)}
              >
                {createVNode(xIcon, {
                  name: "copy",
                  width: "16px",
                  height: "16px",
                })}
              </div>
            </div>
          ),
        });
      });
    },
  });
};

const copyPassword = (pwdText: string) => {
  if (window.isSecureContext && navigator.clipboard) {
    navigator.clipboard.writeText(pwdText).then(() => {
      Message("success", $t("copySuccess"));
    });
  } else {
    unsecuredCopyToClipboard(pwdText);
  }
};
const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    document.execCommand("copy");
    Message("success", $t("copySuccess"));
  } catch (err) {
    console.error("Unable to copy to clipboard", err);
  }
  document.body.removeChild(textArea);
};

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (!bool) {
    offBaseStatus();
  }
};

const isOnline = ref(false);
watch(
  () => socket.baseStatus,
  (newV) => {
    if (newV) {
      isOnline.value = newV?.find((car) => car.deviceId === props.vehicleNo)?.online || false;
    }
  }
);

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      onBaseStatus();
      getSettingInfo();
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  margin-right: 20px;
  height: 100%;
  &-top {
    @include ct-f(y);
    margin-bottom: 15px;
    .info {
      @include sc(14px, #9f9fa4) {
        font-weight: bold;
      }
      &.active {
        color: #383838;
      }
    }
    .badge {
      @include wh(31px, 18px);
      @include ct-f;
      margin-left: 5px;
      border-radius: 4px;
      @include sc(12px, #fff);
      background: #c4c4c4;
      &.active {
        background: #5964fb;
      }
    }
  }
  &-middle {
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
  &-bottom {
    @include ct-f(y) {
      justify-content: space-between;
      flex-wrap: wrap;
    }
    @include wh(100%, auto);
    @include sc(14px, rgb(56, 56, 56));
    min-height: 53px;
    padding: 12px 10px;
    margin-top: 10px;
    background: #fff;
    border-radius: 8px;
    box-sizing: border-box;
  }
}
</style>
