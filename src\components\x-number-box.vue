<template>
  <div class="x-number-box">
    <div
      class="x-number-box-btn left"
      :class="{ disabled: inputVal <= min }"
      @click="decrement"
    >
      -
    </div>
    <div class="x-number-box-input">
      <input
        v-model="inputVal"
        @input="handleInputVal"
        @blur="handleInputVal"
      />
    </div>
    <div
      class="x-number-box-btn right"
      :class="{ disabled: inputVal >= max }"
      @click="increment"
    >
      +
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";

const props = defineProps({
  value: {
    type: Number,
    default: 0,
  },
  min: {
    type: Number,
    default: -Infinity,
  },
  max: {
    type: Number,
    default: Infinity,
  },
});

const inputVal = ref(props.value);

watch(
  () => props.value,
  (newV) => {
    inputVal.value = newV;
  }
);

const emits = defineEmits(["change"]);

const handleInputVal = () => {
  inputVal.value = parseInt(inputVal.value) || props.min;
  if (inputVal.value < props.min) {
    inputVal.value = props.min;
  } else if (inputVal.value > props.max) {
    inputVal.value = props.max;
  }
  emits("change", inputVal.value);
};

const decrement = () => {
  if (inputVal.value > props.min) {
    inputVal.value = Math.max(inputVal.value - 1, props.min);
    emits("change", inputVal.value);
  }
};

const increment = () => {
  if (inputVal.value < props.max) {
    inputVal.value = Math.min(inputVal.value + 1, props.max);
    emits("change", inputVal.value);
  }
};
</script>

<style lang="scss" scoped>
.x-number-box {
  display: flex;
  @include wh(80px, 25px);
  &-btn {
    @include ct-f;
    @include wh(25px, 25px);
    cursor: pointer;
    color: #9f9fa4;
    background: #fff;
    &.left {
      border-radius: 8px 0 0 8px;
    }
    &.right {
      border-radius: 0 8px 8px 0;
    }
    &.disabled {
      cursor: not-allowed;
    }
  }
  &-input {
    @include ct-f;
    @include sc(14px, #555) {
      font-weight: 500;
    }
    background: #fff;
    input {
      width: 30px;
      text-align: center;
    }
  }
}
</style>
