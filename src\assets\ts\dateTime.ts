export type DateType = {
  value: number;
  type: "prev" | "curr" | "next";
  timestamp: number;
  disabled?: boolean;
};
export type MonthType = {
  value: number;
  type: "prev" | "curr" | "next";
  timestamp: number;
  disabled?: boolean;
};

/**
 * 获取指定年月页面需要显示的全部日期
 * @param year
 * @param month
 * @returns
 */
export const getCurrPageDays = (year: number, month: number): DateType[] => {
  const prevDays = getPrevMonthRestDays(year, month); // 上个月的日期数组

  const currDays: number[] = []; // 本月的日期数组
  const currMonthDays = getDaysInMonth(year, month);
  for (let i = 1; i <= currMonthDays; i++) {
    currDays.push(i);
  }

  const nextDays: number[] = []; // 下个月的日期数组
  const nextMonthDaysCount = 42 - prevDays.length - currDays.length;
  for (let i = 1; i <= nextMonthDaysCount; i++) {
    nextDays.push(i);
  }

  const [prevYear, prevMonth] = getPrevYearMonth(year, month);
  const [nextYear, nextMonth] = getNextYearMonth(year, month);

  return [
    ...prevDays.map((item) => ({
      value: item,
      type: "prev",
      timestamp: safeGetTime(prevYear + "-" + (prevMonth + 1) + "-" + item),
    })),
    ...currDays.map((item) => ({
      value: item,
      type: "curr",
      timestamp: safeGetTime(year + "-" + (month + 1) + "-" + item),
    })),
    ...nextDays.map((item) => ({
      value: item,
      type: "next",
      timestamp: safeGetTime(nextYear + "-" + (nextMonth + 1) + "-" + item),
    })),
  ];
};

/**
 * 获取指定年月页面需要显示的上个月的日期数组
 * @param year
 * @param month
 * @returns
 */
export const getPrevMonthRestDays = (year: number, month: number) => {
  const [_year, _month] = getPrevYearMonth(year, month);
  let lastDate = getDaysInMonth(_year, _month); // 上个月共有多少天
  let days = new Date(_year, _month, lastDate).getDay(); // 上个月第一天周几
  // 由于当月的天数为28-31天,总共格子数为6*7=42 => 其它月占用格子数最大为14个,所以上个月必须最低占用1个格子,否则显示不好看
  // 计算占用格子数 (格子第一个为周日)
  if (days === 0) {
    days = 7;
  }
  const restDays: number[] = [];
  while (restDays.length < days) {
    restDays.push(lastDate--);
  }
  return restDays.reverse();
};

export function getPrevYearMonth(year: number, month: number) {
  if (month === 0) {
    year -= 1;
    month = 11;
  } else {
    month -= 1;
  }
  return [year, month];
}

export function getNextYearMonth(year: number, month: number) {
  if (month === 11) {
    year += 1;
    month = 0;
  } else {
    month += 1;
  }
  return [year, month];
}

export const fixZeroToStr = (num: number) => (num < 10 ? `0${num}` : `${num}`);

type FormatToken =
  | "YYYY"
  | "YY"
  | "MM"
  | "M"
  | "DD"
  | "D"
  | "W"
  | "HH"
  | "H"
  | "hh"
  | "h"
  | "mm"
  | "m"
  | "ss"
  | "s";

export function formatDateTime(
  dateTime: any,
  format: string = "YYYY-MM-DD",
  invalid: any = "-"
): string {
  // 时间对象
  const date = new Date(dateTime === 0 ? undefined : dateTime);

  // 无效的时间对象
  if (isNaN(date.getTime())) {
    return invalid;
  }

  const year = date.getFullYear().toString();
  const month = (date.getMonth() + 1).toString().padStart(2, "0");
  const day = date.getDate().toString().padStart(2, "0");
  const hour = date.getHours().toString().padStart(2, "0");
  const minute = date.getMinutes().toString().padStart(2, "0");
  const second = date.getSeconds().toString().padStart(2, "0");

  const tokens: Record<FormatToken, string> = {
    YYYY: year,
    YY: year.slice(-2),
    MM: month,
    M: (date.getMonth() + 1).toString(),
    DD: day,
    D: date.getDate().toString(),
    W: `星期${["日", "一", "二", "三", "四", "五", "六"][date.getDay()]}`,
    HH: hour,
    H: date.getHours().toString(),
    hh: (date.getHours() % 12 || 12).toString().padStart(2, "0"),
    h: (date.getHours() % 12 || 12).toString(),
    mm: minute,
    m: date.getMinutes().toString(),
    ss: second,
    s: date.getSeconds().toString(),
  };

  return format.replace(
    /YYYY|YY|MM|M|DD|D|W|HH|H|hh|h|mm|m|ss|s/g,
    (match: string) => tokens[match as FormatToken]
  );
}

export function safeGetTime(dateTime: string, invalid: any = 0) {
  // 时间对象
  const date = new Date(dateTime);

  // 无效的时间对象
  if (isNaN(date.getTime())) {
    return invalid;
  }

  const [dateString, timeString] = dateTime.split(" ");

  const [year, month, day] = dateString.split("-").map((item) => Number(item));
  const monthIndex = month - 1;
  // 取月有多少天
  const dayNum = getDaysInMonth(year, monthIndex);
  // 解决类似 2022-11-31 会 变为 2022-12-1 日的问题，因为2022年11月只有30天
  const minDay = day > dayNum ? dayNum : day;

  if (!timeString) {
    return new Date(year, monthIndex, minDay).getTime();
  }

  const [hours = 0, minutes = 0, seconds = 0] = timeString
    .split(":")
    .map((item) => (item ? Number(item) : 0));
  // new Date(year, monthIndex [, day [, hours [, minutes [, seconds [, milliseconds]]]]]);
  return new Date(year, monthIndex, minDay, hours, minutes, seconds).getTime();
}

export function getDaysInMonth(year: number, monthIndex: number) {
  // 创建表示指定年份和月份的 Date 对象
  const date = new Date(year, monthIndex);

  // 获取下个月份
  date.setMonth(date.getMonth() + 1);

  // 将日期设置为该月份的第 0 天，即上个月的最后一天
  date.setDate(0);

  // 获取日期属性，即为该月份的天数
  return date.getDate();
}

// 0 今天 / 1 昨天 / 0,6 近7天 / 0,29 近30天
export function getTimeRange(
  end: number,
  start = end,
  type: "date" | "dateTime" = "date"
) {
  const format = "YYYY-MM-DD";
  const oneDay = 24 * 60 * 60 * 1000;
  const dayEnd = formatDateTime(new Date(Date.now() - end * oneDay), format);
  const dayStart = formatDateTime(
    new Date(Date.now() - start * oneDay),
    format
  );

  return type === "dateTime"
    ? [`${dayStart} 00:00:00`, `${dayEnd} 23:59:59`]
    : [dayStart, dayEnd];
}

export const computeTime = (starTime, endTime, getType = "min") => {
  return {
    min: () => {
      return Math.round((+new Date(endTime) - +new Date(starTime)) / 1000 / 60);
    },
  }[getType]();
};

// 日期 => 返回当月的周信息
export const getWeeksInMonth = (
  dateTime: any,
  returnType: "week" | "date" | "dateTime" = "date",
  firstIndex = 1 // 每周第一天是周几 ( 0-6 => 日-六 )
) => {
  const date = new Date(dateTime);
  const year = date.getFullYear();
  const monthIndex = date.getMonth();
  const days = getDaysInMonth(year, monthIndex);
  const firstDayInWeek = new Date(year, monthIndex).getDay();
  const weekIndexArr: number[] = [];
  let weekIndex = firstDayInWeek;
  // 使用"-"方便切割成数组
  for (let i = 0; i < days; i++) {
    weekIndexArr.push(
      ...(weekIndex === firstIndex && weekIndexArr.length > 0
        ? ["-", weekIndex]
        : [weekIndex])
    );
    weekIndex = weekIndex === 6 ? 0 : weekIndex + 1;
  }
  const weekIndexDeepArr = weekIndexArr
    .toString()
    .split(",-,")
    .map((arr) => arr.split(","));
  const getWeekDeepArr = (type: "date" | "dateTime") => {
    const firstWeekDays = weekIndexDeepArr[0].length;
    const yearMonthStr = `${year}-${String(monthIndex + 1).padStart(2, "0")}`;
    return weekIndexDeepArr.map((weekArr, index) => {
      const firstDay = index === 0 ? 1 : firstWeekDays + (index - 1) * 7 + 1;
      const lastDay = firstDay + weekArr.length - 1;
      return [
        `${yearMonthStr}-${String(firstDay).padStart(2, "0")}${
          type === "dateTime" ? " 00:00:00" : ""
        }`,
        `${yearMonthStr}-${String(lastDay).padStart(2, "0")}${
          type === "dateTime" ? " 23:59:59" : ""
        }`,
      ];
    });
  };

  return {
    week: weekIndexDeepArr,
    date: getWeekDeepArr("date"),
    dateTime: getWeekDeepArr("dateTime"),
  }[returnType];
};

// 日期 => 返回当天的周信息
export const getWeekInDay = (
  dateTime: any,
  returnType: "weekRange" | "weekIndex" = "weekRange"
) => {
  const date = new Date(dateTime);
  const day = date.getDate();
  const weeks = getWeeksInMonth(date);
  const weekIndex = weeks.findIndex(
    ([start, end]) =>
      day >= Number(start.split("-")[2]) && day <= Number(end.split("-")[2])
  );

  return {
    weekRange: weeks[weekIndex],
    weekIndex,
  }[returnType];
};
