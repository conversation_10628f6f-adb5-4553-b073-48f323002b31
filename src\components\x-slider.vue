<template>
  <section class="x-slider">
    <div ref="railRef" class="x-slider-rail" @mousedown="railMousedown"></div>
    <div
      class="x-slider-track"
      :style="trackStyle"
      @mousedown="trackMousedown"
    ></div>
    <div
      ref="handleRef"
      class="x-slider-handle"
      :style="handleStyle"
      @mousedown="handleDragStart"
    ></div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, onMounted } from "vue";
const props = withDefaults(
  defineProps<{
    value: number;
    min?: number;
    max?: number;
    dragBallSize?: number;
  }>(),
  {
    min: 0,
    max: 100,
    dragBallSize: 20,
  }
);

// Mousedown用于当操作滚动条时触发
const emits = defineEmits(["update:value", "Mousedown"]);

const percent = computed(
  () => (props.value - props.min) / (props.max - props.min)
);
const trackStyle = computed(() => {
  return { width: `${percent.value * 100}%` };
});
const handleStyle = computed(() => {
  return {
    left: `${percent.value * 100}%`,
    width: `${props.dragBallSize}px`,
    height: `${props.dragBallSize}px`,
  };
});

// 点击调整进度
let railWidth: number;
let railLeft: number;
const railRef = ref<HTMLElement>();
onMounted(() => {
  const { width, left } = railRef.value!.getBoundingClientRect();
  railWidth = width;
  railLeft = left;
});
const railMousedown = (e: MouseEvent) => {
  const percent = e.offsetX / railWidth;
  emits("update:value", percent * props.max);
  emits("Mousedown");
};
const trackMousedown = (e: MouseEvent) => {
  const percent = e.offsetX / railWidth;
  emits("update:value", percent * props.max);
  emits("Mousedown");
};

// 拖动调整进度
const handleRef = ref<HTMLElement>();
const handle = reactive({
  mouseInHandleX: 0,
  isDragging: false,
});
const handleDragStart = () => {
  handle.isDragging = true;
  emits("Mousedown");
  document.addEventListener("mousemove", handleDrag);
  document.addEventListener("mouseup", handleDragEnd);
};
const handleDragEnd = () => {
  handle.isDragging = false;
  document.removeEventListener("mousemove", handleDrag);
  document.removeEventListener("mouseup", handleDragEnd);
};
const handleDrag = (e: MouseEvent) => {
  if (handle.isDragging) {
    let newValue = ((e.clientX - railLeft) / railWidth) * props.max;
    if (newValue < props.min) {
      newValue = props.min;
    } else if (newValue > props.max) {
      newValue = props.max;
    }
    emits("update:value", newValue);
  }
};
</script>

<style lang="scss" scoped>
.x-slider {
  position: relative;
  @include wh(100%);
  &-rail {
    cursor: pointer;
    @include wh(100%) {
      border-radius: 4px;
      background-color: rgba(71, 73, 88, 0.8);
    }
  }
  &-track {
    cursor: pointer;
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    border-radius: 4px;
    background-color: rgb(90, 241, 252);
  }
  &-handle {
    cursor: pointer;
    position: absolute;
    top: 50%;
    transform: translate(-50%, -50%);
    border: 1px solid rgb(255, 255, 255);
    border-radius: 50%;
    background-color: rgb(90, 241, 252);
  }
}
</style>
