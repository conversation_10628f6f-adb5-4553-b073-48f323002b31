<template>
  <x-modal
    :title="$t('vehicleModelDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="672px"
    height="478px"
  >
    <div class="content">
      <div class="content-head">
        <div class="content-head-main">
          <div class="content-head-main-title">
            {{ detail.info.vehicleModel }}
          </div>
          <div class="content-head-main-subtitle">{{ $t("modelNumber") }}</div>
        </div>
        <div class="content-head-side">{{ detail.info.vehicleTypeText }}</div>
      </div>
      <div class="content-body">
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("remark") }}：</div>
            <div class="content-body-item-value">{{ detail.info.remark }}</div>
          </div>
        </div>
      </div>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import type { GetVehicleModelDetailResponse } from "@/services/type";
import { reactive, watch } from "vue";
import { getVehicleModelDetail } from "@/services/api";
import xModal from "@/components/x-modal";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carType");

/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 详情数据
 */
const detail = reactive({
  info: {} as GetVehicleModelDetailResponse,
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = await getVehicleModelDetail(props.id);
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 0 29px;
  height: 384px;
  overflow-y: auto;
  overscroll-behavior: contain;
  -ms-scroll-chaining: contain;

  &-head {
    display: flex;

    &-main {
      flex: 1;

      &-title {
        color: rgb(16, 22, 55);
        font-size: 18px;
        font-weight: 700;
        line-height: 28px;
      }

      &-subtitle {
        color: rgb(159, 159, 164);
        font-size: 12px;
        line-height: 18px;
      }
    }

    &-side {
      flex: 1;
      color: rgb(56, 56, 56);
      font-size: 14px;
      line-height: 28px;
    }
  }

  &-body {
    font-size: 14px;
    padding-top: 8px;

    &-items {
      display: flex;
    }

    &-item {
      display: flex;
      padding: 9px 0;
      flex: 1;

      &-label {
        color: #9f9fa4;
        margin-left: 46px;

        &:first-child {
          margin-left: 0;
        }
      }
      &-value {
        color: #383838;
      }
    }
  }
}
</style>
