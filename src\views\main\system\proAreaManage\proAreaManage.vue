<template>
  <section
    class="pro-area"
    ref="proAreaRef"
  >
    <div class="pro-area-top">
      <div class="top-title">{{ $t("proAreaManage") }}</div>
      <div class="top-add-button">
        <x-button
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="openAdd"
        />
      </div>
    </div>
    <div class="pro-area-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.company"
          :options="searchForm.companyOptions"
          :popupContainer="proAreaRef"
          :placeholder="$t('PEnterEnt')"
          style="width: 320px; margin-right: 16px"
          showSearch
        />
        <x-input
          v-model:value="searchForm.area"
          :placeholder="$t('areaName')"
          suffix="input_search"
          style="width: 240px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.proName"
          :options="searchForm.proNameOptions"
          showPopSearch
          :popupContainer="proAreaRef"
          :placeholder="$t('proName')"
          style="width: 240px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.areaType"
          :options="searchForm.areaTypeOptions"
          :popupContainer="proAreaRef"
          :placeholder="$t('areaType')"
          style="width: 140px"
        />
      </div>
      <div class="middle-right">
        <x-button
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button
          @click="resetSearchForm"
          type="green"
          :text="$t('reset')"
        />
      </div>
    </div>
    <div class="pro-area-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #company="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="proAreaRef"
          >
            {{ record.company }}
            <template #content>
              <div class="pro-area-table-company-hover-popover">
                {{ record.company }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record, recordIndex }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span @click="openCarAuth(record.id, record.proId)">
                {{ $t("carAuth") }}
              </span>
              <span @click="openTemplate(record.id)">{{ $t("taskTemplate") }}</span>
              <span @click="openEdit(record.id)">{{ $t("edit") }}</span>
            </div>
            <x-popover
              v-model:visible="record.opera"
              trigger="click"
              placement="bottom"
              :container="proAreaRef"
              @visibleChange="handleClickPop($event, recordIndex)"
            >
              <div :class="['table-opera-more', { enable: record.opera }]"><span></span><span></span><span></span></div>
              <template #content>
                <div class="pro-area-manage-table-opera-click-popover">
                  <div
                    class="pro-area-manage-table-opera-click-popover-item"
                    @click="openDetail(record)"
                  >
                    {{ $t("detail") }}
                  </div>
                  <div
                    class="pro-area-manage-table-opera-click-popover-item"
                    @click="deletePro(record)"
                  >
                    {{ $t("delete") }}
                  </div>
                </div>
              </template>
            </x-popover>
          </div>
        </template>
      </x-table>
    </div>
    <OperaArea
      v-model:show="operaDrawer.show"
      :id="operaDrawer.id"
      :type="operaDrawer.type"
      :entList="searchForm.companyIdOptions"
      @confirm="operaDrawer.confirm"
    />
    <RouteTemplate
      v-model:show="templateDrawer.show"
      :id="templateDrawer.id"
      @confirm="templateDrawer.confirm"
    />
    <CarAuth
      v-model:show="carDrawer.show"
      :id="carDrawer.id"
      :proId="carDrawer.proId"
      @confirm="carDrawer.confirm"
    />
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive } from "vue";
import { compList, getProAreaList, getProList, delProArea } from "@/services/api";
import { areaType } from "@/assets/ts/config";
import type { PageSizeType } from "@/components/types";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xPopover from "@/components/x-popover.vue";
import OperaArea from "./components/operaArea.vue";
import CarAuth from "./components/carAuth.vue";
import RouteTemplate from "./components/routeTemplate2.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");

const proAreaRef = ref<any>();

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "80",
    },
    {
      key: "areaName",
      title: $t("areaName"),
      width: "120",
    },
    {
      key: "areaSeq",
      title: $t("areaSeq"),
      width: "120",
    },
    {
      key: "areaType",
      title: $t("areaType"),
      width: "100",
    },
    {
      key: "proName",
      title: $t("proName"),
      width: "120",
    },
    {
      key: "proSeq",
      title: $t("proSeq"),
      width: "120",
    },
    {
      key: "company",
      title: $t("entName"),
      width: "150",
      slots: "company",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "200",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

// 模板
const templateDrawer = reactive({
  show: false,
  id: undefined as string | undefined,
  confirm: () => searchList(),
});

const openTemplate = (id: string) => {
  templateDrawer.id = id;
  templateDrawer.show = true;
};

// 新增 / 编辑 / 详情
const operaDrawer = reactive({
  type: "add" as "add" | "edit" | "view",
  show: false,
  id: undefined as string | undefined,
  confirm: () => searchList(),
});

const openAdd = () => {
  operaDrawer.id = undefined;
  operaDrawer.type = "add";
  operaDrawer.show = true;
};

const openEdit = (id: string) => {
  operaDrawer.id = id;
  operaDrawer.type = "edit";
  operaDrawer.show = true;
};

const openDetail = (record: any) => {
  record.opera = false;
  operaDrawer.id = record.id;
  operaDrawer.type = "view";
  operaDrawer.show = true;
};

// 删除
const deletePro = (record: any) => {
  record.opera = false;
  xModal.confirm({
    title: $t("sureToDelProject"),
    content: <div style="color:#E24562;">{$t("willUnbindCarAndProArea")}</div>,
    confirm() {
      return delProArea(record.id).then(() => {
        Message("success", $t("deleteSuccess"));
        searchList();
      });
    },
  });
};

// 车辆授权
const carDrawer = reactive({
  show: false,
  id: "",
  proId: "",
  confirm: () => searchList(),
});
const openCarAuth = (id: string, proId: string) => {
  carDrawer.id = id;
  carDrawer.proId = proId;
  carDrawer.show = true;
};

// 查询
const searchForm = reactive({
  company: "",
  companyOptions: [] as { label: string; value: string }[],
  companyIdOptions: [] as { label: string; value: number }[],
  area: "",
  proName: undefined as number | undefined,
  proNameOptions: [] as { label: string; value: string }[],
  areaType: undefined as undefined | number,
  areaTypeOptions: areaType,
});
const reSearch = () => {
  table.pagination["current"] = 1;
  searchList();
};
const searchList = async () => {
  table.loading = true;
  const { totalCount, list } = await getProAreaList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.company,
    areaName: searchForm.area,
    projectId: searchForm.proName,
    areaType: searchForm.areaType,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            ...item,
            orderNumber: (index + 1).toString().padStart(3, "0"),
            areaName: item.areaName,
            areaType: areaType.find((_item) => _item.value === item.areaType)?.label,
            proName: item.proName,
            company: item.entName,
            opera: ref(false),
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchList();
};
const resetSearchForm = () => {
  searchForm.company = "";
  searchForm.area = "";
  searchForm.proName = undefined;
  searchForm.areaType = undefined;
  table.pagination["current"] = 1;
  searchList();
};
(async () => {
  searchList();
  searchForm.companyIdOptions = (await compList()).map(({ entId, entName }) => ({
    label: entName,
    value: entId,
  }));
  searchForm.companyOptions = searchForm.companyIdOptions.map(({ label }) => ({
    label,
    value: label,
  }));
  searchForm.proNameOptions = (await getProList()).map(({ id, proName }) => ({
    label: proName,
    value: id,
  }));
})();
const visibleIndex = ref();
const handleClickPop = (status: boolean, recordIndex: number) => {
  if (status) {
    table.dataSource = table.dataSource.map((obj, index) => {
      return {
        ...obj,
        opera: ref(index === recordIndex ? true : false),
      };
    });
  }
  visibleIndex.value = recordIndex;
};
</script>

<style lang="scss">
.pro-area-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.pro-area-manage-table-opera-click-popover {
  &-item {
    cursor: pointer;
    @include wh(90px, 40px);
    font-size: 13px;
    line-height: 40px;
    text-align: center;
    &:hover {
      background-color: #242859;
    }
    &:first-child {
      border-radius: 4px 4px 0 0;
    }
    &:last-child {
      border-radius: 0 0 4px 4px;
    }
  }
}
</style>
<style lang="scss" scoped>
.pro-area {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
      // 为了让pop居中
      // margin-left: -26px;
      // padding-left: 26px;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
