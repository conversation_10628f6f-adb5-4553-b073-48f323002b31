<template>
  <div class="traffic-light-page">
    <div class="header">
      <div class="card-info">
        <div class="device-id">AC0001</div>
        <div class="text">过红绿灯校验</div>
        <div class="report-time">上报时间：2025-05-07 15:00:00</div>
      </div>
      <ul class="clock-list">
        <li class="clock-item">00</li>
        <span style="font-weight: 700; font-size: 25px">:</span>
        <li class="clock-item">00</li>
        <span style="font-weight: 700; font-size: 25px">:</span>
        <li class="clock-item">15</li>
      </ul>
    </div>

    <div class="traffic-light-info">
      <div class="item">
        <div class="left">
          <span class="label">通行方向</span>
          <span class="value">直行</span>
        </div>
        <div class="right">
          <x-icon
            width="117px"
            height="88px"
            name="straight-travel-icon"
          />
        </div>
      </div>
      <div class="traffic-light-result">
        <span class="label">感知结果</span>
        <span class="value">红灯</span>
      </div>
    </div>

    <div class="image-container">
      <p class="title">图片</p>
      <div class="image-box">
        <el-image
          class="image"
          :src="imageUrl"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import imageUrl from "@/assets/images/traffic_light_page.png";
import xIcon from "@/components/x-icon.vue";
</script>

<style scoped lang="scss">
.traffic-light-page {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 20px;

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .card-info {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      .device-id {
        font-size: 18px;
        font-weight: 700;
        color: #383838;
      }
      .text {
        font-size: 16px;
        font-weight: 400;
        color: #242859;
      }
      .report-time {
        font-size: 14px;
        font-weight: 400;
        color: #555555;
      }
    }
    .clock-list {
      display: flex;
      align-items: center;
      gap: 10px;
      color: #f8a331;
      .clock-item {
        display: inline-block;
        width: 31px;
        height: 32px;
        border-radius: 4px;
        background: rgb(249, 133, 46);
        font-size: 20px;
        font-weight: 700;
        color: #ffffff;
        text-align: center;
      }
    }
  }
  .traffic-light-info {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;

    .item {
      width: 260px;
      height: 130px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      display: flex;
      align-items: center;
      justify-content: space-around;
      .left {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 16px;

        .label {
          font-size: 14px;
          font-weight: 400;
          color: #383838;
        }
        .value {
          font-size: 18px;
          font-weight: 700;
          color: #383838;
        }
      }
    }
    .traffic-light-result {
      width: 260px;
      height: 130px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      display: flex;
      flex-direction: column;
      padding: 23px 0px 0 23px;
      gap: 16px;
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #383838;
      }
      .value {
        font-size: 18px;
        font-weight: 700;
        color: #383838;
      }
    }
  }
  .image-container {
    .title {
      font-size: 14px;
      font-weight: 400;
      color: #383838;
    }
    .image-box {
      display: flex;
      align-items: center;
      margin-top: 10px;
      width: 100%;
      height: 100%;
      gap: 10px;
      .image {
        width: 145px;
        height: 106.77px;
        border-radius: 4px;
      }
    }
  }
}
</style>
