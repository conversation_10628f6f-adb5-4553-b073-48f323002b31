
.x-screen-message {
  z-index: var(--z-index-modal);
  
  &-error {
    @include ct-f;
    @include ct-p(x){
      top: 58px;
    }
    @include wh(366px, 88px){
      @include bis('@/assets/images/screen_message_bg.png');
    }

    img {
      @include wh(20px);
    }
    span {
      margin-left: 8px;
      @include sc(20px,rgb(255, 255, 255)){
        font-weight: 700;
      }
    }
  }
  &-enter {
    &-from {
      top: 40px;
      opacity: 0;
    }
    &-active {
      transition: all .5s;
    }
    // &-to {
    //   top: 58px;
    //   opacity: 1;
    // }
  }
  &-leave {
    // &-from {
    //   top: 58px;
    //   opacity: 1;
    // }
    &-active {
      transition: all .5s;
    }
    &-to {
      top: 40px;
      opacity: 0;
    }
  }
}