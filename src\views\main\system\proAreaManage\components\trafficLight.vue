<template>
  <div
    class="traffic-light"
    v-show="show"
  >
    <el-scrollbar height="100%">
      <div
        class="traffic-light-item"
        v-for="item in trafficLightList"
        :key="item.id"
        :class="{ 'is-active': currentId === item.id }"
        @click="addtrafficLightMarKer(item)"
      >
        <div class="traffic-light-item-icon">
          <XIcon
            name="traffic-light-color-icon"
            width="20px"
            height="20px"
          />
        </div>
        <div class="traffic-light-item-text">{{ item.trafficName || "-" }}</div>
      </div>
      <XEmpty v-if="trafficLightList.length === 0" />
    </el-scrollbar>
    <Teleport :to="props.container || 'body'">
      <div
        class="traffic-light-form"
        v-show="showOperaAreaSite"
      >
        <div class="traffic-light-form-item">
          <div class="label"><span class="required">*</span>名称</div>
          <div class="value">
            <el-input
              v-model="formModel.trafficName"
              clearable
              disabled
              @blur="checkDuplicateTrafficName"
            />
          </div>
        </div>
        <div class="traffic-light-form-item">
          <div class="label">类型</div>
          <div class="value">红绿灯</div>
        </div>
        <div class="traffic-light-form-item">
          <div class="label">经度</div>
          <div class="value">{{ formModel.longitude || "-" }}</div>
        </div>
        <div class="traffic-light-form-item">
          <div class="label">纬度</div>
          <div class="value">{{ formModel.latitude || "-" }}</div>
        </div>
        <div class="traffic-light-form-item">
          <div class="label">审核时长</div>
          <el-input
            v-model="formModel.passTime"
            style="width: 200px"
            clearable
            :maxlength="2"
            :disabled="type == 'view'"
            @input="(value: string) => (formModel.passTime = value.replace(/[^0-9]/g, '').slice(0, 2))"
            type="text"
            @change="handelpassTime"
          />
          <span>s</span>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script lang="ts" setup>
import { computed, onMounted, ref, watch } from "vue";
import type { GetTrafficLightListResponse } from "@/services/type";
import XIcon from "@/components/x-icon.vue";
import XEmpty from "@/components/x-empty.vue";
import { ElMessage } from "element-plus";

const props = defineProps<{
  trafficLightList: GetTrafficLightListResponse[];
  dataLoaded?: boolean;
  type?: "add" | "edit" | "view";
  show?: boolean;
  container?: HTMLElement;
}>();

const handelpassTime = (val: any) => {
  const newTrafficLightList = props.trafficLightList.map((item) => {
    if (item.id === formModel.value.id) {
      return { ...item, passTime: Number(formModel.value.passTime) };
    }
    return item;
  });
  emits("upTrafficList", newTrafficLightList);
};

const showOperaAreaSite = computed(() => props.show && props.trafficLightList?.length > 0);
const emits = defineEmits<{
  (e: "renderMarker", item: GetTrafficLightListResponse): void;
  (e: "upTrafficList", item: GetTrafficLightListResponse[]): void;
}>();

// 当前选中的红绿灯ID
const currentId = ref<string>("");
const formModel = ref<any>({});

const checkDuplicateTrafficName = () => {
  const newTrafficLightList = props.trafficLightList.map((item) => {
    if (item.id === formModel.value.id) {
      return { ...item, trafficName: formModel.value.trafficName };
    }
    return item;
  });
  emits("upTrafficList", newTrafficLightList);
};

// 添加红绿灯标记
const addtrafficLightMarKer = (item: GetTrafficLightListResponse) => {
  if (!item?.id) return;
  formModel.value = item;
  currentId.value = String(item.id);
  // 触发渲染标记事件
  emits("renderMarker", item);
};

watch(
  () => props.dataLoaded,
  (newV) => {
    if (newV && props.type !== "add") {
      addtrafficLightMarKer(props.trafficLightList[0]);
    }
  }
);
// onMounted(() => {
//   if (props.type !== "view") {
//     formModel.value.passTime = 10;
//   }
// });
</script>

<style scoped lang="scss">
.traffic-light {
  height: calc(100% - 60px);
  .traffic-light-item {
    height: 56px;
    display: flex;
    align-items: center;
    padding: 14px 15px;
    gap: 13px;
    font-size: 14px;
    color: #383838;
    font-weight: 400;
    cursor: pointer;
    &:hover {
      background: rgb(232, 236, 253);
      font-weight: 700;
      color: #5964fb;
    }
  }
  .is-active {
    background: rgb(232, 236, 253) !important;
    font-weight: 700 !important;
    color: #5964fb !important;
  }
}
.traffic-light-form {
  width: 320px;
  position: absolute;
  right: 366px;
  top: 60px;
  padding: 14px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  max-height: calc(100% - 80px);
  &-item {
    // height: 63px;
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    .required {
      color: #ff4d4f;
    }
    .label {
      color: #5e5e5e;
    }
    // &-value {
    //   font-size: 14px;
    //   color: #383838;
    // }
  }
}
</style>
