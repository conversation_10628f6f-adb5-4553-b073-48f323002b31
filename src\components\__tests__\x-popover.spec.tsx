import { describe, it, expect, afterEach, vi, beforeEach } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick, createVNode } from 'vue'
import XPopover from '@/components/x-popover.vue';

beforeEach(() => {
  document.body.outerHTML = '';
});

const clickBaseProps = { 
  trigger: 'click',
  placement: 'bottom',
}
const focusBaseProps = { 
  trigger: 'focus',
  placement: 'bottom',
}

describe('Snapshot', () => {
  it('正确渲染 外部快照', () => {
    const wrapper = mount(XPopover,{
      props: { ...clickBaseProps },
      slots: {
        default: () => [createVNode('div', null, '测试default插槽')],
      }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 内部快照', () => {
    mount(XPopover,{
      props: { ...clickBaseProps },
      slots: {
        default: () => [createVNode('div', null, '测试default插槽')],
      }
    });
    expect(document.querySelector('.x-popover-container')).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 visible + triangle + contentType', () => {
    const wrapper = mount(XPopover,{
      props: { 
        ...clickBaseProps,
        visible: true,
        triangle: false,
        contentType: 'light'
      },
    });
    expect(document.querySelector('.x-popover-container').style.display).not.toBe('none');
    expect(document.querySelector('.x-popover-bottom-triangle-default')).toBeNull();
    expect(document.querySelector('.x-popover-content-light')).toBeDefined();
  });
});

describe('Events', () => {
  it('trigger: focus时验证focus和blur事件', () => {
    const wrapper = mount(XPopover,{
      props: { ...focusBaseProps }
    });
    wrapper.trigger('focus');
    expect(wrapper.emitted()['update:visible'][0][0]).toBe(true);
    wrapper.trigger('blur');
    expect(wrapper.emitted()['update:visible'][1][0]).toBe(false);
  });

  it('验证allowKeepShowElements效果', () => {
    const div = document.createElement("div");
    div.setAttribute("class", "test-test-test");
    document.body.appendChild(div);
    const _div = document.querySelector('.test-test-test')
    const wrapper = mount(XPopover,{
      props: { 
        ...clickBaseProps,
        allowKeepShowElements: [_div]
      }
    });

    // 第一次触发 update:visible 为 true，为后续验证allowKeepShowElements
    wrapper.trigger('mousedown'); 
    expect(wrapper.emitted()['update:visible'][0][0]).toBe(true);

    // 模拟触发 外部div 的mousedown，若触发的update:visible 为 true则证明有效
    const event = new MouseEvent('mousedown', {'bubbles': true,'cancelable': true})
    _div.dispatchEvent(event);
    expect(wrapper.emitted()['update:visible'][1][0]).toBe(true);
  });

  it('验证keepShow效果', () => {
    const wrapper = mount(XPopover,{
      props: { 
        ...clickBaseProps,
        keepShow: true,
      }
    });

    // 第一次触发 update:visible 为 true，为后续验证keepShow
    wrapper.trigger('mousedown'); 
    expect(wrapper.emitted()['update:visible'][0][0]).toBe(true);

    // 模拟触发 document 的mousedown，若无触发update:visible则视为证明有效
    const event = new MouseEvent('mousedown', {'bubbles': true,'cancelable': true})
    document.dispatchEvent(event);
    expect(wrapper.emitted()['update:visible'][1]).toBeUndefined();
  });
});