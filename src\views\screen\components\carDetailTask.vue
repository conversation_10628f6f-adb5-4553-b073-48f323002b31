<template>
  <section class="car-task">
    <sub-title>任务调度</sub-title>
    <div class="car-task-content">
      <div class="content-top">
        <template v-for="(item, index) in list.taskList" :key="index">
          <div
            class="task"
            :class="{ active: item.enable }"
            @click="selectTask(index, item)"
          >
            {{ item.taskName }}
            <x-icon
              v-if="item.enable"
              class="task-check"
              name="screen_detail_check"
              width="20"
              height="20"
            />
            <div class="task-badge" v-if="item.travelling"></div>
          </div>
        </template>
      </div>
      <div class="content-center">
        <template v-for="(item, index) in list.stationList" :key="index">
          <div
            class="station"
            :class="{ active: item.enable }"
            @click="selectStation(index, item)"
            @mouseover="hoverStationIndex = index"
            @mouseleave="hoverStationIndex = null"
          >
            <x-icon :name="getIconName(item, index)" width="50" height="50" />
            <span>{{ item.stationName }}</span>
          </div>
        </template>
      </div>
      <div class="content-bottom">
        <button class="btn" @click="confirm">确定</button>
      </div>
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { reactive, ref, watch, onMounted } from "vue";
import xIcon from "@/components/x-icon.vue";
import subTitle from "./subTitle.vue";
import xModal from "@/components/x-modal";
import { getVehStationList, getRouteTemplate } from "@/services/api";
import { useMainStore } from "@/stores/main";
import { screenTask } from "@/services/wsapi";
import { driveModeMap } from "@/services/wsconfig";
import Message from "./x-message";

const props = withDefaults(
  defineProps<{
    id: string;
  }>(),
  {}
);

const carStatus = reactive({
  isEcar: false,
  online: false,
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
});
onMounted(async () => {
  const { socket } = useMainStore();
  watch(
    () => socket.allStatus,
    (newV) => {
      if (newV) {
        const curItem = newV.find((v) => v.deviceId === props.id)!;
        // 当不显示详情时没有数据
        if (curItem) {
          carStatus.isEcar =
            curItem.manufacturer === "ecar" || curItem.manufacturer === "PA";
          carStatus.online = curItem.online;
          carStatus.driveMode = JSON.parse(
            JSON.stringify(driveModeMap[curItem.driveMode])
          );
          // 是否有正在执行的任务
          if (curItem?.tempId) {
            list.taskList.forEach(
              (item) =>
                (item.travelling = Boolean(curItem.tempId === item.taskId))
            );
          }
        }
      }
    }
  );
  list.taskList = (await getRouteTemplate({ vehNo: props.id })).map(
    ({ templateName, id }, index) => ({
      taskName: templateName,
      taskId: id,
      travelling: false,
      enable: index === 0,
    })
  );
  list.stationList = (await getVehStationList(props.id)).map(
    ({ stationName, stationType, id }) => ({
      stationId: id,
      stationName,
      stationType,
      icon: [
        "screen_station_garbage",
        "screen_station_charging",
        "screen_station_water",
        "screen_station_parking",
      ][stationType - 1],
      enable: false,
    })
  );
});

type StationListType = {
  stationName: string;
  stationType: number;
  icon: string;
  enable: boolean;
};
type TaskListType = {
  taskName: string;
  taskId: string;
  travelling: boolean;
  enable: boolean;
};
const list = reactive({
  taskList: [] as TaskListType[],
  stationList: [] as StationListType[],
});

const hoverStationIndex = ref<number | null>(null);
const getIconName = (item: any, index: number) => {
  if (hoverStationIndex.value === index) {
    return `${item.icon}_hover`;
  } else if (item.enable) {
    return `${item.icon}_enable`;
  } else {
    return item.icon;
  }
};
let activeTaskIndex = -1;
const selectTask = (index: number, item: TaskListType) => {
  if (item.enable) {
    item.enable = false;
    activeTaskIndex = -1;
  } else {
    activeTaskIndex = index;
    list.taskList.forEach((_item, _index) => {
      _item.enable = Boolean(index === _index);
    });
  }
};

let activeStationIndex = -1;
const selectStation = (index: number, item: StationListType) => {
  if (item.enable) {
    item.enable = false;
    activeStationIndex = -1;
  } else {
    activeStationIndex = index;
    list.stationList.forEach((_item, _index) => {
      _item.enable = Boolean(index === _index);
    });
  }
};

const confirm = () => {
  if (!carStatus.online) {
    xModal.screenConfirm({
      title: "提示",
      confirmText: "好的",
      content: (
        <div>
          <div style="font-size:1.8vh;color:#fff;font-weight:500">
            车辆已关机，请先开机
          </div>
          <div style="font-size:1.4vh;color:rgba(255, 255, 255, 0.6);font-weight:500;text-align:center;margin-top: 1vh">
            车辆在线才能下发任务
          </div>
        </div>
      ),
      confirm() {},
    });
  } else if (carStatus.isEcar && carStatus.driveMode.text === "待机") {
    Message("error", "请将车辆切换至自动驾驶模式");
  } else {
    screenTask(
      props.id,

      activeTaskIndex > -1 ? list.taskList[activeTaskIndex]["taskId"] : "",
      activeStationIndex > -1
        ? list.stationList[activeStationIndex]["stationId"]
        : ""
    )
      .then(() => {
        Message("success", "任务下发成功");
      })
      .catch(() => {
        xModal.screenConfirm({
          title: "提示",
          confirmText: "重发",
          content: (
            <div style="font-size:1.8vh;color:#fff;font-weight:500">
              任务下发失败，是否需要重新下发
            </div>
          ),
          confirm() {
            confirm();
          },
          cancel() {},
        });
      });
  }
};
</script>

<style lang="scss" scoped>
.car-task {
  width: 382px;
  background: linear-gradient(
    0deg,
    rgba(8, 11, 35, 0.48) 17.095%,
    rgba(19, 21, 42, 0.28) 62.989%,
    rgba(10, 20, 42, 0.4) 100%
  );
  border-radius: 20px;
  &-content {
    display: flex;
    flex-direction: column;
    padding: 0 22px 1vh 22px;
    .content-top {
      max-height: 21vh;
      overflow-y: overlay;
      scrollbar-width: none;
      .task {
        position: relative;
        @include wh(100%);
        @include sc(1.4vh, rgb(248, 250, 255));
        padding: 1vh 25px;
        margin-bottom: 1vh;
        font-weight: 500;
        background: linear-gradient(
          270deg,
          rgba(120, 122, 140, 0.02) 9.16%,
          rgba(109, 112, 157, 0.37) 100%
        );
        border: 1px solid rgba(109, 112, 157, 0.37);
        border-radius: 8px;
        cursor: pointer;
        &-badge {
          position: absolute;
          top: 0;
          left: 0;
          @include wh(39px, 4.4vh);
          @include bis("@/assets/images/screen_task_badge.png");
        }
        &-check {
          @include ct-p(y);
          @include wh(39px, 4.4vh);
          right: 20px;
          z-index: 2;
        }
        &.active {
          background: linear-gradient(
            to left,
            rgb(90, 118, 252) 0%,
            rgba(109, 112, 157, 0.37) 70%
          );
        }
      }
    }
    .content-center {
      display: flex;
      flex-wrap: wrap;
      margin: 1vh 0;
      height: 18vh;
      overflow: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
      .station {
        display: flex;
        flex-direction: column;
        align-items: center;
        width: 25%;
        height: 9vh;
        @include sc(1.4vh, rgb(255, 255, 255));
        cursor: pointer;
        span {
          margin-top: 0.8vh;
        }
      }
    }
    .content-bottom {
      @include ct-f;
      .btn {
        @include ct-f;
        @include wh(224px, 3.4vh);
        @include sc(1.4vh, rgb(255, 255, 255));
        margin: 1vh 0;
        border-radius: 8px;
        background: linear-gradient(
          180deg,
          rgb(158, 174, 250),
          rgb(90, 118, 252)
        );
        cursor: pointer;
      }
    }
  }
}
</style>
