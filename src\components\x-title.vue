<template>
  <h3>
    <i v-if="props.icon === 'triangle'" class="triangle"></i>
    <x-icon v-else name="title_flag" width="10" height="20"></x-icon>
    <slot></slot>
  </h3>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  icon: {
    type: String as PropType<"flag" | "triangle">,
    default: "flag",
  },
});
</script>

<style lang="scss" scoped>
h3 {
  @include ct-f(y) {
    padding: 5px 0;
  }
  .triangle {
    @include triangle(3px, 3px, #5964fb, right);
    margin-right: 5px;
  }
}
</style>
