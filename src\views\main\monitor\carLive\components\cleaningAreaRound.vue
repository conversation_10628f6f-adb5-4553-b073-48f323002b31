<template>
  <svg
    class="round-percent"
    :width="wh"
    :height="wh"
    viewBox="0 0 96 96"
    fill="none"
  >
    <g>
      <g>
        <!-- 外环 -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          :r="OR"
          stroke-width="3.5"
          stroke="url('#outSideGradient')"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="strokeDasharray.outRound"
        />
      </g>
      <g>
        <!-- 内环 -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          :r="IR"
          fill="url('#inSideGradient')"
          fill-opacity="0.11"
        />
      </g>
    </g>
    <defs>
      <radialGradient
        id="inSideGradient"
        cx="50%"
        cy="50%"
        fx="50%"
        fy="50%"
        r="50%"
      >
        <stop
          offset="0%"
          :stop-color="
            props.offline ? 'rgb(235, 236, 236)' : 'rgb(89, 100, 251, 0.73)'
          "
        />
        <stop
          offset="100%"
          :stop-color="
            props.offline ? 'rgb(191, 192, 192)' : 'rgb(89, 100, 251, 0.54)'
          "
        />
      </radialGradient>
      <linearGradient id="outSideGradient">
        <stop
          offset="0%"
          :stop-color="
            props.offline ? 'rgb(173, 173, 173)' : 'rgb(89, 100, 251)'
          "
        />
        <stop
          offset="100%"
          :stop-color="
            props.offline ? 'rgb(173, 173, 173, 0.1)' : 'rgb(89, 100, 251, 0.1)'
          "
        />
      </linearGradient>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { computed } from "vue";
const props = defineProps({
  offline: {
    type: Boolean,
    default: () => false,
  },
});
const wh = 96;
const OR = 43;
const IR = 36;
const outPerimeter = Math.PI * 2 * OR;
const strokeDasharray = computed(() => {
  return {
    outRound: `${(180 / 360) * outPerimeter} ${outPerimeter}`,
  };
});
</script>

<style lang="scss" scoped>
.round-percent {
  transform: rotate(-45deg);
  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
