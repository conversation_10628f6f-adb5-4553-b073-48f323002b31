<template>
  <section
    class="x-table"
    ref="xTableRef"
  >
    <div
      v-if="props.loading"
      class="x-table-loading"
    >
      <img
        src="@/assets/images/loading.gif"
        alt=""
      />
    </div>
    <div
      v-else-if="props.dataSource.length === 0"
      class="x-table-nodata"
    >
      <img
        src="@/assets/images/table_nodata.png"
        alt=""
      />
      <span>{{ $t("sorryNoData") }}</span>
    </div>
    <template v-else>
      <div class="x-table-container">
        <!-- 表格主体 -->
        <div class="x-table-scroll">
          <!-- 表头 -->
          <div class="x-table-header">
            <table>
              <colgroup>
                <col
                  v-if="props.selectionType"
                  style="width: 50px"
                />
                <col
                  v-for="(col, index) in props.cols"
                  :key="`col-${index}`"
                  :style="{ width: getColWidth(col) }"
                />
              </colgroup>
              <thead>
                <tr>
                  <th
                    v-if="props.selectionType"
                    class="selection-column"
                  >
                    <el-checkbox
                      v-if="props.selectionType === 'checkbox'"
                      v-model="isAllSelected"
                      :indeterminate="isIndeterminate"
                      @change="handleSelectAll"
                    />
                  </th>
                  <th
                    v-for="(col, index) in props.cols"
                    :key="`th-${index}`"
                    :class="{ 'fixed-right': col.fixed === 'right', 'fixed-left': col.fixed === 'left' }"
                  >
                    <slot
                      v-if="$slots.headerCell"
                      name="headerCell"
                      :title="col.title"
                      :column="col"
                    >
                    </slot>
                    <span v-else>{{ col.title }}</span>
                  </th>
                </tr>
              </thead>
            </table>
          </div>

          <!-- 表体 -->
          <div class="x-table-body">
            <table>
              <colgroup>
                <col
                  v-if="props.selectionType"
                  style="width: 50px"
                />
                <col
                  v-for="(col, index) in props.cols"
                  :key="`col-${index}`"
                  :style="{ width: getColWidth(col) }"
                />
              </colgroup>
              <tbody>
                <tr
                  v-for="(item, index) in props.dataSource"
                  :key="`tr-${index}`"
                  v-bind="customRow(item, index)"
                  :class="{ 'row-selected': isRowSelected(item) }"
                  @click="handleRowClick(item)"
                >
                  <td
                    v-if="props.selectionType"
                    class="selection-column"
                  >
                    <el-checkbox
                      v-if="props.selectionType === 'radio'"
                      v-model="selectedRowKey"
                      :label="getRowKey(item)"
                      class="square-radio"
                      @change="() => handleRadioChange(item)"
                      @click.stop
                    />
                    <el-checkbox
                      v-else
                      v-model="selectedRowKeys"
                      :value="getRowKey(item)"
                      @change="(val) => handleCheckboxChange(item, val)"
                      @click.stop
                    />
                  </td>
                  <td
                    v-for="(col, _index) in props.cols"
                    :key="`td-${index}-${_index}`"
                    :class="{ 'fixed-right': col.fixed === 'right', 'fixed-left': col.fixed === 'left' }"
                  >
                    <template v-if="col.slots">
                      <div :class="{ 'wrap-cell': col.slotsWrap }">
                        <slot
                          :name="col.slots"
                          :colIndex="_index"
                          :col="col"
                          :recordIndex="index"
                          :record="item"
                          :row="item"
                        ></slot>
                      </div>
                    </template>
                    <template v-else-if="col.hover">
                      <x-popover
                        :container="xTableRef"
                        trigger="hover"
                        placement="bottom"
                      >
                        {{
                          item[col.key] === "" || item[col.key] === null || item[col.key] === undefined
                            ? "--"
                            : item[col.key]
                        }}
                        <template #content>
                          <div class="x-table-popover-content">
                            {{
                              item[col.key] === "" || item[col.key] === null || item[col.key] === undefined
                                ? "--"
                                : item[col.key]
                            }}
                          </div>
                        </template>
                      </x-popover>
                    </template>
                    <template v-else>
                      {{
                        item[col.key] === "" || item[col.key] === null || item[col.key] === undefined
                          ? "--"
                          : item[col.key]
                      }}
                    </template>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </template>
    <x-pagination
      v-if="props.pagination && props.dataSource.length > 0 && !props.loading"
      style="margin-top: 32px; align-self: flex-end"
      :total="props.pagination.total"
      :current="props.pagination.current"
      @update:current="currentChange"
      :pageSize="props.pagination.pageSize"
      @update:pageSize="pageSizeChange"
    />
  </section>
</template>

<script lang="ts" setup>
import { ref, computed, watch } from "vue";
import type { PropType } from "vue";
import type { PageSizeType } from "./types";
import xPagination from "@/components/x-pagination.vue";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = defineProps({
  /**
   * 表格列配置
   * @type {Array<{key: string, title: string, hover?: boolean, slots?: string, slotsWrap?: boolean, width?: string}>}
   * @property {string} key - 数据源对象中对应的键名
   * @property {string} title - 列标题
   * @property {boolean} [hover] - 是否启用hover显示popover
   * @property {string} [slots] - 自定义插槽名称
   * @property {boolean} [slotsWrap] - 插槽中的内容超出是否换行，默认false
   * @property {string} [width] - 列宽度，如'1'、'2'等，表示flex比例
   */
  cols: {
    type: Array as PropType<
      {
        key: string;
        title: string;
        hover?: boolean; // 是否启用hover显示popover
        slots?: string;
        slotsWrap?: boolean; // 插槽中的内容超出换行 默认false
        width?: string;
        fixed?: "left" | "right";
      }[]
    >,
    default: () => [],
  },
  /**
   * 表格数据源
   * @type {Array<Record<string, any>>}
   */
  dataSource: {
    type: Array as PropType<{ [key: string]: any }[]>,
    default: () => [],
  },
  /**
   * 分页配置
   * @type {{total: number, current: number, pageSize: PageSizeType}}
   * @property {number} total - 总记录数
   * @property {number} current - 当前页码
   * @property {PageSizeType} pageSize - 每页条数
   */
  pagination: {
    type: Object as PropType<{
      total: number;
      current: number;
      pageSize: PageSizeType;
    }>,
  },
  /**
   * 加载状态
   * @type {boolean}
   */
  loading: {
    type: Boolean,
    default: () => false,
  },
  /**
   * 自定义行属性函数
   * @type {Function}
   * @param {Record<string, any>} record - 行数据
   * @param {number} index - 行索引
   * @returns {Record<string, any>} 行属性对象
   */
  customRow: {
    type: Function,
    default: () => {},
  },
  /**
   * 表格选择类型
   * @type {'radio' | 'checkbox' | null}
   */
  selectionType: {
    type: String as PropType<"radio" | "checkbox" | null>,
    default: null,
  },

  /**
   * 选中行的键值
   * @type {any[]}
   */
  selectedKeys: {
    type: Array,
    default: () => [],
  },

  /**
   * 行键名
   * @type {string}
   */
  rowKey: {
    type: String,
    default: "id",
  },
});

// 处理列宽度
const getColWidth = (col: any) => {
  if (!col.width) return "auto";
  // 如果width是数字或只包含数字，添加px
  if (/^\d+$/.test(col.width)) {
    return `${col.width}px`;
  }
  return col.width;
};

// 获取行的唯一键
const getRowKey = (row: any) => {
  return row[props.rowKey];
};

// 单选选中的行键
const selectedRowKey = ref<any>(null);

// 多选选中的行键数组
const selectedRowKeys = ref<any[]>([]);

// 监听外部传入的selectedKeys变化
watch(
  () => props.selectedKeys,
  (newVal) => {
    if (!newVal) return; // 添加空值检查

    if (props.selectionType === "radio") {
      selectedRowKey.value = newVal.length > 0 ? newVal[0] : null;
    } else {
      selectedRowKeys.value = Array.isArray(newVal) ? [...newVal] : []; // 确保是数组
    }
  },
  { immediate: true }
);

// 判断行是否被选中
const isRowSelected = (row: any) => {
  const rowKey = getRowKey(row);
  if (props.selectionType === "radio") {
    return selectedRowKey.value === rowKey;
  } else {
    return selectedRowKeys.value.includes(rowKey);
  }
};

// 处理行点击
const handleRowClick = (row: any) => {
  if (props.selectionType) {
    if (props.selectionType === "radio") {
      selectedRowKey.value = getRowKey(row);
      emitSelectionChange();
    } else if (props.selectionType === "checkbox") {
      const rowKey = getRowKey(row);
      const index = selectedRowKeys.value.indexOf(rowKey);
      if (index === -1) {
        selectedRowKeys.value.push(rowKey);
      } else {
        selectedRowKeys.value.splice(index, 1);
      }
      emitSelectionChange();
    }
  }
};

// 处理单选变化
const handleRadioChange = (row: any) => {
  selectedRowKey.value = getRowKey(row);
  emitSelectionChange();
};

// 处理复选框变化
const handleCheckboxChange = (row: any, checked: boolean) => {
  const rowKey = getRowKey(row);
  if (checked) {
    if (!selectedRowKeys.value.includes(rowKey)) {
      selectedRowKeys.value.push(rowKey);
    }
  } else {
    const index = selectedRowKeys.value.indexOf(rowKey);
    if (index !== -1) {
      selectedRowKeys.value.splice(index, 1);
    }
  }
  emitSelectionChange();
};

// 全选状态
const isAllSelected = computed(() => {
  if (!props.dataSource.length) return false;
  return props.dataSource.every((row) => selectedRowKeys.value.includes(getRowKey(row)));
});

// 部分选中状态
const isIndeterminate = computed(() => {
  if (!props.dataSource.length) return false;
  const selectedCount = selectedRowKeys.value.length;
  return selectedCount > 0 && selectedCount < props.dataSource.length;
});

// 处理全选/取消全选
const handleSelectAll = (val: boolean) => {
  if (val) {
    selectedRowKeys.value = props.dataSource.map((row) => getRowKey(row));
  } else {
    selectedRowKeys.value = [];
  }
  emitSelectionChange();
};

// 触发选择变化事件
const emitSelectionChange = () => {
  let selectedKeys: any[] = [];
  let selectedRows: any[] = [];

  if (props.selectionType === "radio") {
    selectedKeys = selectedRowKey.value ? [selectedRowKey.value] : [];
    selectedRows = props.dataSource.filter((row) => getRowKey(row) === selectedRowKey.value);
  } else {
    selectedKeys = [...selectedRowKeys.value];
    selectedRows = props.dataSource.filter((row) => selectedRowKeys.value.includes(getRowKey(row)));
  }

  emits("selection-change", selectedKeys, selectedRows);
};

props.cols.forEach((col) => {
  if (typeof col.slotsWrap === "undefined") {
    col.slotsWrap = false;
  }
});

const emits = defineEmits(["change", "selection-change"]);

const xTableRef = ref<HTMLElement>();

const currentChange = (current: number) => {
  emits("change", "current", current);
};
const pageSizeChange = (pageSize: number) => {
  emits("change", "pageSize", pageSize);
};
</script>

<style lang="scss" scoped>
.x-table {
  position: relative;
  width: 100%;
  height: 100%;
  min-height: 310px;
  background-color: #fff;
  border-radius: 10px;
  display: flex;
  flex-direction: column;

  &-container {
    position: relative;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }

  &-scroll {
    width: 100%;
    height: 100%;
    overflow: auto;
    @include scrollbar(both, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
  }

  &-header {
    width: 100%;
    background-color: #f7f9fe;

    table {
      table-layout: fixed;
      border-collapse: collapse;
      width: 100%;

      th {
        height: 40px;
        padding: 8px;
        text-align: left;
        font-size: 13px;
        color: #7a7ea8;
        font-weight: normal;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.fixed-right {
          position: sticky;
          right: 0;
          z-index: 2;
          background-color: #f7f9fe;
          box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        }

        &.fixed-left {
          position: sticky;
          left: 0;
          z-index: 2;
          background-color: #f7f9fe;
          box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        &.selection-column {
          text-align: center;
          width: 50px;
        }
      }
    }
  }

  &-body {
    width: 100%;

    table {
      table-layout: fixed;
      border-collapse: collapse;
      width: 100%;

      td {
        padding: 8px;
        height: 56px;
        border-bottom: 1px solid #f7f9fe;
        font-size: 14px;
        color: #555;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;

        &.fixed-right {
          position: sticky;
          right: 0;
          z-index: 1;
          background-color: #fff;
          box-shadow: -2px 0 5px rgba(0, 0, 0, 0.1);
        }

        &.fixed-left {
          position: sticky;
          left: 0;
          z-index: 1;
          background-color: #fff;
          box-shadow: 2px 0 5px rgba(0, 0, 0, 0.1);
        }

        &.selection-column {
          text-align: center;
          width: 50px;
        }
      }

      tr {
        cursor: pointer;

        &:hover {
          background-color: #ecf2ff;

          td {
            font-weight: bold;

            &.fixed-right,
            &.fixed-left {
              background-color: #ecf2ff;
            }
          }
        }

        &.row-selected {
          background-color: #e6f7ff;

          td {
            &.fixed-right,
            &.fixed-left {
              background-color: #e6f7ff;
            }
          }

          &:hover {
            background-color: #dcf0fd;

            td {
              &.fixed-right,
              &.fixed-left {
                background-color: #dcf0fd;
              }
            }
          }
        }
      }
    }
  }

  &-popover-content {
    height: 32px;
    padding: 0 6px;
    line-height: 32px;
  }

  &-loading {
    @include ct-p;
    img {
      @include wh(50px);
    }
  }

  &-nodata {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    span {
      position: absolute;
      top: 80%;
      left: 50%;
      transform: translate(-50%, 0);
    }
  }

  .wrap-cell {
    white-space: normal;
  }

  // 添加方形单选框样式
  :deep(.square-radio) {
    .el-checkbox__inner {
      border-radius: 2px !important;
    }
  }
}
</style>
