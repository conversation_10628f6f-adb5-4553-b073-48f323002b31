<template>
  <x-drawer
    :title="$t('editAreaRouteTemplate')"
    :visible="props.show"
    :btnOption="{ position: 'center', confirm: $t('save') }"
    bodyPadding="0"
    @update:visible="updateVisible"
    @confirm="routeTemplateConfirm"
    @cancel="routeTemplateCancel"
    width="90%"
  >
    <div
      class="route-template"
      v-xloading="loading.value"
      :loading-text="loading.text"
    >
      <div
        class="route-template-left"
        id="container"
      ></div>
      <div
        class="route-template-right"
        ref="templateContentRef"
      >
        <div class="add-bar">
          <div class="add-bar-left">
            <span class="add-bar-left-top">{{ $t("vehicleTaskTemplate") }}</span>
            <span class="add-bar-left-bottom">{{ $t("LongPressDragAdjustOrder") }}</span>
          </div>
          <div class="add-bar-right">
            <x-button
              type="white"
              :text="$t('add')"
              icon="button_add"
              @click="addTemplate"
              style="color: #5964fb"
            />
          </div>
        </div>
        <div class="templates">
          <div
            class="template-items"
            v-for="(temp, index) in templateData"
            :key="index"
          >
            <!-- 编辑模式 -->
            <template v-if="temp.editStatus">
              <div class="template-item-edit">
                <div class="template-header">
                  <div class="template-version">
                    <span>{{ "版本" }}</span>
                    <el-select
                      v-model="temp.version"
                      :placeholder="'请选择'"
                      :disabled="temp.saved"
                      style="width: 140px; margin-right: 10px"
                      :teleported="false"
                      @change="versionChange($event, index, temp)"
                    >
                      <el-option
                        v-for="item in formOptions.version"
                        :key="item.value"
                        :label="item.label"
                        :value="item.value"
                      />
                    </el-select>
                  </div>
                  <div class="template-name">
                    <span class="required">{{ $t("templateName") }}</span>
                    <x-input
                      v-model:value="temp.templateName"
                      :placeholder="$t('PEnterName')"
                      :maxlength="10"
                      style="width: 100%"
                    />
                  </div>
                </div>
                <div class="template-title">
                  <div class="left">
                    {{ templateVersionMap[temp.version] }}
                  </div>
                  <div
                    class="right"
                    @click="resetTaskData(index)"
                  >
                    <x-icon name="reload" />
                    <span>{{ $t("reset") }}</span>
                  </div>
                </div>
                <div class="template-content">
                  <RoutesMultitype2
                    ref="routesMultitype2Ref"
                    :list="temp.list"
                    :options="formOptions.list"
                    :container="templateContentRef"
                    :maxRouteLength="999"
                    :version="temp.version"
                    @delRoute="delMapOverlay"
                    @changeRoute="addMapOverlay(index)"
                    @update:list="updateTempList(index, $event)"
                    @delPoint="delPointOverlay"
                    @choosePoint="choosePoint"
                  />
                </div>
                <div class="template-footer">
                  <x-button
                    type="linearBlue"
                    :text="$t('cancel')"
                    size="small"
                    @click="handleCancel(index)"
                    style="margin-right: 9px; padding: 2px 7px"
                  />
                  <x-button
                    type="blue"
                    size="small"
                    :text="$t('confirm')"
                    @click="handleConfirm(index)"
                  />
                </div>
              </div>
            </template>
            <!-- 预览模式 -->
            <template v-else>
              <div
                class="template-item-view"
                draggable="true"
                @dragstart="handleDragstart($event, index)"
                @dragenter="handleDragenter($event, index)"
                @dragend="handleDragend"
                @dragover="handleDragover"
              >
                <div :class="['template-tag', temp.version === 1 ? 'is-blue' : 'is-green']">
                  {{ temp.version === 1 ? "1.0" : [4, 5, 6].includes(temp.version) ? "3.0" : "2.0" }}
                </div>
                <div class="template-title">
                  <div class="template-title-left">{{ temp.templateName }}</div>
                  <div class="template-title-right">
                    <div
                      class="view"
                      @click="viewTemplate(index)"
                    >
                      <x-popover
                        trigger="hover"
                        placement="bottom"
                        :container="templateContentRef"
                      >
                        <x-icon
                          :name="temp.visible ? 'eye_invisible' : 'eye'"
                          width="16"
                          height="16"
                        />
                        <template #content>
                          <div class="route-template-hover-popover">
                            {{ temp.visible ? $t("hide") : $t("show") }}
                          </div>
                        </template>
                      </x-popover>
                    </div>
                    <div
                      class="del"
                      @click="delTemplate(index)"
                    >
                      <x-popover
                        trigger="hover"
                        placement="bottom"
                        :container="templateContentRef"
                      >
                        <x-icon
                          name="del_x"
                          width="16"
                          height="16"
                        />
                        <template #content>
                          <div class="route-template-hover-popover">
                            {{ $t("delete") }}
                          </div>
                        </template>
                      </x-popover>
                    </div>
                    <div
                      class="edit"
                      @click="editTemplate(index)"
                    >
                      <x-popover
                        trigger="hover"
                        placement="bottom"
                        :container="templateContentRef"
                      >
                        <x-icon
                          name="edit"
                          width="16"
                          height="16"
                        />
                        <template #content>
                          <div class="route-template-hover-popover">
                            {{ $t("edit") }}
                          </div>
                        </template>
                      </x-popover>
                    </div>
                  </div>
                </div>
                <Transition name="template-content-transition">
                  <div>
                    <div
                      class="template-content template-content-transition"
                      :style="{ height: `${temp.list.length * 61}px` }"
                      v-if="temp.visible"
                    >
                      <template v-if="temp.version === 6">
                        <el-scrollbar
                          wrap-class="structure-ponit-wrapper"
                          :height="`${temp.list.length * 61}px`"
                        >
                          <div class="structure-ponit-wrapper-list">
                            <div
                              class="structure-ponit-wrapper-list-item"
                              v-for="(item, index) in temp.list"
                              :key="index"
                              @click="choosePoint(index)"
                            >
                              <div class="structure-ponit-wrapper-list-item-left">
                                <text>{{ item.name }}</text>
                                <text>经度：{{ item.longitude }}</text>
                                <text>纬度：{{ item.latitude }}</text>
                              </div>
                            </div>
                          </div>
                        </el-scrollbar>
                      </template>
                      <template v-else>
                        <div
                          v-for="(item, i) in temp.list"
                          :key="i"
                        >
                          <div class="timeline">
                            <div
                              :class="[
                                'timeline-item',
                                {
                                  line: i !== temp.list.length - 1,
                                },
                              ]"
                            >
                              <div class="timeline-item-title">
                                <span class="timeline-item-title-name">
                                  {{
                                    item.name ||
                                    (["route", "csvRoute"].includes(item.type ?? "") ? "路线名缺失" : "区块名称缺失")
                                  }}
                                </span>
                              </div>
                              <!-- <div
                              v-if="item.type === 'route'"
                              class="timeline-item-content"
                            >
                              <span>{{ getTaskTypeName(item.taskType ?? "") || $t("cleanTypeMissing") }}</span>
                              <span v-if="item.sweepingType">/{{ getStrengthName(item.sweepingType) }}</span>
                            </div> -->
                            </div>
                          </div>
                        </div>
                      </template>
                    </div>
                  </div>
                </Transition>
              </div>
            </template>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch, computed, createVNode, render } from "vue";
import { getProAreaInfo, saveAreaRouteTemplate, getAreaRouteTemplate, getProAreaRange } from "@/services/api";
import { cleaningType, sweepingType } from "@/assets/ts/config";
import { polylineStyle } from "@/services/wsconfig";
import { polylineTemplateStyle, polylineHoverMarkerStyle } from "@/services/wsconfig";
import AMapLoader from "@amap/amap-jsapi-loader";
import xDrawer from "@/components/x-drawer.vue";
import xIcon from "@/components/x-icon.vue";
import xInput from "@/components/x-input.vue";
import xButton from "@/components/x-button.vue";
import xPopover from "@/components/x-popover.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import RoutesMultitype2, { type ListType } from "./routesMultitype2.vue";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import type { GetAreaRangeResponse } from "@/services/type";
import { templateVersionMap } from "@/assets/ts/config";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
import SiteMarker from "@/views/main/components/siteMarker.vue";
const $t = i18nSimpleKey("proAreaManage");

interface Template {
  id?: string;
  templateName?: string;
  list: ListType[];
  editStatus: boolean;
  visible: boolean;
  saved: boolean;
  /** 1(1.0版本) 2(2.0版本) 3(2.0自录路线) 4(3.0版本) */
  version: number;
}

type AreaRangeType = {
  id: string;
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  points: { longitude: number; latitude: number }[];
};

type OverlayItemData = {
  cleanTypeArrays: Array<any>;
  cleanTypeList: Array<any>;
  id: string;
  label: string;
  name: string;
  points: Array<[number, number]>;
  /**
   * route
   */
  type: string;
  value: string;
  vehRouteNo: string;
};

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
  },
});
const routesMultitype2Ref = ref<any>();
/** 结构化点列表 */
const structurePonitList = ref<any[]>([]);
/** 是否处于选点模式 */
const pointEditMode = ref(false);
let mapClickHandler: any = null;
/**
 * 表单
 */
const templateContentRef = ref<any>();
const formOptions = reactive({
  routes: [] as ListType[],
  csvRoutes: [] as ListType[],
  blocks: [] as ListType[],
  list: [] as ListType[], // 所有下拉框数据
  structureRoutes: [] as ListType[], // 结构化路线

  version: [
    { label: "1.0", value: 1 },
    { label: "2.0", value: 2 },
    { label: "2.0自录路线", value: 3 },
    { label: "3.0", value: 4 },
    { label: "3.0结构化路线", value: 5 },
    { label: "3.0结构化选点", value: 6 },
  ],
});
/** 模版列表 */
const templateData = reactive([] as Template[]);
/** 存储上一次保存的数据 */
const tempTemplateData = ref<Template[]>([]);

const choosePoint = (index: number) => {
  const targetMarker = mapInfo.pointMarkers[index];

  if (targetMarker) {
    const position = targetMarker.getPosition();
    if (position) {
      mapInfo.map.setCenter(position);
      mapInfo.map.setZoom(18);
    }
  }

  mapInfo.pointMarkers.forEach((marker) => {
    const { markerDiv: div, id: markerId, pointName } = marker.getExtData();

    render(null, div);

    const newDiv = document.createElement("div");

    render(
      createVNode(SiteMarker, {
        focus: true,
        icon: "point-marker",
        number: 0,
        index: pointName,
      }),
      newDiv
    );

    div.parentNode?.replaceChild(newDiv, div);

    marker.setExtData({
      ...marker.getExtData(),
      markerDiv: newDiv,
      focus: markerId === index,
    });
  });
};

const enableMapClickForPoints = (templateIdx: number) => {
  if (!mapInfo.map) return;
  // 先解绑，防止重复绑定
  disableMapClickForPoints();

  // 使用防抖处理点击事件，避免短时间内多次触发
  mapClickHandler = debounce((e: any) => {
    const { lng, lat } = e.lnglat;

    // 检查模板是否存在
    if (!templateData[templateIdx]) return;

    // 获取当前模板的list
    const list = templateData[templateIdx].list;
    console.log("======", { templateIdx, data: templateData[templateIdx] });

    // 添加新点
    list.push({
      id: Date.now() + "", // 简单唯一id
      name: String(list.length + 1),
      type: "structurePoints",
      objType: "point",
      longitude: lng,
      latitude: lat,
      label: "",
      value: "",
    });

    // 限制点位数量，避免过多点位导致性能问题
    if (list.length > 100) {
      list.shift(); // 移除最早的点位
    }

    // 触发视图更新
    updateTempData();

    // 使用requestAnimationFrame优化渲染
    window.requestAnimationFrame(() => {
      renderPointMarkers();
    });
  }, 300);

  mapInfo.map.on("click", mapClickHandler);
};

const disableMapClickForPoints = () => {
  if (mapInfo.map && mapClickHandler) {
    mapInfo.map.off("click", mapClickHandler);
    mapClickHandler = null;
  }
};

// 同步临时数据
const updateTempData = () => {
  tempTemplateData.value = JSON.parse(JSON.stringify(templateData));
};

type SaveParam = Parameters<typeof saveAreaRouteTemplate>[0];
// 保存设置
const routeTemplateConfirm = debounce(async () => {
  if (editingTemplateIdx.value !== null) {
    Message("error", `${$t("templateBeingEdited")},${$t("PConfirmBeforeSaving")}`);
    const templateDomLists = document.getElementsByClassName("template-items");
    templateDomLists[editingTemplateIdx.value].scrollIntoView({
      behavior: "smooth",
      block: "start",
    });
    return;
  } else {
    const processedData =
      templateData.map(({ id, templateName, list, version }) => {
        const template = {
          id: id || "0",
          templateName,
          version,
          routeTemplateRelEntities: [],
          sysAreaTemplateBlockRelEntities: [],
          sysAreaTemplateRecordingRouteEntities: [],
          sysRouteTemplatePointsEntities: [],
        } as SaveParam["sysAreaTemplateEntities"][0];
        list.forEach((item, index) => {
          if (item.type === "route") {
            template.routeTemplateRelEntities?.push({
              routeId: item.id,
              vehRouteNo: item.vehRouteNo,
              orders: index,
              cleanType: item.taskType,
              sweepingType: item.sweepingType,
            });
          } else if (item.type === "block") {
            template.sysAreaTemplateBlockRelEntities?.push({
              blockId: item.id,
              blockName: item.name,
              orders: index,
              gpsList: formOptions.blocks.find((item) => item.id === item.id)?.points,
            });
          } else if (item.type === "csvRoute") {
            template.sysAreaTemplateRecordingRouteEntities?.push({
              recordingId: item.id,
              orders: index,
              clean: item.cleanStatus,
            });
          } else if (item.type === "structurePoints") {
            template.sysRouteTemplatePointsEntities.push({
              ...item,
            });
          }
        });
        return template;
      }) || [];
    await saveAreaRouteTemplate({
      proAreaId: props.id,
      sysAreaTemplateEntities: processedData,
    });
    emits("update:show", false);
    Message("success", $t("saveSuccess"));
  }
}, 800);

// 关闭抽屉
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
const routeTemplateCancel = () => {
  emits("update:show", false);
};

// 获取任务类型名称
const getTaskTypeName = (type: string) => cleaningType.find((v) => v.value === type)?.label;

// 获取档位类型名称
const getStrengthName = (type: string) => sweepingType.find((v) => v.value === type)?.label;

// 重置路线
const resetTaskData = async (index: number) => {
  if (templateData[index].version === 6) {
    templateData[index].list = [];
  } else {
    templateData[index].list = [
      {
        id: "",
        name: "",
        vehRouteNo: "",
        taskType: "",
        sweepingType: "",
        sweepingTypeList: [] as string[],
        label: "",
        value: "",
        objType: templateData[index].version === 4 ? "block" : undefined,
      },
    ];
  }
  await clearMapOverlay();
  await renderAllOverlays();
};

/**
 * 模板
 */
// 添加模板
const addTemplate = () => {
  if (editingTemplateIdx.value !== null) {
    Message("error", `${$t("templateBeingEdited")},${$t("PConfirmBeforeAdding")}`);
    return;
  }
  templateData.push({
    id: "",
    templateName: "",
    version: 1,
    list: [
      {
        id: "",
        vehRouteNo: "",
        taskType: "",
        sweepingType: "",
        sweepingTypeList: [] as string[],
        label: "",
        value: "",
      },
    ],
    editStatus: true, // 编辑状态
    visible: false, // 展示路线详情
    saved: false, // 已有完整数据
  });
  updateTempData();
};

// 删除模板
const delTemplate = (index: number) => {
  templateData.splice(index, 1);
  updateTempData();
  clearMapOverlay();
  renderAllOverlays();
};

// 进入编辑
const editTemplate = (index: number) => {
  if (editingTemplateIdx.value !== null) {
    Message("error", `${$t("templateBeingEdited")},${$t("PConfirmBeforeEditing")}`);
    return;
  }
  templateData[index].editStatus = true;
  updateTempData();
  addMapOverlay(index);
};

// 同步数据
const updateTempList = (index: number, list: ListType[]) => {
  templateData[index].list = list;
};

// 编辑状态的模板下标
const editingTemplateIdx = computed(() => {
  const editingIndex = templateData.findIndex((template) => template.editStatus);
  return editingIndex !== -1 ? editingIndex : null;
});

/** 版本号更换事件 */
const versionChange = async (val: number, index: number, row: any) => {
  const list = [
    {
      id: undefined,
      name: "",
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
      sweepingTypeList: [] as string[],
      objType: "block",
    },
  ];
  templateData.splice(index, 1, {
    ...row,
    list: val === 6 ? [] : list,
  });
  if (val === 6) {
    pointEditMode.value = true;
    enableMapClickForPoints(index);
  } else {
    pointEditMode.value = false;
    disableMapClickForPoints();
  }
};

/**渲染所有覆盖物 */
const renderAllOverlays = async () => {
  // 创建数组存储覆盖物和名称
  const overlays: any[] = [];
  const overlayNames: any[] = [];

  // // 1.0和3.0版本只显示路线
  // if ([1, 5].includes(val)) {
  //   formOptions.routes.forEach((route) => {
  //     const path = route?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
  //     const overlay = new mapInfo.Amap.Polyline({
  //       path: path,
  //       ...polylineTemplateStyle,
  //       strokeColor: "#555555",
  //       cursor: "pointer",
  //       extData: route,
  //     });
  //     overlay.on("click", overlayClick);
  //     // overlay.on("mouseover", overlayMouseover);
  //     // overlay.on("mouseout", overlayMouseout);
  //     overlays.push(overlay);

  //     if (overlay && overlay.getPath()) {
  //       const overlayName = drawOverlayName(overlay, route.name);
  //       overlayNames.push(overlayName);
  //     }
  //   });
  // }
  // // 2.0版本显示路线和区块
  // else if ([2, 4].includes(val)) {
  //   // 添加路线
  //   formOptions.routes.forEach((route) => {
  //     const path = route?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
  //     const overlay = new mapInfo.Amap.Polyline({
  //       path: path,
  //       ...polylineTemplateStyle,
  //       strokeColor: "#555555",
  //       cursor: "pointer",
  //       extData: route,
  //     });
  //     overlay.on("click", overlayClick);
  //     // overlay.on("mouseover", overlayMouseover);
  //     // overlay.on("mouseout", overlayMouseout);
  //     overlays.push(overlay);

  //     if (overlay && overlay.getPath()) {
  //       const overlayName = drawOverlayName(overlay, route.name);
  //       overlayNames.push(overlayName);
  //     }
  //   });

  //   // 添加区块
  //   formOptions.blocks.forEach((block) => {
  //     const path = block?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
  //     const overlay = new mapInfo.Amap.Polygon({
  //       path: path,
  //       strokeWeight: 0,
  //       fillOpacity: 0.4,
  //       fillColor: "#5964FB",
  //       zIndex: 100,
  //       bubble: true,
  //       cursor: "pointer",
  //       extData: block,
  //     });
  //     overlay.on("click", overlayClick);
  //     // overlay.on("mouseover", overlayMouseover);
  //     // overlay.on("mouseout", overlayMouseout);
  //     overlays.push(overlay);

  //     if (overlay && overlay.getPath()) {
  //       const overlayName = drawOverlayName(overlay, block.name);
  //       overlayNames.push(overlayName);
  //     }
  //   });
  // }
  // // 2.0自录路线版本
  // else if (val === 3) {
  //   formOptions.csvRoutes.forEach((csvRoute) => {
  //     const path = csvRoute?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
  //     const overlay = new mapInfo.Amap.Polyline({
  //       path: path,
  //       ...polylineStyle,
  //       cursor: "pointer",
  //       extData: csvRoute,
  //     });
  //     overlay.on("click", overlayClick);
  //     // overlay.on("mouseover", overlayMouseover);
  //     // overlay.on("mouseout", overlayMouseout);
  //     overlays.push(overlay);

  //     if (overlay && overlay.getPath()) {
  //       const overlayName = drawOverlayName(overlay, csvRoute.name);
  //       overlayNames.push(overlayName);
  //     }
  //   });
  // }

  // 添加路线
  formOptions.routes.forEach((route) => {
    const path = route?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
    const overlay = new mapInfo.Amap.Polyline({
      path: path as any[],
      ...polylineTemplateStyle,
      strokeColor: "#555555",
      cursor: "pointer",
      extData: route,
    });
    overlay.on("click", overlayClick);
    // overlay.on("mouseover", overlayMouseover);
    // overlay.on("mouseout", overlayMouseout);
    overlays.push(overlay);

    if (overlay && overlay.getPath()) {
      const overlayName = drawOverlayName(overlay, route.name);
      overlayNames.push(overlayName);
    }
  });

  // 添加区块
  formOptions.blocks.forEach((block) => {
    const path = block?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
    const overlay = new mapInfo.Amap.Polygon({
      path: path as any[],
      strokeWeight: 0,
      fillOpacity: 0.4,
      fillColor: "#5964FB",
      zIndex: 100,
      bubble: true,
      cursor: "pointer",
      extData: block,
    });
    overlay.on("click", overlayClick);
    // overlay.on("mouseover", overlayMouseover);
    // overlay.on("mouseout", overlayMouseout);
    overlays.push(overlay);

    if (overlay && overlay.getPath()) {
      const overlayName = drawOverlayName(overlay, block.name);
      overlayNames.push(overlayName);
    }
  });

  // 2.0自录路线版本
  formOptions.csvRoutes.forEach((csvRoute) => {
    const path = csvRoute?.points?.map(({ longitude, latitude }) => [longitude, latitude]);
    const overlay = new mapInfo.Amap.Polyline({
      path: path as any[],
      ...polylineStyle,
      cursor: "pointer",
      extData: csvRoute,
    });
    overlay.on("click", overlayClick);
    // overlay.on("mouseover", overlayMouseover);
    // overlay.on("mouseout", overlayMouseout);
    overlays.push(overlay);

    if (overlay && overlay.getPath()) {
      const overlayName = drawOverlayName(overlay, csvRoute.name);
      overlayNames.push(overlayName);
    }
  });

  // 添加所有覆盖物到地图
  mapInfo.map.add([...overlays, ...overlayNames]);
  mapInfo.overlayNames = overlayNames;
  mapInfo.overlays = overlays;
  mapInfo.map.setFitView(mapInfo.overlays, false, [10, 50, 10, 50], 20);
};
/**所有覆盖物点击事件 */
const overlayClick = (e: any) => {
  if (templateData.every((item) => !item.editStatus)) return;
  const overlay = e.target;
  const overlayData: OverlayItemData = overlay.getExtData();
  if (["block"].includes(overlayData.type)) {
    overlay.setOptions({ fillColor: "#5964FB", fillOpacity: 0.6, zIndex: 11 });
  } else if (["route", "csvRoute"].includes(overlayData.type)) {
    overlay.setOptions({ strokeColor: "#5964FB", strokeWeight: 3, zIndex: 11 });
  }
  routesMultitype2Ref.value[0].addRouteByMap(overlayData);
};
/**所有覆盖物鼠标移入事件 */
const overlayMouseover = (e: any) => {
  const overlay = e.target;
  const overlayData: OverlayItemData = overlay.getExtData();
  if (["block"].includes(overlayData.type)) {
    overlay.setOptions({ fillColor: "#F9852E", fillOpacity: 0.6, zIndex: 11 });
  } else if (["route", "csvRoute"].includes(overlayData.type)) {
    overlay.setOptions({ strokeColor: "#F9852E", strokeWeight: 3, zIndex: 11 });
  }
};
/**所有覆盖物鼠标移出事件 */
const overlayMouseout = (e: any) => {
  const overlayData: OverlayItemData = e.target.getExtData();
  if (["block"].includes(overlayData.type)) {
    e.target.setOptions({ fillOpacity: 0.7, fillColor: "#5964FB", zindex: 10 });
  } else if (["route"].includes(overlayData.type)) {
    e.target.setOptions({ strokeColor: "#555555", strokeWeight: 3, zIndex: 10 });
    // e.target.setOptions({ ...polylineTemplateStyle, zIndex: 14 });
  } else if (["csvRoute"].includes(overlayData.type)) {
    e.target.setOptions({ strokeColor: "#555555", strokeWeight: 3, zIndex: 10 });
  }
};

// 取消编辑
const handleCancel = (index: number) => {
  clearMapOverlay();
  const currentTempTemplate = tempTemplateData.value[index] as Template;
  if (!currentTempTemplate.saved) {
    delTemplate(index);
    return;
  }
  templateData[index] = currentTempTemplate;
  templateData[index].editStatus = false;
};

// 提交编辑
const handleConfirm = (index: number) => {
  if (!templateData[index].templateName) {
    return Message("error", $t("PEnterTemplateName"));
  }
  const isValidList = templateData[index].list.every((item) => (item.type === "route" ? item.vehRouteNo : item.id));
  if (!isValidList) {
    return Message("error", "请将模版信息补充完整");
  }

  templateData[index].list.map((item, index) => {
    return {
      ...item,
      name: index + 1,
    };
  });
  templateData[index].editStatus = false;
  templateData[index].saved = true;
  updateTempData();
  clearMapOverlay();
};

// 模版详情显隐
const viewTemplate = async (index: number) => {
  templateData.forEach((v, i) => {
    if (i === index) v.visible = !v.visible;
    else v.visible = false;
  });

  if (templateData[index].visible) {
    if (templateData[index].version === 6) {
      await renderPointMarkers(index);
    } else {
      addMapOverlay(index);
    }
  } else {
    if (templateData[index].version === 6) {
      clearPointMarkers();
    } else {
      //
    }
  }
};

// 模板排序
// start => enter => over => end
// eslint-disable-next-line no-unused-vars
let dragIndex: number | null = null;
const handleDragstart = (event: DragEvent, index: number) => {
  dragIndex = index;
};
const handleDragenter = (event: DragEvent, index: number) => {
  if (dragIndex !== null) {
    const dataArr = templateData;
    const dragging = dataArr[dragIndex];
    dataArr.splice(dragIndex, 1);
    dataArr.splice(index, 0, dragging as any);
    const marker = mapInfo.overlayNames[dragIndex];
    mapInfo.overlayNames.splice(dragIndex, 1);
    mapInfo.overlayNames.splice(index, 0, marker);
    dragIndex = index;
  }
};
const handleDragover = (event: DragEvent) => {
  event.preventDefault();
};
const handleDragend = () => {
  dragIndex = null;
};

/**
 * 地图
 */
const defaultMapInfo = {
  map: null as unknown as AMap.Map,
  Amap: null as unknown as typeof AMap,
  overlays: [] as any[], // 覆盖物：路线|区块
  overlayNames: [] as any[], // 覆盖物名称
  areaRanges: [] as any[], // 高精地图围栏
  pointMarkers: [] as any[], // 点位
};
let mapInfo: typeof defaultMapInfo = JSON.parse(JSON.stringify(defaultMapInfo));

// 载入地图
const initMap = () => {
  return new Promise<void>((resolve) => {
    lazyAMapApiLoader().then((AMap) => {
      mapInfo.map = new AMap.Map("container", {
        zooms: [2, 26],
        mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
      });
      mapInfo.Amap = AMap;
      const scale = new AMap.Scale({
        position: {
          left: "10px",
          bottom: "50px",
        },
      });
      mapInfo.map.addControl(scale);
      // 等地图ready事件
      mapInfo.map.on("complete", () => {
        resolve();
      });
    });
  });
};

/** 添加覆盖物 */
const addMapOverlay = (index: number) => {
  const list = templateData[index].list;
  const overlay = mapInfo.overlays.find((item) => item.getExtData().id === list[0].id);
  const overlayData: OverlayItemData = overlay?.getExtData() ?? {};
  if (["route", "csvRoute"].includes(overlayData.type)) {
    overlay.setOptions({ strokeColor: "#5964FB", strokeWeight: 3, zIndex: 11 });
  } else {
    overlay.setOptions({ fillColor: "#5964FB", fillOpacity: 0.6, zIndex: 11 });
  }
  mapInfo.map.setFitView(overlay, false, [10, 50, 10, 50], 20);
};

/** 绘制覆盖物名称 */
const drawOverlayName = (overlay: any, name?: string, item?: ListType) => {
  const position = item?.type === "block" ? overlay.getBounds().getCenter() : overlay.getPath()[0];
  const markerDiv = document.createElement("div");
  const _marker = new mapInfo.Amap.Marker({
    ...polylineHoverMarkerStyle,
    content: markerDiv,
    position: position,
    clickable: false,
    cursor: "default",
    extData: {
      markerDiv: markerDiv,
      name: name,
    },
  });
  render(createVNode(PolylineNameMarker, { name: name, type: "light" }), markerDiv);

  return _marker;
};

/** 删除覆盖物 */
const delMapOverlay = async (id: string) => {
  const overlay = mapInfo.overlays.find((item) => item.getExtData().id === id);
  const overlayData: OverlayItemData = overlay?.getExtData() ?? {};
  if (["route", "csvRoute"].includes(overlayData.type)) {
    overlay.setOptions({ strokeColor: "#555555", strokeWeight: 3, zIndex: 11 });
  } else {
    overlay.setOptions({ fillColor: "#555555", fillOpacity: 0.6, zIndex: 11 });
  }
  mapInfo.map.setFitView(overlay, false, [10, 50, 10, 50], 20);
};

const delPointOverlay = (index: number) => {
  mapInfo.map.remove([mapInfo.pointMarkers[index]]);
  mapInfo.pointMarkers.splice(index, 1);
};

/** 清空地图覆盖物（路线|区块） */
const clearMapOverlay = () => {
  if (!mapInfo.map) return;

  try {
    // 安全地移除覆盖物
    const overlaysToRemove = [
      ...(mapInfo.overlays || []),
      ...(mapInfo.overlayNames || []),
      ...(mapInfo.pointMarkers || []),
    ].filter(Boolean);

    if (overlaysToRemove.length > 0) {
      mapInfo.map.remove(overlaysToRemove);
    }

    // 清空数组
    mapInfo.overlays = [];
    mapInfo.overlayNames = [];
    mapInfo.pointMarkers = [];
  } catch (error) {
    console.error("Error clearing map overlays:", error);
  }
};

// 新增：只清除点位（pointMarkers）
const clearPointMarkers = () => {
  if (!mapInfo.map) return;
  try {
    if (mapInfo.pointMarkers && mapInfo.pointMarkers.length > 0) {
      mapInfo.map.remove(mapInfo.pointMarkers);
      mapInfo.pointMarkers = [];
    }
  } catch (error) {
    console.error("Error clearing point markers:", error);
  }
};

/** 获取模版信息及列表 */
const getTemplateInfo = async () => {
  const templateList = await getAreaRouteTemplate(props.id as string);
  templateList.forEach((template) => {
    const routes =
      template.routeTemplateRelEntities?.map(
        (route) =>
          ({
            type: "route",
            objType: "route",
            id: route.routeId || "0",
            name: route.routeName || "",
            orders: route.orders,
            vehRouteNo: route.vehRouteNo,
            taskType: route.cleanType,
            sweepingType: route.sweepingType || "",
            sweepingTypeList: route.sweepingType ? [route.cleanType, route.sweepingType] : [route.cleanType],
          } as ListType)
      ) || [];
    const blocks =
      template.sysAreaTemplateBlockRelEntities?.map(
        (block) =>
          ({
            type: "block",
            objType: "block",
            id: block.blockId || "0",
            name: block.blockName,
            orders: block.orders,
          } as ListType)
      ) || [];
    const csvRoutes =
      template.sysAreaTemplateRecordingRouteEntities?.map(
        (csvRoute) =>
          ({
            type: "csvRoute",
            objType: "route",
            id: csvRoute.recordingRouteEntity.id || "0",
            name: csvRoute.recordingRouteEntity.routeName,
            orders: csvRoute.recordingRouteEntity.orders,
            cleanStatus: csvRoute.recordingRouteEntity.clean,
          } as ListType)
      ) || [];

    structurePonitList.value =
      template.sysRouteTemplatePointsEntities?.map(
        (point) =>
          ({
            type: "structurePoints",
            objType: "route",
            id: point.recordingId || "0",
            name: point.orders,
            orders: point.orders,
            cleanStatus: point.clean,
            points: [{ longitude: point.longitude, latitude: point.latitude }],
            longitude: point.longitude,
            latitude: point.latitude,
          } as ListType)
      ) || [];
    templateData.push({
      id: template.id || "0",
      templateName: template.templateName,
      version: template.version,
      list: [...routes, ...blocks, ...csvRoutes, ...structurePonitList.value].toSorted(
        (a, b) => (a.orders ?? 0) - (b.orders ?? 0)
      ),
      editStatus: false,
      visible: false,
      saved: true,
    });
  });

  console.log(templateData, "获取模版信息及列表");
};

/** 获取列表 */
const fetchProAreaInfo = async () => {
  const res = await getProAreaInfo(props.id);
  formOptions.routes = res.areaRoute.map(({ id, gps, routeName, cleanTypeArrays, vehRouteNo }) => {
    const cleanTypeList =
      cleanTypeArrays?.map((value: string) => {
        const item = cleaningType.find((type) => type.value === value);
        return {
          value: item?.value,
          label: item?.label,
        };
      }) || [];
    return {
      type: "route",
      id: id || "0",
      name: routeName,
      label: routeName,
      value: id,
      vehRouteNo,
      cleanTypeArrays: cleanTypeArrays,
      cleanTypeList,
      points:
        gps?.map(({ longitude, latitude }: any) => ({
          longitude,
          latitude,
        })) || [],
    };
  });
  formOptions.blocks = res.areaBlockDtoList.map((block) => ({
    type: "block",
    label: block.blockName,
    value: block.id,
    id: block.id,
    name: block.blockName,
    points:
      block.areaBlockGpsList?.map(({ longitude, latitude }) => ({
        longitude,
        latitude,
      })) || [],
  }));
  formOptions.csvRoutes = res.recordingRouteEntities.map((csvRoute) => {
    return {
      type: "csvRoute",
      id: csvRoute.id,
      label: csvRoute.routeName,
      value: csvRoute.id,
      name: csvRoute.routeName,
      points:
        csvRoute.recordingGpsList?.map(({ longitude, latitude }) => ({
          longitude,
          latitude,
        })) || [],
    };
  });
  formOptions.list = [...formOptions.routes, ...formOptions.blocks, ...formOptions.csvRoutes];
  await renderAllOverlays();
};

/** 渲染点位标记 */
const renderPointMarkers = (index?: number) => {
  // 如果地图不存在，直接返回
  if (!mapInfo.map || !mapInfo.Amap) return;

  // 清除之前的标记
  if (mapInfo.pointMarkers && mapInfo.pointMarkers.length) {
    mapInfo.pointMarkers.forEach((marker) => {
      if (marker) marker.setMap(null);
    });
    mapInfo.pointMarkers = [];
  }

  // 只渲染当前编辑模板的点
  let list;
  if (index != null && templateData[index]) {
    list = templateData[index].list;
  } else {
    const editingIdx = editingTemplateIdx.value;
    if (editingIdx === null || !templateData[editingIdx]) return;
    list = templateData[editingIdx].list;
  }

  // 限制渲染的点位数量，避免过多点位导致性能问题
  const maxPoints = 100;
  const pointsToRender = list
    .filter((item) => item.type === "structurePoints" && item.longitude && item.latitude)
    .slice(0, maxPoints);

  // 批量创建点位，减少重绘次数
  const markers = pointsToRender.map((item) => {
    const markerDiv = document.createElement("div");
    const marker = new mapInfo.Amap.Marker({
      position: [item.longitude!, item.latitude!],
      content: markerDiv,
      zIndex: 100,
      anchor: "bottom-center",
      extData: {
        markerDiv,
        type: "point-marker",
        id: item.id,
        focus: false,
        pointName: item.name,
      },
    });

    // 使用Vue的异步渲染，避免同步渲染大量组件
    setTimeout(() => {
      render(
        createVNode(SiteMarker, {
          focus: true,
          icon: "point-marker",
          number: 0,
          index: Number(item.name),
        }),
        markerDiv
      );
    }, 0);

    return marker;
  });

  // 批量添加到地图
  if (markers.length > 0) {
    mapInfo.map.add(markers);
    mapInfo.pointMarkers = markers;
  }
};

/** 获取高精地图信息 */
const getAreaRangeInfo = async () => {
  const res = await getProAreaRange({ areaId: props.id });
  const grouped = res.reduce((accumulator, item) => {
    const key = `${item.xmlId}_${item.pointType}`;
    if (!accumulator[key]) {
      accumulator[key] = [];
    }
    accumulator[key].push(item);
    return accumulator;
  }, {} as any);
  const list: GetAreaRangeResponse[] = Object.values(grouped);
  drawAreaRange(
    list.map((item) => ({
      id: item[0].id,
      proAreaId: item[0].proAreaId,
      xmlId: item[0].xmlId,
      pointType: item[0].pointType,
      points: item.map(({ longitude, latitude }) => ({ longitude, latitude })),
    }))
  );
};

/** 渲染高精地图 */
const drawAreaRange = (list: AreaRangeType[]) => {
  const polygons = list.map((item) => {
    const polygon = new mapInfo.Amap.Polygon({
      path: item.points.map(({ longitude, latitude }: any) => [longitude, latitude]),
      strokeWeight: 0,
      fillOpacity: 0.4,
      fillColor: item.pointType === 1 ? "#F41515" : "#27D4A1",
      zIndex: 1,
      bubble: true,
      extData: item,
      cursor: "pointer",
    });

    return polygon;
  });
  mapInfo.areaRanges = polygons;
  mapInfo.map.add(polygons);
  mapInfo.map.setFitView(polygons, false, [10, 50, 10, 50], 20);
};

/** 页面加载状态 */
const loading = reactive({
  value: false,
  text: "正在加载数据中...",
});

/**
 * 初始化
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      // 重置内容
      templateData.length = 0;
      tempTemplateData.value = [];
      loading.value = true;

      try {
        await initMap();
        await Promise.all([getTemplateInfo(), fetchProAreaInfo(), getAreaRangeInfo()]);
      } catch (error) {
        console.error("Map initialization error:", error);
      } finally {
        loading.value = false;
      }
    } else {
      // 关闭时清理地图资源
      if (mapInfo.map) {
        clearMapOverlay();
        disableMapClickForPoints();
        mapInfo.map.destroy();
        mapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
      }
    }
  }
);

// 组件卸载时清理资源
import { onBeforeUnmount } from "vue";

onBeforeUnmount(() => {
  // 清理所有地图资源
  disableMapClickForPoints();
  clearMapOverlay();

  // 销毁地图实例
  if (mapInfo.map) {
    mapInfo.map.destroy();
  }

  // 重置地图信息
  mapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
});
</script>

<style lang="scss">
.route-template-hover-popover {
  height: 28px;
  padding: 0 4px;
  line-height: 28px;
}
</style>
<style lang="scss" scoped>
.route-template {
  display: flex;
  @include wh(100%);
  &-left {
    flex: 1;
    height: 100%;
  }
  &-right {
    display: flex;
    flex-direction: column;
    flex-basis: 550px;
    padding: 15px 15px 65px 15px;
    height: 100%;
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.4));
    .add-bar {
      @include ct-f(y) {
        justify-content: space-between;
      }
      @include wh(100%, 52px);
      &-left {
        display: flex;
        flex-direction: column;
        font-weight: 500;
        &-top {
          @include sc(16px, #383838);
        }
        &-bottom {
          @include sc(12px, #9f9fa4);
        }
      }
    }
    // 编辑模板
    .template-item-edit {
      padding: 15px;
      margin-top: 10px;
      background: #fff;
      border-radius: 4px;
      .template-header {
        @include sc(14px, #555555);
        margin-bottom: 20px;
        display: flex;
      }
      .template-version {
        @include ct-f(y);
        span {
          width: 40px;
        }
      }

      .template-name {
        @include ct-f(y);
        span {
          width: 80px;
          &.required::before {
            content: "*";
            color: red;
            margin-right: 5px;
          }
        }
      }
      .template-title {
        display: flex;
        justify-content: space-between;
        .left {
          @include sc(14px, #383838) {
            font-weight: bold;
          }
        }
        .right {
          cursor: pointer;
          span {
            margin-left: 5px;
            @include sc(12px, #5964fb);
          }
        }
      }
      .template-content {
        padding: 15px 0;
      }
      .template-footer {
        @include ct-f;
      }
      transition: all 0.3s;
    }
    // 预览模板
    .template-item-view {
      position: relative;
      padding: 15px;
      margin-top: 10px;
      background: #fff;
      border-radius: 4px;
      .template-tag {
        position: absolute;
        top: 0;
        left: 0;
        width: 35px;
        height: 28px;
        border-radius: 4px 0 0 0;
        clip-path: polygon(0 0, 100% 0, 0 100%, 0 0);
        font-size: 12px;
        color: #fff;
        padding-left: 1px;

        &.is-green {
          background-color: rgba(39, 212, 161, 0.8);
        }

        &.is-blue {
          background-color: rgba(89, 100, 251, 0.7);
        }
      }
      .template-title {
        @include fj;
        &-left {
          margin-left: 5px;
          @include sc(14px, #383838) {
            font-weight: 500;
          }
        }
        &-right {
          @include ct-f;
          .view,
          .del,
          .edit {
            @include ct-f;
            width: 25px;
            cursor: pointer;
          }
        }
      }
      .template-content {
        .timeline {
          .line::before {
            @include wh(1px, 100%) {
              content: "";
              position: absolute;
              top: 19px;
              left: 10px;
              border-right: 2px solid #f3f3f3;
            }
          }
          &-item {
            position: relative;
            padding-left: 25px;
            &::after {
              @include wh(4px) {
                content: "";
                position: absolute;
                left: 8px;
                top: 20px;
                border: 3px solid rgb(89, 100, 251);
                background-color: #fff;
                border-radius: 6px;
              }
            }
            &-title {
              @include ct-f(y);
              padding-top: 15px;
              &-name {
                @include sc(14px, #555) {
                  padding: 0 10px 0 5px;
                }
              }
            }
            &-content {
              position: relative;
              padding-left: 5px;
              width: 470px;
              line-height: 25px;
              @include sc(12px, #9f9fa4) {
                font-weight: 500;
              }
            }
          }
        }
      }
      .template-content-transition {
        overflow: hidden;
        &-enter {
          &-from {
            height: 0 !important;
          }
          &-active {
            transition: all 0.3s ease;
          }
        }
        &-leave {
          &-active {
            transition: all 0.3s ease;
          }
          &-to {
            height: 0 !important;
          }
        }
        .structure-ponit-wrapper {
          border-radius: 4px;
          background: #f4f7fe;
          padding: 10px;

          &-text {
            @include sc(12px, #294478);
          }
          &-list {
            display: flex;
            flex-direction: column;
            margin-top: 10px;
            background-color: #fff;
            &-item {
              display: flex;
              justify-content: space-between;
              align-items: center;
              padding: 10px;
              border-radius: 4px;
              background: #fff;
              cursor: pointer;
              &:hover {
                background: rgba(237, 242, 255, 1);
              }
              &-left {
                display: flex;
                align-items: center;
                width: 100%;
                justify-content: space-around;
              }
            }
          }
        }
      }
      &:active {
        cursor: move;
        box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2);
      }
    }
  }
}
</style>
