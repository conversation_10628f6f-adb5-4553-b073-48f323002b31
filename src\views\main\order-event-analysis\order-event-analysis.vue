<template>
  <section class="orderEvent">
    <div id="container"></div>
    <div class="seachForm">
      <div class="seachForm-item">
        <label class="label">类型</label>
        <el-select
          v-model="formModel.type"
          placeholder="请选择类型"
          clearable
          filterable
          multiple
          collapse-tags
          style="width: 240px"
          @change="load"
        >
          <el-option
            v-for="item in typeOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="seachForm-item">
        <label class="label">所属社区</label>
        <el-select
          v-model="formModel.community"
          placeholder="请选择所属社区"
          clearable
          filterable
          multiple
          collapse-tags
          style="width: 240px"
          @change="load"
        >
          <el-option
            v-for="item in communityOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <div class="seachForm-item">
        <label class="label">热力图</label>
        <div class="btn-wrapper">
          <div
            :class="['btn-item', { active: !formModel.isHot }]"
            @click="handelHot"
          >
            开启
          </div>
          <div
            :class="['btn-item', { active: formModel.isHot }]"
            @click="handelHot"
          >
            关闭
          </div>
        </div>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import SiteMarker from "@/views/main/components/siteMarker.vue";
import xIcon from "@/components/x-icon.vue";
import { ref, reactive, toRefs, onMounted, createVNode, render } from "vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
import { getGarbageList } from "@/services/api";
import { ElMessage } from "element-plus";
const formModel = ref({
  type: [],
  community: [],
  isHot: false,
});

const typeOptions = ref<any>([]);
const communityOptions = ref<any>([]);
const points = ref([]);
const mapLoaded = ref(false);
const mapInfo = {
  map: null as unknown as AMap.Map,
  Amap: null as unknown as typeof AMap,
  heatmap: null as unknown as any,
  overlays: [] as any[],
};
const heatmapData = ref<any>([]);
const originData = ref<any>([]);
lazyAMapApiLoader().then((AMap) => {
  mapInfo.map = new AMap.Map("container", {
    zooms: [2, 26], // 地图缩放级别范围
    mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
  });
  mapInfo.Amap = AMap;
  mapLoaded.value = true;

  const scale = new AMap.Scale({
    position: {
      left: "300px",
      bottom: "400px",
    },
  });
  mapInfo.map.addControl(scale);
});

const runderHeatMap = () => {
  mapInfo.heatmap = new AMap.HeatMap(mapInfo.map, {
    radius: 25, //给定半径
    opacity: [0, 0.8],
    /*,
            gradient:{
                0.5: 'blue',
                0.65: 'rgb(117,211,248)',
                0.7: 'rgb(0, 255, 0)',
                0.9: '#ffea00',
                1.0: 'red'
            }
             */
  });
};

const drawPoint = () => {
  if (!mapInfo.Amap || !mapInfo.map) {
    console.warn("地图实例未初始化");
    return;
  }

  const MARKER_CONFIG = {
    zIndex: 30,
    cursor: "pointer",
    bubble: true,
    anchor: "bottom-center" as const,
  };

  const markers = originData.value.map((item: any) => {
    const position = [item.lng, item.lat];
    const markerDiv = document.createElement("div");

    const marker = new mapInfo.Amap.Marker({
      content: markerDiv,
      position: position as [number, number],
      title: item.overlayName,
      extData: item,
      ...MARKER_CONFIG,
    });

    try {
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: "map_ployline_end_enable",
        }),
        markerDiv
      );
    } catch (error) {
      console.error("渲染点位失败:", error);
    }

    return marker;
  });
  console.log(markers.length,'markers.length');
  
  if (markers.length > 0) {
    mapInfo.overlays.push(...markers);
    mapInfo.map.add(markers);
  }
};

const handelHot = () => {
  if (!isSupportCanvas()) {
    return ElMessage.warning("热力图仅对支持canvas的浏览器适用,您所使用的浏览器不能使用热力图功能,请换个浏览器试试~");
  }
  formModel.value.isHot = !formModel.value.isHot;
  if (formModel.value.isHot) {
    mapInfo.heatmap.show();
  } else {
    mapInfo.heatmap.hide();
  }
};

const load = async () => {
  try {
    const data = await getGarbageList({
      type: formModel.type,
      community: formModel.community,
    });
    originData.value = data || [];
    typeOptions.value = [...new Set(data.map((item) => item.type))].map((item) => ({ label: item, value: item }));
    communityOptions.value = [...new Set(data.map((item) => item.community))].map((item) => ({
      label: item,
      value: item,
    }));

    heatmapData.value = data.map((item) => ({
      lat: item.lat,
      lng: item.lng,
      count: 1,
    }));
    mapInfo.heatmap.setDataSet({
      data: heatmapData,
      max: 100,
    });
    drawPoint();
    console.log(data, "data");
  } catch (err) {
    console.log(err);
  }
};
const isSupportCanvas = () => {
  let elem = document.createElement("canvas");
  return !!(elem.getContext && elem.getContext("2d"));
};
onMounted(async () => {
  await load();
  await runderHeatMap();
});
</script>
<style scoped lang="scss">
.orderEvent {
  #container {
    @include wh(100%);
  }
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fff;
  position: relative;
  .seachForm {
    position: absolute;
    top: 20px;
    left: 20px;
    // width: 300px;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
    background: rgba(255, 255, 255, 1);
    z-index: 2;
    display: flex;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    padding: 11px;
    &-item {
      display: flex;
      align-items: center;
      gap: 10px;
      .label {
        width: 80px;
      }
      .btn-wrapper {
        width: 240px;
        display: flex;
        align-items: center;
        gap: 20px;
        .btn-item {
          width: 78px;
          height: 28px;
          border-radius: 4px;
          color: #9f9fa4;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          &.active {
            font-weight: 600;
            color: #fff;
            background-color: #5964fb;
          }
        }
      }
    }
  }
}
</style>
