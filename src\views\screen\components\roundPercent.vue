<template>
  <svg
    class="round-percent"
    :width="wh"
    :height="wh"
    viewBox="0 0 58 58"
    fill="none"
  >
    <g>
      <g>
        <!-- 基础圆环 -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          r="27"
          fill="none"
          stroke-width="4"
          stroke="rgba(232, 232, 232, 0.6)"
        />
      </g>
      <g>
        <!-- 进度条 -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          r="27"
          fill="none"
          stroke-width="4"
          stroke="rgb(109, 134, 250)"
          stroke-linecap="round"
          :stroke-dasharray="`${strokeDasharray.outRound} ${strokeDasharray.baseRound}`"
          transform="rotate(-90 29 29)"
        />
      </g>
    </g>
  </svg>
</template>

<script lang="ts" setup>
import { computed } from "vue";

const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  start: {
    type: Number,
    default: 0,
  },
  end: {
    type: Number,
    default: 100,
  },
});

const wh = 58;
const perimeter = Math.PI * 2 * 27;

const percentInRange = computed(() => {
  return (
    Math.min(Math.max(props.percent, props.start), props.end) - props.start
  );
});

const strokeDasharray = computed(() => {
  const range = props.end - props.start;
  const scale =
    percentInRange.value / range > 1 ? 1 : percentInRange.value / range;
  return {
    baseRound: perimeter,
    outRound: scale * perimeter,
  };
});
</script>

<style lang="scss" scoped>
.round-percent {
  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
