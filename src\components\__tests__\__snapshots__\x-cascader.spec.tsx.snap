// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 内部快照 1`] = `
<div
  class="x-cascader-content"
  data-v-83ec032c=""
  style="max-width: 0px;"
>
  <ul
    class="menu"
    data-v-83ec032c=""
    style="width: 0px;"
  >
    
    <li
      class="menu-item"
      data-v-83ec032c=""
    >
      <span
        class="menu-item-label"
        data-v-83ec032c=""
      >
        安徽省
      </span>
      <span
        class="menu-item-arrow"
        data-v-83ec032c=""
      />
    </li>
    <li
      class="menu-item"
      data-v-83ec032c=""
    >
      <span
        class="menu-item-label"
        data-v-83ec032c=""
      >
        澳门特别行政区
      </span>
      <span
        class="menu-item-arrow"
        data-v-83ec032c=""
      />
    </li>
    
  </ul>
  <!--v-if-->
  <!--v-if-->
</div>
`;

exports[`Snapshot > 正确渲染 外部快照 1`] = `
"<section class="x-cascader" data-v-83ec032c="">
  <section class="x-popover" style="width: 100%;" data-v-83ec032c="">
    <div class="x-cascader-selector" data-v-83ec032c=""><span data-v-83ec032c="">请选择</span><span class="selector-arrow" data-v-83ec032c=""></span></div>
    <!--teleport start-->
    <!--teleport end-->
  </section>
</section>"
`;
