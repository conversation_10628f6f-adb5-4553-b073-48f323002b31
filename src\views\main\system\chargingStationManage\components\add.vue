<template>
  <x-drawer
    title="新增配件"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    :btnOption="{ position: 'center' }"
    width="672px"
  >
    <div
      ref="contentRef"
      class="content"
    >
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
      >
        <x-form-item
          :label="$t('serialNo')"
          name="serialNumber"
        >
          <x-input
            v-model:value="form.serialNumber"
            :placeholder="$t('PEnter')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('entName')"
          name="entId"
        >
          <x-tree-select
            v-model:value="form.entId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PEnterEnt')"
            showSearch
          />
        </x-form-item>
        <x-form-item
          label="配件类型"
          name="accessoriesType"
        >
          <x-select
            v-model:value="form.accessoriesType"
            :options="formOptions.accessoriesType"
            :popupContainer="contentRef"
            placeholder="配件类型"
            allowClear
          />
        </x-form-item>
        <x-form-item
          :label="$t('chargingPileNo')"
          name="chargingStationNo"
        >
          <x-input
            v-model:value="form.chargingStationNo"
            :placeholder="$t('PEnter')"
            :maxlength="30"
          />
        </x-form-item>
        <x-form-item
          v-if="[3, 2].includes(form.accessoriesType)"
          :label="$t('ratedPower')"
          name="ratedPower"
        >
          <x-input
            v-model:value="form.ratedPower"
            :placeholder="$t('PEnter')"
            :maxlength="10"
            @input="handleInput"
            @blur="handleBlur"
            class="input-number"
          />
          <span>KW</span>
        </x-form-item>
        <x-form-item
          v-if="[3].includes(form.accessoriesType)"
          :label="$t('type')"
          name="type"
        >
          <div class="checkbox-container">
            <div
              v-for="(item, i) in formOptions.chargingType"
              :key="i"
              class="checkbox"
            >
              <x-checkbox
                :text="item.label"
                :checked="form.type?.includes(item.value)"
                @update:checked="changeChargingType($event, item.value)"
              />
            </div>
          </div>
        </x-form-item>
        <x-form-item
          v-if="[3, 2].includes(form.accessoriesType)"
          :label="$t('model')"
          name="chargingStationType"
        >
          <x-select
            v-model:value="form.chargingStationType"
            :options="formOptions.chargingStationType"
            :popupContainer="contentRef"
            :placeholder="$t('PSelect')"
            allowClear
          />
        </x-form-item>
        <x-form-item
          :label="$t('supplier')"
          name="supplier"
        >
          <x-select
            v-model:value="form.supplier"
            :options="formOptions.supplierType"
            :popupContainer="contentRef"
            :placeholder="$t('PSelect')"
            allowClear
          />
        </x-form-item>
        <x-form-item
          :label="$t('notes')"
          name="notes"
        >
          <x-textarea
            v-model:value="form.notes"
            :placeholder="$t('PEnter')"
            :auto-size="{ minRows: 6, maxRows: 6 }"
            :maxlength="200"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import { compTree, saveChargingStationInfo } from "@/services/api";
import { supplierType, chargingStationType, chargingType, accessoriesType } from "@/assets/ts/config";
import { isChecked } from "@/assets/ts/validate";
import { resTreeToXTree, i18nSimpleKey } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTextarea from "@/components/x-textarea.vue";
import Message from "@/components/x-message";
import xTreeSelect from "@/components/x-tree-select.vue";
import xCheckbox from "@/components/x-checkbox.vue";
const $t = i18nSimpleKey("chargingStationManage");

/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    formRef.value && formRef.value.resetFields();
  }
};

/**
 * 表单项
 */
const form = reactive({
  chargingStationNo: "",
  entId: "",
  serialNumber: "",
  ratedPower: "",
  type: [],
  chargingStationType: "",
  supplier: "",
  notes: "",
  accessoriesType: "",
});

/**
 * 表单校验规则
 */
const formRules = reactive({
  serialNumber: [["required", $t("PEnterSerialNo")]],
  accessoriesType: [["required", "请选择配件类型"]],
  entId: [["required", $t("PEnterEnt")]],
  get type() {
    return [3].includes(form.accessoriesType)
      ? [
          ["required", $t("PSelectTaskType")],
          [isChecked, $t("PSelectType")],
        ]
      : [];
  },
});

// 额定功率限制输入数字 最多两位小数
const handleInput = () => {
  const numericValue = form.ratedPower.replace(/[^0-9.]/g, "").replace(/(\..*?)\..*/g, "$1");
  form.ratedPower = numericValue;
  if (!numericValue.includes(".")) {
    return;
  }
  const parts = numericValue.split(".");
  if (parts[1].length > 2) {
    form.ratedPower = `${parts[0]}.${parts[1].slice(0, 2)}`;
  }
};

const handleBlur = () => {
  let numericValue = parseFloat(form.ratedPower);
  if (isNaN(numericValue)) {
    form.ratedPower = "";
    return;
  }
  form.ratedPower = numericValue.toFixed(2);
};

/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    await saveChargingStationInfo(form);
    emits("update:show", false);
    Message("success", $t("addSuccess"));
    emits("confirm");
    formRef.value.resetFields();
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  company: [] as TreeItemType[],
  supplierType: supplierType,
  chargingStationType: chargingStationType,
  chargingType: chargingType,
  accessoriesType: accessoriesType,
});

const changeChargingType = (checked: boolean, value: any) => {
  const typeList = new Set(form.type);
  if (checked) {
    typeList.add(value);
  } else {
    typeList.delete(value);
  }
  form.type = Array.from(typeList);
};

/**
 * 初始化数据
 */
(async () => {
  formOptions.company = reactive(resTreeToXTree([await compTree()]));
})();
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
  .input-number {
    display: inline-block;
    margin-right: 5px;
    width: 104px;
  }
  .checkbox-container {
    @include ct-f(y) {
      flex-wrap: wrap;
    }
    .checkbox {
      margin: 5px 20px 10px 0;
      :deep(.x-checkbox-text) {
        @include sc(14px, #555);
        width: 50px;
      }
    }
  }
}
</style>
