<template>
  <section class="company-data">
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 0,
            },
          })
        "
      >
        <span>前海梦工厂</span>
      </div>
    </div>
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 1,
            },
          })
        "
      >
        <span>坪山聚龙中路</span>
      </div>
    </div>
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 2,
            },
          })
        "
      >
        <span>光明区光明大街</span>
      </div>
    </div>
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 3,
            },
          })
        "
      >
        <span>盐田明珠大道</span>
      </div>
    </div>
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 4,
            },
          })
        "
      >
        <span>大鹏葵涌</span>
      </div>
    </div>
    <div class="company-data-button-out">
      <div
        class="company-data-button-out-in"
        @click="
          router.push({
            name: 'projectData',
            query: {
              id: 5,
            },
          })
        "
      >
        <span>福田东海城市广场</span>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { useRouter } from "vue-router";
const router = useRouter();
</script>

<style lang="scss" scoped>
.company-data {
  position: relative;
  @include wh(100%);
  @include bis("./运营看板.jpg");
  &-button-out {
    cursor: pointer;
    position: absolute;
    &:nth-child(1) {
      top: 43.2%;
      left: 4.8%;
      min-width: 6%;
    }
    &:nth-child(2) {
      top: 43.2%;
      left: 11.8%;
      min-width: 7.5%;
    }
    &:nth-child(3) {
      top: 43.2%;
      left: 19.7%;
      min-width: 7%;
    }
    &:nth-child(4) {
      top: 47.6%;
      left: 4.8%;
      min-width: 6%;
    }
    &:nth-child(5) {
      top: 47.6%;
      left: 11.8%;
      min-width: 7.5%;
    }
    &:nth-child(6) {
      top: 47.6%;
      left: 19.7%;
      min-width: 7%;
    }
    height: 2.1%;
    &-in {
      display: none;
      justify-content: center;
      align-items: center;
      @include wh(100%) {
        background-color: rgba(58, 61, 164, 1);
        border-radius: 2px;
      }
      @include sc(12px, #fff) {
        font-weight: 100;
        white-space: nowrap;
        zoom: 0.83;
      }
    }
    &:hover {
      .company-data-button-out-in {
        display: flex;
      }
    }
  }
}
</style>
