<template>
  <section class="site-marker">
    <div
      v-if="props.focus"
      class="site-marker-radar"
    >
      <div :class="`radar-focus-ripple ${props.icon}`"></div>
      <div :class="`radar-focus-ripple ${props.icon}`"></div>
    </div>
    <div
      :class="['site-marker-icon', props.focus ? '' : props.icon === 'map_ployline_end_disable' ? 'blur-line' : 'blur']"
    >
      <x-icon
        :name="props.icon"
        :width="props.focus ? '40px' : '28px'"
        :height="props.focus ? '40px' : '28px'"
      />
      <div
        v-if="props.number"
        :class="['site-marker-number', { nofocus: !props.focus }]"
      >
        {{ fixZeroToStr(props.number) }}
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import xIcon from "@/components/x-icon.vue";
import { fixZeroToStr } from "@/assets/ts/dateTime.ts";
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  focus: {
    type: Boolean,
    default: () => false,
  },
  number: {
    type: Number,
    default: () => 0,
  },
});
</script>

<style lang="scss" scoped>
.site-marker {
  position: relative;
  &-radar {
    @include ct-p(x) {
      bottom: -14px;
    }
    @include wh(100%) {
      border-radius: 50%;
    }
    .radar-focus-ripple {
      @include ct-p;
      @include wh(20px) {
        border-radius: 50%;
      }
      &.map_ployline_end_enable {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
      }
      &.map_garbage {
        background-color: rgba(39, 212, 161, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(39, 212, 161, 0.2);
      }
      &.map_battery {
        background-color: rgba(116, 34, 254, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(116, 34, 254, 0.2);
      }
      &.map_water {
        background-color: rgba(77, 174, 255, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(77, 174, 255, 0.2);
      }
      &.map_park {
        background-color: rgba(249, 133, 46, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(249, 133, 46, 0.2);
      }
      animation: focusRipple 2.4s linear infinite;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1.2s;
      }
    }
    @keyframes focusRipple {
      80% {
        width: 60px;
        height: 60px;
        opacity: 0.3;
      }
      100% {
        width: 40px;
        height: 40px;
        opacity: 0.3;
      }
    }
  }
  &-icon {
    position: relative;
    transition: 0.3s padding linear;
    &.blur {
      padding: 12px 0 0 7px;
    }
    &.blur-line {
      padding: 18px 0 0 6px;
    }
  }
  &-number {
    position: absolute;
    transition: 0.3s all linear;
    left: 50%;
    top: 8px;
    transform: translate(-50%);
    @include sc(12px, #fff);
    &.nofocus {
      left: 13px;
      top: 20px;
      transform: none;
    }
  }
}
</style>
