<script lang="ts" setup>
import xPopover from "@/components/x-popover.vue";
import TimePanel from "./time-panel.vue";
import { reactive, ref, watch } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    value?: string;
    format?: string;
    placeholder?: string;
    popupContainer?: HTMLElement;
    hideDisabledOptions?: boolean;
    disabledTime?: Function;
    hourStep?: number;
    minuteStep?: number;
    secondStep?: number;
    allowClear?: boolean;
  }>(),
  {
    value: "",
    format: "HH:mm:ss",
    placeholder: "请选择时间",
    allowClear: true,
    hideDisabledOptions: false,
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value"]);

/**
 * 引用
 */
const keepRef = ref();
const triggerRef = ref();
const inputRef = ref();

/**
 * 动态值
 */
const data = reactive({
  popoverVisible: false,
  tempValue: props.value,
});

/**
 * 气泡层隐藏
 */
const popoverVisibleChange = (visible: boolean) => {
  if (visible === false) {
    // 恢复临时值
    data.tempValue = props.value;
  }
};
/**
 * 触发器被点击
 */
const onClickTrigger = () => {
  // 显示气泡层
  if (data.popoverVisible === false) {
    data.popoverVisible = true;
  }

  // 自动聚焦
  if (document.activeElement !== inputRef.value) {
    inputRef.value.focus();
  }
};
/**
 * 保持层被点击
 */
const onClickKeep = () => {
  // 如果气泡层显示，执行触发器被点击
  data.popoverVisible && triggerRef.value.click();
};
/**
 * 更新临时的值
 */
const updateTempValue = (time: string) => {
  // 保持层被点击
  onClickKeep();
  // 更新临时值
  data.tempValue = time;
};
/**
 * 确定
 */
const onClickConfirm = () => {
  // 更新表单值
  emits("update:value", data.tempValue);

  // 隐藏气泡层
  if (data.popoverVisible === true) {
    data.popoverVisible = false;
  }
};
/**
 * 清除
 */
const onClickClear = () => {
  // 更新表单值
  emits("update:value", "");
  // 重置临时值
  data.tempValue = "";
};

watch(
  () => props.value,
  (newV) => (data.tempValue = newV)
);
</script>

<template>
  <x-popover
    trigger="focus"
    placement="leftBottom"
    contentType="none"
    v-model:visible="data.popoverVisible"
    :container="props.popupContainer"
    :triangle="false"
    :allowKeepShowElements="[keepRef]"
    @visibleChange="popoverVisibleChange"
  >
    <div
      class="x-time-picker-trigger"
      :class="[{ 'x-time-picker-trigger_state-actived': data.popoverVisible }]"
      ref="triggerRef"
      @click="onClickTrigger"
    >
      <div class="x-time-picker-trigger__item">
        <input
          class="x-time-picker-trigger__input"
          type="text"
          ref="inputRef"
          readonly
          :placeholder="props.placeholder"
          :value="data.tempValue"
        />
      </div>
      <div class="x-time-picker-trigger__suffix">
        <x-icon
          name="time_picker"
          width="12"
          height="12"
        />
      </div>
      <div
        class="x-time-picker-trigger__clear"
        v-if="props.allowClear"
        @click="onClickClear"
      >
        <x-icon
          name="input_clear"
          width="12"
          height="12"
        />
      </div>
    </div>
    <template #content>
      <div
        class="popover"
        ref="keepRef"
        @click="onClickKeep"
      >
        <div class="popover__body">
          <TimePanel
            :title="$t('selectTime')"
            :value="data.tempValue"
            :format="props.format"
            :disabledTime="props.disabledTime"
            :hideDisabledOptions="props.hideDisabledOptions"
            :hourStep="props.hourStep"
            :minuteStep="props.minuteStep"
            :secondStep="props.secondStep"
            :visible="data.popoverVisible"
            @updateTempValue="updateTempValue"
          >
          </TimePanel>
        </div>
        <slot name="renderExtraFooter"></slot>
        <div class="popover__foot">
          <div
            class="button-confirm"
            @click="onClickConfirm"
          >
            {{ $t("confirm") }}
          </div>
        </div>
      </div>
    </template>
  </x-popover>
</template>

<style scoped src="./time-picker.scss"></style>
