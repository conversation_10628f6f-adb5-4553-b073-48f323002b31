<template>
  <svg
    class="semi-round-percent"
    :width="wh"
    :height="wh / 2"
    viewBox="0 0 96 48"
    fill="none"
  >
    <g>
      <g>
        <!-- 基础半圆 -->
        <circle
          :cx="wh / 2"
          :cy="2"
          r="36"
          fill="none"
          stroke-width="5"
          stroke="rgba(232, 232, 232, 0.6)"
          :stroke-dasharray="strokeDasharray.baseRound"
        />
      </g>
      <g>
        <!-- 进度条 -->
        <circle
          :cx="wh / 2"
          :cy="2"
          r="36"
          fill="none"
          stroke-width="6"
          stroke="url('#progress')"
          stroke-linecap="round"
          :stroke-dasharray="`${strokeDasharray.outRound} ${strokeDasharray.baseRound}`"
        />
      </g>
    </g>
    <defs>
      <linearGradient id="progress">
        <stop stop-color="rgb(109, 134, 250)" />
      </linearGradient>
    </defs>
  </svg>
</template>

<script lang="ts" setup>
import { computed } from "vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  // 起始范围值
  start: {
    type: Number,
    default: 0,
  },
  // 结束范围值
  end: {
    type: Number,
    default: 100,
  },
});
const wh = 96;
const perimeter = Math.PI * 2 * 36;
const percentInRange = computed(() => {
  return (
    Math.min(Math.max(props.percent, props.start), props.end) - props.start
  );
});
const strokeDasharray = computed(() => {
  const range = props.end - props.start;
  const scale =
    percentInRange.value / range > 1 ? 1 : percentInRange.value / range;
  return {
    baseRound: `${(180 / 360) * perimeter}`,
    outRound: `${scale * ((180 / 360) * perimeter)} ${perimeter}`,
  };
});
</script>

<style lang="scss" scoped>
.semi-round-percent {
  transform: rotate(180deg);
  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
