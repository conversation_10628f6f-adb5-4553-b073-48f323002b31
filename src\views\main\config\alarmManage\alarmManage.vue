<template>
  <section
    class="alarm-manage"
    ref="alarmManageRef"
  >
    <div class="alarm-manage-top">
      <div class="top-title">{{ $t("alarmManage") }}</div>
      <div class="top-button">
        <x-button
          v-if="permitList.includes('sys:warnConfig:batch')"
          :text="$t('setting')"
          type="paleGreen"
          icon="button_setting"
          @click="settingModal.show = true"
          style="margin-right: 12px"
        />
        <x-button
          v-if="permitList.includes('sys:warnConfig:save')"
          :text="$t('add')"
          type="paleBlue"
          icon="button_add"
          @click="addModal.show = true"
        />
        <Setting
          v-model:show="settingModal.show"
          @confirm="settingModal.confirm"
        />
        <Add
          v-model:show="addModal.show"
          @confirm="addModal.confirm"
        />
      </div>
    </div>
    <div class="alarm-manage-middle">
      <div class="middle-left">
        <div class="middle-left-items">
          <div class="middle-left-item">
            <div class="middle-left-item-label">{{ $t("vehicleType") }}</div>
            <x-select
              class="middle-left-item-value"
              v-model:value="searchForm.vehicleType"
              :options="formOptions.vehicleType"
              :popupContainer="alarmManageRef"
              :placeholder="$t('all')"
            />
          </div>
          <div class="middle-left-item">
            <div class="middle-left-item-label">{{ $t("alarmVersion") }}</div>
            <x-select
              class="middle-left-item-value"
              v-model:value="searchForm.warnVersion"
              :options="formOptions.warnVersion"
              :popupContainer="alarmManageRef"
              :placeholder="$t('all')"
            />
          </div>
          <div class="middle-left-item">
            <div class="middle-left-item-label">{{ $t("alarmModule") }}</div>
            <x-select
              class="middle-left-item-value_large"
              v-model:value="searchForm.warnModule"
              :options="formOptions.warnModule"
              :popupContainer="alarmManageRef"
              :placeholder="$t('all')"
            />
            <x-select
              class="middle-left-item-value_large"
              v-model:value="searchForm.warnName"
              :options="formOptions.warnName"
              :popupContainer="alarmManageRef"
              :placeholder="$t('PEnterAlarmName')"
              :maxlength="20"
              showSearch
              style="width: 200px"
            />
          </div>
          <div class="middle-left-item">
            <div class="middle-left-item-label">{{ $t("alarmLevel") }}</div>
            <x-select
              class="middle-left-item-value"
              v-model:value="searchForm.warnLevel"
              :options="formOptions.warnLevelType"
              :popupContainer="alarmManageRef"
              :placeholder="$t('all')"
            />
          </div>
          <div class="middle-left-item">
            <div class="middle-left-item-label">{{ "客户账号展示并接受" }}</div>
            <x-select
              class="middle-left-item-value"
              v-model:value="searchForm.isReceived"
              :options="formOptions.isReceivedType"
              :popupContainer="alarmManageRef"
              :placeholder="$t('all')"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:warnConfig:list')"
          :text="$t('search')"
          type="blue"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button
          @click="resetSearchForm"
          type="green"
          :text="$t('reset')"
        />
      </div>
    </div>
    <div class="alarm-manage-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 表头：等级 -->
        <template #headerCell="{ title, column }">
          <div v-if="column.key === 'warnLevel'">
            <span style="vertical-align: middle">{{ title }}</span>
            <warnLevelDefinition />
          </div>
          <span v-else>{{ title }}</span>
        </template>
        <!-- 列表项：告警名称 -->
        <template #warnName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-warnName-popover"
            :container="alarmManageRef"
          >
            {{ record.warnName }}
            <template #content>
              <div class="alarm-manage-table-warnName-hover-popover">
                {{ record.warnName }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 列表项：等级 -->
        <template #warnLevel="{ record }">
          <div
            v-for="(item, index) in levelTips.info"
            :key="index"
            class="table-level-slot"
          >
            <x-icon
              v-if="item.value === record.warnLevel"
              :name="item.icon"
              width="18"
              height="18"
            />
            <span
              style="margin-left: 10px"
              v-if="item.value === record.warnLevel"
              >{{ item.label }}</span
            >
          </div>
        </template>
        <!-- 列表项：是否接收 -->
        <template #isReceived="{ record }">
          <x-switch
            :checkedValue="1"
            :unCheckedValue="0"
            :checked="Boolean(record.isReceived)"
            @click="changeReceived(record)"
          >
            <template #checkedChildren>
              <x-icon name="switch_checked"></x-icon>
            </template>
            <template #unCheckedChildren>
              <x-icon name="switch_unChecked"></x-icon>
            </template>
          </x-switch>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:warnConfig:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:warnConfig:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:warnConfig:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editModal.show"
        :id="editModal.id"
        @confirm="editModal.confirm"
      />
      <Detail
        v-model:show="detailModal.show"
        :id="detailModal.id"
      />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch } from "vue";
import { getWarnConfigList, deleteWarnConfig, getWarnModuleAndName, changeReceiveStatus } from "@/services/api";
import { vehicleType, warnLevelType, isReceivedType, warnLevelDefType } from "@/assets/ts/config";
import type { WarnConfigListRequest } from "@/services/type";
import type { PageSizeType } from "@/components/types";
import { useMainStore } from "@/stores/main";
import xButton from "@/components/x-button.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import xSwitch from "@/components/x-switch.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xPopover from "@/components/x-popover.vue";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import Setting from "./components/setting.vue";
import warnLevelDefinition from "../../components/warnLevelDefinition.vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const alarmManageRef = ref<HTMLDivElement>();

/**
 * 告警等级气泡卡片
 */
const levelTips = reactive({
  info: warnLevelDefType,
  show: false,
});

/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  vehicleType: undefined as number | undefined,
  warnVersion: "",
  warnModule: "",
  warnName: "",
  warnLevel: undefined as number | undefined,
  isReceived: undefined as number | undefined,
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  warnVersion: [] as { label: string; value: any }[],
  warnModule: [] as { label: string; value: any }[],
  warnName: [] as { label: string; value: any }[],
  vehicleType: [{ value: "", label: $t("all") }, ...vehicleType],
  warnLevelType: [{ value: "", label: $t("all") }, ...warnLevelType],
  isReceivedType: [{ value: "", label: $t("all") }, ...isReceivedType],
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderId"),
      width: "40",
    },
    {
      key: "warnName",
      title: $t("alarmName"),
      width: "70",
      slots: "warnName",
    },
    {
      key: "warnModule",
      title: $t("alarmModule"),
      width: "70",
    },
    {
      key: "warnLevel",
      title: $t("level"),
      width: "40",
      slots: "warnLevel",
    },
    {
      key: "vehicleTypeStr",
      title: $t("vehicleType"),
      width: "40",
    },
    {
      key: "interfaceDescription",
      title: $t("interfaceDescription"),
      width: "100",
    },
    {
      key: "isReceived",
      title: "客户账号展示并接受",
      width: "100",
      slots: "isReceived",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "100",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  searchConfigList();
};

/**
 * 表格-搜索
 */
const searchConfigList = async () => {
  table.loading = true;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    warnModule: searchForm.warnModule === "all" ? "" : searchForm.warnModule,
    warnName: searchForm.warnName,
    isReceived: searchForm.isReceived,
    version: searchForm.warnVersion ? searchForm.warnVersion : "",
  } as WarnConfigListRequest;

  searchForm.vehicleType && (params.vehicleType = searchForm.vehicleType);
  searchForm.warnLevel && (params.warnLevel = searchForm.warnLevel);

  const { totalCount, list } = await getWarnConfigList(params);
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];

  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.vehicleType = undefined;
  searchForm.warnVersion = "";
  searchForm.warnModule = "all";
  searchForm.warnName = "";
  searchForm.warnLevel = undefined;
  searchForm.isReceived = undefined;
  table.pagination["current"] = 1;
  searchConfigList();
};

/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  searchConfigList();
};

/**
 * 相关操作
 */
// 设置
const settingModal = reactive({
  show: false,
  confirm: () => searchConfigList(),
});
// 新增
const addModal = reactive({
  show: false,
  confirm: () => searchConfigList(),
});
// 修改接收
const changeReceived = debounce(async (record: any) => {
  const param = {
    id: record.id,
    isReceived: record.isReceived ? 0 : 1,
  };
  await changeReceiveStatus(param);
  reSearch();
});

// 编辑
const editModal = reactive({
  show: false,
  id: "",
  confirm: () => searchConfigList(),
});
const openEdit = (id: string) => {
  editModal.id = id;
  editModal.show = true;
};
// 详情
const detailModal = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailModal.id = id;
  detailModal.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("confirmDeleteAlarmConfig"),
    confirm() {
      return deleteWarnConfig({ id: record.id }).then(() => {
        Message("success", $t("deleteAlarmConfigSuccess"));
        searchConfigList();
      });
    },
  });
};

/** 获取告警模块配置项 */
const getWarnModuleList = async () => {
  const warnModuleList = await getWarnModuleAndName({
    warnModule: "all",
    warnName: "",
    remark1: searchForm.warnVersion,
  });
  const formatModuleList =
    warnModuleList?.map((item: any) => ({
      value: item,
      label: item.replace(/\t|\n/g, ""),
    })) || [];
  formatModuleList.unshift({ value: "all", label: $t("all") });
  formOptions.warnModule = formatModuleList;
};
/** 获取告警名称配置项 */
const getWarnNameList = async () => {
  const warnModule = searchForm.warnModule;
  const warnNameList = await getWarnModuleAndName({
    warnModule: warnModule === "all" ? "" : warnModule,
    warnName: "all",
    remark1: "",
  });
  formOptions.warnName =
    warnNameList?.map((item: any) => ({
      value: item.replace(/\t|\n/g, ""),
      label: item.replace(/\t|\n/g, ""),
    })) || [];
};
// 告警模块与告警名称级联
watch(
  () => searchForm.warnModule,
  () => {
    searchForm.warnName = "";
    formOptions.warnName = [];
    getWarnNameList();
  }
);
/** 告警版本与告警模块级联 */
watch(
  () => searchForm.warnVersion,
  () => {
    searchForm.warnModule = "all";
    searchForm.warnName = "";
    formOptions.warnModule = [];
    getWarnModuleList();
  }
);

/**
 * 首次加载
 */
(() => {
  searchConfigList();
  getWarnModuleList();
  formOptions.warnVersion = [
    { label: "全部", value: "" },
    { label: "V1.0", value: "1" },
    { label: "V2.0", value: "2" },
  ];
  searchForm.warnModule = "all";
})();
</script>

<style lang="scss" scoped>
.alarm-manage-table-warnName-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
.alarm-manage {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
    .top-button {
      display: flex;
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-right {
      display: flex;
    }
    .middle-left-items {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      display: flex;
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        padding: 6px 8px 6px 0;
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
        }
      }
      &-value {
        width: 120px;
      }
      &-value_large {
        width: 150px;
        margin-left: 8px;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .table-warnName-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
    }
  }
}
</style>
