<template>
  <section class="car-work">
    <sub-title>车辆作业排名</sub-title>
    <div class="car-work-top">
      <div class="toggle-button">
        <div
          :class="['toggle-button-time', { enable: carWork.btnIndex === 0 }]"
          @click="toggleSelect(0)"
        >
          时长
        </div>
        <div
          :class="['toggle-button-area', { enable: carWork.btnIndex === 1 }]"
          @click="toggleSelect(1)"
        >
          面积
        </div>
      </div>
      <div class="toggle-select">
        <x-select
          v-model:value="carWork.selValue"
          :options="carWork.selOptions"
        />
      </div>
    </div>
    <div
      ref="carWorkRef"
      class="car-work-bottom"
      @mouseenter="stopAnimate"
      @mousemove="stopAnimate"
      @mouseleave="startAnimate"
      @wheel="carWorkWheel"
    >
      <div
        :class="['car-work-item', { top: item.rank === '01' }]"
        v-for="(item, index) in rankList"
        :key="index"
      >
        <div class="car-work-item-left">
          {{ item.rank }}
        </div>
        <div class="car-work-item-right">
          <div class="car-name">{{ item.name }}</div>
          <div class="car-work-time">{{ item.data }}</div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, watch, computed } from "vue";
import { animateTime } from "@/assets/ts/animate";
import { getTimeRange } from "@/assets/ts/dateTime";
import subTitle from "./subTitle.vue";
import xSelect from "./x-select.vue";
import { screenCarWork } from "@/services/api";
const props = withDefaults(
  defineProps<{
    proId: string;
  }>(),
  {}
);

const carWorkRef = ref<HTMLElement>();
const carWork = reactive({
  timer: null,
  btnIndex: 0,
  selValue: String(getTimeRange(0)),
  selOptions: [
    {
      value: String(getTimeRange(0)),
      label: "当天",
    },
    {
      value: String(getTimeRange(1)),
      label: "昨天",
    },
    {
      value: String(getTimeRange(0, 6)),
      label: "近7天",
    },
    {
      value: String(getTimeRange(0, 29)),
      label: "近30天",
    },
  ],
  rankTimeList: [],
  rankAreaList: [],
  cancelAnimate: null,
  maxScrollTop: 0,
  scrollTop: 0,
});
const animateNum = 4; // 大于等于4个才能实现无限滚动
const rankList = computed(() => {
  const reqList =
    carWork.btnIndex === 0 ? carWork.rankTimeList : carWork.rankAreaList;
  return reqList.length >= animateNum
    ? [...reqList, ...reqList, ...reqList]
    : reqList;
});

onMounted(() => {
  // 切换项目
  watch(
    () => props.proId,
    (newV) => {
      if (carWork.selValue === carWork.selOptions[0].value) {
        updateData(newV, ...carWork.selValue.split(","));
      } else {
        carWork.selValue = carWork.selOptions[0].value;
      }
    }
  );
  // 切换日期
  watch(
    () => carWork.selValue,
    (newV) => {
      updateData(props.proId, ...newV.split(","));
    }
  );
  // 10分钟更新一次
  carWork.timer = setInterval(() => {
    updateData(props.proId, ...carWork.selOptions[0].value.split(","));
  }, 10 * 60 * 1000);
});

onUnmounted(() => {
  clearInterval(carWork.timer);
});

const updateData = (proId: string, workStart: string, workEnd: string) => {
  screenCarWork({
    proId,
    workStart,
    workEnd,
  }).then((res) => {
    carWork.rankTimeList = res.cleanTimeList.map(
      ({ name, statisticalData }, index) => ({
        rank: (index + 1).toString().padStart(2, "0"),
        data: statisticalData + "h",
        name,
      })
    );
    carWork.rankAreaList = res.cleanAreaList.map(
      ({ name, statisticalData }, index) => ({
        rank: (index + 1).toString().padStart(2, "0"),
        data: statisticalData + "㎡",
        name,
      })
    );
    updateDataAnimate();
  });
  // carWork.rankTimeList = new Array(Math.ceil(Math.random() * 10)).fill({
  //   name: "测试",
  //   data: "111111",
  //   rank: "01",
  // });

  // carWork.rankAreaList = new Array(Math.ceil(Math.random() * 10)).fill({
  //   name: "测试",
  //   data: "111111",
  //   rank: "01",
  // });
};

const toggleSelect = (selIndex: number) => {
  carWork.btnIndex = selIndex;
  updateDataAnimate();
};

const animate = (scroll?: number) => {
  carWork.scrollTop += scroll || 0.5;
  if (carWork.scrollTop > carWork.maxScrollTop || carWork.scrollTop < 0) {
    carWork.scrollTop = carWork.maxScrollTop / 2;
  }
  carWorkRef.value.scrollTop = carWork.scrollTop;
};
const updateScroll = () => {
  if (rankList.value.length >= animateNum) {
    carWork.maxScrollTop =
      (carWorkRef.value.clientHeight / animateNum) *
      (rankList.value.length - animateNum);
    carWork.scrollTop =
      (carWorkRef.value.clientHeight / animateNum) *
      (rankList.value.length / 3);
    carWorkRef.value.scrollTop = carWork.scrollTop;
  }
};
const startAnimate = () => {
  if (rankList.value.length >= animateNum) {
    carWork.cancelAnimate = animateTime(animate);
  }
};

// 由于当选择日期后鼠标会自动进入，导致没有触发mouseenter，所以也把mousemove绑定了
const stopAnimate = () => {
  carWork.cancelAnimate && carWork.cancelAnimate();
};
const updateDataAnimate = () => {
  stopAnimate();
  updateScroll();
  startAnimate();
};
const carWorkWheel = (e: Event) => {
  const wheelIsUp = e.deltaY < 0;
  wheelIsUp ? animate(-10) : animate(10);
};
</script>

<style lang="scss" scoped>
.car-work {
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);

  .car-work-top {
    @include fj;
    @include wh(100%, 32px) {
      padding: 0 22px;
    }
  }
  .car-work-bottom {
    // flex: 1;
    height: 18.6vh;
    width: 100%;
    padding: 0 22px;
    margin-top: 1.2vh;
    overflow-y: hidden;
    .car-work-item {
      @include fj;
      @include wh(100%, 23%) {
        margin-top: 0.4vh;
      }
      &-left {
        @include ct-f;
        @include wh(40px, 100%) {
          @include sc(2vh, rgb(237, 238, 255)) {
            font-weight: 700;
          }
        }
      }
      &-right {
        @include fj {
          align-items: center;
        }
        flex: 1;
        height: 100%;
        padding: 0 26px;
        @include bis("@/assets/images/screen_car_work_normal.png");
        color: #fff;
        .car-name {
          font-size: 1.6vh;
        }
        .car-work-time {
          font-size: 1.4vh;
        }
      }
      &.top {
        .car-work-item-left {
          color: rgb(255, 182, 66);
        }
        // .car-work-item-right {
        //   background-image: url("@/assets/images/screen_car_work_top.png");
        // }
      }
    }
  }
  .toggle-button {
    display: flex;
    @include wh(116px, 100%) {
      background-color: #414352;
      border-radius: 4px;
    }
    &-time,
    &-area {
      cursor: pointer;
      flex: 1;
      height: 100%;
      border-radius: 4px;
      @include sc(1.6vh, rgb(248, 250, 255)) {
        text-align: center;
        line-height: 32px;
      }
      &.enable {
        font-weight: 700;
        background-color: #5a76fc;
      }
    }
  }
  .toggle-select {
    width: 100px;
  }
}
</style>
