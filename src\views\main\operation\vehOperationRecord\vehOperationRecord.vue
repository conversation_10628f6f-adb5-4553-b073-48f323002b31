<template>
  <section
    class="operation-record"
    ref="operationRecordRef"
  >
    <div class="operation-record-top">
      <div class="top-title">{{ $t("vehOperationRecord") }}</div>
    </div>
    <div class="operation-record-middle">
      <div class="middle-left">
        <div class="middle-left-row">
          <div
            class="middle-left-item"
            style="width: 200px"
          >
            <x-input
              v-model:value="searchForm.deviceId"
              :placeholder="$t('PEnterEntVehNo')"
              suffix="input_search"
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 200px"
          >
            <x-input
              v-model:value="searchForm.userName"
              :placeholder="$t('PEnterEntUser')"
              suffix="input_search"
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 140px"
          >
            <x-select
              v-model:value="searchForm.taskItem"
              :options="formOptions.taskItemOptions"
              :popupContainer="operationRecordRef"
              showPopSearch
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 120px"
          >
            <x-select
              v-model:value="searchForm.taskResult"
              :options="formOptions.taskResultOptions"
              :popupContainer="operationRecordRef"
            />
          </div>
        </div>
        <div class="middle-left-row">
          <div
            class="middle-left-item"
            style="width: 320px"
          >
            <DateRangePicker
              v-model:value="searchForm.opDate"
              :popupContainer="operationRecordRef"
              :placeholder="[$t('startDate'), $t('endDate')]"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:VehTaskInfo:list')"
          :text="$t('search')"
          type="blue"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button
          :text="$t('reset')"
          type="green"
          @click="resetSearchForm"
        />
      </div>
    </div>
    <div class="operation-record-bottom">
      <x-table
        :dataSource="table.dataSource"
        :cols="table.cols"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 操作结果 -->
        <template #taskResult="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.taskResult === 1 ? 'status_green' : 'status_red'"
              width="12"
              height="12"
            />
            <span>{{ record.taskResultTxt }}</span>
          </div>
        </template>
        <!-- 操作 -->
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:VehTaskInfo:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Detail
        v-model:show="recordDetail.show"
        :id="recordDetail.id"
      />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive } from "vue";
import { useMainStore } from "@/stores/main";
import type { PageSizeType } from "@/components/types";
import type { GetOperationRecordListRequest } from "@/services/type";
import { vehResultType } from "@/assets/ts/config";
import { getVehOperationType, getOperationRecordList } from "@/services/api";
import { DateRangePicker } from "@/components/x-date-picker";
import XButton from "@/components/x-button.vue";
import XInput from "@/components/x-input.vue";
import XSelect from "@/components/x-select.vue";
import XTable from "@/components/x-table.vue";
import Detail from "./components/detail.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("vehOperationRecord");

const {
  userInfo: { permitList },
} = useMainStore();

const operationRecordRef = ref<any>();

/**
 * 表单项
 */
const searchForm = reactive({
  deviceId: "",
  userName: "",
  taskItem: "",
  taskResult: "",
  opDate: ["", ""],
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  taskItemOptions: [] as any,
  taskResultOptions: [
    {
      value: "",
      label: $t("allResults"),
    },
    ...vehResultType,
  ],
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "80",
    },
    {
      key: "deviceId",
      title: $t("vehicleNo"),
      width: "120",
    },
    {
      key: "taskItemTxt",
      title: $t("operationItem"),
      width: "140",
    },
    {
      key: "taskResult",
      title: $t("operationResult"),
      width: "120",
      slots: "taskResult",
    },
    {
      key: "userName",
      title: $t("operator"),
      width: "120",
    },
    {
      key: "createdTime",
      title: $t("operationTime"),
      width: "160",
    },
    {
      key: "sourceTxt",
      title: $t("operationSource"),
      width: "120",
    },
    {
      key: "taskTypeTxt",
      title: "操作类型",
      width: "120",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "120",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  searchRecordList();
};

/**
 * 表格-搜索
 */
const searchRecordList = async () => {
  table.loading = true;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceId: searchForm.deviceId,
    userName: searchForm.userName,
    taskItem: searchForm.taskItem,
    taskResult: searchForm.taskResult,
  } as GetOperationRecordListRequest;

  const [opDateStart, opDateEnd] = searchForm.opDate;
  opDateStart && (params.startTime = opDateStart);
  opDateEnd && (params.endTime = opDateEnd);

  const { totalCount, list } = await getOperationRecordList(params);
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.deviceId = "";
  searchForm.userName = "";
  searchForm.taskItem = "";
  searchForm.taskResult = "";
  searchForm.opDate = ["", ""];
  searchRecordList();
};

/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchRecordList();
};

/**
 * 相关操作
 */
// 详情
const recordDetail = reactive({
  show: false,
  id: "",
});
const openDetail = (id: number) => {
  recordDetail.id = String(id);
  recordDetail.show = true;
};

/**
 * 表格-首次加载
 */
(async () => {
  // 操作事项列表
  const vehOperationType = (await getVehOperationType()).taskItems;
  let OperationTypeList = [];
  for (const key of Object.keys(vehOperationType as any)) {
    OperationTypeList.push({
      label: vehOperationType[key],
      value: key,
    });
  }
  formOptions.taskItemOptions = [{ label: $t("allItems"), value: "" }, ...OperationTypeList];
  // 操作记录列表
  searchRecordList();
})();
</script>

<style lang="scss" scoped>
.operation-record {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px) {
      line-height: 36px;
    }
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj {
      margin-top: 20px;
    }
    @include wh(100%, auto);
    .middle-right {
      display: flex;
    }
    .middle-left-row {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        padding: 6px 8px 6px 0;
        line-height: 20px;
        @include sc(14px, #5e5e5e);
      }
      &-value {
        flex: 1;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
