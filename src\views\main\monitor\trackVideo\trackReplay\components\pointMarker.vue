<template>
  <section class="point-marker">
    <template v-if="props.type === 'start'">
      <div class="point-marker-radar">
        <div class="point-marker-radar-ripple"></div>
      </div>
      <div class="point-marker-start"></div>
    </template>
    <template v-else>
      <div class="point-marker-end"></div>
    </template>
  </section>
</template>

<script lang="ts" setup>
import type { PropType } from "vue";

const props = defineProps({
  type: {
    type: String as PropType<"start" | "end">,
    required: true,
  },
});
</script>

<style lang="scss" scoped>
.point-marker {
  position: relative;
  &-radar {
    @include wh(100%) {
      border-radius: 50%;
    }
    &-ripple {
      @include ct-p;
      @include wh(14px) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 3px 2px rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: Ripple 1s linear infinite;
    }
    @keyframes Ripple {
      to {
        @include wh(20px);
        opacity: 0.3;
      }
    }
  }
  &-start {
    @include wh(13px);
    border-radius: 50px;
    background: rgb(255, 255, 255);
    border: 3px solid rgb(89, 100, 251);
  }
  &-end {
    @include wh(13px);
    border-radius: 50px;
    background: rgb(255, 255, 255);
    border: 3px solid rgb(159, 159, 164);
  }
}
</style>
