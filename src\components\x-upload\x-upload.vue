<template>
  <section class="x-upload" :class="[{ 'x-upload_state-view': props.view }]">
    <input
      type="file"
      :multiple="props.multiple"
      :accept="props.accept"
      :capture="false"
      style="display: none"
      ref="inputRef"
      @change="selectHandler"
    />
    <div class="x-upload__mode-drag">
      <div class="x-upload-file-list" v-if="reactiveValue.fileList.length > 0">
        <template
          v-for="(item, index) in reactiveValue.fileList"
          :key="item.uid"
        >
          <div class="x-upload-file-list__item">
            <div
              :class="[
                {
                  'x-upload-file-item': true,
                  'x-upload-file-item_uploading': item.status === 'uploading',
                },
              ]"
            >
              <div class="x-upload-file-item__type">
                <x-icon
                  :name="'upload_file_' + (acceptIcon[item.type] || 'default')"
                  width="44"
                  height="44"
                />
              </div>
              <div class="x-upload-file-item__info">
                <div class="x-upload-file-item__name">
                  {{ item.name }}
                </div>
                <div class="x-upload-file-item__size" v-if="item.size">
                  {{ formatFileSize(item.size) }}
                </div>
              </div>
            </div>
            <div class="x-upload-file-action">
              <template v-if="props.view === false">
                <div
                  class="x-upload-file-action__status"
                  v-if="
                    ['done', 'success'].includes(item.status) &&
                    item.statusVisible
                  "
                >
                  <x-icon name="upload_success" width="12" height="12" />
                  {{ $t("uploadSuccess") }}
                </div>
                <div
                  class="x-upload-file-action__status"
                  v-else-if="['error'].includes(item.status)"
                >
                  <x-icon name="upload_error" width="12" height="12" />
                  {{ $t("uploadFail") }}
                </div>
                <div
                  class="x-upload-file-action__percent"
                  v-if="item.status === 'uploading' && props.autoUpload"
                >
                  <div class="x-upload-file-progress">
                    <div
                      class="x-upload-file-progress__inner"
                      :style="{ width: `${item.percent}%` }"
                    ></div>
                  </div>
                  {{ `${item.percent}%` }}
                </div>
              </template>
              <div
                v-if="props.showView"
                :class="[
                  {
                    'x-upload-file-action__preview': true,
                    'x-upload-file-action__preview_disabled': !item.url,
                  },
                ]"
                @click="previewDoc(item.url)"
              >
                {{ props.view ? $t("view") : $t("preview") }}
              </div>
              <a
                v-if="props.showUpload"
                target="_blank"
                rel="noopener noreferrer"
                :class="[
                  {
                    'x-upload-file-action__download': true,
                    'x-upload-file-action__download_disabled': !item.url,
                  },
                ]"
                :href="item.url"
              >
                {{ $t("download") }}
              </a>
              <template v-if="props.view === false">
                <div
                  class="x-upload-file-action__delete"
                  @click="deleteFileItem(index)"
                >
                  {{ $t("delete") }}
                </div>
              </template>
            </div>
          </div>
        </template>
      </div>
      <template v-if="props.view === false">
        <div
          :class="[
            {
              'x-upload-drag': true,
              'x-upload-drag_hover': reactiveValue.dragHover,
            },
          ]"
          ref="dropRef"
          @click="selectFiles"
        >
          <div class="x-upload-drag__icons">
            <x-icon name="upload_word" width="16" height="16" />
            <x-icon name="upload_pdf" width="16" height="16" />
            <x-icon name="upload_excel" width="16" height="16" />
          </div>
          <div class="x-upload-drag__tips">
            {{
              reactiveValue.dragHover ? $t("releaseMouse") : $t("dragToArea")
            }}
          </div>
        </div>
      </template>
    </div>
    <template v-if="props.view === false">
      <div class="x-upload-placeholder">{{ props.placeholder }}</div>
    </template>
    <previewFile v-model:show="fileModal.show" :url="fileModal.url" />
  </section>
</template>

<script lang="tsx" setup>
import type { AxiosRequestConfig } from "axios";
import { defaultTimeout } from "@/services/http";
import { ref, reactive, onMounted, onBeforeUnmount, watch } from "vue";
import { uuid } from "@/assets/ts/utils";
import acceptType from "./accept-type";
import acceptIcon from "./accept-icon";
import axios from "axios";
import xIcon from "@/components/x-icon.vue";
import Message from "@/components/x-message";
import PreviewFile from "@/views/main/components/previewFile.vue";
import xModal from "@/components/x-modal";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

/** 文件类型 */
export type FileItem = {
  /** 文件File对象 */
  file: File;
  /** 文件唯一标识 */
  uid: string;
  /** 文件名称 */
  name: string;
  /** 文件大小 */
  size?: number;
  /** 文件类型 */
  type: keyof typeof acceptIcon;
  /** 文件名称 */
  percent: number;
  /** 文件上传状态 */
  status: "error" | "success" | "done" | "uploading" | "removed";
  /** 文件上传地址 */
  url: string;
  /** 上传状态是否可见 */
  statusVisible?: boolean;
};

/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    value: FileItem[];
    beforeUpload?: any;
    accept?: string;
    action?: string;
    data?: any;
    headers?: any;
    maxCount?: number;
    multiple?: boolean;
    placeholder?: string;
    withCredentials?: boolean;
    view?: boolean;
    showView?: boolean; // 是否支持预览
    showUpload?: boolean; // 是否支持下载
    allowTypeRepeat?: boolean; // 是否允许重复文件类型
    autoUpload?: boolean; // 是否自动上传
  }>(),
  {
    value: () => [],
    beforeUpload: () => true,
    accept: "",
    action: "",
    data: {
      name: "abc",
    },
    maxCount: 1,
    multiple: false,
    placeholder: "",
    withCredentials: false,
    view: false,
    showView: true,
    showUpload: true,
    allowTypeRepeat: true,
    autoUpload: true,
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value"]);

/**
 * 引用
 */
const dropRef = ref<HTMLDivElement>();
const inputRef = ref<HTMLInputElement>();

/**
 * 动态值
 */
const reactiveValue = reactive({
  dragHover: false,
  fileList: props.value,
});

/**
 * 事件处理
 */
const uploadFile = (fileItem: FileItem) => {
  if (!props.autoUpload) return false;
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
    },
    transformRequest: [
      function (file) {
        const form = new FormData();
        form.append("file", file);
        return form;
      },
    ],
    onUploadProgress: (progressEvent) => {
      fileItem.percent = progressEvent.total
        ? ((progressEvent.loaded / progressEvent.total) * 100) | 0
        : 0;
    },
  } as AxiosRequestConfig;

  if (props.data) {
    config.params = props.data;
  }

  if (props.headers) {
    config.headers = {
      ...config.headers,
      ...props.headers,
    };
  }

  if (props.withCredentials) {
    config.withCredentials = props.withCredentials;
  }

  config.timeout = defaultTimeout;

  axios
    .post(import.meta.env.VITE_BASE_URL + props.action, fileItem.file, config)
    .then((response) => {
      if (response.status === 200) {
        fileItem.status = "success";
        fileItem.url = response.data.result.url;
        fileItem.name = response.data.result.name;
        fileItem.statusVisible = true;
        setTimeout(() => {
          fileItem.statusVisible = false;
        }, 1000);
        emits("update:value", reactiveValue.fileList);
      }
    })
    .catch((reason) => {
      Message(
        "error",
        reason?.response?.statusText || reason?.message || $t("fileUploadFail")
      );
      // fileItem.status = "error";
      deleteErrorFileItem(fileItem);
    });

  if (!props.allowTypeRepeat) {
    const existingFile = reactiveValue.fileList.find(
      (item) => item.type === fileItem.type
    );
    if (existingFile && reactiveValue.fileList.length > 1) {
      xModal.confirm({
        title: `已存在 ${fileItem.type} 类型文件`,
        content: (
          <div style="font-size:14px;color:#383838;">
            确定替换{existingFile.name}文件
          </div>
        ),
        confirm() {
          reactiveValue.fileList.splice(
            reactiveValue.fileList.indexOf(existingFile),
            1
          );
        },
        cancel() {
          reactiveValue.fileList.splice(
            reactiveValue.fileList.indexOf(fileItem),
            1
          );
        },
      });
    }
  }
};
// 上传文件之前的钩子
const beforeUpload = () => {
  const beforeUploadType = Object.prototype.toString.call(props.beforeUpload);

  reactiveValue.fileList.forEach(async (fileItem) => {
    if (fileItem.status !== "uploading") return;

    if (beforeUploadType === "[object AsyncFunction]") {
      await props
        .beforeUpload(fileItem, reactiveValue.fileList)
        .then((res: FileItem) => {
          uploadFile(res || fileItem);
        })
        .catch(() => {
          // fileItem.status = "error";
          deleteErrorFileItem(fileItem);
        });
    } else if (beforeUploadType === "[object Function]") {
      if (props.beforeUpload(fileItem, reactiveValue.fileList) === false) {
        // fileItem.status = "error";
        deleteErrorFileItem(fileItem);
        return;
      }
      uploadFile(fileItem);
    } else {
      uploadFile(fileItem);
    }
  });

  inputRef.value!.value = "";
};
// 更新文件列表
const updateFileList = (files: File[]) => {
  const spliceLength =
    files.length + reactiveValue.fileList.length - props.maxCount;

  spliceLength > 0 && reactiveValue.fileList.splice(0, spliceLength);

  files.forEach((file) => {
    const fileItem = {
      file,
      uid: uuid(),
      name: file.name,
      size: file.size,
      type: file.type,
      percent: 0,
      status: "uploading",
      url: "",
    } as FileItem;
    // 更新列表
    reactiveValue.fileList.push(fileItem);
  });

  // 上传文件之前的钩子
  beforeUpload();
};
// 选择文件
const selectFiles = () => {
  inputRef.value?.click();
};
// 选择文件后
const selectHandler = (e: Event) => {
  const target = e.target as HTMLInputElement;

  if (target.files && target.files?.length > 0) {
    updateFileList(Array.from(target.files));
  }
};
// 拖拽放手后
const dropHandler = (e: DragEvent) => {
  if (
    e.dataTransfer &&
    e.dataTransfer.files &&
    e.dataTransfer.files.length > 0
  ) {
    // 过滤不接收的文件类型
    const files = Array.from(e.dataTransfer.files);
    const acceptedFiles = files.filter((file: File) =>
      acceptType(file, props.accept)
    );
    updateFileList(acceptedFiles);
  }

  reactiveValue.dragHover = false;
  e.stopPropagation();
  e.preventDefault();
};
const dragleaveHandler = (e: Event) => {
  reactiveValue.dragHover = false;
  e.stopPropagation();
  e.preventDefault();
};
const dragenterHandler = (e: Event) => {
  reactiveValue.dragHover = true;
  e.stopPropagation();
  e.preventDefault();
};
const dragoverHandler = (e: Event) => {
  reactiveValue.dragHover = true;
  e.stopPropagation();
  e.preventDefault();
};
const dropDocumentHandler = (e: Event) => {
  e.stopPropagation();
  e.preventDefault();
};
// 预览
const fileModal = reactive({
  show: false,
  url: "",
});
const previewDoc = (url: any) => {
  fileModal.show = true;
  fileModal.url = url || "";
};
// 删除
const deleteFileItem = (index: number) => {
  reactiveValue.fileList.splice(index, 1);
  emits("update:value", reactiveValue.fileList);
};
// 删除错误文件
const deleteErrorFileItem = (fileItem: FileItem) => {
  reactiveValue.fileList = reactiveValue.fileList.filter(
    (item) => item !== fileItem
  );
  emits("update:value", reactiveValue.fileList);
};

/**
 * 组件挂载后
 */
onMounted(() => {
  document.addEventListener("drop", dropDocumentHandler, false);
  document.addEventListener("dragleave", dropDocumentHandler, false);
  document.addEventListener("dragenter", dropDocumentHandler, false);
  document.addEventListener("dragover", dropDocumentHandler, false);
  dropRef.value?.addEventListener("drop", dropHandler, false);
  dropRef.value?.addEventListener("dragleave", dragleaveHandler, false);
  dropRef.value?.addEventListener("dragenter", dragenterHandler, false);
  dropRef.value?.addEventListener("dragover", dragoverHandler, false);

  // 回显
  watch(
    () => props.value,
    async () => {
      // 比对uid是否相同
      const valueUids = props.value.map((item) => item.uid).join("_");
      const innerUids = reactiveValue.fileList
        .map((item) => item.uid)
        .join("_");
      // 不相同时，更新文件列表
      if (valueUids !== innerUids) {
        reactiveValue.fileList = props.value;
      }
    }
  );
});

onBeforeUnmount(() => {
  document.removeEventListener("drop", dropDocumentHandler);
  document.removeEventListener("dragleave", dropDocumentHandler);
  document.removeEventListener("dragenter", dropDocumentHandler);
  document.removeEventListener("dragover", dropDocumentHandler);
  dropRef.value?.removeEventListener("drop", dropHandler);
  dropRef.value?.removeEventListener("dragleave", dragleaveHandler);
  dropRef.value?.removeEventListener("dragenter", dragenterHandler);
  dropRef.value?.removeEventListener("dragover", dragoverHandler);
});

/**
 * 计算文件大小单位
 */
const formatFileSize = function (bytes: number) {
  if (bytes === 0) return "0 Bytes";

  const k = 1024;
  const sizes = ["Bytes", "KB", "MB", "GB", "TB"];
  const i = Math.floor(Math.log(bytes) / Math.log(k));

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + " " + sizes[i];
};
</script>

<style lang="scss" scoped>
.x-upload {
  &__mode-drag {
    border: 1px dashed #e5e5e5;
    border-radius: 4px;
  }
}
.x-upload-drag {
  height: 140px;
  transition: background 0.3s;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  align-content: center;
  flex-wrap: wrap;
  color: #9f9fa4;
  cursor: pointer;

  &:hover,
  &_hover {
    background: rgba(220, 227, 251, 0.5);
    color: rgba(89, 100, 251, 0.5);
  }

  &__icons {
    flex-basis: 80px;
    display: flex;
    justify-content: space-between;
  }

  &__tips {
    flex-basis: 100%;
    text-align: center;
    font-size: 14px;
    margin-top: 10px;
  }
}
.x-upload-placeholder {
  color: #9f9fa4;
  font-size: 12px;
  line-height: 1.5;
  text-align: center;
  margin-top: 4px;
}
.x-upload-file-list {
  margin: 0 12px;
  padding: 10px 0;

  &__item {
    display: flex;
    align-items: center;
    &:not(:last-child) {
      margin-bottom: 10px;
    }
  }
}
.x-upload-file-item {
  flex-basis: auto;
  flex-grow: 1;
  flex-shrink: 1;
  padding: 5px 0;
  border: 1px solid rgb(229, 237, 255);
  border-radius: 4px;
  display: flex;
  align-items: center;

  &_uploading {
    background-color: rgba(16, 22, 55, 0.2);
  }

  &__type {
    padding: 0 5px;
    .x-icon {
      vertical-align: top;
    }
  }
  &__info {
    flex-basis: auto;
    flex-grow: 1;
    line-height: 22px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &__name {
    font-size: 14px;
    color: #383838;
  }
  &__size {
    font-size: 12px;
    color: #9f9fa4;
  }
}
.x-upload-file-action {
  @include wh(120px, 60px);
  margin-left: 10px;
  display: flex;
  align-items: end;
  flex-wrap: wrap;

  &__status {
    flex-basis: 100%;
    height: 36px;
    color: #5e5e5e;
    font-size: 12px;
    display: flex;
    align-items: center;
    padding-left: 8px;

    .x-icon {
      margin-right: 4px;
    }
  }
  &__percent {
    flex-basis: 100%;
    height: 36px;
    display: flex;
    align-items: center;
    color: rgba(0, 0, 0, 0.45);
    font-size: 12px;
  }
  &__download,
  &__delete,
  &__preview {
    font-size: 12px;
    line-height: 24px;
    color: #5964fb;
    margin-right: 10px;
    cursor: pointer;

    &:last-child {
      margin-right: 0;
    }

    &_disabled {
      color: #9f9fa4;
    }
  }
}
.x-upload-file-progress {
  flex-basis: auto;
  flex-grow: 1;
  height: 8px;
  border-radius: 8px;
  background-color: rgba(0, 0, 0, 0.04);
  overflow: hidden;
  margin-right: 10px;

  &__inner {
    background: #5964fb;
    height: 8px;
    transition: width 0.3s;
  }
}
.x-upload_state-view {
  .x-upload__mode-drag {
    background-color: #f4f7fe;
    border: none;
  }
}
</style>
