module.exports = {
  disableEmoji: false,
  format: '{type}{scope}: {emoji}{subject}',
  list: ['test', 'feat', 'fix', 'chore', 'docs', 'refactor', 'style', 'ci', 'perf'],
  maxMessageLength: 64,
  minMessageLength: 2,
  // questions: ['type', 'scope', 'subject', 'body', 'breaking', 'issues', 'lerna'],
  questions: ['type', 'subject'],
  scopes: [],
  types: {
    chore: {
      description: '构建过程或辅助工具的变化',
      emoji: '🤖',
      value: 'chore'
    },
    ci: {
      description: 'CI相关的变化',
      emoji: '🎡',
      value: 'ci'
    },
    docs: {
      description: '仅文档的变化',
      emoji: '✏️',
      value: 'docs'
    },
    feat: {
      description: '一个新功能',
      emoji: '🎸',
      value: 'feat'
    },
    fix: {
      description: '一个错误修复',
      emoji: '🐛',
      value: 'fix'
    },
    perf: {
      description: '一个提高性能的代码修改',
      emoji: '⚡️',
      value: 'perf'
    },
    refactor: {
      description: '一个既没有修复bug也没有增加功能的代码修改',
      emoji: '💡',
      value: 'refactor'
    },
    release: {
      description: '创建一个发布提交',
      emoji: '🏹',
      value: 'release'
    },
    style: {
      description: '标记、白空间、格式化、缺少分号...',
      emoji: '💄',
      value: 'style'
    },
    test: {
      description: '添加缺少的测试',
      emoji: '💍',
      value: 'test'
    },
    messages: {
      type: '选择类型:\n',
      customScope: '选择该组件影响的范围:\n',
      subject: '写一个简短的、命令式的情绪描述，说明变化情况:\n',
      body: '提供有关变化的较长描述:\n ',
      breaking: '列出任何破坏性的变化:\n',
      footer: '此提交关闭的问题, 例如 #123:',
      confirmCommit: '这次提交影响到的packages是\n',
    },
  }
};