<template>
  <div :class="['x-tabs', { 'x-tabs-centered': centered }]">
    <div
      v-for="(item, index) in panes"
      :key="index"
      :class="[
        'x-tabs-nav',
        { active: item.name === currentTab },
        { disabled: item.disabled || item.disabled === '' },
      ]"
      :style="tabBarStyles"
      @click="item.disabled || item.disabled === '' ? null : changeTab(index)"
    >
      {{ item.tab }}
    </div>
  </div>
  <div class="x-tabs-content">
    <slot></slot>
  </div>
</template>

<script lang="ts" setup>
import { ref, onMounted, computed, provide, useSlots } from "vue";

const props = defineProps({
  // 当前激活 tab 面板的 key
  activeKey: {
    type: [String, Number],
  },
  // 标签居中展示
  centered: {
    type: Boolean,
    default: false,
  },
  // tab bar 的样式对象
  tabBarStyle: {
    type: Object,
  },
});

const emits = defineEmits(["change"]);
const tabBarStyles = computed(() => {
  let style = "";
  if (props.tabBarStyle) {
    for (const key of Object.keys(props.tabBarStyle)) {
      style += `${key}:${props.tabBarStyle[key]};`;
    }
  }
  return style;
});

const slots = useSlots();
const panes = slots.default!().map(({ props }) => {
  if (props) {
    const { tab, name, disabled } = props;
    return {
      disabled,
      tab,
      name,
    };
  }
});

const currentTab = ref(props.activeKey);
const changeTab = (index: number) => {
  const key = panes[index].name;
  if (currentTab.value === key) return;
  currentTab.value = key;
  emits("change", currentTab.value);
};

onMounted(() => {
  const defaultTab = panes.find((child) => child?.name === currentTab.value);
  if (defaultTab) {
    currentTab.value = defaultTab.name;
  } else if (panes.length > 0) {
    currentTab.value = panes[0]?.name;
  }
});

provide("currentTab", currentTab);
</script>

<style scoped lang="scss">
.x-tabs {
  display: flex;
  background: #f4f7fe;
  &-centered {
    justify-content: center;
  }
  &-nav {
    position: relative;
    margin: 5px 10px 0px 10px;
    height: 36px;
    line-height: 36px;
    @include sc(14px, #9f9fa4);
    &:hover {
      color: #5964fb;
      cursor: pointer;
    }
    &.active {
      padding: 0 8px;
      @include sc(14px, #242859) {
        font-weight: bold;
        background: #fff;
        box-shadow: 0px -1px 3px 0px rgba(72, 111, 245, 0.14);
      }
      // 梯形两侧
      &:before,
      &:after {
        content: "";
        display: block;
        position: absolute;
        @include wh(10px, 36px);
        background: #fff;
      }
      // 梯形左侧
      &:before {
        top: 0;
        left: -5px;
        transform: skewX(-10deg);
        border-top-left-radius: 4px;
      }
      // 梯形右侧
      &:after {
        top: 0;
        right: -5px;
        transform: skewX(10deg);
        border-top-right-radius: 4px;
      }
    }
    &.disabled:hover {
      cursor: not-allowed;
    }
  }
  &-content {
    padding: 10px;
  }
}
</style>
