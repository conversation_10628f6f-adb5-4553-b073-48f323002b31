<template>
  <section :class="`project-data-${activeIndex || 0}`">
    <div
      class="project-data-button-out-1"
      @click="router.push({ name: 'carLive' })"
    ></div>
    <div
      class="project-data-button-out-2"
      @click="router.push({ name: 'carLive' })"
    ></div>
    <div
      class="project-data-button-out-3"
      @click="router.push({ name: 'carLive' })"
    ></div>
    <div
      class="project-data-button-out-4"
      @click="router.push({ name: 'carLive' })"
    ></div>
  </section>
</template>
<script lang="ts" setup>
import { ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
const router = useRouter();
const route = useRoute();
const activeIndex = ref(0);
onMounted(() => {
  if (route.query.id !== "") {
    activeIndex.value = route.query.id;
  } else {
    activeIndex.value = 0;
  }
});
</script>

<style lang="scss" scoped>
.project-data-0 {
  position: relative;
  @include wh(100%);
  @include bis("./前海梦工厂.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 27%;
    left: 53.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 37.5%;
    left: 58.6%;
    @include wh(4.4%, 8%);
  }
}
.project-data-1 {
  position: relative;
  @include wh(100%);
  @include bis("./坪山聚龙中路.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 16%;
    left: 41%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 26.5%;
    left: 44%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-3 {
    cursor: pointer;
    position: absolute;
    top: 52.5%;
    left: 53%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-4 {
    cursor: pointer;
    position: absolute;
    top: 43.5%;
    left: 48%;
    @include wh(4.4%, 8%);
  }
}
.project-data-2 {
  position: relative;
  @include wh(100%);
  @include bis("./光明区光明大街.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 47.5%;
    left: 41.8%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-3 {
    cursor: pointer;
    position: absolute;
    top: 56%;
    left: 49.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 57.5%;
    left: 56.8%;
    @include wh(4.4%, 8%);
  }
}
.project-data-3 {
  position: relative;
  @include wh(100%);
  @include bis("./盐田明珠大道.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 42%;
    left: 41%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 49%;
    left: 47.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-3 {
    cursor: pointer;
    position: absolute;
    top: 46.5%;
    left: 52.8%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-4 {
    cursor: pointer;
    position: absolute;
    top: 49.5%;
    left: 58.8%;
    @include wh(4.4%, 8%);
  }
}
.project-data-4 {
  position: relative;
  @include wh(100%);
  @include bis("./大鹏葵涌.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 36%;
    left: 40.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 24.5%;
    left: 53.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-3 {
    cursor: pointer;
    position: absolute;
    top: 37.5%;
    left: 58.8%;
    @include wh(4.4%, 8%);
  }
}
.project-data-5 {
  position: relative;
  @include wh(100%);
  @include bis("./福田东海城市广场.jpg");

  .project-data-button-out-1 {
    cursor: pointer;
    position: absolute;
    top: 16.5%;
    left: 35%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-2 {
    cursor: pointer;
    position: absolute;
    top: 16%;
    left: 45.5%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-3 {
    cursor: pointer;
    position: absolute;
    top: 48.5%;
    left: 65.2%;
    @include wh(4.4%, 8%);
  }
  .project-data-button-out-4 {
    cursor: pointer;
    position: absolute;
    top: 60.5%;
    left: 64.2%;
    @include wh(4.4%, 8%);
  }
}
</style>
