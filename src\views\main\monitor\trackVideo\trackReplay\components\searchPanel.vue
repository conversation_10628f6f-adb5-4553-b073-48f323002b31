<template>
  <section
    class="track-info"
    ref="contentRef"
  >
    <x-select
      v-model:value="form.deviceId"
      :options="formOptions.vehList"
      :placeholder="$t('PSelect') + $t('deviceId')"
      allowClear
      showPopSearch
      @update:value="changeDeviceId"
      :popupContainer="contentRef"
      style="margin-bottom: 10px"
    />
    <DatePicker
      v-model:value="form.opDate"
      format="YYYY-MM-DD"
      :spotList="formOptions.dateList"
      @changeYearMonth="changeYearMonth"
      :popupContainer="props.popupContainer"
      style="margin-bottom: 10px"
      :allowClear="false"
      showToday
    />
    <TimeRangePicker
      v-model:value="form.opTime"
      :placeholder="[$t('startTime'), $t('endTime')]"
      @update:value="updateTimeRange"
      :popupContainer="props.popupContainer"
      format="HH:mm:ss"
      :allowClear="false"
      style="margin-bottom: 10px"
    />
    <x-checkbox
      :text="$t('loadRoutePoint')"
      :checked="Boolean(form.showTrack)"
      @update:checked="form.showTrack = $event ? 1 : 0"
      style="margin-bottom: 10px"
    />
    <x-button
      :text="$t('search')"
      @click="searchForm"
      :loading="searchLoading"
      style="width: 100%"
    />
  </section>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted } from "vue";
import { DatePicker } from "@/components/x-date-picker";
import { TimeRangePicker } from "@/components/x-time-picker";
import { getTrackVehicles, getTrackDates, getTrackPositions } from "@/services/api";
import xSelect from "@/components/x-select.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import xButton from "@/components/x-button.vue";
import Message from "@/components/x-message";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { formatDateTime } from "@/assets/ts/dateTime";

const $t = i18nSimpleKey("trackReplay");

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  deviceId: {
    type: String,
    default: "",
  },
});

const contentRef = ref<HTMLDivElement>();
const searchLoading = ref<boolean>(false);

const form = reactive({
  deviceId: props.deviceId,
  opDate: formatDateTime(Date.now()),
  opTime: ["00:00:00", "23:59:59"],
  showTrack: 0,
});

const formOptions = reactive({
  vehList: [] as any[],
  dateList: [] as string[],
});

onMounted(() => {
  getVehList();
});

// 查询车辆列表
const getVehList = async () => {
  const vehList = await getTrackVehicles();
  formOptions.vehList =
    vehList?.map((item: any) => ({
      value: item.vehicleNo,
      label: item.vehicleNo,
    })) || [];
};

// 车牌号选择
const changeDeviceId = (id: string) => {
  if (id) {
    getValidateDateList();
  }
};

// 年月变更
const curYear = ref(0);
const curMonth = ref(0);
const changeYearMonth = (change: any) => {
  const { year, month } = change;
  curYear.value = year;
  curMonth.value = month + 1;
  getValidateDateList();
};

// 查询轨迹日期列表
const getValidateDateList = async () => {
  const dateList = await getTrackDates({
    deviceId: form.deviceId,
    year: curYear.value || new Date().getFullYear(),
    month: curMonth.value || new Date().getMonth() + 1,
  });
  formOptions.dateList = dateList.map((dateString: string) => dateString.split(" ")[0]);
};

const emits = defineEmits(["search"]);

const searchForm = debounce(async () => {
  const { deviceId, opDate, opTime, showTrack } = form;
  if (!deviceId) {
    Message("error", "请输入车牌");
  } else {
    searchLoading.value = true;
    const lineArr = await getTrackPositions({
      deviceId,
      startTime: `${opDate} ${opTime[0]}`,
      lastTime: `${opDate} ${opTime[1]}`,
    });
    searchLoading.value = false;
    emits("search", lineArr, showTrack);
  }
}, 500);

// 修改时间
const updateTimeRange = (value: any) => {
  const isDurationValid = isTimeDurationValid(value);
  if (!isDurationValid) {
    Message("error", $t("PSelectValidTimeRange"));
    form.opTime = ["", ""];
  }
};
// 时间转时间戳
const timeToMilliseconds = (timeStr: string) => {
  const [hours, minutes, seconds] = timeStr.split(":").map(Number);
  return hours * 3600000 + minutes * 60000 + seconds * 1000;
};
// 校验时长是否有效
const isTimeDurationValid = (opDate: string[]) => {
  const startTime = timeToMilliseconds(opDate[0]);
  const endTime = timeToMilliseconds(opDate[1]);
  return endTime - startTime >= 1000; // 1s
};
</script>

<style lang="scss" scoped>
.track-info {
  position: absolute;
  top: 0;
  left: 0;
  padding: 20px 10px;
  @include wh(240px, 230px);
  background-color: rgba(255, 255, 255, 0.9);
  box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
}
</style>
