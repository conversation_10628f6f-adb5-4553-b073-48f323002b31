<template>
  <x-drawer
    :title="$t('addCompany')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    :btnOption="{ position: 'center' }"
    bodyPadding="0"
    width="570px"
  >
    <div ref="contentRef" class="content">
      <div class="content-top">
        <div class="content-title">{{ $t("baseInfo") }}</div>
        <x-form ref="formRef" :model="form" :rules="formRules">
          <x-form-item :label="$t('entName')" name="cszgSysEnterprise.entName">
            <x-input
              v-model:value="form.cszgSysEnterprise.entName"
              :placeholder="$t('PEnterEntName')"
              :maxlength="100"
            />
          </x-form-item>
          <x-form-item
            :label="$t('parentEnt')"
            name="cszgSysEnterprise.parentEntId"
          >
            <x-tree-select
              v-model:value="form.cszgSysEnterprise.parentEntId"
              :treeData="formOptions.company"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectParentEnt')"
              showSearch
            />
          </x-form-item>
          <div class="content-contact">
            <div class="content-contact-user-name">
              <x-form-item
                :label="$t('userName')"
                name="sysUser.userName"
                :labelFlex="21"
              >
                <x-input
                  v-model:value="form.sysUser.userName"
                  :placeholder="$t('PEnterUserName')"
                  :maxlength="10"
                />
              </x-form-item>
            </div>
            <div class="content-contact-mobile">
              <x-form-item
                :label="$t('mobile')"
                name="sysUser.mobile"
                :labelFlex="17"
              >
                <x-input
                  v-model:value="form.sysUser.mobile"
                  :placeholder="$t('PEnterMobile')"
                  :maxlength="11"
                  :filter="mobileFilter"
                />
              </x-form-item>
            </div>
          </div>
          <x-form-item
            :label="$t('account')"
            name="sysUser.userAccount"
            :extra="$t('PEntCharNumberLetterAcc')"
          >
            <x-input
              v-model:value="form.sysUser.userAccount"
              :maxlength="15"
              :placeholder="$t('PEnterAccount')"
            />
          </x-form-item>
          <x-form-item
            :label="$t('loginPassword')"
            name="sysUser.password"
            :extra="$t('PEntCharNumberLetterPWD')"
          >
            <x-input
              v-model:value="form.sysUser.password"
              :maxlength="15"
              :placeholder="$t('PEnterPassword')"
            />
          </x-form-item>
          <x-form-item :label="$t('confirmationOWD')" name="sysUser.repassword">
            <x-input
              v-model:value="form.sysUser.repassword"
              :maxlength="15"
              :placeholder="$t('PEnterConfirmPWD')"
            />
          </x-form-item>
          <div class="content-area">
            <div class="content-area__label">{{ $t("entAddress") }}</div>
            <div class="content-area__value">
              <x-form-item
                label=""
                name="cszgSysEnterprise.provinceCityDistrict"
                wrapperStyle="width:100%;"
              >
                <x-cascader
                  v-model:value="form.cszgSysEnterprise.provinceCityDistrict"
                  :options="formOptions.areas"
                  :popupContainer="contentRef"
                />
              </x-form-item>
            </div>
          </div>
          <div class="content-addr">
            <x-form-item label="" name="cszgSysEnterprise.entAddr">
              <x-textarea
                v-model:value="form.cszgSysEnterprise.entAddr"
                :placeholder="$t('PEnterEntAddress')"
                :auto-size="{ minRows: 2, maxRows: 5 }"
                :maxlength="150"
                style="margin-left: 74px"
              />
            </x-form-item>
          </div>
        </x-form>
      </div>
      <div class="content-bottom">
        <div class="content-title">{{ $t("functionConfig") }}</div>
        <x-checkbox-tree
          v-model:value="form.permitIdList"
          :treeData="formOptions.permTree"
        />
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  compTree,
  addCompany,
  getEntCurrentPermits,
  mobileRepeat,
  accountRepeat,
  getPublicKey,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import xCascader from "@/components/x-cascader.vue";
import Message from "@/components/x-message";
import xTextarea from "@/components/x-textarea.vue";
import xCheckboxTree from "@/components/x-checkbox-tree.vue";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { resTreeToXTree, treeBfsParse, i18nSimpleKey } from "@/assets/ts/utils";
import { enRSACrypt } from "@/assets/ts/encrypt";
import {
  isPhoneNumber,
  includeNumberCharacter,
  isArea,
} from "@/assets/ts/validate";
import areaOptions from "@/assets/ts/area";
const $t = i18nSimpleKey("companyManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    formRef.value && formRef.value.resetFields();
    form.permitIdList = [];
  }
};

/**
 * 重复手机号
 */
const repeatPhoneNumber = async (value: string) => {
  const result = await mobileRepeat({ mobile: value });
  return !result?.repeat;
};

/**
 * 重复帐号
 */
const repeatAccount = async (value: string) => {
  const result = await accountRepeat({ userAccount: value });
  return !result?.repeat;
};

/**
 * 插入半选
 */
const appendHalfChecked = (tree: any[], values: any[]) => {
  const half = [] as any[];
  treeBfsParse(tree, "children", (item: any) => {
    if (!(item.children && item.children.length > 0)) return;
    if (values.includes(item.value)) return;
    if (item.children.some((sub: any) => values.includes(sub.value))) {
      half.push(item.value);
    }
  });
  return [...half, ...values];
};

/**
 * 表单项
 */
const form = reactive({
  cszgSysEnterprise: {
    entName: "",
    parentEntId: 0,
    entAddr: "",
    provinceCityDistrict: [],
  },
  sysUser: {
    userAccount: "",
    userName: "",
    password: "",
    repassword: "",
    mobile: "",
  },
  permitIdList: [],
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  "cszgSysEnterprise.entName": [["required", $t("PEnterEntName")]],
  "cszgSysEnterprise.parentEntId": [["required", $t("PSelectParentEnt")]],
  "cszgSysEnterprise.provinceCityDistrict": [
    [isArea, $t("PSelectProvinceCityDistrict")],
  ],
  "sysUser.userAccount": [
    ["required", $t("PEnterAccount")],
    [includeNumberCharacter, $t("PEntCharNumberLetterAcc")],
    [repeatAccount, $t("accountExist")],
  ],
  "sysUser.password": [
    ["required", $t("PEnterPassword")],
    [includeNumberCharacter, $t("PEntCharNumberLetterPWD")],
  ],
  "sysUser.repassword": [
    ["required", $t("PEnterConfirmPWD")],
    [
      (value: string, model: any) => value === model.sysUser.password,
      $t("notMatchPassword"),
    ],
  ],
  "sysUser.mobile": [
    ["required", $t("PEnterMobile")],
    [isPhoneNumber, $t("mobileIncorrect")],
    [repeatPhoneNumber, $t("mobileExist")],
  ],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    const publicKey = (await getPublicKey()).publicKey;
    const [province, city, district] =
      form.cszgSysEnterprise.provinceCityDistrict;
    const permitIdList = appendHalfChecked(
      formOptions.permTree,
      form.permitIdList
    );
    await addCompany({
      ...form,
      cszgSysEnterprise: {
        ...form.cszgSysEnterprise,
        province: Number(province),
        city: Number(city),
        district: Number(district),
      },
      sysUser: {
        ...form.sysUser,
        password: enRSACrypt(form.sysUser.password, publicKey),
      },
      permitIdList,
    });
    emits("update:show", false);
    Message("success", $t("addSuccess"));
    emits("confirm");
    // 重置
    formRef.value.resetFields();
    form.permitIdList = [];
  }
};

// 为地区选项添加索引
treeBfsParse(areaOptions, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  areas: areaOptions,
  company: [] as TreeItemType[],
  permTree: [] as any[],
});

(async () => {
  // 企业树
  formOptions.company = reactive(resTreeToXTree([await compTree()]));
})();

const updatePermTree = async (parentEntId: number) => {
  // 功能树
  formOptions.permTree =
    resTreeToXTree([await getEntCurrentPermits({ parentEntId })])?.[0]
      ?.children || [];
};

watch(
  () => form.cszgSysEnterprise.parentEntId,
  (v) => {
    if (v) {
      updatePermTree(v);
    } else {
      formOptions.permTree = [];
    }
  }
);

const mobileFilter = (v: string) => v.replace(/\D/g, "");
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 64px 20px;
  &-top,
  &-bottom {
    padding: 15px;
    border-radius: 8px;
    background: #fff;
    .content-title {
      @include sc(14px, #9f9fa4);
    }
  }
  &-top {
    margin-bottom: 15px;
    padding-bottom: 0;
    .content-title {
      padding-bottom: 15px;
    }
    .content-contact {
      display: flex;
      &-user-name {
        flex: 1;
      }
      &-mobile {
        margin-left: 19px;
        width: 258px;
      }
    }
    &-addr {
      padding-left: 71px;
    }
  }
  &-bottom {
    .content-title {
      &::before {
        content: "*";
        color: red;
      }
    }
  }
}
.content-area {
  display: flex;
  &__label {
    flex-basis: 15%;
    flex-shrink: 0;
    flex-grow: 0;
    line-height: 38px;
    padding-left: 8px;
    color: #5e5e5e;
  }
  &__value {
    flex-basis: 0;
    flex-shrink: 1;
    flex-grow: 1;
    overflow: hidden;
  }
}
</style>
