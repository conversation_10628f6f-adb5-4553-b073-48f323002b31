<template>
  <div class="task-edit">
    <div class="task-edit-left">
      <div
        class="task-edit-left-item"
        v-if="task.scheduleType === 1"
      >
        <div class="title">{{ $t("taskType") }}</div>
        <div class="tabs">
          <div
            v-for="(tab, i) in taskType"
            :key="i"
            class="tab"
            :class="{ active: task.taskType === tab.value }"
            @click="task.taskType = tab.value"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>

      <div class="task-edit-left-item">
        <div class="title">{{ $t("cycleTime") }}</div>
        <div class="tabs">
          <div
            v-for="(tab, tabIdx) in executeDayType"
            :key="tabIdx"
            class="tab"
            :class="{ active: task.executeDayType === tab.value }"
            @click="switchExecuteDayType(tab.value)"
          >
            {{ tab.label }}
          </div>
        </div>
        <div
          class="checkboxs"
          v-if="task.executeDayType === 'weekly'"
        >
          <div
            v-for="(day, dayIdx) in dayType"
            :key="dayIdx"
            class="checkbox"
          >
            <x-checkbox
              :text="day.label"
              :checked="task.executeDays?.includes(day.value)"
              @update:checked="updateExecuteDays($event, day.value)"
            />
          </div>
        </div>
      </div>

      <div
        v-if="task.scheduleType === 4"
        class="task-edit-left-item"
      >
        <div class="title">开机/关机</div>
        <div class="tabs">
          <div
            v-for="(tab, tabIdx) in switchTypeOptions"
            :key="tabIdx"
            class="tab"
            :class="{ active: task._switchType === tab.value }"
            @click="switchTypeChange(tab)"
          >
            {{ tab.label }}
          </div>
        </div>
      </div>
      <div
        v-else
        class="task-edit-left-item"
      >
        <div class="title">{{ getStartTimeLabel(task.scheduleType) }}</div>
        <div>
          <TimePicker
            v-model:value="task.executeTimeStart"
            :popupContainer="popupContainer"
            :placeholder="$t('PSelectTime')"
            format="HH:mm"
            style="width: 178px"
          />
        </div>
      </div>

      <div
        class="task-edit-left-item"
        v-if="task.scheduleType === 4"
      >
        <template v-if="task._switchType === 6">
          <div class="title">{{ $t("offTime") }}</div>
          <div>
            <TimePicker
              v-model:value="task.executeTimeEnd"
              :popupContainer="popupContainer"
              :placeholder="$t('PSelectTime')"
              format="HH:mm"
              style="width: 178px"
            />
          </div>
        </template>
        <template v-else>
          <div class="title">{{ $t("powerOnTime") }}</div>
          <div>
            <TimePicker
              v-model:value="task.executeTimeStart"
              :popupContainer="popupContainer"
              :placeholder="$t('PSelectTime')"
              format="HH:mm"
              style="width: 178px"
            />
          </div>
        </template>
      </div>
      <div
        class="task-edit-left-item"
        v-if="task.scheduleType === 1"
      >
        <div class="title">{{ $t("cleanTimes") }}</div>
        <div class="tabs">
          <div
            v-for="(count, i) in cleanTimes"
            :key="i"
            class="tab"
            :class="{ active: task.executeTimes === count.value }"
            @click="task.executeTimes = count.value"
          >
            {{ count.label }}
          </div>
        </div>
      </div>
    </div>
    <!-- 右侧内容 -->
    <div class="task-edit-right">
      <div v-if="task.scheduleType === 1">
        <label class="required">{{ $t("selectedJobScope") }}</label>
        <div class="task-edit-right-cover">
          <div class="tabs">
            <div
              v-for="(tab, i) in coverRangeOptions"
              :key="i"
              class="tab"
              :class="{ active: task.coverCleanType === tab.value }"
              @click="coverCleanTypeChange(tab)"
            >
              {{ tab.label }}
            </div>
          </div>
          <div
            class="controller-box"
            v-if="task.scheduleType === 1"
          >
            <div class="controller">
              <div class="controller-item">{{ getSpeedText(task.speed) }}</div>
              <!-- <div class="controller-item">{{ $t("fan") }}：{{ getFanText(task.fan) }}</div> -->
              <div class="controller-item">{{ $t("blowWater") }}：{{ task.blowWater ? $t("on") : $t("off") }}</div>
            </div>
            <div @click="customClick">
              <x-icon
                ref="templateEditRef"
                name="edit_active"
                width="12"
                height="12"
                style="cursor: pointer"
              />
            </div>
          </div>
        </div>
        <div
          class="task-edit-right-list"
          v-if="task.coverCleanType === 2"
        >
          <div
            :class="['task-edit-right-list-item', { 'is-active': item.value === task.relId }]"
            v-for="item in formOpts.routeTemplate"
            :key="item.value"
            @click="stationSelectChange(item.value, task)"
          >
            <span>{{ item.label }}</span>
            <x-icon
              v-if="task.relId === item.value"
              name="list_selected_icon"
              width="20"
              height="20"
            />
          </div>
        </div>
      </div>
      <div v-if="task.scheduleType === 2">
        <label>{{ $t("selectGarbageStation") }}</label>
        <div class="task-edit-right-list">
          <div
            :class="['task-edit-right-list-item', { 'is-active': item.value === task.relId }]"
            v-for="item in formOpts.garbageStation"
            :key="item.value"
            @click="stationSelectChange(item.value, task)"
          >
            <span>{{ item.label }}</span>
            <x-icon
              v-if="task.relId === item.value"
              name="list_selected_icon"
              width="20"
              height="20"
            />
          </div>
        </div>
      </div>
      <div v-if="task.scheduleType === 3">
        <label>{{ $t("selectChargingStation") }}</label>
        <div class="task-edit-right-list">
          <div
            :class="['task-edit-right-list-item', { 'is-active': item.value === task.relId }]"
            v-for="item in formOpts.chargingStation"
            :key="item.value"
            @click="stationSelectChange(item.value, task)"
          >
            <span>{{ item.label }}</span>
            <x-icon
              v-if="task.relId === item.value"
              name="list_selected_icon"
              width="20"
              height="20"
            />
          </div>
        </div>
      </div>
      <div v-if="task.scheduleType === 4 && task._switchType === 6">
        <label>{{ $t("selectShutdownStation") }}</label>
        <div class="tabs">
          <div
            v-for="(tab, i) in parkingStationTypes"
            :key="i"
            class="tab"
            :class="{ active: task.stationType === tab.value }"
            @click="parkingStationTypesClick(tab)"
          >
            {{ tab.label }}
          </div>
        </div>
        <div class="task-edit-right-list">
          <div
            :class="['task-edit-right-list-item', { 'is-active': item.value === task.relId }]"
            v-for="item in getSwitchParkingList(task.stationType)"
            :key="item.value"
            @click="stationSelectChange(item.value, task)"
          >
            <span>{{ item.label }}</span>
            <x-icon
              v-if="task.relId === item.value"
              name="list_selected_icon"
              width="20"
              height="20"
            />
          </div>
        </div>
      </div>
      <div v-if="task.scheduleType === 5">
        <label>{{ $t("selectWateringStation") }}</label>
        <div class="task-edit-right-list">
          <div
            :class="['task-edit-right-list-item', { 'is-active': item.value === task.relId }]"
            v-for="item in formOpts.wateringStation"
            :key="item.value"
            @click="stationSelectChange(item.value, task)"
          >
            <span>{{ item.label }}</span>
            <x-icon
              v-if="task.relId === item.value"
              name="list_selected_icon"
              width="20"
              height="20"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
  <Custom
    v-model:show="customDrawer.show"
    :speed="customDrawer.speed"
    :fan="customDrawer.fan"
    :blowWater="customDrawer.blowWater"
    @confirm="updateCustom"
  />
</template>

<script lang="tsx" setup>
import { computed, reactive, type PropType } from "vue";
import { TimePicker } from "@/components/x-time-picker";
import { taskType, dayType, executeDayType } from "@/assets/ts/config";
import { getStartTimeLabel, getSpeedText, type TaskItemType } from "./utils";
import { i18nSimpleKey } from "@/assets/ts/utils";
import xCheckbox from "@/components/x-checkbox.vue";
import xIcon from "@/components/x-icon.vue";
import Custom from "./custom.vue";
import type { OptionsType } from "@/components/x-select.vue";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  formOptions: {
    type: Object,
    required: true,
  },
  task: {
    type: Object as PropType<TaskItemType>,
    required: true,
  },
});

const formOpts = computed(() => ({
  routeTemplate: props.formOptions.routeTemplate as OptionsType,
  chargingStation: [{ label: "自寻充电点", value: "0" }, ...props.formOptions.chargingStation] as OptionsType,
  garbageStation: [{ label: "自寻垃圾点", value: "0" }, ...props.formOptions.garbageStation] as OptionsType,
  parkingStation: [{ label: "不指定", value: "0" }, ...props.formOptions.parkingStation] as OptionsType,
  wateringStation: [{ label: "自寻加水点", value: "0" }, ...props.formOptions.wateringStation] as OptionsType,
}));

/** 所选作业范围 */
const coverRangeOptions = [
  { label: "一键清扫", value: 1 },
  { label: "模版任务", value: 2 },
];
const coverCleanTypeChange = (tab: typeof coverRangeOptions[0]) => {
  props.task.coverCleanType = tab.value;
  if (tab.value === 1) {
    props.task.relId = "0";
  }
};
/** 清扫次数 */
const cleanTimes = [
  { label: "1次", value: 1 },
  { label: "2次", value: 2 },
  { label: "3次", value: 3 },
];
/** 选择开关机的停车场所 */
const parkingStationTypes = [
  { label: "不指定", value: -1 },
  { label: "停车点", value: 4 },
  { label: "充电点", value: 2 },
];
const getSwitchParkingList = (stationType?: number) => {
  return stationType === 2 ? formOpts.value.chargingStation : stationType === 4 ? formOpts.value.parkingStation : [];
};
const parkingStationTypesClick = (item: typeof parkingStationTypes[0]) => {
  props.task.stationType = item.value;
  props.task.relId = "0";
};
/** 开关机 */
const switchTypeOptions = [
  { label: "开机", value: 4 },
  { label: "关机", value: 6 },
];
const switchTypeChange = (opt: typeof switchTypeOptions[0]) => {
  props.task._switchType = opt.value;
};

// 循环周期
const switchExecuteDayType = (value: string) => {
  props.task.executeDayType = value;
  props.task.executeDays = value === "daily" ? ["0"] : ["1", "2", "3", "4", "5", "6", "7"];
};

const updateExecuteDays = (checked: boolean, value: string) => {
  const executeDays = props.task.executeDays;
  if (props.task.executeDayType === "daily") {
    props.task.executeDays = ["0"];
    return;
  }
  if (checked) {
    if (!executeDays.includes(value)) {
      executeDays.push(value);
    }
  } else {
    const dayIndex = executeDays.indexOf(value);
    if (dayIndex !== -1) {
      executeDays.splice(dayIndex, 1);
    }
  }
};

const stationSelectChange = (value: string, task: TaskItemType) => {
  if (task.relId === value && [4, 6].includes(task.scheduleType)) {
    task.relId = "0";
  } else {
    task.relId = value;
  }
};

// 自定义设置
const customDrawer = reactive({
  show: false,
  speed: 2,
  fan: 0,
  blowWater: 0,
});
const customClick = () => {
  const { speed, fan, blowWater } = props.task;
  customDrawer.speed = speed;
  customDrawer.fan = fan;
  customDrawer.blowWater = blowWater;
  customDrawer.show = true;
};
const updateCustom = (data: any) => {
  const { speed, fan, blowWater } = data;
  props.task.speed = speed;
  props.task.fan = fan;
  props.task.blowWater = blowWater;
};
</script>

<style lang="scss" scoped>
.task-edit {
  width: 100%;
  display: flex;
  padding: 0 10px;
  border-radius: 8px;
  &-left,
  &-right {
    flex: 1;
  }
  &-left {
    &-item {
      display: flex;
      flex-direction: column;
      margin-bottom: 10px;
      .title {
        margin: 5px 0;
        @include sc(14px, #383838) {
          font-weight: 500;
        }
      }
      .checkboxs {
        display: flex;
        margin-top: 10px;
        .checkbox {
          margin-right: 15px;
          :deep(.x-checkbox-text) {
            @include sc(14px, #555);
          }
        }
      }
    }
  }
  &-right {
    label {
      @include wh(80px, 30px) {
        line-height: 30px;
      }
      @include sc(14px, rgb(85, 85, 85));
      &.required::before {
        content: "* ";
        color: red;
      }
    }
    .controller-box {
      @include ct-f(y) {
        column-gap: 10px;
        justify-content: space-between;
      }
      margin-top: 10px;
      .controller {
        display: flex;
        &-item {
          @include sc(14px, #555555);
          margin-right: 10px;
          padding: 0 10px;
          height: 34px;
          line-height: 34px;
          background: #f5f8fe;
          border-radius: 4px;
        }
      }
    }
    &-cover {
      display: flex;
      justify-content: space-between;
    }
    &-list {
      width: 500px;
      row-gap: 10px;
      margin-top: 10px;
      @include scrollbar(y, 4px) {
        overflow-y: auto;
        max-height: 300px;
      }
      &-item {
        height: 54px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        box-sizing: border-box;
        border-radius: 8px;
        background-color: #f5f8fe;
        border: 1px solid transparent;
        margin-bottom: 10px;
        cursor: pointer;
        &.is-active,
        &:hover {
          background-color: #fff;
          border-color: rgb(229, 237, 255);
        }
      }
    }
  }
  .tabs {
    width: max-content;
    padding: 0 5px;
    height: 40px;
    @include ct-f;
    background: #f5f8fe;
    border-radius: 4px;
    .tab {
      @include ct-f;
      padding: 5px 8px;
      min-width: 60px;
      height: 32px;
      @include sc(14px, #9f9fa4);
      cursor: pointer;
      &.active {
        background: #fff;
        border-radius: 4px;
        @include sc(14px, #5964fb);
      }
    }
  }
}
</style>
