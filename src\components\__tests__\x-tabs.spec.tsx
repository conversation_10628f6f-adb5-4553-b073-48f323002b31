import { describe, it, expect, vi } from "vitest";
import { mount, shallowMount } from '@vue/test-utils';
import XTabs from '@/components/x-tabs.vue';
import XTabPane from '@/components/x-tab-pane.vue';

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = mount({
        render() {
            return (
                <XTabs activeKey="1">
                    <XTabPane tab="tab1" name="1">
                        tab1内容
                    </XTabPane>
                    <XTabPane tab="tab2" name="2">
                        tab2内容
                    </XTabPane>
                </XTabs>
            );
        },
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
    const wrapper = mount({
        render() {
            return (
                <XTabs activeKey="1" centered>
                    <XTabPane tab="tab1" name="1">
                        tab1内容
                    </XTabPane>
                    <XTabPane tab="tab2" name="2">
                        tab2内容
                    </XTabPane>
                </XTabs>
            );
        },
    });
    it('正确渲染激活tab和面板内容', () => {
        expect(wrapper.find(".x-tabs-nav.active").text()).toBe("tab1");
        expect(wrapper.find(".x-tab-pane").text()).toBe("tab1内容");
    });
    it('正确渲染选项居中展示', () => {
        expect(wrapper.find(".x-tabs-centered").exists()).toBe(true);
    });
});

describe('Events', () => {
    const handleChange = vi.fn();
    const wrapper = mount({
        render() {
            return (
                <XTabs activeKey="1">
                    <XTabPane tab="tab1" name="1" disabled>
                        tab1内容
                    </XTabPane>
                    <XTabPane tab="tab2" name="2">
                        tab2内容
                    </XTabPane>
                </XTabs>
            );
        },
    });
    it('禁用单个tab选项时应阻止该选项点击事件', () => {
        const tabs = wrapper.findAll('.x-tabs-nav');
        tabs[0].trigger("click");
        expect(wrapper.emitted().change).not.toBeDefined();
    });
});
