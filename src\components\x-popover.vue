<template>
  <section
    ref="_bindRef"
    class="x-popover"
    @mousedown="_bindRefClick"
    @focus="_bindRefFocus"
    @blur="_bindRefBlur"
  >
    <slot></slot>
    <Teleport :to="props.container || 'body'">
      <div
        v-show="props.visible !== undefined ? props.visible : _visible"
        ref="_containerRef"
        class="x-popover-container"
        :style="{
          zIndex: props.zIndex || 2000,
        }"
      >
        <div
          v-if="props.triangle"
          ref="_triangleRef"
          :class="`x-popover-${props.placement}-triangle-${props.contentType}`"
        ></div>
        <div
          ref="_contentRef"
          :class="`x-popover-content-${props.contentType}`"
        >
          <slot name="content"></slot>
        </div>
      </div>
    </Teleport>
  </section>
</template>
<script lang="ts" setup>
import { nextTick, ref, watchEffect, onMounted, onBeforeUnmount } from "vue";
import type { PropType } from "vue";
const props = defineProps({
  container: {
    type: HTMLElement,
  },
  trigger: {
    // popover的focus无实际效果仅为避免click
    type: String as PropType<"hover" | "click" | "focus">,
    required: true,
  },
  placement: {
    // 气泡相对被绑定节点的位置，与常规UI框架不同
    type: String as PropType<"rightBottom" | "bottom" | "leftBottom" | "top">,
    required: true,
  },
  gap: {
    // 气泡与被绑定节点的距离
    type: Number,
    default: 10,
  },
  visible: {
    type: Boolean,
    default: undefined,
  },
  triangle: {
    type: Boolean,
    default: true,
  },
  allowKeepShowElements: {
    type: Array as PropType<HTMLElement[]>,
    default: () => [],
  },
  keepShow: {
    type: Boolean,
    default: false,
  },
  contentType: {
    type: String as PropType<"default" | "light" | "none">,
    default: "default",
  },
  // 当有多个popover时，且不在同一个父节点时，需要配置层次关系
  zIndex: {
    type: Number,
    default: 0,
  },
});

const emits = defineEmits(["update:visible", "visibleChange"]);

const _bindRef = ref<any>(); // 被绑定的节点
const _containerRef = ref<any>(); // 用于绑定位置的气泡
const _triangleRef = ref<any>(); // 气泡内的箭头
const _contentRef = ref<any>(); // 气泡内的内容

const _visible = ref<boolean>();
watchEffect(() => {
  // 由于有可能在组件外部改变visible，这里也需要同步改变
  _visible.value = Boolean(props.visible);
  props.trigger === "focus" && _visible.value && showPop();
});

const _bindRefClick = (e: Event) => {
  props.trigger === "click" && (_visible.value ? closePop(e) : showPop(e));
};
const _bindRefFocus = (e: Event) => {
  props.trigger === "focus" && showPop(e);
};
const _bindRefBlur = (e: Event) => {
  props.trigger === "focus" && closePop(e);
};
const showPop = (e?: Event) => {
  e && e.stopPropagation();
  emits("update:visible", true);
  emits("visibleChange", true);
  _visible.value = true;
  // _containerRef.value.style.visibility = "hidden";
  document.addEventListener("mousedown", docClickHandler);
  nextTick(() => {
    const { height: pheight, width: pwidth } = _contentRef.value.getBoundingClientRect();
    let posTop = 0;
    let posLeft = 0;
    let posParent: HTMLElement | typeof window = window;
    let scrollTop = posParent.scrollY;
    let scrollLeft = posParent.scrollX;
    let maxTop = document.body.offsetHeight - pheight;
    let maxLeft = document.body.offsetWidth - pwidth;
    if (props.container) {
      posParent = props.container;
      if (getComputedStyle(posParent).position === "static") posParent.style.position = "relative";
      posTop = posParent.getBoundingClientRect().top;
      posLeft = posParent.getBoundingClientRect().left;
      scrollTop = posParent.scrollTop;
      scrollLeft = posParent.scrollLeft;
      maxTop = posParent.scrollHeight - pheight;
      maxLeft = posParent.scrollWidth - pwidth;
    }

    // 气泡的位置
    const { width, height, top, left } = _bindRef.value.getBoundingClientRect();
    const positions = {
      leftBottom: {
        top: top - posTop + scrollTop + height + props.gap,
        left: left - posLeft,
      },
      bottom: {
        top: top - posTop + scrollTop + height + props.gap,
        left: left - posLeft + scrollLeft + width / 2 - pwidth / 2,
      },
      leftTop: {
        top: top - posTop + scrollTop - pheight - props.gap,
        left: left - posLeft + scrollLeft + width - pwidth,
      },
      right: {
        top: top - posTop + scrollTop + height / 2 - pheight / 2,
        left: left - posLeft + scrollLeft + width + props.gap,
      },
      rightBottom: {
        top: top - posTop + scrollTop,
        left: left - posLeft + scrollLeft + width + props.gap,
      },
      top: {
        top: top - posTop + scrollTop - pheight - props.gap,
        left: left - posLeft + scrollLeft + width / 2 - pwidth / 2,
      },
    };

    const _contentRefLeft = ~~Math.min(positions[props.placement].left, maxLeft);
    const _contentRefTop = ~~Math.min(positions[props.placement].top, maxTop);
    _containerRef.value.style.left = _contentRefLeft + "px";
    _containerRef.value.style.top = _contentRefTop + "px";

    if (props.triangle) {
      // 气泡内箭头的位置
      const _triangleStyle = window.getComputedStyle(_triangleRef.value);
      if (/^right|rightBottom$/.test(props.placement)) {
        // top = 被触发节点的top - 展示浮窗的实际top - props.container的top + 被触发节点的高度/2 - 箭头高度/2
        _triangleRef.value.style.top =
          top -
          _contentRefTop -
          (props.container?.getBoundingClientRect().top || 0) +
          height / 2 -
          parseInt(_triangleStyle.getPropertyValue("border-top-width")) +
          "px";
      } else if (/^leftBottom|leftTop$/.test(props.placement)) {
        // right = 被触发节点的宽度/2 - 箭头宽度/2
        _triangleRef.value.style.right =
          width / 2 - parseInt(_triangleStyle.getPropertyValue("border-left-width")) + "px";
      } else if (/^bottom$/.test(props.placement)) {
        // left = 被触发节点的left - 展示浮窗的实际left - props.container的left + 被触发节点的宽度/2 - 箭头宽度/2
        _triangleRef.value.style.left =
          left -
          _contentRefLeft -
          (props.container?.getBoundingClientRect().left || 0) +
          width / 2 -
          parseInt(_triangleStyle.getPropertyValue("border-left-width")) +
          "px";
      } else if (/^top$/.test(props.placement)) {
        // left = 被触发节点的left - 展示浮窗的实际left - props.container的left + 被触发节点的宽度/2 - 箭头宽度/2
        _triangleRef.value.style.left =
          left -
          _contentRefLeft -
          (props.container?.getBoundingClientRect().left || 0) +
          width / 2 -
          parseInt(_triangleStyle.getPropertyValue("border-left-width")) +
          "px";
        _triangleRef.value.style.top = _contentRef.value.clientHeight + "px";
      }
    }
    // console.log(
    //   `${_contentRefLeft}`,
    //   `${positions[props.placement].left}: ${left}-${posLeft}+${scrollLeft}+${
    //     width / 2
    //   }-${pwidth / 2}`,
    //   `${maxLeft}: ${document.body.offsetWidth} - ${pwidth}`
    // );
  });
};
const closePop = (e: Event) => {
  e.stopPropagation();
  emits("update:visible", false);
  emits("visibleChange", false);
  _visible.value = false;
  document.removeEventListener("mousedown", docClickHandler);
};

// 点击其它区域关闭
const docClickHandler = (event: Event) => {
  if (props.keepShow) return;
  const _bool =
    _containerRef.value.contains(event.target) ||
    props.allowKeepShowElements.some((item) => item.contains(event.target as any));

  // 当 _bindRef为表单类 && 点击在表单范围内 时，不让它关闭
  if (props.trigger === "focus" && _bindRef.value.contains(event.target)) {
    return;
  }
  _visible.value = _bool;
  emits("update:visible", _bool);
  emits("visibleChange", _bool);
  if (!_bool) {
    document.removeEventListener("mousedown", docClickHandler);
  }
};

onMounted(() => {
  if (props.trigger === "hover") {
    _bindRef.value.addEventListener("mouseenter", showPop);
    _bindRef.value.addEventListener("mouseleave", closePop);
  }
});
onBeforeUnmount(() => {
  if (props.trigger === "hover") {
    _bindRef.value.removeEventListener("mouseenter", showPop);
    _bindRef.value.removeEventListener("mouseleave", closePop);
  }

  document.removeEventListener("mousedown", docClickHandler);
});
</script>

<style lang="scss">
.x-popover {
  // 默认深色背景
  &-rightBottom-triangle-default,
  &-rightBottom-triangle-none {
    position: absolute;
    left: -7px;
    @include triangle(8px, 6px, rgba(1, 5, 31, 0.82), left);
  }
  &-bottom-triangle-default,
  &-bottom-triangle-none {
    position: absolute;
    top: -7px;
    @include triangle(6px, 8px, rgba(1, 5, 31, 0.82), top);
  }
  &-top-triangle-default,
  &-top-triangle-none {
    position: absolute;
    @include triangle(6px, 8px, rgba(1, 5, 31, 0.82), bottom);
  }
  &-container {
    position: absolute;
    // 必须设置left和top，否则再设置时会改变宽高
    left: 0;
    top: 0;
    box-shadow: 0px 0px 10px 0 #0000001a;
  }
  &-content-none {
    cursor: auto;
  }
  &-content-default {
    border-radius: 4px;
    color: #fff;
    background-color: rgba(1, 5, 31, 0.82);
  }
  // 白色背景+阴影
  &-bottom-triangle-light {
    position: absolute;
    top: -7px;
    @include triangle(6px, 8px, #fff, top);
  }
  &-top-triangle-light {
    position: absolute;
    @include triangle(6px, 8px, #fff, bottom);
  }
  &-content-light {
    padding: 10px;
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
  }
}
</style>
