<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :title="$t('baseInfo')"
    width="784px"
  >
    <div class="user-detail-model">
      <div class="model-info">
        <div class="model-info-top">
          <div class="model-info-top-name">
            {{ config.userinfo.userName }}
          </div>
          <div
            :class="[
              'model-info-top-status',
              { error: config.userinfo.status === 0 },
            ]"
          >
            <div class="status-inner">
              {{ config.userinfo.statusText }}
            </div>
          </div>
        </div>
        <div class="model-info-bottom">
          <div
            class="model-info-bottom-item"
            v-for="(item, index) in bottomInfo"
            :key="index"
          >
            <div class="item-label">{{ item.label }}</div>
            <div class="item-value">{{ item.value }}</div>
          </div>
        </div>
      </div>
      <div class="model-auth">
        <div class="model-auth-top">{{ $t("authorizedCars") }}：</div>
        <div class="model-auth-bottom">
          <x-tree :treeData="tree" />
        </div>
      </div>
    </div>
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, computed, watch } from "vue";
import { getUserDetail } from "@/services/api";
import { resTreeToXTree, i18nSimpleKey } from "@/assets/ts/utils";
import type { GetUserDetailResponse } from "@/services/type";
import xTree from "@/components/x-tree";
import xDrawer from "@/components/x-drawer.vue";
const $t = i18nSimpleKey("userManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const config = reactive({
  userinfo: {} as GetUserDetailResponse,
});
const bottomInfo = computed(() => [
  {
    label: $t("phone") + ":",
    value: config.userinfo.mobile,
  },
  {
    label: $t("account") + ":",
    value: config.userinfo.userAccount,
  },
  {
    label: $t("company") + ":",
    value: config.userinfo.entName,
  },
  {
    label: $t("role") + ":",
    value:
      (config.userinfo.roleList && config.userinfo.roleList[0]?.roleName) || "",
  },
  {
    label: $t("accountType") + ":",
    value: config.userinfo.userTypeText,
  },
  {
    label: $t("stationType") + ":",
    value: config.userinfo.stationText,
  },
]);
const tree = computed(() => {
  const _tree = config.userinfo.enterpriseTree;
  return _tree && _tree.entId ? resTreeToXTree([_tree]) : [];
});
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      config.userinfo = await getUserDetail(props.id);
    }
  }
);
</script>

<style lang="scss" scoped>
.user-detail-model {
  @include fj;
  @include wh(100%, 320px);
  .model-info,
  .model-auth {
    width: 365px;
    padding: 15px;
    border-radius: 8px;
    background: #fff;
  }
  .model-info {
    &-top {
      @include fj;
      @include wh(100%, 28px);
      &-name {
        @include sc(18px, #242859) {
          font-weight: bolder;
          line-height: 28px;
        }
      }
      &-status {
        @include wh(50px, 100%) {
          padding: 3px;
          border-radius: 4px;
          border: 1px solid rgb(39, 212, 161);
        }
        .status-inner {
          @include wh(100%) {
            border-radius: 4px;
            background-color: rgb(39, 212, 161);
          }
          color: #fff;
          text-align: center;
          line-height: 20px;
        }
        &.error {
          border-color: rgb(244, 40, 78);
          .status-inner {
            background-color: rgb(244, 40, 78);
          }
        }
      }
    }
    &-bottom {
      &-item {
        @include fj;
        @include wh(100%, 21px) {
          margin-top: 20px;
        }

        .item-label {
          width: 77px;
          color: rgb(159, 159, 164);
          line-height: 21px;
        }
        .item-value {
          flex: 1;
          color: #383838;
          line-height: 21px;
        }
      }
    }
  }
  .model-auth {
    &-top {
      @include sc(16px, #9f9fa4);
      @include wh(100%, 21px) {
        line-height: 21px;
      }
    }
    &-bottom {
      width: 100%;
      height: calc(100% - 29px);
      margin-top: 10px;
      padding: 0 10px;
      background-color: rgba(244, 247, 254, 0.5);
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    }
  }
}
</style>
