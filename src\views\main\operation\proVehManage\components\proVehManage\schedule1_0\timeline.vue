<template>
  <div
    :class="[
      'timeline',
      {
        high: hasValid<PERSON>ang<PERSON>,
      },
    ]"
  >
    <div class="timeline-label">
      <div class="label" v-for="(item, index) in timeLableList" :key="index">
        {{ item }}
      </div>
    </div>
    <div class="timeline-content">
      <div class="ruler">
        <template v-for="(item, index) in rangeStyles" :key="index">
          <template
            v-if="props.timeRange[index][0] && props.timeRange[index][1]"
          >
            <div class="range-bg" :style="item.bgStyle"></div>
            <div class="range-label" :style="item.labelStyle">
              <div>班</div>
              <div>次</div>
              <div>{{ index + 1 }}</div>
            </div>
          </template>
        </template>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { ref, watch } from "vue";
import type { PropType } from "vue";

const props = defineProps({
  timeRange: {
    type: Array as PropType<string[][]>,
    default: () => [],
  },
});

const timeLableList = [
  "00:00",
  "02:00",
  "04:00",
  "06:00",
  "08:00",
  "10:00",
  "12:00",
  "14:00",
  "16:00",
  "18:00",
  "20:00",
  "22:00",
  "24:00",
];

const rangeColor = [
  { background: "rgba(89, 100, 251, 0.1)", border: "rgb(89, 100, 251)" },
  { background: "rgba(39, 212, 161, 0.1)", border: "rgb(39, 212, 161)" },
  { background: "rgba(116, 34, 254, 0.1)", border: "rgb(116, 34, 254)" },
  { background: "rgba(248, 201, 57, 0.1)", border: "rgb(248, 201, 57)" },
];

const rangeStyles = ref<
  { bgStyle: Record<string, string>; labelStyle: Record<string, string> }[]
>([]);

// 时间范围样式
const getRangeStyle = (item: string[], index: number) => {
  const [startHour, startMinute] = item[0].split(":").map(Number);
  const [endHour, endMinute] = item[1].split(":").map(Number);
  const totalMinutes = (endHour - startHour) * 60 + (endMinute - startMinute);
  const pixelsPerHour = 50;
  // 宽度
  const width = (totalMinutes / 60) * pixelsPerHour;
  // 距左
  const distance = ((startHour * 60 + startMinute) * pixelsPerHour) / 60;
  // 背景/边框
  const rangeColorIndex = index % rangeColor.length;
  const { background, border } = rangeColor[rangeColorIndex];
  // 范围图示
  const bgStyle = {
    width: `${width}px`,
    left: `${distance}px`,
    backgroundColor: background,
    borderColor: border,
  };
  // 范围文字
  const labelStyle = {
    width: `${width}px`,
    left: `${distance}px`,
    borderColor: border,
    color: border,
  };
  return { bgStyle, labelStyle };
};

const hasValidRange = ref(false);
watch(
  () => props.timeRange,
  (newV) => {
    rangeStyles.value = newV.map((item, index) => getRangeStyle(item, index));
    hasValidRange.value = newV.some(([start, end]) => start && end);
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style scoped lang="scss">
.timeline {
  padding: 25px 20px 25px 0;
  &.high {
    padding: 25px 20px 65px 0 !important;
  }
  &-label {
    margin-bottom: 5px;
    width: 1230px;
    display: flex;
    justify-content: space-between;
    .label {
      @include sc(12px, #9f9fa4);
    }
  }
  &-content {
    width: 1245px;
    .ruler {
      position: relative;
      width: 1201px; // 1px终点刻度
      min-height: 10px;
      margin: 0 15px;
      border-bottom: 1px solid #dcdcdc;
      background-image: 
        // 长刻度
        linear-gradient(90deg, #dcdcdc 0, #dcdcdc 2%, transparent 2%),
        // 透明刻度
        linear-gradient(180deg, #fff 50%, transparent 50%),
        // 短刻度
        linear-gradient(
            90deg,
            transparent 50%,
            #dcdcdc 50%,
            #dcdcdc 52%,
            transparent 52%
          );
      background-size: 50px 10px;
      background-repeat: repeat-x;
      .range-bg {
        position: absolute;
        top: -5px;
        height: 30px;
        border-width: 0 1px 0 1px;
        border-style: solid;
      }
      .range-label {
        position: absolute;
        top: 20px;
        @include ct-f(both) {
          flex-direction: column;
        }
        font-size: 14px;
        font-weight: bold;
      }
    }
  }
}
</style>
