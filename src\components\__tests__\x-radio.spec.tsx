import { describe, it, expect } from "vitest"
import { shallowMount } from '@vue/test-utils'
import XRadio from '@/components/x-radio.vue'

const baseProps = { 
  value:'value1',
  options: [
    { label: 'label1', value: 'value1', disabled: true },
    { label: 'label2', value: 'value2' },
    { label: 'label3', value: 'value3' },
  ],
} 

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 所有选项', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    expect(wrapper.findAll('.x-radio-wrap').length).toBe(3);
  });

  it('正确渲染 所有选项名', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    radios.forEach((radio, index) => {
      const option = baseProps.options[index];
      const labelElement = radio.find('.x-radio-label');
      expect(labelElement.text()).toBe(option.label);
    });
  });

  it('正确渲染 选中/非选中样式', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    expect(radios[0].find('.x-radio-checked').exists()).toBe(true);
    expect(radios[1].find('.x-radio-checked').exists()).toBe(false);
  });

  it('正确计算 默认排列时单选器间距', () => {
    const wrapper = shallowMount(XRadio, {
      props: {
        gap: 20,
        ...baseProps
      }
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    expect(radios[1].attributes().style).toBe('margin-right: 20px;');
    expect(radios[2].attributes().style).toBe('');
  });

  it('正确计算 垂直排列时单选器间距', () => {
    const wrapper = shallowMount(XRadio, {
      props: {
        gap: 20,
        vertical: true,
        ...baseProps
      }
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    expect(radios[1].attributes().style).toBe('margin-bottom: 20px;');
    expect(radios[2].attributes().style).toBe('');
  });
});

describe('Events Click', () => {
  it('点击选项时应触发update和change事件并传值', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    radios[1].trigger('click');
    expect(wrapper.emitted()['update:value'][0][0]).toBe('value2');
    expect(wrapper.emitted().change[0][0]).toBe('value2');
  });

  it('禁用单个子单选器时应阻止该单选器点击事件', () => {
    const wrapper = shallowMount(XRadio, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-wrap');
    radios[0].trigger('click');
    expect(wrapper.emitted().change).not.toBeDefined();
  });

  it('禁用所有子单选器时应阻止所有单选器点击事件', () => {
    const wrapper = shallowMount(XRadio, {
      props: { 
        disabled: true, 
        ...baseProps 
      }
    });
    wrapper.trigger('click');
    expect(wrapper.emitted().change).not.toBeDefined();
  });
})
