<template>
  <section class="veh-task-summary" ref="vehTaskSummaryRef">
    <div class="veh-task-summary-top">
      <div class="top-title">{{ $t("vehTaskSummary") }}</div>
    </div>
    <div class="veh-task-summary-middle">
      <div class="middle-left">
        <div class="middle-left-top">
          <x-select
            v-model:value="searchForm.vehicNo"
            :options="searchForm.vehicNoOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请输入车牌号码"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.company"
            :options="searchForm.companyOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请输入企业名"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.vin"
            :options="searchForm.vinOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请选择VIN"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.project"
            :options="searchForm.projectOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请选择项目"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.area"
            :options="searchForm.areaOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请选择区域"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.model"
            :options="searchForm.modelOptions"
            :popupContainer="vehTaskSummaryRef"
            placeholder="请选择型号"
            allowClear
          />
        </div>
        <div class="middle-left-bottom">
          <div style="display: flex">
            <DatePicker
              v-model:value="searchForm.date"
              :popupContainer="vehTaskSummaryRef"
              style="width: 300px"
              :allowClear="false"
              @changeSelect="toggleDateType"
              showSelect
            />
          </div>
          <div style="display: flex">
            <x-button
              @click="exportExcel"
              icon="export_blue"
              type="paleBlue"
              :text="$t('export')"
              style="margin-right: 12px"
            />
            <x-button
              @click="searchList"
              type="blue"
              :text="$t('search')"
              style="margin-right: 12px"
            />
            <x-button
              @click="resetSearchForm"
              type="green"
              :text="$t('reset')"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="veh-task-summary-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #opera="{ record }">
          <span class="table-opera" @click="showImage(record)"> 路径 </span>
        </template>
      </x-table>
    </div>
    <x-image
      :visible="Boolean(imageUrl)"
      :src="imageUrl"
      @cancel="imageUrl = ''"
    />
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import {
  getVehList,
  getVehTaskSummaryInfo,
  compList,
  getProjectAndArea,
  getVehAndVinList,
  vehicleModelList,
  exportVehTaskSummaryInfo,
} from "@/services/api";
import { download, i18nSimpleKey, formatRoundNum } from "@/assets/ts/utils";
import {
  computeTime,
  formatDateTime,
  getDaysInMonth,
  getWeekInDay,
} from "@/assets/ts/dateTime";
import type { PageSizeType } from "@/components/types";
import { DatePicker } from "@/components/x-date-picker";
import xButton from "@/components/x-button.vue";
// import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xImage from "@/components/x-image.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";

const $t = i18nSimpleKey("vehTaskSummary");

const vehTaskSummaryRef = ref<any>();

const imageUrl = ref("");
const weekMonthCols = [
  {
    key: "orderNumber",
    title: $t("orderNumber"),
    width: "10",
  },
  {
    key: "deviceId",
    title: $t("deviceId"),
    width: "20",
    hover: true,
  },
  {
    key: "vin",
    title: "VIN",
    width: "40",
    hover: true,
  },
  {
    key: "moduleName",
    title: "型号",
    width: "20",
  },
  {
    key: "projectName",
    title: "项目",
    width: "30",
    hover: true,
  },
  {
    key: "areaName",
    title: "区域名",
    width: "30",
    hover: true,
  },
  {
    key: "entName",
    title: "企业",
    width: "30",
    hover: true,
  },
  {
    key: "dateText",
    title: "日期",
    width: "50",
    hover: true,
  },
  {
    key: "taskTimes",
    title: "任务数(次)",
    width: "20",
  },
  {
    key: "time",
    title: "时长(h)",
    width: "20",
  },
  {
    key: "mile",
    title: "里程(km)",
    width: "20",
  },
  {
    key: "area",
    title: "面积(㎡)",
    width: "20",
  },
  {
    key: "water",
    title: "用水(L)",
    width: "20",
  },
  {
    key: "electronic",
    title: "用电(kw.H)",
    width: "20",
  },
  {
    key: "garbage",
    title: "垃圾量(L)",
    width: "20",
  },
];
const dayCols = [
  ...weekMonthCols,
  { key: "opera", title: "操作", slots: "opera", width: "20" },
];
const table = reactive({
  cols: dayCols,
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

onMounted(() => {
  searchList();
  getVehList().then((res) => {
    searchForm.vehicNoOptions = res.map((v) => ({ label: v, value: v }));
  });
  compList().then((res) => {
    searchForm.companyOptions = res.map(({ entName, entId }) => ({
      label: entName!,
      value: entId!,
    }));
  });
  getProjectAndArea([]).then((res) => {
    searchForm.projectOptions.push(
      ...res.map(({ name, id }) => ({
        label: name!,
        value: id!,
      }))
    );
  });
  watch(
    () => searchForm.project,
    (newV) => {
      if (newV) {
        getProjectAndArea([newV]).then((res) => {
          searchForm.areaOptions = res.map(({ name, id }) => ({
            label: name!,
            value: id!,
          }));
        });
      } else {
        searchForm.area = "";
        searchForm.areaOptions = [];
      }
    }
  );
  getVehAndVinList().then((res) => {
    searchForm.vinOptions.push(
      ...res.map(({ vin }) => ({
        label: vin!,
        value: vin!,
      }))
    );
  });
  vehicleModelList({ limit: 100, page: 1 }).then(({ list }) => {
    searchForm.modelOptions.push(
      ...list.map(({ id, vehicleModel }) => ({
        label: vehicleModel!,
        value: id!,
      }))
    );
  });
});

// 查询
type OptItem = { label: string; value: string | number };
const searchForm = reactive({
  vehicNo: "",
  vehicNoOptions: [] as OptItem[],
  company: "",
  companyOptions: [] as OptItem[],
  project: "",
  projectOptions: [] as OptItem[],
  area: "",
  areaOptions: [] as OptItem[],
  vin: "",
  vinOptions: [] as OptItem[],
  model: "",
  modelOptions: [] as OptItem[],
  date: formatDateTime(Date.now(), "YYYY-MM-DD"),
  dateType: 1 as 1 | 2 | 3,
});

const nowInfo = {
  1: formatDateTime(Date.now(), "YYYY-MM-DD"),
  2: getWeekInDay(Date.now()).toString(),
  3: formatDateTime(Date.now(), "YYYY-MM"),
};
const toggleDateType = (value: "day" | "week" | "month") => {
  searchForm.dateType = { day: 1, week: 2, month: 3 }[value];
  searchForm.date = nowInfo[searchForm.dateType];
};
const reqDate = computed(
  () =>
    ({
      1: {
        workStart: `${searchForm.date} 00:00:00`,
        workEnd: `${searchForm.date} 23:59:59`,
      },
      2: (() => {
        const [start, end] = searchForm.date.split(",");
        return {
          workStart: `${start} 00:00:00`,
          workEnd: `${end} 23:59:59`,
          weekNo: Number(getWeekInDay(start, "weekIndex")) + 1,
        };
      })(),
      3: (() => {
        const days = getDaysInMonth(
          Number(searchForm.date.slice(0, 4)),
          Number(searchForm.date.slice(5, 7)) - 1
        );
        return {
          workStart: `${searchForm.date}-01 00:00:00`,
          workEnd: `${searchForm.date}-${days} 23:59:59`,
        };
      })(),
    }[searchForm.dateType])
);

const searchList = async () => {
  table.loading = true;
  table.cols = searchForm.dateType === 1 ? dayCols : weekMonthCols;
  const { totalCount, list } = await getVehTaskSummaryInfo({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    dateType: searchForm.dateType,
    ...reqDate.value,
    ...(searchForm.vehicNo ? { deviceId: searchForm.vehicNo } : {}),
    ...(searchForm.company ? { entId: searchForm.company } : {}),
    ...(searchForm.vin ? { vin: searchForm.vin } : {}),
    ...(searchForm.project ? { proId: searchForm.project } : {}),
    ...(searchForm.area ? { areaId: searchForm.area } : {}),
    ...(searchForm.model ? { vehicleModelId: searchForm.model } : {}),
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            ...item,
            time: formatRoundNum(item.taskDuration / 60, 1),
            mile: formatRoundNum(item.mileage, 1),
            area: formatRoundNum(item.finishedWorkArea, 1),
            water: formatRoundNum(item.usedWaterAmount, 1),
            electronic: formatRoundNum(item.usedElectricityAmount, 1),
            garbage: formatRoundNum(item.litter, 1),
            orderNumber: (index + 1).toString().padStart(3, "0"),
            workTime: `${item.workStart} - ${
              item.end ? item.workEnd : $t("now")
            }`,
            workMin: `${computeTime(item.workStart, item.workEnd)} min`,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
const resetSearchForm = () => {
  searchForm.vehicNo = "";
  searchForm.company = "";
  searchForm.project = "";
  searchForm.area = "";
  searchForm.vin = "";
  searchForm.model = "";
  searchForm.date = nowInfo[searchForm.dateType];
  searchList();
};
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchList();
};

const showImage = (record: any) => {
  if (record.picUrl) {
    imageUrl.value = record.picUrl;
  } else {
    Message("error", "该车辆无路径");
  }
};

// 导出
const exportExcel = async () => {
  try {
    const response = await exportVehTaskSummaryInfo({
      dateType: searchForm.dateType,
      ...reqDate.value,
      ...(searchForm.vehicNo ? { deviceId: searchForm.vehicNo } : {}),
      ...(searchForm.company ? { entId: searchForm.company } : {}),
      ...(searchForm.vin ? { vin: searchForm.vin } : {}),
      ...(searchForm.project ? { proId: searchForm.project } : {}),
      ...(searchForm.area ? { areaId: searchForm.area } : {}),
      ...(searchForm.model ? { vehicleModelId: searchForm.model } : {}),
    });
    download(response);
    Message("success", $t("exportSuccess"));
  } catch (error) {
    console.log(error);
    Message("error", $t("exportFail"));
  }
};
</script>

<style lang="scss" scoped>
.veh-task-summary {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px) {
      line-height: 36px;
    }
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include wh(100%, 76px) {
      margin-top: 20px;
    }
    .middle-left {
      width: 100%;
      @include fj {
        flex-direction: column;
      }
      &-top {
        @include fj;
        :deep(.x-select) {
          flex: 1;
        }
        :deep(.x-select + .x-select) {
          margin-left: 16px;
        }
      }
      &-bottom {
        @include fj;
        margin-top: 10px;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .table-opera {
      color: #5964fb;
      cursor: pointer;
    }
  }
}
</style>
