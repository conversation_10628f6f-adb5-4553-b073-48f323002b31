<template>
  <section
    class="remote-dev-ops"
    ref="remoteDevOpsRef"
  >
    <div id="container"></div>
    <ToolBox
      @handleClickIcon="handleClickIcon"
      :carItem="carItem"
    />
    <Sidebar ref="sideBarRef" />
    <MainContent
      ref="mainContentRef"
      @selectCar="selectCar"
    />
    <DataTable
      ref="dataTableRef"
      :deviceId="deviceId"
      @skip="handleSkip"
    />
    <TrafficLightCheckDialog
      v-model:show="trafficLightCheckDialogVisible"
      :trafficLightData="trafficLightData"
      :openWith="openWith"
      :deviceId="deviceId"
      @handleCameraClick="handleCameraClick"
      @confirm="sideBarRef.Refresh()"
      @previewItemClick="handlePreviewItemClick"
      @handleImageClick="handleImageClick"
    />
    <NavigationDialog v-model="navigationDialogVisible" />
    <carAlarms
      v-if="mapLoaded"
      :map="mapInfo.map"
      :Amap="mapInfo.Amap"
    />
    <VideoRtvs
      :left="videoData.left"
      :top="videoData.top"
    />

    <!-- 红绿灯图片预览 -->
    <div
      class="traffic-light-img-preview"
      v-show="isShowPreview && trafficLightCheckDialogVisible"
    >
      <div
        class="img-container"
        ref="imgContainerRef"
        @wheel.prevent="handleWheel"
        @mousedown.prevent="handleMouseDown"
        @mousemove.prevent="handleMouseMove"
        @mouseup.prevent="handleMouseUp"
        @mouseleave.prevent="handleMouseUp"
      >
        <el-image
          class="img-item"
          :style="{
            transform: isDragging
              ? `translate3d(${translateX}px, ${translateY}px, 0) scale(${scale})`
              : `translate3d(${translateX}px, ${translateY}px, 0) scale(${scale})`,
            cursor: isDragging ? 'grabbing' : 'grab',
            willChange: 'transform',
          }"
          :src="iamgeUrl"
          fit="cover"
        />
      </div>
      <div
        class="close-icon"
        @click="isShowPreview = false"
      >
        <x-icon
          width="20"
          height="20"
          name="switch_unChecked"
        />
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import SiteMarkerInfoWindow from "@/views/main/visualize/remoteDevOps/components/siteMarkerInfoWindow.vue";
import xIcon from "@/components/x-icon.vue";
import ToolBox from "./components/toolBox/toolBox.vue";
import Sidebar from "./components/sideBar/sideBar.vue";
import MainContent from "./components/mainContent/mainContent.vue";
import DataTable from "./components/dataTable/dataTable.vue";
import TrafficLightCheckDialog from "./components/toolDialog/traffic_light_check.vue";
import NavigationDialog from "./components/toolDialog/navugattion.vue";
import { createVNode, nextTick, onMounted, onUnmounted, provide, ref, render, watch, computed, h } from "vue";
import {
  getProAreaRange,
  getVehBlockList,
  getVehRouteList,
  getVehStationList,
  queryVehRecordingRoute,
  queryCustomArea,
  getTrafficLightList,
} from "@/services/api";
import {
  addCar,
  offBaseStatus,
  offFahrtroutes,
  onBaseStatus,
  onFahrtroutes,
  openChargingStatus,
} from "@/services/wsapi";
import { useMainStore } from "@/stores/main";
import Marker from "./components/map/marker.vue";
import SiteMarker from "./components/map/siteMarker.vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
import type { OverlayItemData, GetAreaRangeResponse, AreaRangeType, TagAreaInfo } from "./type";
import { sitesType } from "@/assets/ts/config";
import { polylineStyle, polylineTemplateStyle } from "@/services/wsconfig";
import PolylineNameMarker from "./components/map/polylineNameMarker.vue";
import carAlarms from "./components/carAlarms.vue";
import { betchXYZToGCJ02 } from "@/assets/ts/mapUtils";
import { ElLoading, ElMessage, ElMessageBox } from "element-plus";
import VideoRtvs from "./components/videoRtvs.vue";
import { getLocalStorage } from "@/assets/ts/storage";
const { socket, updateActiveCarIdVideo } = useMainStore();
const videoData = ref({
  left: "300px",
  top: "60px",
});
const remoteDevOpsRef = ref<HTMLElement | null>(null);
const iamgeUrl = ref<string>("");
const isShowPreview = ref<boolean>(false);
const mainContentRef = ref();
const dataTableRef = ref();
const sideBarRef = ref();
const trafficLightCheckDialogVisible = ref(false);
const navigationDialogVisible = ref(false);
/** overlay显示状态 */
const showOverlay = ref(false);
/** 请求中断器 */
let abortController: AbortController | undefined;
/** 请求获取的数据集合（site&block&route，trafficLightList） */
const mapOverlays = ref<OverlayItemData[]>([]);
const mapLoaded = ref(false);
const mapInfo = {
  map: null as unknown as AMap.Map,
  Amap: null as unknown as typeof AMap,
  markers: [] as any[],
  /** 覆盖物(场所&路线&区块&自定义区块) */
  overlays: [] as any[],
  /** 覆盖物名称(场所&路线&区块&自定义区块) */
  overlayNames: [] as any[],
  /** 高精地图范围 */
  areaRanges: [] as any[],
  /** 多边形编辑器 */
  polygonEditor: null as any,
  /** 正在编辑的多边形 */
  editingPolygon: null as OverlayItemData | null,
  /** 自定义绘制的多边形 */
  customPolygons: [] as any[],
  /** 标识区域多边形 */
  tagAreaPolygons: [] as AMap.Polygon[],
  /** 标识区域名称标记 */
  tagAreaNames: [] as AMap.Marker[],
  /** 路线 */
  polylines: [] as AMap.Polyline[],
  /** 行车路线 */
  fahrtroutes: [] as AMap.Polyline[],
};
/** 选中的车辆信息 */
const carItem = ref<any>({});
const deviceId = ref("");
/**  type === 0 车辆信息 */
const carInfo = ref<any>({});

// 使用 computed 来确保 deviceId 的响应式更新
const providedCarInfo = computed(() => ({
  ...carInfo.value,
  deviceId: deviceId.value,
}));

provide("carInfo", providedCarInfo);

const openWith = ref<"auto" | "click">("auto");
const trafficLightData = ref<_AlarmMessageStatusType | null>(null);
/** zIndex: 区块block(default: 10,active: 11) 车位park(default: 12, active: 13) 路线(default: 14, active: 15) 场所site(default: 30, active: 30) 高精地图范围(default: 5) */
const defaultPolygonOptions = {
  strokeWeight: 0,
  fillOpacity: 0.7,
  fillColor: "#9F9FA4",
  bubble: true,
  zIndex: 10,
  cursor: "pointer",
};

// 添加状态变量
const showRoute = ref(false);

// 图片缩放和拖拽相关的状态
const scale = ref(1);
const translateX = ref(0);
const translateY = ref(0);
const isDragging = ref(false);
const startX = ref(0);
const startY = ref(0);
const imgContainerRef = ref<HTMLElement | null>(null);
lazyAMapApiLoader().then((AMap) => {
  mapInfo.map = new AMap.Map("container", {
    zooms: [2, 26], // 地图缩放级别范围
    mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
  });
  mapInfo.Amap = AMap;
  mapLoaded.value = true;

  const scale = new AMap.Scale({
    position: {
      left: "300px",
      bottom: "400px",
    },
  });
  mapInfo.map.addControl(scale);
});

const handleImageClick = () => {
  if (trafficLightData.value) {
    isShowPreview.value = true;
  }
};

const handlePreviewItemClick = (item: any) => {
  const trafficLightMarker = mapInfo.overlays.find(
    (overlay) =>
      overlay.getExtData()?.overlayType === "trafficLight" && overlay.getExtData()?.lightId === item.trafficId
  );
  if (trafficLightMarker) {
    const position = trafficLightMarker.getPosition();
    mapInfo.map.setCenter(position);
    mapInfo.map.setZoom(20);
    const markerContent = trafficLightMarker.getContent() as HTMLElement;
    if (markerContent) {
      markerContent.style.transform = "scale(1.5)";
      markerContent.style.transition = "transform 0.3s ease-in-out";
      setTimeout(() => {
        markerContent.style.transform = "scale(1)";
      }, 3000);
    }
  }
};
const handleCameraClick = () => {
  videoData.value.left = "0px";
  videoData.value.top = "60px";
  handleClickIcon("video");
};

const handleSkip = (deviceId: string) => {
  mainContentRef.value?.handleSkipCar(deviceId);
};

const selectCar = (item: any) => {
  useMainStore().updateActiveCarId(item.deviceId);
  addCar([item.deviceId]);

  deviceId.value = item.deviceId;
  carItem.value = item;
  getMapInfo(item);

  // 显示选中的车辆点位
  showSelectedCarMarker();

  // 确保地图聚焦到选中的车辆
  setTimeout(() => {
    const selectedMarker = mapInfo.markers.find((marker) => marker.getExtData().key === item.deviceId);
    if (selectedMarker && mapInfo.map) {
      const position = selectedMarker.getPosition();
      // 使用 setZoomAndCenter 同时设置缩放级别和中心点，并禁用动画
      mapInfo.map.setFitView([selectedMarker], true, [0, 400, 0, 0]);
    }
  }, 300); // 延迟一点时间确保标记已经添加到地图上

  // 添加安全检查，确保引用存在
  if (sideBarRef.value) {
    sideBarRef.value.load();
  }

  // 添加安全检查，确保引用存在
  if (dataTableRef.value) {
    dataTableRef.value.reset();
  }
};

// 添加定时器相关变量
let positionUpdateTimer: number | null = null;

// 更新选中车辆位置的函数
const updateSelectedCarPosition = () => {
  if (!deviceId.value) return;

  const selectedMarker = mapInfo.markers.find((marker) => marker.getExtData().key === deviceId.value);
  if (selectedMarker) {
    // 确保 marker 在地图上可见
    selectedMarker.setMap(mapInfo.map);
    // 使用固定偏移量
    selectedMarker.setOffset(new mapInfo.Amap.Pixel(0, -5));

    // 获取车辆当前位置
    const position = selectedMarker.getPosition();
    // 更新地图中心点，使用动画效果
    mapInfo.map.setCenter(position);
  }
};

// 启动位置更新定时器
const startPositionUpdateTimer = () => {
  // 清除可能存在的旧定时器
  if (positionUpdateTimer) {
    clearInterval(positionUpdateTimer);
  }

  // 每100ms更新一次位置，使移动更流畅
  // positionUpdateTimer = window.setInterval(updateSelectedCarPosition, 100);
};

// 停止位置更新定时器
const stopPositionUpdateTimer = () => {
  if (positionUpdateTimer) {
    clearInterval(positionUpdateTimer);
    positionUpdateTimer = null;
  }
};

// 监听 deviceId 变化，启动或停止定时器
watch(
  () => deviceId.value,
  (newV) => {
    if (newV) {
      // 先清理所有覆盖物数据
      clearAllMapData();
      // 隐藏所有点位
      hideAllMarkers();
      // 显示选中车辆的点位
      showSelectedCarMarker();
      // 启动位置更新定时器
      startPositionUpdateTimer();
      Promise.all([getAreaRange(), getAreaOverlayData()]);
    } else {
      clearAllMapData();
      hideAllMarkers();
      // 停止位置更新定时器
      stopPositionUpdateTimer();
    }
  }
);

// 隐藏所有点位
const hideAllMarkers = () => {
  mapInfo.markers.forEach((marker) => {
    marker.setMap(null);
  });
};

// 显示选中的车辆点位
const showSelectedCarMarker = () => {
  // 先隐藏所有点位
  hideAllMarkers();

  // 如果没有选中车辆，直接返回
  if (!deviceId.value) return;

  // 找到选中的车辆标记并显示
  const selectedMarker = mapInfo.markers.find((marker) => marker.getExtData().key === deviceId.value);
  if (selectedMarker) {
    selectedMarker.setMap(mapInfo.map);

    // 使用固定偏移量，不再根据缩放级别计算
    selectedMarker.setOffset(new mapInfo.Amap.Pixel(0, -5));

    // 将地图中心移动到选中的车辆位置
    const position = selectedMarker.getPosition();
    mapInfo.map.setCenter(position);
  }
};

/** 切换overlay显示状态 */
const toggleOverlayDisplay = () => {
  showOverlay.value = !showOverlay.value;
  mapInfo.overlays.forEach((overlay) => {
    const overlayData = overlay.getExtData();
    if (overlayData.overlayType === "block") {
      if (showOverlay.value) {
        overlay.show();
      } else {
        overlay.hide();
      }
    }
  });
};

const handleClickIcon = (type: string) => {
  switch (type) {
    case "traffic_light":
      trafficLightCheckDialogVisible.value = true;
      openWith.value = "click";
      break;
    case "navigation":
      navigationDialogVisible.value = true;
      break;
    case "video":
      if (carInfo.value.online) {
        updateActiveCarIdVideo(carInfo.value.deviceId);
      } else {
        ElMessage.error("车辆不在线，无法查看实时视频");
      }
      break;
    case "block":
      toggleOverlayDisplay();
      break;
    case "router":
      showRoute.value = !showRoute.value;
      if (showRoute.value) {
        onFahrtroutes(deviceId.value);
        mapInfo.fahrtroutes?.forEach((polyline) => {
          polyline.show();
        });
      } else {
        mapInfo.fahrtroutes?.forEach((polyline) => {
          polyline.hide();
        });
      }
      break;
    case "remote_control": {
      if (!carItem.value?.vin) return ElMessage.error("车辆信息获取失败");
      const loadingInstance = ElLoading.service({ fullscreen: true, text: "正在开启远程控制" });
      fetch("https://127.0.0.1:8553/clientDaemonService/set/client/start", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          vin: carItem.value.vin,
          token: getLocalStorage("token"),
        }),
      })
        .then((res) => res.json())
        .then((data) => {
          console.log("返回结果:", data);
          if (data.status === "success") {
            ElMessage.success("正在开启远程控制");
          } else {
            ElMessage.error(`开启远程控制失败，原因：${data.message}`);
          }
        })
        .catch((err) => {
          ElMessageBox({
            title: "提示",
            type: "warning",
            customClass: "remote-control-message",
            dangerouslyUseHTMLString: true,
            message: `<div style="font-weight: bold">开启失败原因可能有：</div><div>1. 本地未启动该服务【ClientDaemonService.exe】</div><div>2. 证书是否被浏览器信任？<a style="color: blue" href="https://127.0.0.1:8553" target="_blank">【点击验证】</a></div><div style="margin-top: 10px;padding-left: 15px">如未信任，请点击<a style="color: blue" href="https://127.0.0.1:8553" target="_blank">【点击验证】</a>，</div><div style="padding-left: 15px">然后点击【高级】>【继续前往(127.0.0.1)】</div>`,
          });
        })
        .finally(() => loadingInstance.close());
      break;
    }
    default:
      break;
  }
};

const renderMarker = (
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: _BaseStatusType,
  focus: boolean
) => {
  render(
    createVNode(Marker, {
      status: {
        online: info.online,
        warn: [2, 3].includes(info.warnLevel),
        deviceId: info.deviceId,
      },
      focus,
      direction: info.direction,
    }),
    markerDiv
  );
};

const addMarker = (data: typeof socket.baseStatus) => {
  const map: any = mapInfo.map;
  const Amap: any = mapInfo.Amap;
  if (!map || !Amap) {
    return;
  }

  data?.forEach((v) => {
    let marker: any = mapInfo.markers.find((item) => item.getExtData().key === v.deviceId);

    if (marker) {
      const { markerDiv, markerFocus } = marker.getExtData();
      marker.setPosition([v.longitude, v.latitude]);
      renderMarker(marker, markerDiv, v, markerFocus);
      if (v.deviceId !== deviceId.value) {
        marker.setMap(null);
      } else {
        marker.setOffset(new Amap.Pixel(0, -5));
      }
    } else {
      const markerDiv = document.createElement("div");
      marker = new Amap.Marker({
        content: markerDiv,
        position: [v.longitude, v.latitude],
        anchor: "bottom-center",
        offset: new Amap.Pixel(0, -5),
        extData: {
          key: v.deviceId,
          markerDiv: markerDiv,
          markerFocus: false,
        },
      });
      renderMarker(marker, markerDiv, v, false);
      if (v.deviceId === deviceId.value) {
        map.add(marker);
        mapInfo.map.setFitView([marker], true, [0, 400, 0, 0]);
      }
      mapInfo.markers.push(marker);
    }
  });
};

/** 绘制交通灯 */
const drawTrafficLightList = () => {
  if (!mapInfo.Amap || !mapInfo.map) {
    console.warn("地图实例未初始化");
    return;
  }

  const MARKER_CONFIG = {
    zIndex: 30,
    cursor: "pointer",
    bubble: true,
    anchor: "bottom-center" as const,
  };

  const markers = mapOverlays.value
    .filter((item) => ["trafficLight"].includes(item.overlayType))
    .map((item: any) => {
      const position = [item.longitude, item.latitude];
      const markerDiv = document.createElement("div");

      const marker = new mapInfo.Amap.Marker({
        content: markerDiv,
        position: position as [number, number],
        title: item.overlayName,
        extData: item,
        ...MARKER_CONFIG,
      });

      try {
        render(
          createVNode(SiteMarker, {
            focus: false,
            icon: "traffic-light-color-icon",
          }),
          markerDiv
        );
      } catch (error) {
        console.error("渲染交通灯标记失败:", error);
      }

      return marker;
    });

  if (markers.length > 0) {
    mapInfo.overlays.push(...markers);
    mapInfo.map.add(markers);
  }
};
/** 绘制场所|车位 */
const drawSiteAndParkOverlay = async () => {
  let polygons: any[] = [];
  const markers = mapOverlays.value
    .filter((item) => ["park"].includes(item.overlayType))
    .map((item) => {
      let marker: any;
      let position = [item.sourceData?.site?.longitude, item.sourceData?.site?.latitude] as any;
      const path = item.points.map(({ longitude, latitude }: any) => [longitude, latitude]);

      if (path.length) {
        const polygon = new mapInfo.Amap.Polygon({
          ...defaultPolygonOptions,
          path,
          strokeWeight: 1,
          strokeColor: "#5964FB",
          strokeOpacity: 0.8,
          extData: item,
          zIndex: 12,
        } as AMap.PolygonOptions);
        polygons.push(polygon);

        position = polygon.getBounds()?.getCenter() as any;
      }
      const markerDiv = document.createElement("div");
      marker = new mapInfo.Amap.Marker({
        content: markerDiv,
        position: position as any,
        title: item.overlayName,
        extData: item,
        zIndex: 30,
        cursor: "pointer",
        bubble: true,
        anchor: "bottom-center",
      });
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: sitesType.find((site) => site.value === item.sourceData?.site?.stationType || "")?.icon,
        }),
        markerDiv
      );
      marker.on("click", async () => {
        const infoWindowDiv = document.createElement("div");
        const infoWindow = new mapInfo.Amap.InfoWindow({
          isCustom: true,
          content: infoWindowDiv,
          position: position,
          offset: [0, -38],
        });

        render(
          createVNode(SiteMarkerInfoWindow, {
            name: `${item.sourceData?.site?.stationName ?? ""} - ${
              item.sourceData?.site?.stationParkingRelEntityList?.at(0)?.parkName ?? ""
            }`,
            popupContainer: remoteDevOpsRef.value,
            window: infoWindow,
            type: sitesType.find((site) => site.value === item.sourceData?.site?.stationType || "")?.icon,
            id: item.sourceData?.site?.id,
            areaId: item.sourceData?.site?.proAreaId,
            stationPic: item.sourceData?.site?.stationPic,
            stationType: item.sourceData?.site?.stationType,
            stationRealTimeInfo: item.sourceData?.site?.stationRealTimeInfo,
            parkNo: item.sourceData?.park?.parkNo,
            stationList:
              item.sourceData?.site?.stationParkingRelEntityList?.map((station: any) => ({
                ...station,
                status: "idle",
                warn: false,
              })) || [],
          } as InstanceType<typeof SiteMarkerInfoWindow>["$props"]),
          infoWindowDiv
        );
        infoWindow.open(mapInfo.map, position);
        // if (item.icon === "map_battery") {
        //   const stationNoArr = item.stationList?.map((v: any) => v.chargingStationNo) || [];
        //   openChargingStatus(stationNoArr);
        // }
      });

      return marker;
    });

  mapInfo.overlays.push(...polygons, ...markers);
  mapInfo.map.add([...polygons, ...markers]);
};

/** 绘制路线|区块|录制路线 */
const drawRouteAndBlockOverlay = () => {
  const overlays = mapOverlays.value
    .filter((item) => ["block", "route", "csvRoute"].includes(item.overlayType))
    .map((item) => {
      let overlay;
      const path = item.points.map(({ longitude, latitude }: any) => [longitude, latitude]);
      if (item.overlayType === "block") {
        // 区块
        overlay = new mapInfo.Amap.Polygon({
          ...defaultPolygonOptions,
          path,
          extData: item,
        } as AMap.PolygonOptions);
        overlay.hide();
      } else if (item.overlayType === "route") {
        // 路线
        overlay = new mapInfo.Amap.Polyline({
          ...polylineTemplateStyle,
          path,
          zIndex: 14,
          cursor: "pointer",
          extData: item,
        });
      } else if (item.overlayType === "site") {
        // 场所
        const markerDiv = document.createElement("div");
        overlay = new mapInfo.Amap.Marker({
          content: markerDiv,
          position: [item.points[0].longitude, item.points[0].latitude] as any,
          title: item.overlayName,
          zIndex: 30,
          cursor: "pointer",
          draggable: false,
          bubble: true,
          extData: item,
          anchor: "bottom-center",
        });
        render(
          createVNode(SiteMarker, {
            focus: false,
            icon: sitesType.find((site) => site.value === item.sourceData?.stationType || "")?.icon,
          }),
          markerDiv
        );
      } else if (item.overlayType === "csvRoute") {
        // 录制路线
        overlay = new mapInfo.Amap.Polyline({
          ...polylineStyle,
          path,
          zIndex: 14,
          cursor: "pointer",
          extData: item,
        });
      }
      return overlay;
    });
  mapInfo.overlays.push(...overlays);
  mapInfo.map.add([...mapInfo.overlays, ...mapInfo.overlayNames]);
};

/** 清空 清扫区域 等覆盖物 */
const clearMapOverlay = () => {
  mapInfo.map.remove([...mapInfo.overlays, ...mapInfo.overlayNames]);
  mapInfo.overlays = [];
  mapInfo.overlayNames = [];
};

/** 清空所有地图数据 */
const clearAllMapData = () => {
  mapInfo.map.remove([
    ...mapInfo.overlays,
    ...mapInfo.overlayNames,
    ...mapInfo.areaRanges,
    ...(mapInfo.tagAreaPolygons || []),
    ...(mapInfo.tagAreaNames || []),
    ...(mapInfo.fahrtroutes || []),
  ]);
  mapInfo.overlays = [];
  mapInfo.overlayNames = [];
  mapInfo.areaRanges = [];
  mapInfo.tagAreaPolygons = [];
  mapInfo.tagAreaNames = [];
  mapInfo.fahrtroutes = [];
};

/** 获取地图数据 */
const getAreaOverlayData = async () => {
  mapOverlays.value = [];
  let customArea: _SiteInformation = {};
  queryCustomArea({ vehNo: deviceId.value }).then((res) => {
    const areas = res.reduce((prev: any, cur: any) => {
      const target = prev.find((v: any) => v.groupId === cur.groupId);
      const [[lng, lat]] = betchXYZToGCJ02([[cur.x, cur.y]], [cur.longitude, cur.latitude]);
      const point = {
        x: cur.x,
        y: cur.y,
        longitude: lng,
        latitude: lat,
        id: cur.id,
      } as TagAreaInfo["points"][number];

      if (target) {
        target.points.push(point);
      } else {
        prev.push({
          groupId: cur.groupId,
          name: cur.name,
          pointType: cur.pointType,
          originPoint: [cur.longitude, cur.latitude],
          points: [point],
        });
      }
      return prev;
    }, [] as TagAreaInfo[]);

    // 转换自定义区域为OverlayItemData类型
    const tagAreas = areas.map(
      (area: TagAreaInfo) =>
        ({
          overlayType: "tagArea",
          overlayId: area.groupId?.toString(),
          overlayName: area.name,
          points: area.points.map((p) => ({
            longitude: p.longitude,
            latitude: p.latitude,
            x: p.x,
            y: p.y,
          })),
          sourceData: area,
        } as OverlayItemData)
    );
    mapOverlays.value.push(...tagAreas);
  });

  await Promise.all([
    getVehStationList(deviceId.value, { signal: abortController?.signal }),
    getVehRouteList(deviceId.value, { signal: abortController?.signal }),
    getVehBlockList(deviceId.value, { signal: abortController?.signal }),
    queryVehRecordingRoute({ id: deviceId.value }, { signal: abortController?.signal }),
    getTrafficLightList({ vehNo: deviceId.value }),
  ]).then(([siteList, routeList, blockList, csvRouteList, trafficLightEntityList]) => {
    const sites = siteList.map(
      (site) =>
        ({
          overlayType: "site",
          overlayId: site.id,
          overlayName: site.stationName,
          points: [{ longitude: site.longitude, latitude: site.latitude }],
          sourceData: site,
        } as OverlayItemData)
    );
    const parks = siteList.flatMap(
      (site) =>
        site.stationParkingRelEntityList?.map(
          (park) =>
            ({
              overlayType: "park",
              overlayId: park.id,
              overlayName: park.parkName,
              points:
                park.location
                  .split(";")
                  .slice(0, -1)
                  .map((item) => item.split(","))
                  .map(([longitude, latitude]) => ({ longitude, latitude })) || [],
              sourceData: { site, park },
            } as OverlayItemData)
        ) || []
    );
    const routes = routeList.map(
      (route) =>
        ({
          overlayType: "route",
          overlayId: route.id,
          overlayName: route.routeName,
          points: route.gps,
          sourceData: route,
        } as OverlayItemData)
    );
    const blocks = blockList.map(
      (block) =>
        ({
          overlayType: "block",
          overlayId: block.id,
          overlayName: block.blockName,
          points: block.areaBlockGpsList,
          sourceData: block,
        } as OverlayItemData)
    );
    const csvRoutes = csvRouteList.map(
      (csvRoute) =>
        ({
          overlayType: "csvRoute",
          overlayId: csvRoute.id,
          overlayName: csvRoute.routeName,
          points: csvRoute.recordingGpsList,
          sourceData: csvRoute,
        } as OverlayItemData)
    );

    // 红绿灯
    const trafficLightList = trafficLightEntityList.map((item: any) => {
      const firstPoint = item.location.split(";")[0];
      const [longitude, latitude] = firstPoint.split(",").map(Number);

      return {
        overlayType: "trafficLight",
        ...item,
        longitude,
        latitude,
      };
    });

    mapOverlays.value.push(...routes, ...blocks, ...sites, ...parks, ...csvRoutes, ...trafficLightList);
    clearMapOverlay();
    drawRouteAndBlockOverlay();
    drawSiteAndParkOverlay();
    drawTagAreaList();
    drawTrafficLightList();
  });
};

/** 渲染标识禁行区  临停区 区域 */
const drawTagAreaList = async () => {
  if (!mapInfo.map || !mapInfo.Amap) return;
  const tagAreaArr = ref<TagAreaInfo[]>([]);
  const _overlays = {
    polygons: [] as AMap.Polygon[],
    markers: [] as AMap.Marker[],
  };
  const tagAreaList = mapOverlays.value.filter((item) => item.overlayType === "tagArea");

  tagAreaArr.value = tagAreaList.map((item) => {
    const info = JSON.parse(JSON.stringify(item.sourceData)) as TagAreaInfo;
    let polygon: AMap.Polygon | undefined;
    if (!info.points || info.points.length < 3) {
      console.warn("Invalid points for tagArea", info);
      return info;
    }

    const path = info.points.map((p) => new mapInfo.Amap.LngLat(p.longitude, p.latitude));
    polygon = createPolygon({ path: path });
    if (info.pointType === 0) {
      if (path.length >= 2) {
        const startPoint = path[0];
        const endPoint = path[1];
        const arrow = createArrowPolyline({ path: [startPoint, endPoint] });
        if (arrow) {
          info.arrowLineInstance = arrow;
          mapInfo.map.add(arrow);
        }
      }
    }
    if (polygon) {
      const center = polygon.getBounds()?.getCenter() as AMap.LngLat;
      const marker = createTagAreaNameMarker(center, info, false);
      info.overlayInstance = polygon;

      if (marker) {
        info.nameMarkerInstance = marker;
        polygon.setExtData({ info: info });
        marker.setExtData({ info: info });
        _overlays.polygons.push(polygon);
        _overlays.markers.push(marker);
        mapInfo.map.add([polygon, marker]);
      } else {
        _overlays.polygons.push(polygon);
        polygon.setExtData({ info: info });
        mapInfo.map.add(polygon);
      }
    }

    return info;
  });
  mapInfo.tagAreaPolygons = _overlays.polygons;
  mapInfo.tagAreaNames = _overlays.markers;
};

/** 渲染标识区域名称Marker */
const createTagAreaNameMarker = (position: AMap.LngLat, info: TagAreaInfo, selected: boolean = false) => {
  if (!mapInfo.map || !mapInfo.Amap) return null;

  const markerDiv = document.createElement("div");
  const marker = new mapInfo.Amap.Marker({
    zIndex: 40,
    anchor: "top",
    bubble: true,
    cursor: "pointer",
    position: position,
    content: markerDiv,
    zooms: [14, 26],
    extData: {
      markerDiv: markerDiv,
      info: info,
    },
  });
  let nameClass = "tag-area";

  if (info.pointType === 0) {
    nameClass += " temporary-stop";
  } else if (info.pointType === 1) {
    nameClass += " no-entry";
  } else if (info.pointType === 2) {
    nameClass += " no-sweep";
  }

  try {
    render(
      createVNode(PolylineNameMarker, {
        name: info.name || "标识区域",
        type: selected ? "dark" : "light",
        class: nameClass,
      }),
      markerDiv
    );
  } catch (error) {
    console.error("Failed to render tag area marker:", error);
    markerDiv.innerHTML = `<div class="${nameClass}">${info.name || "标识区域"}</div>`;
  }

  return marker;
};

/** 创建多边形 */
const createPolygon = (opts: Partial<AMap.PolygonOptions>) => {
  if (!mapInfo.Amap) return undefined;

  const polygonStyle = {
    strokeWeight: 3,
    strokeColor: "#242859",
    fillOpacity: 0.1,
    fillColor: "#242859",
    zIndex: 10,
    cursor: "pointer",
    bubble: true,
  } as AMap.PolygonOptions;

  try {
    const polygon = new mapInfo.Amap.Polygon({
      ...polygonStyle,
      path: [],
      ...opts,
    });

    return polygon;
  } catch (error) {
    console.error("Failed to create polygon:", error);
    return undefined;
  }
};

/** 创建箭头线段 */
const createArrowPolyline = (opts: AMap.PolylineOptions) => {
  if (!mapInfo.Amap) return null;

  const arrowLineStyle = {
    strokeWeight: 6,
    strokeColor: "#F41515",
    strokeOpacity: 1,
    showDir: true,
    dirColor: "#fff",
    zIndex: 15,
  } as AMap.PolylineOptions;

  const arrow = new mapInfo.Amap.Polyline({
    ...arrowLineStyle,
    ...opts,
  });

  return arrow;
};

/** 获取高精地图信息 */
const getAreaRange = async () => {
  const res = await getProAreaRange({ vehNo: deviceId.value });
  const grouped = res.reduce((accumulator, item) => {
    const key = `${item.xmlId}_${item.pointType}`;
    if (!accumulator[key]) {
      accumulator[key] = [];
    }
    accumulator[key].push(item);
    return accumulator;
  }, {} as any);
  const list: GetAreaRangeResponse[] = Object.values(grouped);

  drawAreaRange(
    list.map((item) => ({
      id: item[0].id,
      proAreaId: item[0].proAreaId,
      xmlId: item[0].xmlId,
      pointType: item[0].pointType,
      points: item.map(({ longitude, latitude }) => ({ longitude, latitude })),
    }))
  );
};

/** 渲染高精地图范围 */
const drawAreaRange = (list: AreaRangeType[]) => {
  const polygons = list.map((item) => {
    const polygon = new mapInfo.Amap.Polygon({
      path: item.points.map(({ longitude, latitude }: any) => [longitude, latitude]),
      strokeWeight: 0,
      fillOpacity: 0.4,
      fillColor: item.pointType === 1 ? "#F41515" : "#27D4A1",
      zIndex: 5,
      bubble: true,
      extData: { ...item, overlayType: "areaRange" },
    });
    return polygon;
  });
  mapInfo.areaRanges = polygons;
  mapInfo.map.add(polygons);
};

const getMapInfo = async (item: any) => {
  const res = await getProAreaRange({ vehNo: item.deviceId });
};

watch(
  () => socket.baseStatus,
  (newV) => {
    if (newV) {
      console.debug("%c gps:", "color: red", newV);
      addMarker(newV);
    }
  }
);

/**车辆消息 */
watch(
  () => socket.alarmMessageStatus,
  (newV) => {
    if (newV?.device_id === deviceId.value && newV?.messageType === 6 && newV?.alert) {
      console.debug("%c 车辆消息:", "color: red", newV);
      [trafficLightData.value, iamgeUrl.value, openWith.value] = [newV, newV.picUrl, "auto"];
      nextTick(() => {
        trafficLightCheckDialogVisible.value = true;
        isShowPreview.value = true;
      });
    }
  }
);

/** type === 0 车辆信息 */
watch(
  () => socket.allStatus,
  (newV) => {
    if (newV) {
      console.debug("%c 车辆信息:", "color: red", newV);
      const car = newV.find((item: any) => item.deviceId === deviceId.value);
      Object.assign(carInfo.value, car);
      if (car?.workStatus === 1) {
        mapInfo.fahrtroutes?.forEach((polyline) => {
          polyline.hide();
        });
      }
    }
  }
);

/** 行车路线 */
watch(
  () => socket.fahrtroutes,
  (newV) => {
    if (newV?.deviceId === deviceId.value) {
      console.debug("%c 行车路线:", "color: red", newV);

      // 获取路径数据
      const traveledPath = betchXYZToGCJ02(
        newV.traveled_path_waypoints?.map((v) => [v.point.pose_xyz.x, v.point.pose_xyz.y]) ?? [],
        [newV.lonOrigin, newV.latOrigin]
      ) as [number, number][];

      const untraveledPath = betchXYZToGCJ02(
        newV.untraveled_path_waypoints?.map((v) => [v.point.pose_xyz.x, v.point.pose_xyz.y]) ?? [],
        [newV.lonOrigin, newV.latOrigin]
      ) as [number, number][];
      if (mapInfo.fahrtroutes?.length >= 2) {
        const [traveledPline, untraveledPline] = mapInfo.fahrtroutes;
        traveledPline.setPath(traveledPath);
        untraveledPline.setPath(untraveledPath);
      } else {
        if (mapInfo.fahrtroutes?.length > 0) {
          mapInfo.fahrtroutes.forEach((polyline) => mapInfo.map.remove(polyline));
          mapInfo.fahrtroutes = [];
        }
        const traveledPline = new mapInfo.Amap.Polyline({
          path: traveledPath,
          strokeColor: "#62658F",
          strokeWeight: 6,
          strokeOpacity: 1,
          zIndex: 15,
          bubble: true,
          showDir: true,
          dirColor: "#fff",
          extData: { type: "traveled" },
        });
        const untraveledPline = new mapInfo.Amap.Polyline({
          path: untraveledPath,
          strokeColor: "#5964FB",
          strokeWeight: 6,
          strokeOpacity: 1,
          bubble: true,
          showDir: true,
          dirColor: "#fff",
          extData: { type: "untraveled" },
        });

        mapInfo.map.add([traveledPline, untraveledPline]);

        mapInfo.fahrtroutes = [traveledPline, untraveledPline];
      }

      if (showRoute.value) {
        mapInfo.fahrtroutes.forEach((polyline) => polyline.show());
      } else {
        mapInfo.fahrtroutes.forEach((polyline) => polyline.hide());
      }
    }
  }
);

// 处理鼠标滚轮缩放
const handleWheel = (e: WheelEvent) => {
  e.preventDefault();
  const delta = e.deltaY > 0 ? -0.1 : 0.1;
  const newScale = Math.max(0.1, Math.min(3, scale.value + delta));
  const rect = imgContainerRef.value?.getBoundingClientRect();
  if (!rect) return;

  const mouseX = e.clientX - rect.left;
  const mouseY = e.clientY - rect.top;
  const scaleChange = newScale - scale.value;
  const translateXChange = (mouseX - translateX.value) * (scaleChange / scale.value);
  const translateYChange = (mouseY - translateY.value) * (scaleChange / scale.value);

  scale.value = newScale;
  translateX.value -= translateXChange;
  translateY.value -= translateYChange;
};

// 处理鼠标按下开始拖拽
const handleMouseDown = (e: MouseEvent) => {
  isDragging.value = true;
  const rect = imgContainerRef.value?.getBoundingClientRect();
  if (!rect) return;

  startX.value = e.clientX - translateX.value;
  startY.value = e.clientY - translateY.value;

  // 添加全局事件监听
  document.addEventListener("mousemove", handleMouseMove);
  document.addEventListener("mouseup", handleMouseUp);
};

// 处理鼠标移动拖拽
const handleMouseMove = (e: MouseEvent) => {
  if (!isDragging.value) return;

  requestAnimationFrame(() => {
    translateX.value = e.clientX - startX.value;
    translateY.value = e.clientY - startY.value;
  });
};

// 处理鼠标释放结束拖拽
const handleMouseUp = () => {
  if (!isDragging.value) return;
  isDragging.value = false;

  // 移除全局事件监听
  document.removeEventListener("mousemove", handleMouseMove);
  document.removeEventListener("mouseup", handleMouseUp);
};

// 重置图片状态
const resetImageState = () => {
  // 添加过渡效果类
  const imgElement = imgContainerRef.value?.querySelector(".img-item");
  if (imgElement) {
    imgElement.classList.add("resetting");
  }

  // 重置所有状态
  scale.value = 1;
  translateX.value = 0;
  translateY.value = 0;

  // 移除过渡效果类
  setTimeout(() => {
    if (imgElement) {
      imgElement.classList.remove("resetting");
    }
  }, 300);
};

// 处理键盘事件
const handleKeyDown = (e: KeyboardEvent) => {
  if (e.key === "Escape") {
    resetImageState();
  }
};

onMounted(async () => {
  // 确保地图加载完成后才执行后续操作
  if (!mapLoaded.value) {
    const checkMapLoaded = setInterval(() => {
      if (mapLoaded.value) {
        clearInterval(checkMapLoaded);
        initializeComponents();
      }
    }, 100);

    // 设置超时，防止无限等待
    setTimeout(() => {
      clearInterval(checkMapLoaded);
    }, 10000);
  } else {
    initializeComponents();
  }

  // 在组件挂载时添加键盘事件监听
  document.addEventListener("keydown", handleKeyDown);
});

// 初始化组件
const initializeComponents = () => {
  // 等待ws连接完成
  const reqWebsocketData = () => {
    setTimeout(() => {
      Promise.all([onBaseStatus(), onFahrtroutes(deviceId.value)]).catch(() => {
        // ws还在连接中
      });
    }, 200);
  };
  reqWebsocketData();

  // 如果之前是显示状态,重连后重新订阅
  if (showRoute.value) {
    onFahrtroutes(deviceId.value);
  }
};

onUnmounted(() => {
  offBaseStatus();
  offFahrtroutes(deviceId.value);
  stopPositionUpdateTimer();

  // 在组件卸载时移除键盘事件监听
  document.removeEventListener("keydown", handleKeyDown);
});
</script>

<style lang="scss" scoped>
.remote-dev-ops {
  #container {
    @include wh(100%);
  }
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #fff;
  .traffic-light-img-preview {
    position: absolute;
    top: 0;
    left: 0;
    width: calc(100% - 390px);
    height: 100%;
    overflow: hidden;
    z-index: 1000;
    border-radius: 20px;

    .img-container {
      position: relative;
      width: 100%;
      height: 100%;
      overflow: hidden;
      user-select: none;
      touch-action: none;
      -webkit-user-drag: none;
    }

    .img-item {
      position: absolute;
      transform-origin: center center;
      will-change: transform;
      backface-visibility: hidden;
      -webkit-backface-visibility: hidden;
      -webkit-perspective: 1000;
      perspective: 1000;
      max-width: none;
      max-height: none;
      pointer-events: none;
      cursor: pointer;

      // 添加重置时的过渡效果
      &.resetting {
        transition: all 0.3s ease-out;
      }

      &:not(.resetting) {
        transition: transform 0.1s ease-out;
      }
    }
    .close-icon {
      position: absolute;
      top: 20px;
      right: 20px;
      cursor: pointer;
      z-index: 1000;
      width: 24px;
      height: 24px;
      background: rgba(0, 0, 0, 0.3);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
<style lang="scss">
.remote-control-message {
  .el-message-box__container {
    align-items: flex-start;
  }
}
</style>
