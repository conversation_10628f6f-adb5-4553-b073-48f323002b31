<template>
  <section class="canvas">
    <canvas id="bg-canvas" ref="bgCanvasDom"></canvas>
    <canvas
      id="car-canvas"
      ref="carCanvasDom"
      style="position: absolute; top: 0; left: 0"
    ></canvas>
  </section>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import mapImageSrc from "@/assets/images/image.png";
import carImageSrc from "@/assets/icons/map_car.svg";

let canvasWidth: number;
let canvasHeight: number;
const bgCanvasDom = ref<HTMLCanvasElement>();
const carCanvasDom = ref<HTMLCanvasElement>();
let bgCtx: CanvasRenderingContext2D;
let carCtx: CanvasRenderingContext2D;
let bgImage: HTMLImageElement;
let carImage: HTMLImageElement;

// 图片坐标
let pathIndex = 0;
const paths = [
  { x: 750, y: 570 },
  { x: 760, y: 580 },
  { x: 770, y: 590 },
  { x: 780, y: 600 },
  { x: 790, y: 610 },
  { x: 800, y: 620 },
  { x: 810, y: 630 },
  { x: 820, y: 640 },
  { x: 830, y: 650 },
];

onMounted(() => {
  setCanvasSize();
  bgCtx = bgCanvasDom.value!.getContext("2d")!;
  carCtx = carCanvasDom.value!.getContext("2d")!;
  // bgCtx.imageSmoothingEnabled = false;
  // carCtx.imageSmoothingEnabled = false;

  loadMapImage(() => loadCarImage(animate));
});

const setCanvasSize = () => {
  const dpr = window.devicePixelRatio || 1;
  const { width, height } = bgCanvasDom.value!.getBoundingClientRect();
  canvasWidth = width * dpr;
  canvasHeight = height * dpr;
  bgCanvasDom.value!.width = canvasWidth;
  bgCanvasDom.value!.height = canvasHeight;
  carCanvasDom.value!.width = canvasWidth;
  carCanvasDom.value!.height = canvasHeight;
};

const loadMapImage = (fn: Function) => {
  bgImage = new Image();
  bgImage.src = mapImageSrc;
  bgImage.onload = function () {
    fn.call(undefined);
  };
};
const loadCarImage = (fn: Function) => {
  carImage = new Image();
  carImage.src = carImageSrc;
  carImage.onload = function () {
    fn.call(undefined);
  };
};

const drawImage = () => {
  // ----------------绘制地图
  // 整个图片矩形范围[0, 0, bgImage.width, bgImage.height]
  // 整个画布矩形范围[0, 0, canvasWidth, canvasHeight]

  // 以小车为中心的画布  在  整个图片当中的坐标
  const { x: carX, y: carY } = paths[pathIndex];
  let leftTopX = carX - canvasWidth / 2,
    leftTopY = carY - canvasHeight / 2;

  // 若  以小车为中心的画布  超越  图片边界，则进行修正
  let digressX = 0; // 小车相对画布中心偏离X
  let digressY = 0; // 小车相对画布中心偏离Y
  const { width, height } = bgImage;
  if (leftTopX < 0) {
    digressX = leftTopX;
    leftTopX = 0;
  } else if (leftTopX + canvasWidth > width) {
    digressX = leftTopX + canvasWidth - width;
    leftTopX -= digressX;
  }
  if (leftTopY < 0) {
    digressY = leftTopY;
    leftTopY = 0;
  } else if (leftTopY + canvasHeight > height) {
    digressY = leftTopY + canvasHeight - height;
    leftTopY -= digressY;
  }

  // 源图像, 源图像的矩形选区, 目标画布的矩形区域
  bgCtx.drawImage(
    bgImage,
    leftTopX,
    leftTopY,
    canvasWidth,
    canvasHeight,
    0,
    0,
    canvasWidth,
    canvasHeight
  );

  // ----------------绘制小车
  const { width: carWidth, height: carHeight } = carImage;
  carCtx.drawImage(
    carImage,
    canvasWidth / 2 + digressX - carWidth / 2,
    canvasHeight / 2 + digressY - carHeight / 2,
    carWidth,
    carHeight
  );
};

const carSpeed = 1;
let times = 20;
const animate = () => {
  if (times === 0) {
    carCtx.clearRect(0, 0, canvasWidth, canvasHeight); // 清除小车画布
    bgCtx.clearRect(0, 0, canvasWidth, canvasHeight); // 清除地图画布
    drawImage();
    pathIndex += carSpeed; // 更新小车的位置
    if (pathIndex === paths.length) pathIndex = 0;
    times = 20;
  }
  times--;

  requestAnimationFrame(animate);
};

// const drawPaths = () => {
//   bgCtx.beginPath();
//   paths.forEach((item, index) => {
//     if (index + 1 < paths.length) {
//       bgCtx.moveTo(item.x, item.y);
//       bgCtx.lineTo(paths[index + 1].x, paths[index + 1].y);
//     } else {
//       bgCtx.moveTo(item.x, item.y);
//       bgCtx.lineTo(paths[0].x, paths[0].y);
//     }
//   });
//   bgCtx.stroke();
// };
</script>

<style lang="scss" scoped>
.canvas {
  @include ct-p;
  position: relative;
  @include wh(600px, 400px);
  #bg-canvas {
    @include wh(100%);
  }
}
</style>
