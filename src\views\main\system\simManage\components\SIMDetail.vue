<template>
  <x-modal
    :visible="props.show"
    @update:visible="updateVisible"
    :title="$t('simDetail')"
    width="500px"
    height="390px"
  >
    <div ref="contentRef" class="content">
      <div class="sim-detail">
        <div class="top" :style="detailStyle">
          <div class="first-row">
            <div class="simId">
              {{ form.simId }}<br />
              <span
                class="simIdText"
                :style="{
                  color:
                    form.status === 1
                      ? 'rgba(255, 255, 255, 0.8)'
                      : 'rgb(94, 94, 94)',
                }"
                >{{ $t("simNo") }}</span
              >
            </div>
            <div class="netOperatorText">{{ form.netOperatorText }}</div>
          </div>
          <div class="second-row">
            <div class="registerDateText">{{ form.registerDateText }}</div>
            <div
              class="statusText"
              :style="{
                backgroundColor:
                  form.status === 1
                    ? 'rgba(255, 255, 255, 0.4)'
                    : 'rgba(16, 22, 55, 0.7)',
              }"
            >
              {{ form.statusText }}
            </div>
          </div>
        </div>
        <div class="bottom">
          <div>
            <span style="color: rgb(159, 159, 164)"
              >{{ $t("vehicleNo") }}:</span
            >
            <span>{{ form.vehicleNo }}</span>
          </div>
          <div>
            <span style="color: rgb(159, 159, 164)"
              >{{ $t("enterprise") }}: &nbsp;&nbsp;&nbsp;&nbsp;</span
            ><span>{{ form.entName }}</span>
          </div>
        </div>
      </div>
    </div>
  </x-modal>
</template>
<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import { getSimDetail } from "@/services/api";
import xModal from "@/components/x-modal";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("simManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const contentRef = ref<any>();

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const form = reactive({
  netOperatorText: "",
  registerDateText: "",
  statusText: "",
  status: 0,
  entName: "",
  simId: "",
  vehicleNo: "",
});

const detailStyle = reactive({
  color: "",
  backgroundImage: "",
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const {
        netOperatorText,
        registerDateText,
        entName,
        simId,
        vehicleNo,
        statusText,
        status,
      } = await getSimDetail(props.id);
      form.entName = entName;
      form.netOperatorText = netOperatorText;
      form.registerDateText = registerDateText;
      form.simId = simId;
      form.vehicleNo = vehicleNo;
      form.statusText = statusText;
      form.status = status;
      detailStyle.color =
        form.status === 1 ? "rgb(255, 255, 255)" : "rgb(56, 56, 56)";
      const bg =
        form.status === 1
          ? new URL("/src/assets/images/sim_detail.png", import.meta.url).href
          : new URL("/src/assets/images/sim_detail_unused.png", import.meta.url)
              .href;
      detailStyle.backgroundImage = `url(${bg})`;
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  display: flex;
  margin: auto;
  width: 390px;
  height: 100%;
  overflow-y: auto;
  overscroll-behavior: contain;
  -ms-scroll-chaining: contain;
  padding: 10px;
  .sim-detail {
    width: 100%;
    height: 280px;
    border-radius: 10px;
    background-color: rgba(229, 237, 255, 0.5);
    box-shadow: 0 0 10px rgba(155, 162, 190, 0.4);
    display: flex;
    flex-direction: column;
    .top {
      width: 370px;
      height: 175px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      color: rgba(255, 255, 255);
      .first-row {
        display: flex;
        justify-content: space-between;
        .simId {
          margin-left: 15px;
          margin-top: 18px;
          font-size: 20px;
          line-height: 18px;
          .simIdText {
            font-size: 14px;
          }
        }
        .netOperatorText {
          margin-right: 20px;
          margin-top: 18px;
          font-size: 14px;
        }
      }
      .second-row {
        display: flex;
        justify-content: space-between;
        .registerDateText {
          display: flex;
          flex-direction: column;
          justify-content: center;
          margin-left: 15px;
          margin-bottom: 15px;
          font-size: 14px;
        }
        .statusText {
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 15px;
          margin-bottom: 15px;
          width: 80px;
          height: 32px;
          border-radius: 40px;
          color: rgb(255, 255, 255);
        }
      }
    }
    .bottom {
      display: flex;
      flex: 1;
      padding: 20px 0;
      flex-direction: column;
      margin-left: 9px;
      justify-content: space-evenly;
    }
  }
}
</style>
