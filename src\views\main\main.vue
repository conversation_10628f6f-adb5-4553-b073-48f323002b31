<template>
  <section class="main">
    <div class="main-top">
      <div
        class="main-top-left"
        @dblclick="isMobile && toggleFullScreen()"
      >
        <img
          v-if="isJieyaPlatform"
          src="@/assets/images/logo_jieya.png"
          alt=""
          srcset=""
        />
        <img
          v-else
          src="@/assets/images/logo.png"
          alt=""
          srcset=""
        />
        <span>
          {{ isJieyaPlatform ? $t("jiaya") : `Auto City — ${$t("cszg")}2.0` }}
        </span>
      </div>
      <!-- 告警音效 -->
      <audio
        ref="audioRef"
        :muted="!audio.status"
        loop
      >
        <source src="@/assets/audios/alarm_audio.mp3" />
      </audio>
      <div class="main-top-right">
        <!-- 看板 -->
        <div
          v-if="urlList.includes('sys/dataCalculateSort')"
          class="go-screen"
          @click="router.push({ name: 'screen' })"
        >
          <x-icon
            name="main_screen"
            width="16"
            height="16"
          />
          <span>看板</span>
        </div>
        <!-- 音效开关 -->
        <div
          class="fault-sound"
          @click="toggleSound"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <x-icon
              :name="audio.status ? 'alarm_sound_on' : 'alarm_sound_off'"
              width="16"
              height="16"
            />
            <template #content>
              <div class="system-top-button-hover-popover">
                {{ $t("alarmSound") }}
              </div>
            </template>
          </x-popover>
        </div>
        <!-- 告警灯 -->
        <div
          class="fault-tip"
          @click="toAlarmStatistics"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <img
              v-if="alarmState.number > 0"
              src="@/assets/images/alarm_light_on.gif"
              alt=""
            />
            <x-icon
              v-else
              name="alarm_light_off"
              width="20"
              height="20"
            />
            <template #content>
              <div class="system-top-button-hover-popover">
                {{ $t("alarmLight") }}
              </div>
            </template>
          </x-popover>
        </div>
        <!-- 个人中心 -->
        <x-popover
          trigger="hover"
          placement="bottom"
        >
          <div>
            <x-popover
              trigger="click"
              placement="bottom"
              v-model:visible="userMenuVisible"
            >
              <template #content>
                <div class="user-menu">
                  <div
                    v-for="(item, index) in userMenu"
                    :key="index"
                  >
                    <div
                      class="user-menu-item"
                      @click="handleOpera(item, index)"
                    >
                      {{ item.title }}
                    </div>
                  </div>
                </div>
              </template>
              <div class="user">
                <div class="user-avatar">
                  <div
                    class="user-head-img"
                    :style="{ backgroundImage: 'url(' + headImgUrl + ')' }"
                  ></div>
                </div>
              </div>
            </x-popover>
          </div>
          <template #content>
            <div class="system-top-button-hover-popover">
              {{ $t("personalCenter") }}
            </div>
          </template>
        </x-popover>
      </div>
      <personal v-model:show="userMenu[0].show" />
      <modifyPassword v-model:show="userMenu[1].show" />
    </div>
    <div class="main-bottom">
      <div :class="['main-bottom-left', { open: menusOpen }]">
        <div class="menus">
          <div
            class="menus-item"
            v-for="(item, index) in menus"
            :key="index"
          >
            <!-- 收缩时菜单项 -->
            <x-popover
              v-show="!menusOpen"
              v-model:visible="item.hoverVisible"
              @visibleChange="(status) => status && (item.clickVisible = false)"
              trigger="hover"
              placement="rightBottom"
            >
              <template #content>
                <div class="menus-parent-hover-popover">{{ item.title }}</div>
              </template>
              <x-popover
                v-model:visible="item.clickVisible"
                @visibleChange="handleClickPop($event, index)"
                trigger="click"
                placement="rightBottom"
              >
                <template #content>
                  <div class="menus-parent-click-popover">
                    <div
                      class="menus-parent-click-popover-item"
                      v-for="(_popItem, _popIndex) in item.children"
                      :key="_popIndex"
                      @click.stop="handlePopItemClick(item, _popItem.name)"
                    >
                      <span class="menus-parent-click-popover-item-point"></span>
                      <span class="menus-parent-click-popover-item-title">
                        {{ _popItem.title }}
                      </span>
                    </div>
                  </div>
                </template>
                <div
                  :class="[
                    'menus-parent',
                    {
                      enable: curRouteArr.includes(item.name),
                      open: item.open && menuControl.menuShow,
                    },
                  ]"
                >
                  <x-icon
                    :name="`menu_${item.name}${curRouteArr.includes(item.name) ? '_enable' : ''}`"
                    width="16"
                    height="16"
                  />
                  <span
                    class="menus-parent-title"
                    v-show="menuControl.menuShow"
                    >{{ item.title }}</span
                  >
                </div>
              </x-popover>
            </x-popover>

            <!-- 展开时菜单项 -->
            <div
              v-show="menusOpen"
              :class="[
                'menus-parent',
                {
                  enable: curRouteArr.includes(item.name),
                  open: item.open && menuControl.menuShow,
                },
              ]"
              @click="item.open = !item.open"
            >
              <x-icon
                :name="`menu_${item.name}${curRouteArr.includes(item.name) ? '_enable' : ''}`"
                width="16"
                height="16"
              />
              <span
                class="menus-parent-title"
                v-show="menuControl.menuShow"
                >{{ item.title }}</span
              >
            </div>

            <!-- 菜单子项 -->
            <Transition name="menus-child-transition">
              <div
                class="menus-child-transition"
                :style="{ height: `${item.children.length * 36 + 4}px` }"
                v-show="item.open && menuControl.menuShow"
              >
                <div
                  :class="[
                    'menus-child',
                    {
                      enable: curRouteArr[curRouteArr.length - 1] === _item.name,
                    },
                  ]"
                  v-for="(_item, _index) in item.children"
                  :key="_index"
                  @click="handleClickMenu(_item)"
                >
                  <span class="menus-child-point"></span>
                  <span class="menus-child-title">{{ _item.title }}</span>
                </div>
              </div>
            </Transition>
          </div>
        </div>
        <div class="toggle">
          <div
            class="toggle-area"
            @click="toggleMenu"
          >
            <span :class="['toggle-icon', { open: menuControl.menuShow }]">
              <x-icon
                name="menu_toggle"
                width="11.24px"
                height="9.96px"
              />
            </span>
            <span
              v-show="menuControl.menuShow"
              class="toggle-text"
              >{{ $t("putAway") }}</span
            >
          </div>
        </div>
      </div>
      <div
        class="main-bottom-right"
        :style="{ width: mainBottomRightWidth }"
      >
        <div class="tag-column">
          <div class="tag-column-left">
            <span
              class="left-arrow"
              @click="moveTabs('left')"
            ></span>
          </div>
          <div
            ref="tabsRef"
            class="tag-column-list"
            :style="{ transform: `translateX(-${tabTranslateX}px)` }"
          >
            <div
              ref="tabItemsRef"
              :class="['tab-item', { enable: item.enable }]"
              v-for="(item, index) in tabs"
              :key="index"
              @click="router.push({ name: item.routeName })"
            >
              <span class="tab-item-name">{{ item.title }}</span>
              <span
                class="tab-item-close"
                @click.stop="removeTab(index)"
                v-html="closeText"
              ></span>
            </div>
          </div>
          <div class="tag-column-right">
            <span
              class="right-arrow"
              @click="moveTabs('right')"
            ></span>
          </div>
        </div>
        <div :class="['route-view', { mobile: isMobile }]">
          <RouterView v-slot="{ Component }">
            <KeepAlive v-if="route.meta.keepAlive">
              <Transition
                name="main"
                mode="out-in"
              >
                <component :is="Component" />
              </Transition>
            </KeepAlive>
            <Transition
              v-if="!route.meta.keepAlive"
              name="main"
              mode="out-in"
            >
              <component :is="Component" />
            </Transition>
          </RouterView>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import { useMainStore } from "@/stores/main";
import { useRoute, useRouter, RouterView, onBeforeRouteUpdate } from "vue-router";
import { routes } from "@/router/router";
import xPopover from "@/components/x-popover.vue";
import personal from "@/views/main/components/personal.vue";
import { setLocalStorage } from "@/assets/ts/storage";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import modifyPassword from "@/views/main/components/modifyPassword.vue";
import { connect } from "@/services/ws";
import { isMobile, toggleFullScreen, i18nSimpleKey } from "@/assets/ts/utils";
// import { useI18n } from "vue-i18n";
// const { locale } = useI18n();
// setInterval(() => {
//   locale.value = Math.random() > 0.5 ? "zh-cn" : "en-us";
// }, 1000);
const $t = i18nSimpleKey("main");

onMounted(() => {
  // 使用onMounted原因：当从大屏切回来时，需等大屏地图onUnmounted执行完
  connect();
});

useMainStore().loadEnableCarIds();
useMainStore().loadUserInfo();
useMainStore().loadTabs();
useMainStore().loadMenuControl();
useMainStore().loadPwdModalStatus();
useMainStore().loadShowRoutes();
useMainStore().loadShowFences();
const {
  tabs,
  menuControl,
  userInfo,
  userInfo: { urlList },
  logout,
  audioStatus,
  socket,
  isJieyaPlatform,
} = useMainStore();

if (isJieyaPlatform) {
  document.title = $t("jieya");
  (document.querySelector('link[rel="icon"]') as HTMLLinkElement).href = "/favicon_jieya.ico";
}

const visibleIndex = ref();
const closeText = ref("&#10005");

/**
 * 告警音效
 */
const audioRef = ref();
const audio = reactive({
  status: audioStatus,
});
const toggleSound = () => {
  audio.status = !audio.status;
  useMainStore().audioStatus = audio.status;
};

/**
 * 告警灯
 */
const alarmState = reactive({
  number: 0,
});
const toAlarmStatistics = () => {
  router.push({
    name: "alarmStatistics",
    query: {
      mode: "4",
    },
  });
};
watch(
  () => socket.systemWarnStatus,
  (newV) => {
    const warnNum = newV?.warnNum;
    if (warnNum && audio.status) {
      audioRef.value.play();
    } else {
      audioRef.value.pause();
    }
    alarmState.number = warnNum ?? 0;
  }
);

/**
 * 个人中心
 */
const userMenu = reactive([
  {
    title: $t("personalCenter"),
    show: false,
  },
  {
    title: $t("changePassword"),
    show: false,
  },
  {
    title: $t("logout"),
    show: false,
  },
]);
const userMenuVisible = ref(false);
// 登入弹出修改密码窗口
if (userInfo.pwdChange && useMainStore().pwdModalStatus) {
  userMenu[1].show = true;
}

const route = useRoute();
const router = useRouter();

const curRouteArr = computed(() => route.path.slice(1).split("/"));
const menusOpen = ref(menuControl.menuShow);

const handleClickPop = (status: any, recordIndex: number) => {
  if (status) {
    menus.forEach((menu, index) => {
      menu.clickVisible = index === recordIndex;
      menu.hoverVisible = false;
    });
  }
  visibleIndex.value = recordIndex;
};
const toggleMenu = () => {
  menusOpen.value = !menusOpen.value;
  // 为了让收放显示好看些
  if (menusOpen.value) {
    setTimeout(() => {
      menuControl.menuShow = menusOpen.value;
      setLocalStorage("menuControl", menuControl);
    }, 300);
  } else {
    menuControl.menuShow = menusOpen.value;
    setLocalStorage("menuControl", menuControl);
  }
};

const tabsRef = ref<any>();
const tabItemsRef = ref<any>();
const tabTranslateX = ref(0);
const moveTabs = (type: string) => {
  const { width: tabWidth } = window.getComputedStyle(tabsRef.value);
  const { width: tabItemWidth } = window.getComputedStyle(tabItemsRef.value[0]);
  const outWidth = Number(tabWidth.replace("px", ""));
  const inWidth = tabsRef.value.scrollWidth;
  const itemWidth = Number(tabItemWidth.replace("px", ""));
  const canMoveWidth = type === "left" ? inWidth - tabTranslateX.value - outWidth : tabTranslateX.value;
  const computeWidth =
    type === "left"
      ? itemWidth * 2 - ((tabTranslateX.value + outWidth) % itemWidth)
      : itemWidth * 2 - ((inWidth - tabTranslateX.value) % itemWidth);
  if (outWidth >= inWidth || canMoveWidth <= 0) return;
  tabTranslateX.value += type === "left" ? Math.min(canMoveWidth, computeWidth) : -Math.min(canMoveWidth, computeWidth);
};
const addTab = (tabItem: Omit<_TabItem, "enable">) => {
  tabs.forEach((v) => (v.enable = false));
  const _tab = tabs.find((v) => v.routeName === tabItem.routeName);
  if (_tab) {
    _tab.enable = true;
  } else {
    tabs.push({ ...tabItem, enable: true });
    setLocalStorage("tabs", tabs);
  }
};
const removeTab = (tabIndex: number) => {
  if (tabs.length === 1) return;

  const current = tabs[tabIndex];
  const next = tabs[tabIndex > 0 ? tabIndex - 1 : tabIndex];
  tabs.splice(tabIndex, 1);
  if (current.enable) {
    router.push({ name: next.routeName });
  } else {
    setLocalStorage("tabs", tabs);
  }
};

const menus = reactive(
  routes
    .find((v) => v.name === "main")
    ?.children?.filter((v) => v.meta.isRoute)
    .map((v) => ({
      title: v.meta.title,
      name: v.name,
      open: v.name === curRouteArr.value[curRouteArr.value.length - 2],
      hoverVisible: false,
      clickVisible: false,
      children: v.children
        .filter((v) => userInfo.urlList.includes(v.meta.authorizationCode))
        .map((v) => ({
          title: v.meta.title,
          name: v.name,
        })),
    }))
    .filter((v) => v.children.length > 0)!
);
addTab({
  title: route.meta.title as string,
  routeName: route.name as string,
});

const headImgUrl = computed(() => {
  if (userInfo.imgUrl) {
    return userInfo.imgUrl;
  } else {
    return new URL("/src/assets/images/user_default_head.png", import.meta.url).href;
  }
});

onBeforeRouteUpdate((to) => {
  if (to.matched[0].path === "/main" && to.meta.title) {
    addTab({
      title: to.meta.title as string,
      routeName: to.name as string,
    });
  }
});
const handleOpera = (item: any, index: number) => {
  userMenuVisible.value = false;
  if (item.title === $t("logout")) {
    xModal.confirm({
      title: $t("DoYouLogout"),
      confirm() {
        return logout().then(() => {
          Message("success", $t("logout"));
        });
      },
    });
  } else {
    userMenu[index].show = true;
  }
};
const handleClickMenu = (item: any) => {
  if (item.name === "remoteDevOps") {
    // 远程运维页面保持收起状态
    menusOpen.value = false;
    menuControl.menuShow = false;
    setLocalStorage("menuControl", menuControl);
  }
  router.push({ name: item.name });
};
const handlePopItemClick = (item: any, popItemName: string) => {
  console.log(popItemName, "9456");
  if (popItemName === "remoteDevOps") {
    // 远程运维页面保持收起状态
    menusOpen.value = false;
    menuControl.menuShow = false;
    setLocalStorage("menuControl", menuControl);
  } else {
    // menusOpen.value = true;
    // menuControl.menuShow = true;
    setTimeout(() => {
      menusOpen.value = true;
      menuControl.menuShow = true;
      setLocalStorage("menuControl", menuControl);
    }, 300);
  }
  router.push({ name: popItemName });
};

const mainBottomRightWidth = computed(() => {
  return menusOpen.value ? "calc(100% - 184px)" : "calc(100% - 64px)";
});
</script>
<style lang="scss">
.system-top-button-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}

.menus-parent-hover-popover {
  @include wh(88px, 36px);
  line-height: 36px;
  text-align: center;
}

.menus-parent-click-popover {
  padding: 9px 14px 9px 12px;

  &-item {
    cursor: pointer;
    @include ct-f(y);
    @include wh(100%, 34px);

    &-point {
      display: block;

      @include wh(8px) {
        border: 2px solid rgba(194, 207, 224, 0.6);
        border-radius: 50%;
      }
    }

    &-title {
      margin-left: 10px;
      @include sc(13px, rgba(255, 255, 255, 0.7));
    }

    &:hover {
      .menus-parent-click-popover-item-point {
        border-color: #fff;
      }

      .menus-parent-click-popover-item-title {
        color: #fff;
      }
    }
  }
}
.user-menu {
  width: 90px;
  height: 120px;
  &-item {
    cursor: pointer;
    height: 40px;
    display: flex;
    justify-content: center;
    align-items: center;
    &:hover {
      background-color: #242859;
      color: rgb(178, 183, 251);
    }
  }
}
</style>

<style lang="scss" scoped>
.main {
  display: flex;
  flex-direction: column;
  @include wh(100%);
  @include bis("@/assets/images/main_bg.png");
  &-enter {
    &-from {
      opacity: 0.3;
      transform: translateX(3%);
    }
    &-active {
      transition: all 0.3s ease-out;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease-out;
    }
    &-to {
      opacity: 0.3;
      transform: translateX(-3%);
    }
  }
  &-top {
    @include fj;

    @include wh(100%, 56px) {
      padding: 0 20px;
    }

    &-left {
      height: 100%;
      @include ct-f(y);

      img {
        @include wh(24px);
      }

      span {
        padding-left: 10px;

        @include sc(18px, #242859) {
          font-weight: bolder;
        }
      }
    }

    &-right {
      height: 100%;
      @include ct-f(y);
      .go-screen {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(68px, 32px) {
          padding: 0 10px;
          background-color: rgb(249, 251, 254);
          border-radius: 4px;
          margin-right: 8px;
        }
      }
      .fault-sound,
      .fault-tip,
      .user {
        @include ct-f(both) {
          margin-right: 8px;
        }
        @include wh(32px, 32px);
        background-color: #fff;
        border-radius: 50%;
        cursor: pointer;
      }
      .user {
        .user-avatar {
          @include ct-f;
          .user-head-img {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background-size: cover;
          }
        }
      }
    }
  }

  &-bottom {
    display: flex;
    flex: 1;
    overflow: hidden;
    width: 100%;

    &-left {
      z-index: var(--z-index-text);
      display: flex;
      flex-direction: column;
      flex-shrink: 0;
      overflow-x: hidden;
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.4));

      @include wh(54px, 100%) {
        padding-top: 10px;
      }

      transition: all 0.5s ease;

      &.open {
        width: 184px;
      }

      .menus {
        flex: 1;
        width: 100%;
        padding: 0 8px;

        &-parent {
          cursor: pointer;
          position: relative;
          @include ct-f(y);

          @include wh(100%, 38px) {
            padding: 0 10px;
            border-radius: 6px;
          }

          color: rgb(94, 94, 94);

          &.enable {
            background-color: rgb(89, 100, 251);
            color: #fff;
          }

          &.open {
            &::after {
              content: "";

              @include ct-p(y) {
                right: 10px;
              }

              @include v-arrow(top, 1px, 6px);
            }
          }

          &-title {
            margin-left: 10px;
          }
        }

        &-child-transition {
          overflow: hidden;

          &-enter {
            &-from {
              height: 0 !important;
            }

            &-active {
              transition: all 0.3s ease;
            }
          }

          &-leave {
            &-active {
              transition: all 0.3s ease;
            }
            &-to {
              height: 0 !important;
            }
          }
        }

        &-child {
          cursor: pointer;
          @include ct-f(y);

          @include wh(100%, 36px) {
            padding-left: 40px;
          }

          &.enable {
            .menus-child-point {
              border-color: #5964fb;
            }

            .menus-child-title {
              color: #5964fb;
              font-weight: bolder;
            }
          }

          &-point {
            display: block;

            @include wh(8px) {
              border: 2px solid rgba(194, 207, 224, 0.6);
              border-radius: 50%;
            }
          }

          &-title {
            margin-left: 10px;
            @include sc(13px, #5e5e5e);
          }
        }
      }

      .toggle {
        @include wh(100%, 48px) {
          padding-left: 22px;
        }

        &-area {
          cursor: pointer;
          @include wh(68px, 100%);
          @include ct-f(y);
        }

        &-icon {
          @include ct-f;

          @include wh(24px) {
            border-radius: 50%;
          }

          transition: all 0.5s ease;

          &.open {
            background-color: #fff;
          }
        }

        &-text {
          margin-left: 6px;
          color: rgb(151, 157, 254);
        }
      }
    }

    &-right {
      background-color: #fff;
      border-radius: 10px 0px 0px 0px;
      height: 100%;
      display: flex;
      flex-direction: column;

      .tag-column {
        display: flex;
        overflow: hidden;

        @include wh(100%, 42px) {
          padding: 2px 0;
          border-bottom: 1px solid rgb(229, 237, 255);
        }

        &-left,
        &-right {
          cursor: pointer;
          position: relative;

          @include ct-f;
          @include wh(38px, 100%) {
            border-radius: 10px 0px 0px 0px;
            background-color: #fff;
            z-index: var(--z-index-text);
          }

          &::after {
            content: "";
            @include ct-p(y);

            @include wh(1px, 20px) {
              background-color: #e5e5e5;
            }
          }
        }

        .left-arrow,
        .right-arrow {
          position: relative;

          @include wh(20px) {
            border-radius: 4px;
          }

          color: #999;

          &::before {
            content: "";
            position: absolute;
            top: 6px;
          }

          &:hover {
            color: #5964fb;
          }

          &:active {
            background-color: rgb(245, 248, 255);
          }
        }

        &-left {
          .left-arrow {
            &::before {
              left: 8px;
              @include v-arrow(left, 2px, 8px);
            }
          }

          &::after {
            right: 0;
          }
        }

        &-right {
          .right-arrow {
            &::before {
              right: 6px;
              @include v-arrow(right, 2px, 8px);
            }
          }

          &::after {
            left: 0;
          }
        }

        &-list {
          flex: 1;
          width: 0;
          height: 100%;
          transition: all 0.5s;
          @include ct-f(y);
          .tab-item {
            cursor: pointer;
            position: relative;
            @include ct-f(y);
            min-width: 100px;
            height: 36px;
            padding: 0 8px 0 14px;

            &::after {
              content: "";

              @include ct-p(y) {
                right: 0;
              }

              @include wh(1px, 20px) {
                background-color: #e5e5e5;
              }
            }

            &-name {
              min-width: 52px;
              height: 100%;
              margin-right: 12px;

              @include sc(13px, #999) {
                line-height: 36px;
              }

              @include ell;
            }

            &-close {
              font-weight: 400;
            }

            &:hover {
              .tab-item-name,
              .tab-item-close {
                color: #5964fb;
              }
            }
            &:active {
              background-color: rgb(245, 248, 255);
            }

            &.enable {
              .tab-item-name,
              .tab-item-close {
                color: #5964fb;
              }
            }
          }
        }
      }

      .route-view {
        flex: 1;
        width: 100%;
        overflow: hidden;
        &.mobile {
          zoom: 0.7;
        }
      }
    }
  }
}
</style>
