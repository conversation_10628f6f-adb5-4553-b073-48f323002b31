<template>
  <x-modal
    :visible="props.show"
    @update:visible="updateVisible"
    :title="$t('uploadPicture')"
    width="674px"
    height="471px"
  >
    <div class="upload-wrapper">
      <div
        class="upload-container"
        :class="{ picked: picked }"
        :style="{ backgroundImage: fixedFrameBgUrl }"
        @click="pickImg"
        @drop="fileSelect"
        @dragover="dragOver"
        @mouseleave="mouseUp()"
        ref="fixedFrameRef"
      >
        <div
          v-show="picked"
          class="upload-container-mask"
          :style="{
            width: `${props.fixedWidth}px`,
            height: `${props.fixedHeight}px`,
          }"
        >
          <span
            v-for="position in dotClassList"
            :key="position"
            class="dot"
            :class="position"
          ></span>
        </div>
        <div v-show="!picked" class="picking-tip">
          <div class="picking-tip-top">+</div>
          <div class="picking-tip-bottom">{{ $t("dragImageToThis") }}</div>
        </div>
        <div
          v-show="picked"
          ref="variableImgBoxRef"
          class="variable-image-box"
          :style="{
            width: `${variableImgBoxPos.width}px`,
            height: `${variableImgBoxPos.height}px`,
            left: `${variableImgBoxPos.left}px`,
            top: `${variableImgBoxPos.top}px`,
          }"
          @click="
            (e) => {
              e.stopPropagation();
            }
          "
          @wheel="zoom"
          @mousedown="mouseDown"
        >
          <img ref="imageRef" class="selected-image" draggable="false" />
        </div>
      </div>

      <div class="upload-opera" v-show="picked">
        <div class="img-translate">
          <div class="zoomIn" @click="zoomIn">
            <x-icon name="user_personal_larger" width="16px" height="16px" />
          </div>
          <div class="separate">|</div>
          <div class="zoomOut" @click="zoomOut">
            <x-icon name="user_personal_smaller" width="16px" height="16px" />
          </div>
        </div>
        <div class="picked-tip">{{ $t("dragImageSelCropRange") }}</div>
        <x-button text="保存" @click="saveImg" />
      </div>

      <canvas
        ref="canvasRef"
        :width="props.fixedWidth"
        :height="props.fixedHeight"
        v-show="false"
      ></canvas>

      <input
        ref="imgInputRef"
        @change="getImg"
        v-show="false"
        type="file"
        accept="image/*"
      />
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { ref, watch, reactive } from "vue";
import xModal from "@/components/x-modal";
import xButton from "@/components/x-button.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  // 截取框的固定宽度
  fixedWidth: {
    type: Number,
    required: true,
  },
  // 截取框的固定高度
  fixedHeight: {
    type: Number,
    required: true,
  },
});

const emits = defineEmits(["update:show", "imageSaved"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  imgInputRef.value.value = "";
  picked.value = false;
};

const variableImgBoxRef = ref();
const canvasRef = ref();
const imageRef = ref();
const fixedFrameRef = ref();
const fixedFrameBgUrl = ref();
const imgInputRef = ref();

const variableImgBoxPos = reactive({
  width: 0,
  height: 0,
  left: 0,
  top: 0,
});

// 已选择图片
const picked = ref(false);
const pickImg = () => {
  if (picked.value) {
    return;
  }
  imgInputRef.value.click();
};

watch(picked, (newX) => {
  if (newX === false) {
    variableImgBoxPos.top = 0;
    variableImgBoxPos.left = 0;
    fixedFrameBgUrl.value = "";
  } else {
    setTimeout(() => {
      setVariableImgBoxSize();
      fixedFrameBgUrl.value = `url(${
        new URL(
          "/src/assets/images/user_personal_frame_bg.jpg",
          import.meta.url
        ).href
      })`;
    }, 10);
  }
});

// 设置裁剪框大小
const setVariableImgBoxSize = () => {
  const widthRatio =
    fixedFrameRef.value.offsetWidth / imageRef.value.naturalWidth;
  const heightRatio =
    fixedFrameRef.value.offsetHeight / imageRef.value.naturalHeight;
  if (widthRatio >= heightRatio) {
    variableImgBoxPos.height = fixedFrameRef.value.offsetHeight;
    variableImgBoxPos.width = imageRef.value.naturalWidth * heightRatio;
  } else {
    variableImgBoxPos.width = fixedFrameRef.value.offsetWidth;
    variableImgBoxPos.height = imageRef.value.naturalHeight * widthRatio;
  }
};

const getImg = (e: Event) => {
  const files = (e.target as HTMLInputElement).files;
  if (files!.length > 0) {
    picked.value = true;
    imageRef.value.src = URL.createObjectURL(files![0]);
  }
};

const dragOver = (e: DragEvent) => {
  e.stopPropagation();
  e.preventDefault();
  e.dataTransfer!.dropEffect = "copy";
};

const fileSelect = (e: DragEvent) => {
  e.stopPropagation();
  e.preventDefault();

  const files = e.dataTransfer!.files;
  if (files.length > 0) {
    const fileName = files[0].name;
    const suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
    if (suffix == "jpg" || suffix == "png" || suffix == "jpeg") {
      picked.value = true;
      imageRef.value.src = URL.createObjectURL(files[0]);
    }
  }
};

/**
 * 鼠标事件处理
 */
const mouseDownX = ref(0);
const mouseDownY = ref(0);
const currentTop = ref(0);
const currentLeft = ref(0);

const mouseDown = (e: MouseEvent) => {
  mouseDownX.value = e.clientX;
  mouseDownY.value = e.clientY;
  currentTop.value = variableImgBoxPos.top;
  currentLeft.value = variableImgBoxPos.left;
  variableImgBoxRef.value.style.cursor = "grabbing";
  fixedFrameRef.value.addEventListener("mousemove", mouseMove);
  fixedFrameRef.value.addEventListener("mouseup", mouseUp);
};

const transX = ref(0);
const transY = ref(0);
const mouseMove = (e: MouseEvent) => {
  transX.value = e.clientX - mouseDownX.value;
  transY.value = e.clientY - mouseDownY.value;
  variableImgBoxPos.top = currentTop.value + transY.value;
  variableImgBoxPos.left = currentLeft.value + transX.value;
};

const mouseUp = () => {
  fixedFrameRef.value.removeEventListener("mousemove", mouseMove);
  fixedFrameRef.value.removeEventListener("mouseup", mouseUp);
  variableImgBoxRef.value.style.cursor = "grab";
};

/**
 * 底部操作栏
 */
const SCALE = 1.1;
const zoom = (e: WheelEvent) => {
  e.deltaY < 0 ? zoomIn() : zoomOut();
};

// 放大图片
const zoomIn = () => {
  const newWidth = variableImgBoxPos.width * SCALE;
  const newHeight = variableImgBoxPos.height * SCALE;
  variableImgBoxPos.width = newWidth;
  variableImgBoxPos.height = newHeight;
};

// 缩小图片
const zoomOut = () => {
  const newWidth = variableImgBoxPos.width / SCALE;
  const newHeight = variableImgBoxPos.height / SCALE;
  if (
    variableImgBoxPos.width < props.fixedWidth ||
    variableImgBoxPos.height < props.fixedHeight
  ) {
    return;
  }
  variableImgBoxPos.width = newWidth;
  variableImgBoxPos.height = newHeight;
};

// 保存裁剪后的图片
const saveImg = () => {
  const ctx = canvasRef.value.getContext("2d");
  const initScale = Math.max(
    imageRef.value.naturalWidth / variableImgBoxPos.width,
    imageRef.value.naturalHeight / variableImgBoxPos.height
  );

  ctx.drawImage(
    imageRef.value,
    imageRef.value.naturalWidth / 2 -
      (props.fixedWidth / 2) * initScale -
      variableImgBoxPos.left * initScale,
    imageRef.value.naturalHeight / 2 -
      (props.fixedHeight / 2) * initScale -
      variableImgBoxPos.top * initScale,
    props.fixedWidth * initScale,
    props.fixedHeight * initScale,
    0,
    0,
    props.fixedWidth,
    props.fixedHeight
  );

  canvasRef.value.toBlob(
    async (blob: Blob) => {
      const formData = new FormData();
      formData.append("file", blob, "myHead.jpg");
      emits("imageSaved", formData);
      ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
      imgInputRef.value.value = "";
      picked.value = false;
      URL.revokeObjectURL(imageRef.value.src);
    },
    "image/jpeg",
    0.9
  );
};

const dotClassList = [
  "top-left",
  "top-center",
  "top-right",
  "center-left",
  "center-right",
  "bottom-left",
  "bottom-center",
  "bottom-right",
];
</script>

<style lang="scss" scoped>
.upload-wrapper {
  @include ct-f(y);
  flex-direction: column;
  .upload-container {
    position: relative;
    @include ct-f(both);
    @include wh(625px, 270px);
    border: 1px solid #dcdcdc;
    border-radius: 4px;
    overflow: hidden;
    &.picked {
      border: none;
    }
    &-mask {
      content: "";
      @include ct-p;
      background-color: transparent;
      box-shadow: 0 0 0 1000px rgba(0, 0, 0, 0.4);
      border: solid 1px rgb(89, 100, 251);
      pointer-events: none;
      z-index: 1;
      .dot {
        content: "";
        position: absolute;
        background-color: rgb(89, 100, 251);
        border-radius: 50%;
        @include wh(8px, 8px);
        &.top-left {
          top: -4px;
          left: -4px;
        }
        &.top-center {
          top: -4px;
          right: calc(50% - 4px);
        }
        &.top-right {
          top: -4px;
          right: -4px;
        }
        &.center-left {
          top: calc(50% - 4px);
          left: -4px;
        }
        &.center-right {
          top: calc(50% - 3px);
          right: -4px;
        }
        &.bottom-left {
          bottom: -4px;
          left: -4px;
        }
        &.bottom-center {
          bottom: -4px;
          right: calc(50% - 4px);
        }
        &.bottom-right {
          bottom: -4px;
          right: -4px;
        }
      }
    }
    .picking-tip {
      @include ct-f(both) {
        flex-direction: column;
      }
      color: #9f9fa4;
      &-top {
        font-size: 24px;
      }
      &-bottom {
        font-size: 14px;
      }
    }
    .variable-image-box {
      position: relative;
      cursor: grab;
      width: 100%;
      height: 100%;
      background-color: black;
      .selected-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
  }
  .upload-opera {
    @include ct-f(both) {
      flex-direction: column;
    }
    .img-translate {
      @include ct-f;
      margin: 10px;
      border: solid 1px #dcdcdc;
      border-radius: 20px;
      .zoomIn,
      .zoomOut {
        @include ct-f;
        @include wh(60px, 34px);
        cursor: pointer;
      }
      .separate {
        @include ct-f;
        color: #dcdcdc;
      }
    }
    .picked-tip {
      margin-bottom: 10px;
      @include sc(14px, #555555);
    }
  }
}
</style>
