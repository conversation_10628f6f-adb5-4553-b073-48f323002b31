<template>
  <div
    class="remote-status-part"
    v-loading="onlineLoading"
    element-loading-text="处理中..."
  >
    <div class="control-wrapper">
      <div
        class="control-wrapper-item"
        v-if="controlAlerts"
      >
        <div class="left">
          <xIcon
            class="control-icon"
            name="control-icon"
            :width="20"
            :height="20"
            color="#fff"
          ></xIcon>
          <span>管线提醒</span>
        </div>
        <div
          class="right"
          @click="handelRelease('control')"
        >
          去解除
        </div>
      </div>
      <div
        class="control-wrapper-item"
        v-if="collisionAlerts"
      >
        <div class="left">
          <xIcon
            class="control-icon"
            name="control-icon"
            :width="20"
            :height="20"
            color="#fff"
          ></xIcon>
          <span>碰撞提醒</span>
        </div>
        <div
          class="right"
          @click="handelRelease('collision')"
        >
          去解除
        </div>
      </div>
      <div
        class="control-wrapper-item"
        v-if="carInfo.emgcyStopReason === 1"
      >
        <div class="left">
          <xIcon
            class="control-icon"
            name="control-icon"
            :width="20"
            :height="20"
            color="#fff"
          ></xIcon>
          <span>车辆硬急停启动</span>
        </div>
        <div
          class="right"
          @click="handelRelease('stop')"
        >
          去解除
        </div>
      </div>
    </div>
    <div class="row1">
      <div class="row1-btn">
        <div
          class="row1-btn-item"
          :class="{ close_disable: carInfo.inCharge === userInfo.mobile }"
        >
          <view
            class="icon-wrapper"
            @click="handelShutdown"
            :class="{ close: !closeLoading }"
          >
            <xIcon
              v-if="closeLoading"
              name="loading"
              :width="16"
              :height="16"
              color="#fff"
              class="rotate-spin"
            ></xIcon>
            <xIcon
              v-else
              name="map_close"
              :width="16"
              :height="16"
              color="#fff"
            ></xIcon>
          </view>
          <span>{{ carInfo.online ? "关机" : "开机" }}</span>
        </div>
        <div class="row1-btn-item">
          <view class="icon-wrapper restart">
            <xIcon
              name="progress_restart"
              :width="16"
              :height="16"
              color="#fff"
            ></xIcon>
          </view>
          <span>重启</span>
        </div>
        <div
          class="row1-btn-item"
          @click="handelMaintenanceChange"
        >
          <view class="icon-wrapper repair">
            <xIcon
              name="repair-icon"
              :width="16"
              :height="16"
              color="#fff"
            ></xIcon>
          </view>
          <span>{{ carInfo.inCharge ? "结束维护" : "车辆维护" }}</span>
        </div>
      </div>

      <div
        class="row1-step"
        @dblclick="handleEmergencyStopCmdChange"
      >
        <div class="left">
          <span>软急停</span>
          <span class="tip">双击打开关闭急停</span>
        </div>
        <div class="right">
          <el-switch
            v-model="emergencyStopCmd"
            inline-prompt
            :active-icon="Check"
            :inactive-icon="Close"
            size="large"
            :disabled="true"
          >
          </el-switch>
        </div>
      </div>
    </div>

    <div class="row2">
      <div class="top">
        <div class="top-left">
          <span>管控中</span>
          <xIcon
            name="warningFilled"
            :width="16"
            :height="16"
          ></xIcon>
        </div>
        <div class="top-right">
          <el-switch
            v-model="pipelineData.charge"
            inline-prompt
            :active-icon="Check"
            :inactive-icon="Close"
            size="large"
            @change="onPipelineSwitchChange"
          >
          </el-switch>
        </div>
      </div>
      <div class="bottom">
        <span>时段</span>
        <el-time-picker
          v-model="pipelineData.data"
          is-range
          range-separator="-"
          format="HH:mm:ss"
          value-format="HH:mm:ss"
          size="default"
          :disabled="!pipelineData.charge"
          style="width: 126px"
        />
      </div>
    </div>
    <el-dialog
      v-model="dialogVisible"
      :title="dialogType === 'control' ? '管线解除' : '碰撞解除'"
      width="30%"
      append-to-body
    >
      <div class="dialog-content">
        <div class="dialog-content-item">
          <label class="dialog-content-item-label">上报车辆：</label>
          <span class="dialog-content-item-value">{{ alarmMessageData?.device_id ?? "--" }}</span>
        </div>
        <div class="dialog-content-item">
          <label class="dialog-content-item-label">上报时间：</label>
          <span class="dialog-content-item-value">{{ alarmMessageData?.dataContent?.updateTime ?? "--" }}</span>
        </div>

        <div class="dialog-content-item">
          <el-image
            style="width: 100%; height: 348px; cursor: pointer"
            :preview-src-list="[alarmMessageData?.picUrl ?? '']"
            :src="alarmMessageData?.picUrl ?? ''"
          />
        </div>

        <div
          class="dialog-content-item"
          v-if="dialogType === 'control'"
          style="flex-direction: column; align-items: flex-start"
        >
          <p>1.如是管线误识别，请检查后，点击"解除"通行即可；</p>
          <p>2.如是真实管线，请现场人工处理</p>
        </div>
      </div>

      <template #footer>
        <div style="display: flex; justify-content: center; gap: 10px">
          <el-button @click="handelUnbind(2)">不解除</el-button>
          <el-button
            type="primary"
            @click="handelUnbind(1)"
            >解除</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { Check, Close } from "@element-plus/icons-vue";
import XIcon from "@/components/x-icon.vue";
import { ref, reactive, toRefs, onMounted, inject, type Ref, nextTick, watch } from "vue";
import type { CarInfo } from "../../../type";
import { ElMessage, ElMessageBox } from "element-plus";
import {
  getIncharge,
  getPipelineCollisionAudit,
  getRedLightStop,
  getReleaseKeyEmergencyStop,
  getVehicleAdvancedConfig,
  setVehicleAdvancedConfig,
} from "@/services/api";
import { useMainStore } from "@/stores/main";
import { closeMachine, openMachine } from "@/services/wsapi";

const props = defineProps({
  pipelineData: {
    type: Object,
    default: () => ({}),
  },
});
const { socket } = useMainStore();
const { userInfo } = useMainStore();
const carInfo = inject("carInfo") as Ref<CarInfo>;
const closeLoading = ref(false);
const onlineLoading = ref<boolean>(false);
const alarmMessageData = ref<any>({});
/**管控数据 */
const pipelineData = ref(props.pipelineData);
/** 管线、碰撞、弹窗 */
const dialogVisible = ref(false);
const dialogType = ref<"control" | "collision">("control");
const controlAlerts = ref(false);
const collisionAlerts = ref(false);

const emergencyStopCmd = ref(false);

const handelRelease = (type: "control" | "collision" | "stop") => {
  dialogVisible.value = true;
  switch (type) {
    case "control":
      dialogVisible.value = true;
      dialogType.value = "control";
      break;
    case "collision":
      dialogVisible.value = true;
      dialogType.value = "collision";
      break;
    case "stop":
      ElMessageBox.confirm(`确定解除${carInfo.value.deviceId}车辆硬急停吗？`, {
        distinguishCancelAndClose: true,
        confirmButtonText: "解除",
        cancelButtonText: "不解除",
      })
        .then(async () => {
          await getReleaseKeyEmergencyStop({
            vehNo: carInfo.value.deviceId,
            keyEmergencyStopClearCmd: 1,
          });
          ElMessage.success(`${carInfo.value.deviceId}车辆硬急停已解除`);
        })
        .catch(async () => {
          await getReleaseKeyEmergencyStop({
            vehNo: carInfo.value.deviceId,
            keyEmergencyStopClearCmd: 0,
          });
          ElMessage.success(`${carInfo.value.deviceId}车辆硬急停不解除`);
        });
      break;
    default:
      break;
  }
};

const handleEmergencyStopCmdChange = debounce(async () => {
  emergencyStopCmd.value = !emergencyStopCmd.value;
  try {
    await getRedLightStop({
      vehNo: carInfo.value.deviceId,
      emergencyStopCmd: emergencyStopCmd.value ? "1" : "2",
    });
    ElMessage.success(`${carInfo.value.deviceId}急停已${emergencyStopCmd.value ? "开启" : "关闭"}`);
  } catch (error) {
    ElMessage.error("操作失败");
  }
});

const onPipelineSwitchChange = async (val: boolean) => {
  if (val) {
    const data = pipelineData.value.data;
    if (!Array.isArray(data) || data.length !== 2 || data[0] == null || data[1] == null) {
      ElMessage.error("请选择管控时段");
      // 阻止打开，强制回弹为 false
      pipelineData.value.charge = false;
      return;
    }
  }
  pipelineData.value.charge = val;
  try {
    await setVehicleAdvancedConfig({
      vehNo: carInfo.value.deviceId,
      charge: pipelineData.value.charge ? 1 : 0,
      chargeStartTime: pipelineData.value.data[0],
      chargeEndTime: pipelineData.value.data[1],
      id: pipelineData.value.id,
    } as any);
    ElMessage.success("操作成功");
    init();
  } catch (error) {
    ElMessage.error("操作失败");
  }
};
const handelUnbind = debounce(async (val: number) => {
  try {
    await getPipelineCollisionAudit({
      vehNo: carInfo.value.deviceId,
      dataTime: alarmMessageData.value.dataContent.updateTime,
      eventId: alarmMessageData.value.dataContent.id,
      navigationAdvice: val,
      picUrl: alarmMessageData.value.picUrl,
    });
    ElMessage.success(
      `${carInfo.value.deviceId}${dialogType.value === "control" ? "管线" : "碰撞"}${val === 1 ? "已解除" : "不解除"}`
    );
  } catch (error: any) {
    ElMessage.error(error.message);
  } finally {
    dialogVisible.value = false;
  }
});

const handelMaintenanceChange = async () => {
  if (!carInfo.value.inCharge) {
    ElMessage.warning(`请联系电话${carInfo.value.inCharge}解除维护`);
    return;
  }
  try {
    await getIncharge({ vehNo: carInfo.value.deviceId });
    ElMessage.success("操作成功");
  } catch (error) {
    ElMessage.error("操作失败");
  }
};

const handelShutdown = async () => {
  if (carInfo.value.inCharge == userInfo.mobile) {
    return;
  }
  closeLoading.value = true;
  ElMessageBox.confirm(`确认${carInfo.value.online ? "关机" : "开机"}${carInfo.value.deviceId}吗？`)
    .then(async () => {
      onlineLoading.value = true;
      try {
        await (carInfo.value.online ? closeMachine : openMachine)(carInfo.value.deviceId, carInfo.value.manufacturer);
        ElMessage.success(`${carInfo.value.online ? "关机" : "开机"}成功`);
      } catch (error) {
        ElMessage.error(`${carInfo.value.online ? "关机" : "开机"}失败`);
      } finally {
        onlineLoading.value = false;
        closeLoading.value = false;
      }
    })
    .catch(() => {
      closeLoading.value = false;
    });
};

watch(
  () => socket.alarmMessageStatus,
  (newV) => {
    if (newV?.device_id === carInfo.value.deviceId && newV?.messageType === 7) {
      alarmMessageData.value = newV;
      controlAlerts.value = true;
    }
    if (newV?.device_id === carInfo.value.deviceId && newV?.messageType === 8) {
      alarmMessageData.value = newV;
      collisionAlerts.value = true;
    }
  }
);
</script>
<script lang="ts">
import { debounce } from "@/assets/ts/utils";

export default {
  name: "remoteStatusPart",
};
</script>
<style scoped lang="scss">
.remote-status-part {
  width: 100%;
  height: 100%;
  padding: 12px 24px 28px 24px;
  .control-wrapper {
    width: 100%;
    display: flex;
    gap: 10px;
    &-item {
      flex: 1;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      height: 38px;
      border-radius: 30px;
      background: linear-gradient(270deg, rgba(244, 21, 21, 0.16), rgba(244, 21, 21, 0.4) 100%);
      padding: 5px 15px;
      color: rgba(244, 21, 21, 1);

      .left {
        display: flex;
        align-content: center;
        gap: 5px;
        font-weight: 600;
        .control-icon {
          animation-duration: 0.8s;
          animation-name: flicker;
          animation-iteration-count: infinite;
          animation-timing-function: linear;
          @keyframes flicker {
            0% {
              opacity: 1;
            }
            100% {
              opacity: 0.4;
            }
          }
        }
      }
      .right {
        width: 52px;
        height: 28px;
        background: #fff;
        border-radius: 30px;
        cursor: pointer;
        text-align: center;
        line-height: 28px;
      }
    }
  }
  .row1 {
    display: flex;
    gap: 20px;
    &-btn {
      width: 337px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      border-radius: 8px;
      background: rgb(244, 247, 254);
      padding: 13px 15px;
      &-item {
        // width: 90px;
        display: flex;
        align-items: center;
        justify-content: center;
        gap: 5px;
        cursor: pointer;
        user-select: none;
        border-radius: 4px;
        background: rgb(255, 255, 255);
        padding: 7px 8px;
        .icon-wrapper {
          width: 28px;
          height: 28px;
          border-radius: 8px;
          display: flex;
          align-items: center;
          justify-content: center;
          &.close {
            background: rgb(249, 133, 46);
          }
          &.restart {
            background: rgb(244, 21, 21);
          }
          &.repair {
            background: rgb(89, 100, 251);
          }
          .rotate-spin {
            animation: spin 1s linear infinite;
          }

          @keyframes spin {
            100% {
              transform: rotate(360deg);
            }
          }
        }
      }
    }
    &-step {
      display: flex;
      justify-content: space-between;
      align-items: center;
      // width: 181px;
      height: 65px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      padding: 13px 15px;
      gap: 20px;
      .left {
        display: flex;
        flex-direction: column;
        gap: 5px;
        font-size: 14px;
        color: #383838;
        .tip {
          color: #9f9fa4;
          font-size: 12px;
        }
      }
    }
  }

  .row2 {
    width: 236px;
    display: flex;
    align-items: center;
    gap: 20px;
    border-radius: 8px;
    background: rgb(244, 247, 254);
    padding: 13px 15px 13px 15px;
    margin-top: 10px;
    flex-direction: column;
    .top {
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: 20px;
      .top-left {
        display: flex;
        align-items: center;
        gap: 5px;
        font-size: 14px;
        color: #383838;
      }
    }
    .bottom {
      width: 202px;
      display: flex;
      align-items: center;
      gap: 10px;
      font-size: 14px;
      color: #383838;
      border-radius: 4px;
      padding: 10px;

      background: rgb(255, 255, 255);
    }
  }
  &-wrapper {
    width: 100%;
    display: flex;
    gap: 50px;
    margin-top: 50px;
    &-item {
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 14px;
      &-label {
        font-size: 14px;
      }
    }
  }
}
:deep(.dialog-content-item) {
  display: flex;
  align-items: center;
  color: rgba(85, 85, 85, 1);
  font-size: 14px;
  margin-bottom: 10px;
}

:deep(.dialog-footer) {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
</style>
