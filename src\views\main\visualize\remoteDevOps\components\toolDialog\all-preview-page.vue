<template>
  <section class="all-preview-page">
    <div class="back">
      <span>{{ `< ` }}</span>
      <span>返回</span>
    </div>
    <el-table
      :data="tableData"
      style="width: 100%"
    >
      <el-table-column
        v-for="item in column"
        :key="item.prop"
        :prop="item.prop"
        :label="item.label"
        :width="item.width"
      />
    </el-table>
    <XEmpty v-if="tableData?.length === 0"> </XEmpty>
  </section>
</template>

<script lang="ts" setup>
import { onUnmounted, ref } from "vue";
import { usePolling } from "@/assets/ts/utils";
import { getAllVehFirstPreviewInformation } from "@/services/api";
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  deviceId: {
    type: String,
    default: "",
  },
});
const loading = ref(false);
const tableData = ref([]);
const column = ref([
  {
    prop: "date",
    label: "Date",
    width: "180",
  },
]);
const load = async () => {
  try {
    loading.value = true;
    const res = await getAllVehFirstPreviewInformation({
        deviceId: "",
      });
    tableData.value = res ?? [];
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const { startPolling, stopPolling } = usePolling(load, 5);
onMounted(() => {
  startPolling();
});
onUnmounted(() => {
  stopPolling();
});


function onMounted(arg0: () => void) {
    throw new Error("Function not implemented.");
}
</script>
<style lang="scss">
.all-preview-page {
  .back {
    color: #5964fb;
    font-size: 14px;
    font-weight: 500;
  }
}
</style>
