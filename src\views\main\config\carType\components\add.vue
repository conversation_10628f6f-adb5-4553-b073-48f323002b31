<template>
  <x-drawer
    :title="$t('addVehicleModel')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    :btnOption="{ position: 'center' }"
    width="672px"
  >
    <div ref="contentRef" class="content">
      <x-form ref="formRef" :model="form" :rules="formRules">
        <x-form-item :label="$t('modelName')" name="vehicleModel">
          <x-input
            v-model:value="form.vehicleModel"
            :placeholder="$t('PEnterModelName')"
            :maxlength="30"
          />
        </x-form-item>
        <x-form-item :label="$t('type')" name="vehicleType">
          <x-select
            v-model:value="form.vehicleType"
            :options="formOptions.vehicleType"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectType')"
          />
        </x-form-item>
        <x-form-item :label="$t('remark')" name="remark">
          <x-textarea
            v-model:value="form.remark"
            :placeholder="$t('PEnterRemark')"
            :auto-size="{ minRows: 6, maxRows: 6 }"
            :maxlength="300"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  addVehicleModel,
  getConfigList,
  vehicleModelRepeat,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTextarea from "@/components/x-textarea.vue";
import Message from "@/components/x-message";
import { debounce } from "@/assets/ts/utils";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carType");

/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    formRef.value && formRef.value.resetFields();
  }
};

/**
 * 重复型号
 */
const repeatVehicleModel = async (value: string) => {
  const result = await vehicleModelRepeat({ vehicleModel: value });
  return !result?.repeat;
};

/**
 * 表单项
 */
const form = reactive({
  vehicleModel: "",
  vehicleType: 0,
  remark: "",
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  vehicleModel: [
    ["required", $t("PEnterModelName")],
    [debounce(repeatVehicleModel), $t("youEnterModelExists")],
  ],
  vehicleType: [["required", $t("PSelectType")]],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    await addVehicleModel(form);
    emits("update:show", false);
    Message("success", $t("createVehicleModelSuccess"));
    emits("confirm");
    formRef.value.resetFields();
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  vehicleType: [] as any[],
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      // 车辆类型
      const { operConfigList } = await getConfigList({
        configList: ["vehicle_type"],
      });
      formOptions.vehicleType = operConfigList.map((item) => ({
        value: Number(item.k),
        label: item.val,
      }));
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
}
</style>
