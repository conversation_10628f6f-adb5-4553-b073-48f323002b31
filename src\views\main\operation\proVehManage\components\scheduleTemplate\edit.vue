<template>
  <section
    class="template-edit"
    ref="templateEditRef"
    v-if="props.show"
  >
    <div class="template-edit-top">
      <div class="template-edit-top-left">{{ props.proAreaName }}—{{ props.proName }}</div>
      <div class="template-edit-top-right">
        <x-button
          type="white"
          :text="$t('cancel')"
          @click="cancelAll"
          style="margin-right: 12px"
        />
        <x-button
          type="blue"
          :disabled="!isSaveValid"
          :text="$t('save')"
          @click="saveAll"
        />
      </div>
    </div>
    <div class="template-edit-bottom">
      <template
        v-for="(template, templateIdx) in templateList"
        :key="templateIdx"
      >
        <div class="template">
          <div class="template-title">
            <div class="left">
              <x-input
                v-model:value="template.tempName"
                :placeholder="$t('templateName')"
                style="width: 400px"
                @input="validateTempName(template.tempName, templateIdx)"
              />
            </div>
            <div class="right">
              <x-button
                type="highlightWhite"
                :text="$t('addTask')"
                @click="addTask(templateIdx)"
                style="margin-right: 12px"
              />

              <x-popover
                trigger="hover"
                placement="bottom"
                :container="templateEditRef"
              >
                <x-icon
                  name="del_x"
                  width="16"
                  height="16"
                  class="del-icon"
                  @click="deleteTemplate(templateIdx)"
                />
                <template #content>
                  <div class="task-preview-hover-popover">
                    {{ $t("deleteTemplate") }}
                  </div>
                </template>
              </x-popover>
            </div>
          </div>
          <div
            class="template-content"
            v-for="(task, taskIdx) in template.scheduleList"
            :key="taskIdx"
          >
            <TaskItem
              :task="task"
              :formOptions="formOptions"
              :popupContainer="templateEditRef"
              @deleteTask="deleteTask(templateIdx, taskIdx)"
              @editTask="editTask(templateIdx, taskIdx)"
              @confirmTask="confirmTask(templateIdx, taskIdx)"
            />
          </div>
        </div>
      </template>
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive, computed } from "vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import {
  getScheduleTempDetail,
  getRouteTempListByArea,
  getStationListByArea,
  updateScheduleTemplate,
} from "@/services/api";
import { createNewTask, validateTask, type TaskItemType } from "../utils";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xIcon from "@/components/x-icon.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xPopover from "@/components/x-popover.vue";
import TaskItem from "../taskItem.vue";
import type { GetScheduleTempDetailResponse } from "@/services/type";
import { getTemplateVersionName } from "@/assets/ts/config";
const $t = i18nSimpleKey("proVehManage");

type TemplateItem = Omit<GetScheduleTempDetailResponse["scheduleTemplateList"][0], "scheduleList"> & {
  scheduleList: TaskItemType[];
};

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  proAreaId: {
    type: String,
    required: true,
  },
  proName: {
    type: String,
    required: true,
  },
  proAreaName: {
    type: String,
    required: true,
  },
});
const emit = defineEmits(["back"]);

const templateEditRef = ref<any>();

const templateList = ref([] as TemplateItem[]);

// 控制只能有一个任务处于编辑状态
const isEditing = ref(false);

const formOptions = reactive({
  routeTemplate: [] as { label: string; value: string }[],
  chargingStation: [] as { label: string; value: string }[],
  garbageStation: [] as { label: string; value: string }[],
  parkingStation: [] as { label: string; value: string }[],
  wateringStation: [] as { label: string; value: string }[],
});

const isSaveValid = computed(() => {
  return templateList.value.every((template: any) => {
    return (
      !isEditing.value &&
      template.tempName &&
      template.scheduleList.length > 0 &&
      template.scheduleList.every((task: any) => validateTask(task))
    );
  });
});

const validateTempName = (name: string, idx: number) => {
  const isDuplicate = templateList.value.some(
    (template: any, templateIdx: number) => template.tempName === name && templateIdx !== idx
  );
  if (isDuplicate) {
    Message("error", $t("tempNameCannotRepeat"));
  }
};

/**
 * 操作
 */
// 取消所有更改
const cancelAll = debounce(() => {
  getDetailInfo();
  isEditing.value = false;
  Message("success", $t("initialSettingRestored"));
});

// 保存所有更改
const saveAll = debounce(async () => {
  const param: any = {
    proAreaId: props.proAreaId,
    scheduleTemplateList: templateList.value.map((v) => ({
      ...v,
      scheduleList: v.scheduleList.map((t) => ({
        ...t,
        stationType: t.stationType === -1 ? null : t.stationType,
        scheduleType: t.scheduleType === 4 ? t._switchType : t.scheduleType,
        _switchType: null,
      })),
    })),
  };
  await updateScheduleTemplate(param);
  Message("success", $t("saveSuccess"));
  emit("back");
});

// 删除模板
const deleteTemplate = (idx: number) => {
  xModal.confirm({
    title: $t("sureToDeletTemplate"),
    content: <div style="font-size:14px;color:#383838;">{$t("canNotRecoverDeleted")}</div>,
    cancelText: $t("cancel"),
    confirmText: $t("ok"),
    confirm() {
      return new Promise((resolve) => {
        templateList.value.splice(idx, 1);
        resolve({});
      });
    },
  });
};

// 添加任务
const addTask = debounce((idx: number) => {
  if (isEditing.value) {
    Message("error", $t("PConfirmBeforeAdd"));
    return;
  }
  templateList.value[idx].scheduleList.push(createNewTask());
  isEditing.value = true;
});

// 保存任务
const confirmTask = debounce((templateIdx: number, taskIdx: number) => {
  const task = templateList.value[templateIdx].scheduleList[taskIdx];
  task.editStatus = false;
  isEditing.value = false;
});

// 编辑任务
const editTask = (templateIdx: number, taskIdx: number) => {
  if (isEditing.value) {
    Message("error", $t("PEditBeforeConfirm"));
    return;
  }
  const task = templateList.value[templateIdx].scheduleList[taskIdx];
  task.editStatus = true;
  isEditing.value = true;
};

// 删除任务
const deleteTask = (templateIdx: number, taskIdx: number) => {
  const taskList = templateList.value[templateIdx].scheduleList;
  if (taskList[taskIdx].editStatus) {
    isEditing.value = false;
  }
  taskList.splice(taskIdx, 1);
};

// 获取模板数据
const getDetailInfo = async () => {
  const { scheduleTemplateList } = await getScheduleTempDetail({ proAreaId: props.proAreaId });
  templateList.value =
    scheduleTemplateList?.map((template) => {
      template.scheduleList = template.scheduleList.map(
        (task) =>
          ({
            ...task,
            executeDayType: task.executeDays && !task.executeDays.includes("0") ? "weekly" : "daily",
            editStatus: false,
            isNew: false,
            coverCleanType: task.relId !== "0" ? 2 : 1,
            executeTimes: task.executeTimes ?? 1,
            scheduleType: task.scheduleType === 6 ? 4 : task.scheduleType,
            stationType: task.stationType ? task.stationType : -1,
            _switchType: [4, 6].includes(task.scheduleType ?? 1) ? task.scheduleType : null,
          } as TaskItemType)
      );
      return template as any;
    }) || [];
};

/** 获取站点列表及模版列表 */
const getOptinsDataList = async () => {
  const resArr = await Promise.all([
    getRouteTempListByArea({ proAreaId: props.proAreaId }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 1 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 2 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 3 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 4 }),
  ]);
  const [routeTemplateList, garbageStationList, chargingStationList, wateringStationList, parkingStationList] =
    resArr.map((v) =>
      v.map((item) => ({ ...item, label: item.relName + getTemplateVersionName(item.version), value: item.relId }))
    );
  formOptions.routeTemplate = routeTemplateList;
  formOptions.garbageStation = garbageStationList;
  formOptions.chargingStation = chargingStationList;
  formOptions.wateringStation = wateringStationList;
  formOptions.parkingStation = parkingStationList;
};
getOptinsDataList();
getDetailInfo();
</script>

<style lang="scss" scoped>
.template-edit {
  height: 100%;
  &-top {
    @include ct-f(y) {
      justify-content: space-between;
    }
    margin: 15px 0;
    @include sc(12px, #383838);
    &-right {
      display: flex;
    }
  }
  &-bottom {
    height: calc(100% - 100px);
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    .template {
      padding: 10px 15px;
      margin: 10px 0 20px 0;
      background: #f5f8fe;
      border-radius: 8px;
      &-title {
        width: 100%;
        @include ct-f(y) {
          justify-content: space-between;
        }
        .left {
          :deep(.x-input-content) {
            border: 1px solid #fff;
            &.focus {
              border: 1px solid #5964fb !important;
            }
          }
        }
        .right {
          @include ct-f;
          .del-icon {
            cursor: pointer;
          }
        }
      }
    }
  }
  :deep(.button-confirm) {
    margin: 0 auto;
  }
  :deep(.popover__body) {
    width: 140px;
    justify-content: center;
  }
  :deep(.time-panel) {
    height: 180px;
  }
}
</style>
