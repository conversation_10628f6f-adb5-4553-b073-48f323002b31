<template>
  <section class="sub-title" :class="{ 'hide-after': !props.showIcon }">
    <x-timer-image
      class="work-mile"
      imageName="screen_sub_title_animate.png"
      :frameNum="125"
      :frameTime="50"
      :vertical="true"
    >
      <span><slot></slot></span>
    </x-timer-image>
  </section>
</template>
<script lang="ts" setup>
import xTimerImage from "@/components/x-timer-image.vue";
const props = defineProps({
  showIcon: {
    type: Boolean,
    default: true,
  },
});
</script>

<style lang="scss" scoped>
.sub-title {
  position: relative;
  @include wh(318px, 6vh) {
    @include sc(1.8vh, #dde5fa) {
      font-weight: 600;
    }
  }
  &::after {
    content: "";
    position: absolute;
    top: 0;
    left: 13px;
    @include wh(21px, 2.68vh) {
      @include bis("@/assets/images/screen_sub_title_left.png");
    }
  }
  &.hide-after::after {
    display: none;
  }
  span {
    padding-left: 34px;
  }
}
</style>
