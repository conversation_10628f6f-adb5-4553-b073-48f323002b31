<template>
  <x-modal
    :visible="props.show"
    @update:visible="updateVisible"
    width="1800px"
    height="90vh"
    :maskClosable="false"
    :bodyStyle="{ padding: '4vh 60px' }"
  >
    <template #topTitle>
      <sub-title :showIcon="false" class="content-header-top-title">{{
        id
      }}</sub-title>
    </template>
    <div class="content">
      <div class="content-top">
        <span
          class="content-top-close"
          v-html="closeText"
          @click="updateVisible(false)"
        ></span>
        <template v-for="(item, index) in tabsTop.config" :key="index">
          <div
            :class="['tab', { active: item.value === tabsTop.active }]"
            @click="changeTabTop(item.value)"
          >
            <x-icon
              :name="item.icon"
              width="22px"
              height="22px"
              style="margin-right: 10px"
            />
            {{ item.label }}
          </div>
        </template>
      </div>
      <div class="content-bottom">
        <div v-if="tabsTop.active === 'monitor'" class="monitor">
          <div class="monitor-left">
            <div v-if="info.videoArr.length === 0" class="monitor-left-novideo">
              <img src="@/assets/images/screen_table_nodata.png" alt="" />
              <div style="margin-top: 0.6vh">此车没有视频流</div>
            </div>
            <video-flv
              style="width: 50%; height: 50%"
              v-for="(item, index) in info.videoArr"
              :key="index"
              :url="item.url"
              :name="item.name"
              :position="item.position"
            />
          </div>
          <carDetailTask :id="props.id" class="monitor-right" />
          <!-- <div class="monitor-bottom">
            <div class="monitor-tabs">
              <template v-for="(item, index) in tabsBottom.config" :key="index">
                <div
                  class="tab"
                  :class="{ active: item.value === tabsBottom.active }"
                  @click="changeTabBottom(item.value)"
                >
                  <x-icon
                    :name="
                      item.value === tabsBottom.active
                        ? `${item.icon}_active`
                        : item.icon
                    "
                    width="70px"
                    height="62px"
                  />
                  <span class="tab-text">{{ item.label }}</span>
                </div>
              </template>
            </div>
          </div> -->
        </div>
        <CarDetailHistory :id="props.id" v-if="tabsTop.active === 'history'" />
      </div>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import xModal from "./x-modal.vue";
import carDetailTask from "./carDetailTask.vue";
import xIcon from "@/components/x-icon.vue";
import subTitle from "./subTitle.vue";
import { liveCarVideoInfo } from "@/services/api";
import VideoFlv from "./videoFlv.vue";
import CarDetailHistory from "./carDetailHistory.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

const closeText = ref("&#10005");

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      tabsTop.active = "monitor";
      const carInfo = await liveCarVideoInfo(props.id);
      if (carInfo?.sn) {
        info.videoArr = [
          {
            url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch0`,
            name: props.id,
            position: "前",
          },
          {
            url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch3`,
            name: props.id,
            position: "后",
          },
          {
            url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch1`,
            name: props.id,
            position: "左",
          },
          {
            url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch2`,
            name: props.id,
            position: "右",
          },
          // {
          //   url: "https://zhuguibiao.github.io/flv.js/assets/264.flv",
          //   name: "A0007",
          //   position: "环",
          // },
        ];
      }
    } else {
      info.videoArr = [];
    }
  }
);

const info = reactive({
  videoArr: [] as { url: string; name: any; position: any }[],
});

const tabsTop = reactive({
  config: [
    {
      label: "实时监控",
      value: "monitor",
      icon: "screen_detail_monitor",
    },
    {
      label: "历史监控",
      value: "history",
      icon: "screen_detail_history",
    },
  ],
  active: "",
});

const changeTabTop = (value: string) => {
  tabsTop.active = value;
};

// const tabsBottom = reactive({
//   config: [
//     {
//       label: "实时数据",
//       value: "live",
//       icon: "screen_detail_live",
//     },
//     {
//       label: "实时视频",
//       value: "video",
//       icon: "screen_detail_video",
//     },
//     {
//       label: "数据统计",
//       value: "summary",
//       icon: "screen_detail_summary",
//     },
//     {
//       label: "基础信息",
//       value: "base",
//       icon: "screen_detail_base",
//     },
//   ],
//   active: "live",
// });

// const changeTabBottom = (value: string) => {
//   tabsBottom.active = value;
// };
</script>

<style lang="scss" scoped>
.content {
  // overflow-y: auto;
  width: 100%;
  &-top {
    position: relative;
    @include ct-f(y);
    @include sc(2vh, rgba(255, 255, 255, 0.7));
    margin-bottom: 1vh;
    position: relative;
    &-close {
      position: absolute;
      right: 20px;
      top: 2vh;
      cursor: pointer;
    }
    .tab {
      @include ct-f;
      @include wh(180px, 5vh);
      cursor: pointer;
      position: relative;
      z-index: 1;
      &.active {
        color: rgb(221, 229, 250);
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          @include wh(100%, 100%);
          background: linear-gradient(
            0deg,
            rgba(11, 16, 54, 0.8) 17.095%,
            rgba(19, 21, 42, 0.67) 62.989%,
            rgba(21, 35, 64, 0.66) 100%
          );
          clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
          border-top-left-radius: 30px;
          border-top-right-radius: 30px;
          border-top: 1px solid rgb(147, 166, 255);
          z-index: -1;
        }
        &::after {
          content: "";
          position: absolute;
          bottom: -0.1vh;
          @include wh(100%, 1vh);
          background: url("@/assets/images/screen_tab_bottom.png") no-repeat
            center center;
          background-size: cover;
          z-index: 0;
        }
      }
    }
  }
  &-bottom {
    @include wh(1680px, 75vh);
    .monitor {
      @include wh(100%);
      &-left {
        display: flex;
        flex-wrap: wrap;
        @include wh(1294px, 100%);
        &-novideo {
          margin: auto;
          color: #dde5fa;
          font-size: 1.6vh;
          text-align: center;
        }
      }
      &-right {
        position: absolute;
        right: 60px;
        top: 10vh;
      }
      &-bottom {
        @include wh(100%, 10vh);
        position: absolute;
        bottom: 2vh;
        background: url("@/assets/images/screen_detail_bottom.png") no-repeat
          center center;
        @include ct-f;
        .tabs {
          width: 400px;
          margin-bottom: 3vh;
          @include ct-f {
            justify-content: space-between;
          }
          .tab {
            position: relative;
            cursor: pointer;
            &-text {
              width: 100%;
              text-align: center;
              @include ct-p {
                top: 80%;
              }
              @include sc(1.4vh, rgba(255, 255, 255, 0.8));
              font-weight: 500;
            }
          }
        }
      }
    }
  }
}
.content-header-top-title {
  position: absolute;
  top: -1.6vh;
  left: 45px;
  :deep(span) {
    display: inline-block;
    margin-top: 1vh;
  }
}
</style>
