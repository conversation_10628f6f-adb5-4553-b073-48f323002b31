<template>
  <section class="rtk-manage" ref="rtkManageRef">
    <div class="rtk-manage-top">
      <div class="top-title">{{ $t("rtkManage") }}</div>
      <x-button
        v-if="permitList.includes('sys:rtk:save')"
        :text="$t('add')"
        type="paleBlue"
        icon="button_add"
        @click="addRTK.show = true"
      />
      <Add v-model:show="addRTK.show" @confirm="addRTK.confirm" />
    </div>
    <div class="rtk-manage-middle">
      <div class="middle-left">
        <div class="middle-left-row">
          <div class="middle-left-item" style="width: 320px">
            <x-select
              v-model:value="searchForm.entName"
              :options="formOptions.companyOptions"
              :popupContainer="rtkManageRef"
              :placeholder="$t('PEnterEnt')"
              showSearch
            />
          </div>
          <div class="middle-left-item" style="width: 200px">
            <x-input
              v-model:value="searchForm.rtkAccount"
              :placeholder="$t('PEnterRTK')"
            />
          </div>
          <div class="middle-left-item" style="width: 120px">
            <x-select
              v-model:value="searchForm.status"
              :options="formOptions.statusOptions"
              :popupContainer="rtkManageRef"
            />
          </div>
        </div>
        <div class="middle-left-row">
          <div class="middle-left-item" style="width: 320px">
            <DateRangePicker
              v-model:value="searchForm.opDate"
              :popupContainer="rtkManageRef"
              :placeholder="[$t('startDate'), $t('endDate')]"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:rtk:list')"
          :text="$t('search')"
          type="blue"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button :text="$t('reset')" type="green" @click="resetSearchForm" />
      </div>
    </div>
    <div class="rtk-manage-bottom">
      <x-table
        :dataSource="table.dataSource"
        :cols="table.cols"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- RTK账号 -->
        <template #rtkAccount="{ record }">
          {{ formatAccount(record.rtkAccount) }}
        </template>
        <!-- 状态 -->
        <template #statusText="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.status === 1 ? 'status_blue' : 'status_gray'"
              width="12"
              height="12"
            />
            <span>{{ record.status === 1 ? $t("inUse") : $t("unUsed") }}</span>
          </div>
        </template>
        <!-- 企业名 -->
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            :container="rtkManageRef"
            class="table-company-popover"
          >
            {{ record.entName }}
            <template #content>
              <div class="rtk-manage-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:rtk:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:rtk:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:rtk:delete')"
                @click="handleDelete(record.id)"
                style="color: #e24562"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editRTK.show"
        :id="editRTK.id"
        @confirm="addRTK.confirm"
      />
      <Detail v-model:show="rtkDetail.show" :id="rtkDetail.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive } from "vue";
import type { PageSizeType } from "@/components/types";
import { rtkList, compList, delRTK } from "@/services/api";
import type { RTKListRequest } from "@/services/type";
import { rtkStatusType } from "@/assets/ts/config";
import { formatAccount } from "@/assets/ts/utils";
import { useMainStore } from "@/stores/main";
import { DateRangePicker } from "@/components/x-date-picker";
import XButton from "@/components/x-button.vue";
import XInput from "@/components/x-input.vue";
import XSelect from "@/components/x-select.vue";
import XTable from "@/components/x-table.vue";
import XPopover from "@/components/x-popover.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("rtkManage");

const {
  userInfo: { permitList },
} = useMainStore();

const rtkManageRef = ref<any>();

/**
 * 表单项
 */
const searchForm = reactive({
  entName: "",
  rtkAccount: "",
  status: "",
  opDate: ["", ""],
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  companyOptions: [] as any[],
  statusOptions: [
    {
      value: "",
      label: $t("all"),
    },
    ...rtkStatusType,
  ],
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "80",
    },
    {
      key: "rtkAccount",
      title: $t("rtkAccount"),
      width: "180",
      slots: "rtkAccount",
    },
    {
      key: "statusText",
      title: $t("status"),
      width: "120",
      slots: "statusText",
    },
    {
      key: "vehicleNo",
      title: $t("vehicleNo"),
      width: "120",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "200",
      slots: "entName",
    },
    {
      key: "registerDateText",
      title: $t("registerDate"),
      width: "180",
    },
    {
      key: "netOperatorText",
      title: $t("netOperator"),
      width: "180",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "330",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  searchRTKList();
};

/**
 * 表格-搜索
 */
const searchRTKList = async () => {
  table.loading = true;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.entName,
    rtkAccount: searchForm.rtkAccount,
  } as RTKListRequest;

  const [opDateStart, opDateEnd] = searchForm.opDate;
  opDateStart && (params.registerStatDate = opDateStart);
  opDateEnd && (params.registerEndDate = opDateEnd);

  searchForm.status !== "" && (params.status = parseInt(searchForm.status));

  const { totalCount, list } = await rtkList(params);
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.rtkAccount = "";
  searchForm.status = "";
  searchForm.opDate = ["", ""];
  searchRTKList();
};

/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchRTKList();
};

/**
 * 相关操作
 */
// 新增
const addRTK = reactive({
  show: false,
  confirm: () => searchRTKList(),
});
// 编辑
const editRTK = reactive({
  show: false,
  id: "",
  confirm: () => searchRTKList(),
});
const openEdit = (id: string) => {
  editRTK.id = id;
  editRTK.show = true;
};
// 详情
const rtkDetail = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  rtkDetail.id = id;
  rtkDetail.show = true;
};
// 删除
const handleDelete = (id: string) => {
  xModal.confirm({
    title: $t("sureToDelRTK"),
    confirm() {
      return delRTK(id).then(() => {
        Message("success", $t("deleteSuccess"));
        searchRTKList();
      });
    },
  });
};

/**
 * 表格-首次加载
 */
(async () => {
  searchRTKList();
  // 企业名称
  formOptions.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
})();
</script>

<style lang="scss" scoped>
.rtk-manage-table-company-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
.rtk-manage {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj {
      margin-top: 20px;
    }
    @include wh(100%, auto);
    .middle-right {
      display: flex;
    }
    .middle-left-row {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        padding: 6px 8px 6px 0;
        line-height: 20px;
        @include sc(14px, #5e5e5e);
      }
      &-value {
        flex: 1;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-time {
      color: rgb(159, 159, 164);
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
