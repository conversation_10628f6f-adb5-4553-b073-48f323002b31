<template>
  <section class="car-alarms">
    <div
      ref="floatBallRef"
      @mousemove="handleMouseMove"
      @mouseup="handleMouseUp"
      @mousedown="handleMouseDown"
      :class="{
        'float-ball': !panel.show,
        'float-panel': panel.show,
        'click-ball-status': !panel.show && position.mouseDownState,
      }"
      :style="intialPosition"
    >
      <!-- 悬浮球 -->
      <div
        v-if="!panel.show"
        class="car-alarms-ball"
        @click="showPanel"
      >
        <div v-if="showNum && warnNum">
          <x-icon
            name="alarm_pop_bg"
            width="30px"
            height="30px"
          />
          <span class="alarm-count">{{ warnNum < 100 ? warnNum : "99+" }}</span>
        </div>
        <div v-else>
          <x-icon
            name="alarm_pop"
            width="30"
            height="30"
          />
        </div>
      </div>
      <!-- 消息面板 -->
      <div
        ref="panelContainerRef"
        v-else
        :class="[
          'car-alarms-panel',
          {
            'panel-show-animate': panel.show,
          },
        ]"
      >
        <div class="car-alarms-panel-top">
          <div class="top-left">
            <x-radio-button
              type="tab"
              :gap="5"
              v-model:value="readTab.activeIndex"
              :options="readTab.statusList"
              @change="changeReadStatus"
            />
          </div>
          <div class="top-right">
            <div
              class="collapse"
              @click="hidePanel"
            ></div>
          </div>
        </div>
        <div class="car-alarms-panel-center">
          <div
            class="center-item"
            v-for="(item, index) in alarmTab.typeList"
            :key="index"
          >
            <x-popover
              trigger="hover"
              placement="bottom"
              :container="panelContainerRef"
            >
              <x-icon
                v-if="item.icon"
                :name="alarmTab.activeIndex === index ? `${item.icon}_enable` : `${item.icon}_default`"
                width="16"
                height="16"
                @click="changeAlarmType(index)"
              />
              <template #content>
                <div class="car-alarms-type-tab-hover-popover">
                  {{ item.tip }}
                </div>
              </template>
            </x-popover>
          </div>
        </div>
        <div
          ref="panelContentRef"
          class="car-alarms-panel-bottom"
          @scroll="loadMore"
        >
          <div
            v-if="panel.loading"
            class="car-alarms-panel-bottom-loading"
          >
            <img
              src="@/assets/images/loading.gif"
              alt=""
            />
          </div>
          <div v-else>
            <div
              v-if="alarmInfo.alarmList.length === 0"
              class="car-alarms-panel-bottom-no-result"
            >
              <x-icon
                name="no_result"
                width="40"
                height="45"
              />
              <div class="tip">{{ $t("noContent") }}</div>
            </div>
            <div
              v-else
              v-for="(item, index) in alarmInfo.alarmList"
              :key="index"
              class="bottom-item"
            >
              <div class="bottom-item-time">
                {{ item.time }}
              </div>
              <div
                class="bottom-item-content"
                @click="clickAlarmHandle(item.device_id)"
              >
                <x-icon
                  v-if="item.icon"
                  :name="item.icon"
                  class="bottom-item-content-icon"
                />
                {{ item.device_id }}，{{ item.message }}
                <div
                  v-if="item.messageType === 5"
                  @click="showImage(item)"
                  class="img"
                >
                  <span class="img-text">{{ $t("view") }}</span>
                  <x-icon
                    name="alarm_img"
                    class="bottom-item-content-icon"
                  />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />
  </section>
</template>
<script lang="ts" setup>
import { reactive, ref, watch, computed, onMounted, onBeforeUnmount, nextTick } from "vue";
import type { PropType } from "vue";
import { useMainStore } from "@/stores/main";
import { openAlarmList, closeAlarmList } from "@/services/wsapi";
import { mapZoom, alarmTypeMap } from "@/services/wsconfig";
import { animate } from "@/assets/ts/animate";
import xIcon from "@/components/x-icon.vue";
import xPopover from "@/components/x-popover.vue";
import xRadioButton from "@/components/x-radio-button.vue";
import xImage from "@/components/x-image.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
});

const { socket, floatBallPosition } = useMainStore();

const floatBallRef = ref<any>();
const panelContainerRef = ref<any>();
const panelContentRef = ref<any>();

/*
 * 接收状态
 */
const readTab = reactive({
  statusList: [
    {
      value: 0,
      label: $t("unread"),
    },
    {
      value: 1,
      label: $t("read"),
    },
    {
      value: 2,
      label: $t("all"),
    },
  ],
  activeIndex: 0,
});
const changeReadStatus = (index: number) => {
  readTab.activeIndex = index;
  alarmInfo.alarmList = [];
  lastId.value = "";
  getMessageList();
};

/*
 * 消息类别
 */
const alarmTab = reactive({
  typeList: alarmTypeMap,
  activeIndex: 0,
});
const changeAlarmType = (index: number) => {
  alarmTab.activeIndex = index;
  alarmInfo.alarmList = [];
  lastId.value = "";
  getMessageList();
};

/*
 * 消息面板
 */
const panel = reactive({
  show: false,
  loading: false,
});
// 打开面板
const showPanel = () => {
  if (position.lastMoveIndex - position.curMoveIndex <= 10) {
    panel.show = true;
    alarmInfo.alarmList = [];
    lastId.value = "";
    getMessageList();
  }
  position.curMoveIndex = position.lastMoveIndex;
};
// 隐藏面板
const hidePanel = () => {
  closeAlarmList();
  panel.show = false;
  // 关闭面板后隐藏消息数 避免数据延迟展示
  showNum.value = false;
  readTab.activeIndex = 0;
  alarmTab.activeIndex = 0;
};

/*
 * 消息内容
 */
const lastId = ref("");
const showNum = ref(false);
const alarmInfo = reactive({
  alarmList: [] as any[],
});
// 消息数量
const warnNum = computed(() => {
  return socket.alarmMessageStatus?.numUnread || 0;
});
// 获取消息列表
const getMessageList = () => {
  panel.loading = true;
  let newTab = lastId.value ? false : true;
  openAlarmList(String(alarmTab.activeIndex), String(readTab.activeIndex), lastId.value)
    .then((res: any) => {
      const list = res.message.messageList as _AlarmMessageStatusType[];
      if (list.length > 0) {
        const newList = list.map((item) => {
          return {
            ...item,
            icon: alarmTab.typeList[item.messageType === 4 ? 3 : item.messageType]?.icon,
          };
        });
        if (lastId.value) {
          alarmInfo.alarmList = alarmInfo.alarmList.concat(newList);
        } else {
          alarmInfo.alarmList = newList;
        }
        lastId.value = list.length > 0 ? list[list.length - 1].id : "";
      }
      console.log(alarmInfo.alarmList);

      panel.loading = false;
      nextTick(() => {
        const scrollDom = panelContentRef.value;
        scrollDom.scrollTop = newTab ? 0 : scrollDom.scrollHeight - scrollDom.clientHeight - 10;
      });
    })
    .catch((error) => {
      panel.loading = false;
      console.log("getMessageList", error);
    });
};
// 滚动加载
const loadMore = () => {
  const scrollDom = panelContentRef.value;
  if (scrollDom.clientHeight + scrollDom.scrollTop >= scrollDom.scrollHeight) {
    if (!panel.loading) {
      panel.loading = true;
      getMessageList();
    }
  }
};
// 点击告警信息
const clickAlarmHandle = (id: string) => {
  const carBaseStatus = socket.baseStatus?.filter((v) => id?.includes(v.deviceId));
  props.map.clearInfoWindow();
  props.map.setZoomAndCenter(mapZoom, [carBaseStatus![0].longitude, carBaseStatus![0].latitude]);
};
// 查看事件图片
const image = reactive({
  show: false,
  url: "",
});
const showImage = (info: any) => {
  image.show = true;
  image.url = info.picUrl || "";
};
// 监控新消息
watch(
  () => socket.alarmMessageStatus,
  (newV) => {
    if (newV && panel.show) {
      const newAlarm = [newV].map((item) => {
        return {
          device_id: newV.device_id,
          time: newV.time,
          message: newV.message,
          messageType: newV.messageType,
          picUrl: newV.picUrl,
          icon: alarmTab.typeList[item.messageType === 4 ? 3 : item.messageType]?.icon,
        };
      });
      if (newV.device_id && (newV.messageType === alarmTab.activeIndex || alarmTab.activeIndex === 0)) {
        alarmInfo.alarmList = newAlarm.concat(alarmInfo.alarmList);
      }
    }
    showNum.value = true;
  }
);

/*
 * 拖拽效果
 */
const { clientHeight: containerHeight, clientWidth: containerWidth } = document.getElementsByClassName("car-live")[0];
const position = reactive({
  // 鼠标按下状态
  mouseDownState: false,
  // 原点坐标
  iX: 0,
  iY: 0,
  // 拖拽后坐标
  dX: containerWidth - 50,
  dY: containerHeight - 10,
  // 区分拖拽和点击
  lastMoveIndex: 0,
  curMoveIndex: 0,
  zoom: 1,
});
// 鼠标按下
const handleMouseDown = (event: MouseEvent) => {
  // 适配全局zoom缩放
  // @ts-ignore
  position.zoom = Number(getComputedStyle(document.querySelector(".main")).zoom);
  const clientX = event.clientX / position.zoom;
  const clientY = event.clientY / position.zoom;
  position.iX = clientX - floatBallRef.value.offsetLeft;
  position.iY = clientY - floatBallRef.value.offsetTop;
  position.mouseDownState = true;
};
// 鼠标拖拽
const handleMouseMove = (event: MouseEvent) => {
  const { clientHeight: containerHeight, clientWidth: containerWidth } = document.getElementsByClassName("car-live")[0];
  if (position.mouseDownState && !panel.show) {
    const clientX = event.clientX / position.zoom;
    const clientY = event.clientY / position.zoom;
    const { style: floatBallStyle } = floatBallRef.value;
    let [x, y] = [clientX - position.iX, clientY - position.iY];
    // 超出可视区时贴边处理
    if (x > containerWidth - 50) x = containerWidth - 50;
    else if (x < 0) x = 0;
    if (y > containerHeight - 50) y = containerHeight - 50;
    else if (y < 0) y = 0;
    position.dX = x;
    position.dY = y;
    floatBallStyle.left = `${x}px`;
    floatBallStyle.top = `${y}px`;
    floatBallStyle.bottom = "auto";
    floatBallStyle.right = "auto";
    position.lastMoveIndex++;
    // 拖拽状态处理
    const floatBallRect = floatBallRef.value.getBoundingClientRect();
    if (
      clientX < floatBallRect.left ||
      clientX > floatBallRect.right ||
      clientY < floatBallRect.top ||
      clientY > floatBallRect.bottom
    ) {
      handleMouseUp();
    }
  }
};
// 鼠标抬起
const handleMouseUp = () => {
  const { clientHeight: containerHeight, clientWidth: containerWidth } = document.getElementsByClassName("car-live")[0];
  let { style: floatBallStyle } = floatBallRef.value;
  if (position.dY > 0 && position.dY < containerHeight - 50) {
    //  left小于半屏：左吸附
    if (position.dX < containerWidth / 2) {
      if (floatBallStyle.left !== "0px" || floatBallStyle.right !== "auto") {
        animate(
          floatBallRef.value,
          {
            left: 0,
            right: containerWidth - 50,
          },
          400,
          "ease"
        ).then(() => {
          floatBallStyle.left = 0;
          floatBallStyle.right = "auto";
        });
      }
    } else {
      // left大于半屏：右吸附
      if (floatBallStyle.right !== "0px" || floatBallStyle.left !== "auto") {
        animate(
          floatBallRef.value,
          {
            right: 0,
            left: containerWidth - 50,
          },
          400,
          "ease"
        ).then(() => {
          floatBallStyle.left = "auto";
          floatBallStyle.right = 0;
        });
      }
    }
    if (position.dY >= containerHeight / 2) {
      floatBallStyle.top = "auto";
      floatBallStyle.bottom = containerHeight - position.dY - 50 + "px";
    }
  } else {
    if (position.dY === 0) {
      // 贴顶
      floatBallStyle.top = 0;
      floatBallStyle.bottom = "auto";
    } else if (position.dY === containerHeight - 50) {
      // 贴底
      floatBallStyle.bottom = 0;
      floatBallStyle.top = "auto";
    }
    if (position.dX >= containerWidth / 2) {
      floatBallStyle.left = "auto";
      floatBallStyle.right = containerWidth - position.dX - 50 + "px";
    }
  }
  position.mouseDownState = false;
  // 保存悬浮球位置
  setTimeout(() => {
    useMainStore().floatBallPosition = {
      top: floatBallStyle.top,
      right: floatBallStyle.right,
      bottom: floatBallStyle.bottom,
      left: floatBallStyle.left,
    };
  }, 500);
};
const intialPosition = computed(() => {
  const { bottom, right, top, left } = floatBallPosition;
  return `top: ${top};right: ${right};bottom: ${bottom};left: ${left};`;
});
onMounted(() => {
  window.addEventListener("mousemove", handleMouseMove);
  if (floatBallPosition.left === "0px") {
    position.dX = 0;
  }
});
onBeforeUnmount(() => {
  window.removeEventListener("mousemove", handleMouseMove);
});
</script>

<style lang="scss" scoped>
.car-alarms {
  .car-alarms-type-tab-hover-popover {
    height: 24px;
    padding: 0 6px;
    line-height: 24px;
  }
  .float-ball {
    position: absolute;
    @include wh(50px);
    border-radius: 5px;
    background: #7422fe;
    z-index: calc(var(--z-index-popover) - 1);
    &:hover {
      cursor: pointer;
      transform: scale(1.16);
    }
  }
  .click-ball-status {
    background: #5964fb;
    .car-alarms-ball {
      .alarm-count {
        @include sc(12px, #5964fb);
      }
    }
  }
  &-ball {
    @include wh(100%, 100%);
    @include ct-f(both);
    position: relative;
    .alarm-count {
      @include ct-p {
        margin-top: -3px;
      }
      @include sc(12px, #7422fe) {
        font-weight: bold;
      }
    }
  }
  .float-panel {
    position: absolute;
    background-color: rgba(255, 255, 255, 0.9);
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    z-index: calc(var(--z-index-popover) - 1);
    @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
  }
  &-panel {
    @include wh(240px, 320px);
    &-top {
      @include fj {
        align-items: center;
      }
      @include wh(100%, 42px) {
        padding-right: 18px;
        background-color: rgb(247, 249, 254);
      }
      .top-left {
        display: flex;
      }
      .top-right {
        .collapse {
          position: relative;
          @include wh(20px);
          cursor: pointer;
          &::after {
            content: "";
            @include ct-p;
            @include wh(10px, 1.3px) {
              background-color: #383838;
              border-radius: 10px;
            }
          }
        }
      }
    }
    &-center {
      display: flex;
      padding: 5px 0 18px 9px;
      .center-item {
        margin-right: 10px;
        cursor: pointer;
      }
    }
    &-bottom {
      padding: 0 18px 0 9px;
      height: calc(320px - 86px);
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
      &-loading {
        @include ct-p;
        img {
          @include wh(30px);
        }
      }
      &-no-result {
        @include ct-p {
          margin-top: 18px;
        }
        .tip {
          margin-left: -7px;
          @include sc(14px, #9f9fa4);
        }
      }
      .bottom-item {
        margin-bottom: 15px;
        &-time {
          margin-bottom: 5px;
          @include sc(12px, #999999);
        }
        &-content {
          padding: 5px;
          cursor: pointer;
          @include sc(13px, #383838) {
            background: #f7f9fe;
            border-radius: 4px;
          }
          &-icon {
            padding: 0 2px;
            @include wh(12px);
          }
          &-img {
            display: inline-block;
            &-text {
              @include sc(11px, #5964fb);
              text-decoration: underline;
            }
          }
        }
      }
    }
  }
  .panel-show-animate {
    animation: fadeIn 0.2s ease-out forwards;
  }
}
</style>
