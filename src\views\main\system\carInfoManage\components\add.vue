<template>
  <x-drawer
    :title="$t('addCar')"
    :visible="props.show"
    :btnOption="{
      position: 'center',
      confirm: $t('confirm'),
    }"
    bodyPadding="0 0 32px 20px"
    footerType="following"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="handleCancel"
    width="550px"
  >
    <div
      ref="contentRef"
      class="content"
    >
      <div class="content-top">
        <div class="content-top-title">{{ $t("baseInfo") }}</div>
        <x-form
          ref="formRef"
          :model="form"
          :rules="formRules"
          labelFlex="80px"
        >
          <x-form-item
            :label="$t('enterprise')"
            name="entId"
          >
            <x-tree-select
              v-model:value="form.entId"
              :treeData="formOptions.company"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectEnterprise')"
              showSearch
              @update:value="getRTKList"
            />
          </x-form-item>
          <x-form-item
            :label="$t('vehicleNo')"
            name="vehicleNo"
          >
            <x-input
              v-model:value="form.vehicleNo"
              :placeholder="$t('PEnterVehicleNo')"
              :maxlength="10"
            />
          </x-form-item>
          <x-form-item
            :label="$t('vehicleType')"
            name="vehicleType"
          >
            <x-select
              v-model:value="form.vehicleType"
              :options="formOptions.vehicleType"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectvehicleType')"
              @change="changeVehicleType"
            />
          </x-form-item>
          <x-form-item
            :label="$t('vinNo')"
            name="vin"
          >
            <x-input
              v-if="form.vehicleType === 2"
              v-model:value="form.vin"
              :placeholder="$t('PEnterVinNo')"
            />
            <x-input
              v-else
              v-model:value="form.vin"
              :placeholder="$t('PEnterVinNo')"
              :maxlength="17"
            />
          </x-form-item>

          <x-form-item
            :label="$t('vehicleModel')"
            name="vehicleModelId"
          >
            <x-select
              v-model:value="form.vehicleModelId"
              :options="formOptions.vehicleModelId"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectVehicleModel')"
            />
          </x-form-item>
          <x-form-item
            :label="$t('simNo')"
            name="simId"
          >
            <x-select
              v-model:value="form.simId"
              :options="formOptions.simId"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectSimNo')"
              showSearch
            />
          </x-form-item>
          <x-form-item
            :label="$t('sn')"
            name="sn"
          >
            <x-input
              v-model:value="form.sn"
              :placeholder="$t('PEnterSN')"
              :maxlength="30"
            />
          </x-form-item>
          <x-form-item
            :label="$t('remoteSim')"
            name="remoteSim"
          >
            <x-input
              v-model:value="form.remoteSim"
              :placeholder="$t('PEnterRemoteSim')"
              :maxlength="30"
            />
          </x-form-item>
          <x-form-item
            :label="'DVR id'"
            name="dvrId"
          >
            <x-input
              v-model:value="form.dvrId"
              :placeholder="'请输入DVR id'"
              :maxlength="30"
            />
          </x-form-item>
          <x-form-item
            :label="$t('dandelionAcc')"
            name="dandelionAccount"
          >
            <x-input
              v-model:value="form.dandelionAccount"
              :placeholder="$t('PEnterDandelionAcc')"
              :maxlength="20"
            />
          </x-form-item>
          <x-form-item
            :label="$t('dandelionPWD')"
            name="dandelionPassword"
          >
            <x-input
              v-model:value="form.dandelionPassword"
              :placeholder="$t('PEnterDandelionPWD')"
              :maxlength="20"
            />
          </x-form-item>
          <x-form-item
            :label="$t('rtkAccount')"
            name="rtkAccount"
          >
            <x-select
              v-model:value="form.rtkAccount"
              :options="formOptions.rtkAccount"
              :popupContainer="contentRef"
              :disabled="!form.entId"
              :placeholder="$t('PSelectRtkAcc')"
              showSearch
            />
          </x-form-item>
          <x-form-item
            :label="$t('opDate')"
            name="opDate"
          >
            <DatePicker
              v-model:value="form.opDate"
              format="YYYY-MM-DD"
              :popupContainer="contentRef"
            />
          </x-form-item>
          <x-form-item
            label="运营状态"
            name="vehOperateStatus"
          >
            <x-select
              v-model:value="form.vehOperateStatus"
              :options="formOptions.vehOperateStatus"
              :popupContainer="contentRef"
              placeholder="请选择运营状态"
            />
          </x-form-item>
          <x-form-item
            label="远程码"
            name="remoteNo"
          >
            <x-input
              v-model:value="form.remoteNo"
              placeholder="请输入远程码"
              :maxlength="30"
            />
          </x-form-item>
        </x-form>
      </div>
      <div class="content-bottom">
        <div class="content-bottom-title">{{ $t("carConfigList") }}</div>
        <div class="content-bottom-files">
          <x-upload
            v-model:value="form.files"
            :multiple="true"
            :headers="formOptions.headers"
            :maxCount="3"
            :beforeUpload="beforeUpload"
            accept=".pdf,.doc,.docx,application/msword,application/vnd.openxmlformats-officedocument.wordprocessingml.document,.csv,application/vnd.openxmlformats-officedocument.spreadsheetml.sheet,application/vnd.ms-excel"
            action="/cszg/file/upload"
            :placeholder="$t('supportsWordPDForExcel')"
          />
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import type { FileItem } from "@/components/x-upload/x-upload.vue";
import { ref, reactive, watch, computed } from "vue";
import { getLocalStorage } from "@/assets/ts/storage";
import {
  compTree,
  addVehicle,
  getConfigList,
  vehicleModelList,
  getEntSim,
  vehicleVinRepeat,
  vehicleNoRepeat,
  dandelionAccountRepeat,
  getEntRTK,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm, { type FormRulesType } from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import { DatePicker } from "@/components/x-date-picker";
import xSelect from "@/components/x-select.vue";
import xUpload from "@/components/x-upload";
import Message from "@/components/x-message";
import { resTreeToXTree, debounce } from "@/assets/ts/utils";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { vehOperateStatusType } from "@/assets/ts/config";

const $t = i18nSimpleKey("carInfoManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

//车辆类型change
const changeVehicleType = () => {
  form.vin = "";
};

// 车辆型号
const updateVehicleModelList = async () => {
  const params = {
    page: 1,
    limit: 50,
  } as any;

  // 车辆类型与车辆型号联动
  form.vehicleType && (params.vehicleType = form.vehicleType);

  // 车辆型号
  const { list: vehicleModels } = await vehicleModelList(params);

  if (vehicleModels) {
    formOptions.vehicleModelId = vehicleModels.map((item) => ({
      value: String(item.id),
      label: item.vehicleModel,
    }));
  } else {
    formOptions.vehicleModelId = [];
  }
};

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    // 重置
    formRef.value && formRef.value.resetFields();
    formOptions.simId = [];
    formOptions.vehicleModelId = [];
  }
};

/**
 * 重复VIN
 */
const repeatVehicleVin = async () => {
  // if (form.vin.length !== 17) {
  //   return false;
  // }
  const result = await vehicleVinRepeat({
    vin: form.vin,
  });
  return !result?.repeat;
};
/**
 * 重复车牌号
 */
const repeatVehicleNo = async () => {
  const result = await vehicleNoRepeat({
    vehicleNo: form.vehicleNo,
  });
  return !result?.repeat;
};
/**
 * 重复蒲公英账号
 */
const repeatDandelionAccount = async () => {
  const result = await dandelionAccountRepeat({
    dandelionAccount: form.dandelionAccount,
  });
  return !result?.repeat;
};
/**
 * 校验vin是否等于17位 仅限car
 */
const vaildateVin = () => {
  if (Number(form.vehicleType) === 1) {
    return form.vin.length === 17;
  }
  return true;
};

/**
 * 表单项
 */
const form = reactive({
  entId: 0,
  vin: "",
  vehicleNo: "",
  vehicleType: "",
  vehicleModelId: "",
  simId: "",
  sn: "",
  remoteSim: "",
  dvrId: "",
  dandelionAccount: "",
  dandelionPassword: "",
  rtkAccount: "",
  opDate: "",
  vehOperateStatus: "", // 运营状态 ，0部署中，1运营中，2维修中
  remoteNo: "", // 远程码
  instrUrl: [] as any[],
  files: [] as FileItem[],
});
/**
 * 表单校验规则
 */
const formRules = reactive<FormRulesType>({
  entId: [
    ["required", $t("PSelectEnterprise")],
    [(v: any) => v !== 0, $t("PSelectEnterprise")],
  ],
  vin: [
    ["required", $t("vinNoRequired")],
    [debounce(vaildateVin), "车辆VIN不正确，请重新输入"],
    [debounce(repeatVehicleVin), () => $t("vinNoExist")],
  ],
  vehicleNo: [
    ["required", $t("vehicleNoRequired")],
    [debounce(repeatVehicleNo), $t("vehicleNoExist")],
  ],
  vehicleType: [["required", $t("vehicleTypeRequired")]],
  simId: [["required", $t("simNoRequired")]],
  dandelionAccount: [[debounce(repeatDandelionAccount), $t("dandelionAccExist")]],
  rtkAccount: [["required", $t("PSelectRtkAcc")]],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  const isValid = Number(form.vehicleType) === 2 || (await formRef.value.asyncValidate());
  if (isValid) {
    if (form.files && form.files.length > 0) {
      form.instrUrl = form.files.map((item) => ({
        instrUrl: item.url,
        fileName: item.name,
      }));
    } else {
      form.instrUrl = [];
    }
    const params = {
      entId: form.entId,
      vin: form.vin,
      vehicleNo: form.vehicleNo,
      vehicleType: form.vehicleType,
      vehicleModelId: form.vehicleModelId,
      simId: form.simId,
      sn: form.sn,
      remoteSim: form.remoteSim,
      dandelionAccount: form.dandelionAccount,
      dandelionPassword: form.dandelionPassword,
      rtkAccount: form.rtkAccount,
      opDate: form.opDate,
      instrUrl: form.instrUrl,
      vehOperateStatus: form.vehOperateStatus,
      remoteNo: form.remoteNo,
      dvrId: form.dvrId,
    };
    await addVehicle(params);
    emits("update:show", false);
    Message("success", $t("createSuccess"));
    emits("confirm");
    formRef.value.resetFields();
    formOptions.simId = [];
    formOptions.vehicleModelId = [];
  }
};
const handleCancel = () => {
  emits("update:show", false);
  formRef.value.resetFields();
  formOptions.simId = [];
  formOptions.vehicleModelId = [];
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  headers: { token: getLocalStorage("token") },
  company: [] as TreeItemType[],
  vehicleType: [] as any[],
  vehicleModelId: [] as any[],
  simId: [] as any[],
  rtkAccount: [] as any[],
  vehOperateStatus: vehOperateStatusType,
});

/**
 * SIM列表
 */
const getSimList = async () => {
  if (!form.entId) return;

  const { simList } = await getEntSim({
    entId: String(form.entId),
  });
  formOptions.simId = simList.map((item) => ({
    value: String(item.simId),
    label: item.simId,
  }));
};

/**
 * RTK列表
 */
const getRTKList = async () => {
  if (!form.entId) return;

  const { sysRTKEntities } = await getEntRTK({
    entId: String(form.entId),
  });
  formOptions.rtkAccount = sysRTKEntities.map((item: any) => ({
    value: item.rtkAccount,
    label: item.rtkAccount,
  }));
};

/**
 * 相关操作
 */
// 上传前
// 8bit(位)=1Byte(字节)
// 1024Byte(字节)=1KB
// 1024KB=1MB
const beforeUpload = (file: FileItem) => {
  const maxSize = 10 * 1024 * 1024;
  if (file.size && file.size > maxSize) {
    Message("error", `${file.name} ${$t("fileSizeExceedsLimit")}`);
    return false;
  }
};

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      // 企业树
      formOptions.company = reactive(resTreeToXTree([await compTree()]));
      // 车辆类型
      const { operConfigList } = await getConfigList({
        configList: ["vehicle_type"],
      });
      formOptions.vehicleType = operConfigList.map((item) => ({
        value: Number(item.k),
        label: item.val,
      }));
      // SIM列表
      getSimList();
    }
  }
);
watch(
  () => form.entId,
  () => {
    // 清除选择
    form.simId = "";
    // SIM列表
    getSimList();
  }
);
watch(
  () => form.vehicleType,
  () => {
    // 车辆型号
    form.vehicleModelId = "";
    updateVehicleModelList();
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 25px 20px 0 0;
  &-top,
  &-bottom {
    padding: 15px;
    margin-bottom: 15px;
    background: #fff;
    border-radius: 8px;
    &-title {
      margin-bottom: 15px;
      @include sc(16px, #9f9fa4);
    }
  }
}
</style>
