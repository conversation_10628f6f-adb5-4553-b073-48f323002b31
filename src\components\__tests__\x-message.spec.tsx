import { describe, it, expect, beforeEach, vi } from "vitest";
import { nextTick } from 'vue';
import Message from "@/components/x-message";

beforeEach(() => {
  vi.useFakeTimers();
  document.body.outerHTML = '';
});

describe('Snapshot', () => {
  it('正确渲染 默认快照', async () => {
    Message('success', '快照测试');
    await nextTick(()=>{
      document.querySelector('img').src = ''; // src的值包含了本地的系统目录路径
      expect(document.querySelector('.x-message-container')).toMatchSnapshot();
    })
  });
});

describe('Function Params Render', () => {
  it('正确渲染 type + text', async () => {
    Message('error', '函数参数测试');
    await nextTick(()=>{
      expect(document.querySelector('img').src.includes('x_message_error.png')).toBe(true);
      expect(document.querySelector('span')?.innerHTML).toBe('函数参数测试');
    })
  });
});