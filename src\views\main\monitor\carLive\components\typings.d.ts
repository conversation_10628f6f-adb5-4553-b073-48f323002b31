/** 实时视频配置项 */
declare type _RealtimeVideoOptions = {
  /** sim卡号 */
  simNumber?: string;
  /** 通道号（不支持 0） */
  channelId?: number;
  /** 码流类型（0：主码流，1：子码流，2：主码流或子码流，3：主码流、子码流或三码流） */
  streamType?: number;
  /** 是否打开音频 */
  hasAudio?: boolean;
  /** 窗口ID (0表示当前选中窗口 其他按顺序选择) */
  videoId?: number;
  /** 播放器配置项， 请参考: https://github.com/vanjoge/RTVS/blob/master/JsAccess.md#初始化控件 */
  config?: Record<string, any>;
  /** 回调函数 */
  callback?: null | (() => void);
  /** 播放模式（0：自动，1：WASM软解(canvas+audioAPI)，2：js封装FMP4(h264Demuxer+audioAPI)，3：WASM封装FMP4(WASM2FMP4)，4：服务器推fmp4流，5：webrtc，6：hls，7：webrtc(srs)） */
  playMode?: number;
};
/** 历史回放配置项 */
declare type _PlaybackVideoOptions = {
  /** sim卡号 */
  simNumber?: string;
  /** 通道号（不支持 0） */
  channelId?: number;
  /** 音视频资源类型（0：音视频，1：音频，2：视频，3：视频或音视频） */
  mediaType?: number;
  /** 码流类型（0：主码流，1：子码流，2：主码流或子码流，3：主码流、子码流或三码流） */
  streamType?: number;
  /** 存储器类型（0：主存储器或灾备存储器，1：主存储器，2：灾备存储器 */
  storageType?: number;
  /** 回放模式（0：正常回放，1：快进回放，2：关键帧快退回放，3：关键帧播放，4：单帧上传） */
  playbackMode?: number;
  /** 快进/快退倍速（0：无效，1：1倍，2：2倍，3：4倍，4：8倍，5：16倍） */
  multiple?: number;
  /** 开始时间 */
  startTime?: string;
  /** 结束时间 */
  endTime?: string;
  /** 窗口ID (0表示当前选中窗口 其他按顺序选择) */
  videoId?: number;
  /** 播放器配置项， 请参考: https://github.com/vanjoge/RTVS/blob/master/JsAccess.md#初始化控件 */
  config?: Record<string, any>;
  /** 回调函数 */
  callback?: null | (() => void);
  /** 播放模式（0：自动，1：WASM软解(canvas+audioAPI)，2：js封装FMP4(h264Demuxer+audioAPI)，3：WASM封装FMP4(WASM2FMP4)，4：服务器推fmp4流，5：webrtc，6：hls，7：webrtc(srs)） */
  playMode?: number;
  /** 来源（0：自动 1：设备 2：服务端缓存） */
  dataSource?: number;
  /** 报警标志（0默认） */
  alarm?: number;
  /** 回放日期 格式：YYYY-MM-DD */
  playbackDate?: string;
};
/** 查询所在日期回放时间配置项 */
declare type _SetBackDateTimeOptions = {
  /** sim卡号 */
  simNumber?: string;
  /** 通道号（不支持 0） */
  channelId?: number;
  /** 回放日期 格式：YYYY-MM-DD */
  playbackDate?: string;
  /** 窗口ID (0表示当前选中窗口 其他按顺序选择) */
  videoId?: number;
  /** 报警标志（0默认） */
  alarm?: number;
  /** 音视频资源类型（0：音视频，1：音频，2：视频，3：视频或音视频） */
  mediaType?: number;
  /** 码流类型（0：主码流，1：子码流，2：主码流或子码流，3：主码流、子码流或三码流） */
  streamType?: number;
  /** 存储器类型（0：主存储器或灾备存储器，1：主存储器，2：灾备存储器 */
  storageType?: number;
  /** 播放器配置项， 请参考: https://github.com/vanjoge/RTVS/blob/master/JsAccess.md#初始化控件 */
  config?: Record<string, any>;
  /** 来源（0：自动 1：设备 2：服务端缓存） */
  dataSource?: number;
};
/** 查询历史录像文件列表配置项 */
declare type _QueryVideoFileListOptions = {
  /** sim卡号 */
  simNumber?: string;
  /** 通道号（不支持 0） */
  channelId?: number;
  /** 开始时间 */
  startTime?: string;
  /** 开始时间-时间戳 */
  startTimeStamp?: number;
  /** 结束时间 */
  endTime?: string;
  /** 结束时间-时间戳*/
  endTimeStamp?: number;
  /** 报警标志（0默认） */
  alarm?: number;
  /** 音视频资源类型（0：音视频，1：音频，2：视频，3：视频或音视频） */
  mediaType?: number;
  /** 码流类型码流类型（0：所有码流，1：主码流，2：子码流） */
  streamType?: number;
  /** 存储器类型（0：主存储器或灾备存储器，1：主存储器，2：灾备存储器 */
  storageType?: number;
  /** 回调函数 */
  callback?: null | ((e?: any) => void);
  /** 窗口ID (0表示当前选中窗口 其他按顺序选择) */
  videoId?: number;
  /** 播放器配置项， 请参考: https://github.com/vanjoge/RTVS/blob/master/JsAccess.md#初始化控件 */
  config?: Record<string, any>;
  /** 来源（0：自动 1：设备 2：服务端缓存） */
  dataSource?: number;
};
/** 查询历史录像文件列表结果 */
declare type _QueryVideoFileListData = {
  /** 文件数量 */
  FileCount: number;
  /** 文件列表 */
  FileList: _QueryVideoFileListInfo[];
  /** 二进制填充 */
  FillByBinary: (e: any, t: any, i: any) => void;
  /** 序列号 */
  SerialNumber: number;
};
/** 查询历史录像文件列表数据项 */
declare type _QueryVideoFileListInfo = {
  Alarm: { high: number; low: number; unsigned: boolean };
  /** 通道号 */
  Channel: number;
  /** 开始时间 格式："241010173058" 详解："24 10 10 17 30 58" 年月日时分秒 */
  StartTime: string;
  /** 结束时间 格式："241010173058" 详解："24 10 10 17 30 58" 年月日时分秒 */
  EndTime: string;
  /** 文件大小 */
  FileSize: number;
  /** 二进制 */
  FillByBinary: (e: any, t: any) => void;
  /** 音视频资源类型（0：音视频，1：音频，2：视频，3：视频或音视频） */
  MediaType?: number;
  /** 码流类型码流类型（0：所有码流，1：主码流，2：子码流） */
  StreamType?: number;
  /** 存储器类型（0：主存储器或灾备存储器，1：主存储器，2：灾备存储器 */
  StorageType?: number;
};
