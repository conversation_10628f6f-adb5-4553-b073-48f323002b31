<template>
  <x-modal
    title="项目车辆"
    :visible="props.show"
    @update:visible="updateVisible"
    width="1045px"
    height="55vh"
    :bodyStyle="{ padding: '0 30px' }"
  >
    <div class="content">
      <div class="content-top">
        <template v-for="(item, index) in tabs.config" :key="index">
          <div
            class="tab"
            :class="{ active: item.value === tabs.active }"
            @click="changeTab(item.value)"
          >
            {{ item.label }}
          </div>
        </template>
      </div>
      <div class="content-bottom">
        <x-table
          :cols="table.cols"
          :dataSource="table.dataSource"
          :customRow="customRowHandler"
        >
          <template #deviceId="{ record }">
            <div class="table-veh">
              <x-icon
                v-if="record.warnLevel !== ''"
                :name="`alarm_level_${record.warnLevel}`"
                width="12px"
                height="12px"
                class="warn"
              />
              <span>{{ record.deviceId }}</span>
            </div>
          </template>
          <template #workStatus="{ record }">
            <div class="table-status">
              <span class="status-dot" :class="getStatusClass(record)"></span>
              <span>{{ record.statusText }}</span>
            </div>
          </template>
        </x-table>
      </div>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import xModal from "./x-modal.vue";
import xTable from "./x-table.vue";
import xIcon from "@/components/x-icon.vue";
import { useMainStore } from "@/stores/main";
import { workStatusMap } from "@/services/wsconfig";

const { socket } = useMainStore();

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String,
    required: true,
  },
});

const tabs = reactive({
  config: [
    {
      label: "全部",
      value: "all",
    },
    {
      label: "在线",
      value: "online",
    },
    {
      label: "离线",
      value: "offline",
    },
    {
      label: "正在作业",
      value: "work",
    },
    {
      label: "告警",
      value: "warn",
    },
  ],
  active: "all",
});

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: "序号",
      width: "30",
    },
    {
      key: "deviceId",
      title: "车牌号",
      width: "60",
      slots: "deviceId",
    },
    {
      key: "workStatus",
      title: "状态",
      width: "40",
      slots: "workStatus",
    },
    {
      key: "areaName",
      title: "所在区域",
      width: "70",
    },
  ],
  dataSource: [] as any[],
});

const customRowHandler = (record: any) => {
  return {
    onClick: () => {
      emits("clickVeh", record.deviceId);
    },
  };
};

const getStatusClass = (record: any) => {
  if (!record.online) {
    return "gray";
  }
  const statusClassMap: { [key: number]: string } = {
    0: "yellow", // 未作业
    1: "green", // 作业中
    2: "gray", // 未知
    3: "orange", // 充电中
  };
  return statusClassMap[record.workStatus] || "gray";
};

const emits = defineEmits(["update:show", "clickVeh"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      tabs.active = props.type;
      updateDataSource();
    }
  }
);

const updateDataSource = () => {
  const info =
    socket.baseStatus?.map((item) => {
      return {
        statusText: item.online
          ? workStatusMap[item.workStatus]?.text || "--"
          : "关机",
        ...item,
      };
    }) || [];
  let filteredInfo = info;
  if (tabs.active === "online") {
    filteredInfo = info.filter((item: any) => item.online);
  } else if (tabs.active === "offline") {
    filteredInfo = info.filter((item: any) => !item.online);
  } else if (tabs.active === "work") {
    filteredInfo = info.filter(
      (item: any) => item.workStatus === 1 && item.online
    );
  } else if (tabs.active === "warn") {
    filteredInfo = info.filter((item: any) => item.warnLevel !== 4);
  }
  table.dataSource = filteredInfo.map((item, index) => {
    return {
      orderId: (index + 1).toString().padStart(3, "0"),
      ...item,
    };
  });
};

const changeTab = (value: string) => {
  tabs.active = value;
  updateDataSource();
};

watch(
  () => socket.baseStatus,
  (newV) => {
    if (newV) {
      updateDataSource();
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 0 10px;
  height: 40vh;
  overflow-y: auto;
  &-top {
    @include ct-f(y);
    @include sc(1.4vh, rgba(255, 255, 255, 0.7));
    margin-bottom: 1vh;
    .tab {
      @include ct-f;
      @include wh(90px, 3.5vh);
      cursor: pointer;
      position: relative;
      z-index: 1;
      &.active {
        color: rgb(221, 229, 250);
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          @include wh(100%, 100%);
          background: linear-gradient(
            0deg,
            rgba(11, 16, 54, 0.8) 17.095%,
            rgba(19, 21, 42, 0.67) 62.989%,
            rgba(21, 35, 64, 0.66) 100%
          );
          clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
          border-top-left-radius: 20px;
          border-top-right-radius: 20px;
          border-top: 1px solid rgb(147, 166, 255);
          z-index: -1;
        }
        &::after {
          content: "";
          position: absolute;
          bottom: -0.1vh;
          @include wh(100%, 1vh);
          background: url("@/assets/images/screen_tab_bottom.png") no-repeat
            center center;
          background-size: cover;
          z-index: 0;
        }
      }
    }
  }
  &-bottom {
    height: calc(100% - 6vh);
    .table-veh,
    .table-status {
      display: flex;
      align-items: center;
    }
    .table-veh {
      .warn {
        margin-right: 10px;
      }
    }
    .table-status {
      .status-dot {
        @include wh(8px, 8px);
        border-radius: 50%;
        margin-right: 5px;
        &.green {
          background-color: rgb(30, 227, 168);
        }
        &.yellow {
          background-color: rgb(255, 234, 52);
        }
        &.orange {
          background-color: rgb(255, 97, 0);
        }
        &.gray {
          background-color: rgb(161, 162, 165);
        }
      }
    }
  }
}
</style>
