<template>
  <section class="echart-base" ref="echartBaseRef"></section>
</template>
<script lang="ts" setup>
import { ref, watchEffect, onMounted } from "vue";
import type { EChartsOption } from "echarts";
import useEchart from "./hooks/useEchart";

const props = withDefaults(
  defineProps<{
    options: EChartsOption;
  }>(),
  {}
);

const echartBaseRef = ref<HTMLElement>();

onMounted(() => {
  const { setOptions } = useEchart(echartBaseRef.value!);
  watchEffect(() => {
    setOptions(props.options);
  });
});
</script>

<style lang="scss" scoped>
.echart-base {
  @include wh(100%);
}
</style>
