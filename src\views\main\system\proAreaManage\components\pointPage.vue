<template>
  <section class="pointPage">
    <el-scrollbar
      height="85vh"
      wrap-class="pointPage-content"
    >
      <div
        class="pointPage-content-item"
        v-for="item in pointList"
        :key="item.id"
      >
        <div class="pointPage-content-item-title">
          <span>{{ item.pointName }}</span>
          <XIcon
            class="icon"
            :width="20"
            :height="20"
            name="del_x"
            @click="handleDelete(item.id)"
          />
        </div>
        <div
          class="pointPage-content-item-info"
          @click="handleChoosePoint(item.id)"
        >
          <div class="pointPage-content-item-info-row">
            <div class="pointPage-content-item-info-row-label">x</div>
            <div class="pointPage-content-item-info-row-value">{{ item.x }}</div>
          </div>
          <div class="pointPage-content-item-info-row">
            <div class="pointPage-content-item-info-row-label">y</div>
            <div class="pointPage-content-item-info-row-value">{{ item.y }}</div>
          </div>
          <div class="pointPage-content-item-info-row">
            <div class="pointPage-content-item-info-row-label">z</div>
            <div class="pointPage-content-item-info-row-value">{{ item.z }}</div>
          </div>
          <div class="pointPage-content-item-info-row">
            <div class="pointPage-content-item-info-row-label">yaw</div>
            <div class="pointPage-content-item-info-row-value">{{ item.yaw }}</div>
          </div>
        </div>
      </div>
      <XEmpty v-if="pointList.length === 0"></XEmpty>
    </el-scrollbar>

    <el-dialog
      v-model="elMessageVisible"
      title="您确认要删除该点位吗?"
      width="20%"
    >
      <template #footer>
        <el-button @click="elMessageVisible = false">取消</el-button>
        <el-button
          type="primary"
          @click="handleConfirm"
          >确定</el-button
        >
      </template>
    </el-dialog>
  </section>
</template>

<script lang="ts" setup>
import XEmpty from "@/components/x-empty.vue";
import { removeMunicipalFacilities } from "@/services/api";
import { MunicipalFacilitiesListResponse } from "@/services/type";
import { ElMessage, ElMessageBox } from "element-plus";
import { ref } from "vue";

const props = defineProps<{
  pointList: Array<MunicipalFacilitiesListResponse>;
  areaId: string;
}>();

const emit = defineEmits(["choosePoint", "refresh"]);
const elMessageVisible = ref(false);
const pointList = ref<MunicipalFacilitiesListResponse[]>(props.pointList);
const deleteId = ref<string>("");

const handleChoosePoint = (id: string) => {
  emit("choosePoint", id);
};

const handleDelete = (id: string) => {
  deleteId.value = id;
  elMessageVisible.value = true;
};

const handleConfirm = async () => {
  try {
    await removeMunicipalFacilities({ areaId: props.areaId, id: deleteId.value });
    elMessageVisible.value = false;
    ElMessage.success("删除成功");
    emit("refresh");
  } catch (error) {
    ElMessage.error("删除失败");
  }
};
</script>
<style lang="scss">
.pointPage {
  &-content {
    height: 100%;
    width: 100%;
    padding: 10px;
    &-item {
      margin-bottom: 10px;
      width: 100%;
      padding: 10px;
      border-radius: 4px;
      background: rgba(232, 236, 253, 1);
      cursor: pointer;
      &-title {
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 14px;
        font-weight: 500;
        color: #5964fb;
        margin-bottom: 10px;
        .icon {
          cursor: pointer;
        }
      }
      &-info {
        &-row {
          border: 1px solid rgba(192, 190, 190, 0.3);
          display: flex;
          align-items: center;
          &-label {
            font-size: 14px;
            font-weight: 500;
            color: #383838;
            width: 40px;
            text-align: center;
            line-height: 36px;
            height: 36px;
            border-radius: 4px;
            background: rgba(182, 203, 250, 0.3);
          }
          &-value {
            font-size: 14px;
            font-weight: 500;
            color: #383838;
            width: 100%;
            padding-left: 10px;
            line-height: 36px;
            background-color: #fff;
          }
        }
      }
    }
  }
}
</style>
