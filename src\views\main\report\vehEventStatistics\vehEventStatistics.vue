<template>
  <section class="veh-event" ref="vehEventRef">
    <div class="veh-event-top">
      <div class="top-title">{{ $t("vehEventStatistics") }}</div>
    </div>
    <div class="veh-event-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.deviceId"
          :options="formOptions.device"
          :popupContainer="vehEventRef"
          :placeholder="$t('vehNumber')"
          showSearch
          style="width: 240px; margin-right: 16px"
        />
        <DateRangePicker
          v-model:value="searchForm.opDate"
          :popupContainer="vehEventRef"
          :placeholder="[$t('startTime'), $t('endTime')]"
          style="width: 320px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.eventType"
          :options="formOptions.eventType"
          :popupContainer="vehEventRef"
          style="width: 140px"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:vehCaptureEvent:list')"
          @click="searchList"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="veh-event-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 图像标题 -->
        <template #headerCell="{ title, column }">
          <div v-if="column.key === 'image'">
            <span style="vertical-align: middle">{{ title }}</span>
            <imgLineDefinition />
          </div>
          <span v-else>{{ title }}</span>
        </template>
        <!-- 所属区域 -->
        <template #areaName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-area-popover"
            :container="vehEventRef"
          >
            {{ record.areaName || "--" }}
            <template #content>
              <div class="veh-event-table-area-hover-popover">
                {{ record.areaName || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 所属项目 -->
        <template #projectName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-project-popover"
            :container="vehEventRef"
          >
            {{ record.projectName || "--" }}
            <template #content>
              <div class="veh-event-table-project-hover-popover">
                {{ record.projectName || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 位置 -->
        <template #location="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-location-popover"
            :container="vehEventRef"
          >
            {{ record.location || "--" }}
            <template #content>
              <div class="veh-event-table-location-hover-popover">
                {{ record.location || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 图像 -->
        <template #image="{ record, recordIndex }">
          <img
            v-if="record.picUrl"
            :src="record.picUrl"
            alt=""
            :style="{
              height: record.picError ? '24px' : '35px',
              cursor: record.picError ? 'default' : 'pointer',
            }"
            @error="handleImgError(recordIndex)"
            @click="!record.picError && showImage(record)"
          />
        </template>
      </x-table>
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
import type { PageSizeType } from "@/components/types";
import { getVehEventList, getVehList, getVehEventType } from "@/services/api";
import { DateRangePicker } from "@/components/x-date-picker";
import { useMainStore } from "@/stores/main";
import xTable from "@/components/x-table.vue";
import xImage from "@/components/x-image.vue";
import xPopover from "@/components/x-popover.vue";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";
import imgLineDefinition from "../../components/imgLineDefinition.vue";

const $t = i18nSimpleKey("vehEventStatistics");

const vehEventRef = ref<any>();

const {
  userInfo: { permitList },
} = useMainStore();

// 数据支持
const formOptions = reactive({
  eventType: [] as { label: string; value: string }[],
  device: [] as { label: string; value: string }[],
});

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "vehNo",
      title: $t("deviceId"),
      width: "60",
    },
    {
      key: "eventTypeTxt",
      title: $t("status"),
      width: "60",
    },
    {
      key: "cameraTxt",
      title: $t("camera"),
      width: "60",
    },
    {
      key: "createdTime",
      title: $t("time"),
      width: "100",
    },
    {
      key: "areaName",
      title: $t("ofArea"),
      width: "120",
      slots: "areaName",
    },
    {
      key: "projectName",
      title: $t("ofProject"),
      width: "120",
      slots: "projectName",
    },
    {
      key: "location",
      title: $t("location"),
      width: "170",
      slots: "location",
    },
    {
      key: "image",
      title: $t("image"),
      width: "80",
      slots: "image",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

// 重置
const resetSearchForm = () => {
  searchForm.deviceId = "";
  searchForm.opDate = ["", ""];
  searchForm.eventType = "";
  table.pagination["current"] = 1;
  searchList();
};

// 查询
const searchForm = reactive({
  deviceId: "",
  opDate: ["", ""],
  eventType: "",
});
const searchList = async () => {
  table.loading = true;
  const { totalCount, list } = await getVehEventList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceId: searchForm.deviceId,
    startTime: searchForm.opDate[0] || "",
    endTime: searchForm.opDate[1] || "",
    eventType: searchForm.eventType,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            ...item,
            orderNumber: (index + 1).toString().padStart(3, "0"),
            image: ref(false),
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  // 图片错误处理重置
  table.dataSource.forEach((item) => {
    item.picUrl = "";
    item.picError = false;
  });
  searchList();
};

// 查看事件图片
const image = reactive({
  show: false,
  url: "",
});
const showImage = (info: any) => {
  image.show = true;
  image.url = info.picUrl || "";
};

// 图片容错处理
const handleImgError = (index: number) => {
  table.dataSource[index].picUrl = new URL(
    "@/assets/images/img_error.png",
    import.meta.url
  ).href;
  table.dataSource[index].picError = true;
};

(async () => {
  searchList();
  // 车辆编号列表
  formOptions.device = (await getVehList()).map((item) => ({
    label: item!,
    value: item!,
  }));
  // 事件类型列表
  const vehEventType = (await getVehEventType()).eventTypeMap;
  let eventTypeList = [];
  for (const key of Object.keys(vehEventType as any)) {
    eventTypeList.push({
      label: vehEventType[key],
      value: key,
    });
  }
  formOptions.eventType = [{ label: $t("full"), value: "" }, ...eventTypeList];
})();
</script>

<style lang="scss">
.veh-event-table-area-hover-popover,
.veh-event-table-project-hover-popover,
.veh-event-table-location-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
</style>
<style lang="scss" scoped>
.veh-event {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-area-popover,
    .table-project-popover,
    .table-location-popover {
      @include ell;
    }
  }
}
</style>
