import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import XSwitch from '@/components/x-switch.vue';

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XSwitch);
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 选中/非选中时的内容', async () => {
    const wrapper = shallowMount(XSwitch, {
      props: {
        checkedChildren: 'On',
        unCheckedChildren: 'Off',
      },
    });
    const switchHandle = wrapper.find('.x-switch-handle');
    expect(switchHandle.text()).toBe('Off');

    await switchHandle.trigger('click');
    expect(switchHandle.text()).toBe('On'); 
  });

  it('正确渲染 选中/非选中时slot中的内容', () => {
    const checkedText = 'Slot Content for checkedChildren';
    const unCheckedText = 'Slot Content for checkedChildren';
    const wrapper = shallowMount(XSwitch, {
      slots: {
        checkedChildren: checkedText,
        unCheckedChildren: unCheckedText,
      },
    });
    expect(wrapper.find('.x-switch-handle').text()).toBe(unCheckedText);
    wrapper.trigger('click');
    expect(wrapper.find('.x-switch-handle').text()).toBe(checkedText);
  });

  it('正确渲染 size属性为small时的样式', () => {
    const wrapper = shallowMount(XSwitch, {
      props: {
        size: 'small',
      },
    });
    expect(wrapper.find('.small').exists()).toBe(true);
  });
});

describe('Events Click', () => {
  it('点击开关时应触发update和change事件并传值', () => {
    const wrapper = shallowMount(XSwitch, {
      props: {
        checkedValue: 1,
        unCheckedValue: 0,
      },
    });
    wrapper.find('.x-switch-handle').trigger('click');
    expect(wrapper.emitted()['update:checked'][0][0]).toBe(1);
    expect(wrapper.emitted().change[0][0]).toBe(1);
  });
  
  it('禁用开关时应阻止切换开关', () => {
    const wrapper = shallowMount(XSwitch, {
      props: {
        disabled: true,
      },
    });
    wrapper.find('.x-switch-handle').trigger('click');
    expect(wrapper.emitted().change).not.toBeDefined();
  });
})
