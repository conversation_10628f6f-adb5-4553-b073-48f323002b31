<template>
  <section class="site-marker">
    <div
      v-if="showRipple"
      class="site-marker-radar"
    >
      <div :class="`radar-focus-ripple ripple ${props.icon}`"></div>
      <div :class="`radar-focus-ripple ripple ${props.icon}`"></div>
    </div>
    <div
      :class="['site-marker-icon', props.focus ? '' : props.icon === 'map_ployline_end_disable' ? 'blur-line' : 'blur']"
    >
      <div class="x-icon-wrapper">
        <x-icon
          :name="props.icon"
          :width="props.focus ? '40px' : '28px'"
          :height="props.focus ? '40px' : '28px'"
        />
        <div
          v-if="props.index"
          :style="{ color: props.indexColor }"
          :class="['point-marker-index', { nofocus: !props.focus }]"
        >
          {{ props.index }}
        </div>
      </div>
      <div
        v-if="props.number"
        :class="['site-marker-number', { nofocus: !props.focus }]"
      >
        {{ fixZeroToStr(props.number) }}
      </div>
      <div
        v-if="props.labelName"
        :class="['site-marker-labelName', { nofocus: !props.focus }]"
      >
        {{ props.labelName }}
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import xIcon from "@/components/x-icon.vue";
import { fixZeroToStr } from "@/assets/ts/dateTime";
import { computed } from "vue";
const props = defineProps({
  icon: {
    type: String,
    required: true,
  },
  /**是否开启动画 */
  focus: {
    type: Boolean,
    default: () => false,
  },
  number: {
    type: Number,
    default: () => 0,
  },

  labelName: {
    type: String,
    default: () => "",
  },
  /*   居中序号*/
  index: {
    type: Number || String,
    default: () => 0,
  },
  indexColor: {
    type: String,
    default: () => "#5964fb",
  },
});

/**动画白名单 兼容之前旧逻辑 */
const rippleIconList = [
  "point-marker-icon",
  "map_park",
  "map_water",
  "map_battery",
  "map_garbage",
  "map_ployline_end_enable",
];

const showRipple = computed(() => {
  if (props.focus === true) return true;
  if (props.focus === false) return false;
  // 兼容旧逻辑：只有特定 icon 才有动画
  return rippleIconList.includes(props.icon);
});
</script>

<style lang="scss" scoped>
.site-marker {
  position: relative;
  &-radar {
    @include ct-p(x) {
      bottom: -14px;
    }
    @include wh(100%) {
      border-radius: 50%;
    }
    .radar-focus-ripple {
      @include ct-p;
      @include wh(20px) {
        border-radius: 50%;
      }
      &.map_ployline_end_enable {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
      }
      &.map_garbage {
        background-color: rgba(39, 212, 161, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(39, 212, 161, 0.2);
      }
      &.map_battery {
        background-color: rgba(116, 34, 254, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(116, 34, 254, 0.2);
      }
      &.map_water {
        background-color: rgba(77, 174, 255, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(77, 174, 255, 0.2);
      }
      &.map_park {
        background-color: rgba(249, 133, 46, 0.2);
        box-shadow: 0px 0px 2px 4px rgba(249, 133, 46, 0.2);
      }
      &.point-marker-icon {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
        animation: focusRipple_point 2.4s linear infinite;
      }
      &.ripple {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
        animation: focusRipple_point 2.4s linear infinite;
      }
      animation: focusRipple 2.4s linear infinite;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1.2s;
      }
    }
    @keyframes focusRipple {
      80% {
        width: 60px;
        height: 60px;
        opacity: 0.3;
      }
      100% {
        width: 40px;
        height: 40px;
        opacity: 0.3;
      }
    }

    @keyframes focusRipple_point {
      0% {
        width: 10px;
        height: 10px;
        opacity: 0.5;
      }
      50% {
        width: 30px;
        height: 30px;
        opacity: 0.3;
      }
      100% {
        width: 30px;
        height: 30px;
        opacity: 0;
      }
    }
  }
  &-icon {
    position: relative;
    transition: 0.3s padding linear;
    &.blur {
      padding: 12px 0 0 7px;
    }
    &.blur-line {
      padding: 18px 0 0 6px;
    }
  }
  &-number {
    position: absolute;
    transition: 0.3s all linear;
    left: 50%;
    top: 8px;
    transform: translate(-50%);
    @include sc(12px, #fff);
    &.nofocus {
      left: 13px;
      top: 20px;
      transform: none;
    }
  }
  .x-icon-wrapper {
    position: relative;
    .point-marker-index {
      position: absolute;
      transition: 0.3s all linear;
      left: 50%;
      top: 40%;
      transform: translate(-50%, -50%);
      @include sc(12px, "");
    }
  }
  &-labelName {
    position: absolute;
    transition: 0.3s all linear;
    left: 50%;
    top: -20px;
    transform: translate(-50%);
    background-color: rgba(89, 100, 251, 0.8);
    padding: 2px 6px;
    border-radius: 2px;
    @include sc(12px, #fff);
    white-space: nowrap;
    &.nofocus {
      top: -25px;
    }
  }
}
</style>
