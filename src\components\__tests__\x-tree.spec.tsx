import { describe, it, expect, vi } from "vitest";
import { mount } from '@vue/test-utils';
import xTree from "@/components/x-tree";
import Tree from "@/components/x-tree/tree.vue"
import xCheckbox from "@/components/x-checkbox.vue";

// 无可选框
const baseProps = {
  treeData: [
    {
      title: 'title-1',
      value: 'value-1',
      children: [
        {
          title: 'title-1-1',
          value: 'value-1-1'
        },
        {
          title: 'title-1-2',
          value: 'value-1-2'
        }
      ]
    },
    {
      title: 'title-2',
      value: 'value-2'
    }
  ]
}
// 有可选框
const checkableBaseProps = {
  checkable: true,
  treeData: [
    {
      title: 'title-1',
      value: 'value-1',
      checked: false,
      children: [
        {
          title: 'title-1-1',
          value: 'value-1-1',
          checked: true,
        },
        {
          title: 'title-1-2',
          value: 'value-1-2',
          checked: false,
        }
      ]
    },
    {
      title: 'title-2',
      value: 'value-2',
      checked: false,
    }
  ]
}

describe('Snapshot', () => {
  it('正确渲染 无可选框快照', () => {
    const wrapper = mount(xTree,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 有可选框快照', () => {
    const wrapper = mount(xTree,{
      props: { ...checkableBaseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Events', () => {

  it('验证autoExpandParent + clickHandle', () => {
    const spyOnFunc = {
      clickHandle(item) { }
    }
    const clickHandleSpy = vi.spyOn(spyOnFunc, 'clickHandle');
    const wrapper = mount(xTree,{
      props: { ...baseProps, autoExpandParent: true, clickHandle: spyOnFunc.clickHandle },
    });
    // 成功传递autoExpandParent
    expect(wrapper.findAllComponents(Tree)[0].props().autoExpandParent).toBe(true);
    // 点击后触发clickHandle
    wrapper.findAll('.tree-root')[1].trigger('click');
    expect(clickHandleSpy.mock.calls[0][0].value).toBe('value-1-1');
  });

  it('单选(无可选框)时，点击选中后验证selectedItems', () => {
    const wrapper = mount(xTree,{
      props: { ...baseProps },
    });
    wrapper.findAll('.tree-root')[2].trigger('click');
    expect(wrapper.emitted()['update:selectedItems'][0][0][0].value).toBe('value-1-2')
  });

  it('多选(有可选框)时，点击勾选后验证selectedItems', () => {
    const wrapper = mount(xTree,{
      props: { ...checkableBaseProps },
    });
    wrapper.findAllComponents(xCheckbox)[0].trigger('click');
    const _selectedItems = wrapper.findAllComponents(Tree)[0].props().selectedItems
    expect(_selectedItems.length).toBe(3);
    expect(_selectedItems[0].checked).toBe(true);
    expect(_selectedItems[1].checked).toBe(true);
    expect(_selectedItems[2].checked).toBe(true);
    expect(_selectedItems[0].value).toBe('value-1-1');
    expect(_selectedItems[1].value).toBe('value-1-2');
    expect(_selectedItems[2].value).toBe('value-1');
  });

});
