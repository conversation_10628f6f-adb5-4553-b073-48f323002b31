<template>
  <x-drawer
    :title="$t('viewDetails')"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="20px 0 20px 20px"
    width="672px"
  >
    <div class="content">
      <!-- 操作信息 -->
      <div class="content-panel-top">
        <div class="panel-title">{{ $t("operationInfo") }}</div>
        <div class="panel-items">
          <div class="panel-item">
            <div class="panel-item-label">{{ $t("operationItem") }}：</div>
            <div class="panel-item-value">
              {{ detail.info.taskItemTxt }}
              <span v-if="detail.info.taskItem === 6"> - {{ speedText }}</span>
            </div>
          </div>
          <div class="panel-item">
            <div class="panel-item-label">{{ $t("operationResult") }}：</div>
            <div class="panel-item-value">
              <div
                class="result"
                :class="detail.info.taskResult ? 'success' : 'fail'"
              >
                <span class="text">{{ detail.info.taskResultTxt }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="panel-items">
          <div class="panel-item">
            <div class="panel-item-label">{{ $t("operationSource") }}：</div>
            <div class="panel-item-value">
              {{ detail.info.sourceTxt }}
            </div>
          </div>
          <div class="panel-item">
            <div class="panel-item-label">{{ $t("operationType") }}：</div>
            <div class="panel-item-value">
              {{ detail.info.taskTypeTxt }}
            </div>
          </div>
        </div>
        <div class="panel-table">
          <div class="panel-table-label">
            {{ $t("operationItemDetails") }}：
          </div>
          <div
            class="panel-table-content"
            v-if="detail.info.taskNameTypeList?.length > 0"
          >
            <table>
              <thead>
                <tr>
                  <td>{{ $t("taskName") }}</td>
                  <td>{{ $t("taskType") }}</td>
                </tr>
              </thead>
              <tbody>
                <tr
                  v-for="item in detail.info.taskNameTypeList"
                  :key="item.taskName"
                >
                  <td>{{ item.taskName }}</td>
                  <td>{{ item.taskType }}</td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("loopCount") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.executionNumber }}
          </div>
        </div>
      </div>
      <!-- 车辆信息 -->
      <div class="content-panel-center">
        <div class="panel-title">{{ $t("vehicleInfo") }}</div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("vehicleNo") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.deviceId }}
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("projectArea") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.proAreaName }}
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("belongingProject") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.proName }}
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("belongingCompany") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.vehEntName }}
          </div>
        </div>
      </div>
      <!-- 操作者信息 -->
      <div class="content-panel-bottom">
        <div class="panel-title">{{ $t("operatorInfo") }}</div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("operator") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.userName }}
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("operationAccount") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.userAccount }}
          </div>
        </div>
        <div class="panel-item">
          <div class="panel-item-label">{{ $t("belongingCompany") }}：</div>
          <div class="panel-item-value">
            {{ detail.info.userEntName }}
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch, computed } from "vue";
import { getOperationRecordDeatil } from "@/services/api";
import type { GetOperationRecordDeatilResponse } from "@/services/type";
import { speedType } from "@/assets/ts/config";
import XDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("vehOperationRecord");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const detail = reactive({
  info: {} as GetOperationRecordDeatilResponse,
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = (await getOperationRecordDeatil(props.id)) || {};
    }
  }
);

const speedText = computed(() => {
  const target = speedType.find((item) => detail.info.speed === item.prop);
  return target?.label;
});
</script>

<style lang="scss" scoped>
.content {
  margin-right: 20px;
  &-panel-top,
  &-panel-center,
  &-panel-bottom {
    padding: 12px 20px;
    margin-top: 16px;
    background: #fff;
    border-radius: 8px;
    .panel-title {
      @include sc(14px, #9f9fa4) {
        font-weight: bold;
      }
    }
    .panel-item {
      display: flex;
      margin-top: 20px;
      height: 21px;
      line-height: 21px;
      font-size: 14px;
      &-label {
        width: 80px;
        color: #9f9fa4;
      }
      &-value {
        color: #383838;
        .result {
          position: relative;
          @include ct-f(both) {
            margin-top: -4px;
          }
          @include sc(14px, #fff);
          @include wh(70px, 31px) {
            border-radius: 4px;
          }
          .text {
            &::before {
              content: "";
              display: inline-block;
              margin-right: 5px;
              @include wh(6px, 6px) {
                border: 2px solid #fff;
                border-radius: 50px;
              }
            }
          }
          &.success {
            background: #27d4a1;
            &::before {
              position: absolute;
              left: -6px;
              @include triangle(6px, 6px, #27d4a1, left);
            }
          }
          &.fail {
            background: #f41515;
            &::before {
              position: absolute;
              left: -6px;
              @include triangle(6px, 6px, #f41515, left);
            }
          }
        }
      }
    }
  }
  &-panel-top {
    margin-top: 0;
    .panel-items {
      display: flex;
      .panel-item {
        flex: 1;
      }
    }
    .panel-table {
      &-label {
        @include wh(140px, 21px) {
          line-height: 21px;
        }
        @include sc(14px, #9f9fa4);
        margin-top: 20px;
      }
      &-content {
        table {
          margin: 20px 0;
          width: 100%;
          box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
          border-radius: 4px;
          thead {
            background: #f4f7fe;
            tr {
              @include fj(flex-start);
            }
            td {
              flex: 1;
              height: 36px;
              line-height: 36px;
              padding-left: 20px;
              @include sc(14px, #555);
            }
          }
          tbody {
            tr {
              @include fj(flex-start);
              height: 56px;
              line-height: 56px;
              &:not(:last-child) {
                border-bottom: 1px solid rgba(220, 220, 220, 0.4);
              }
            }
            td {
              flex: 1;
              padding-left: 20px;
              @include sc(14px, #555);
            }
          }
        }
      }
    }
  }
}
</style>
