export type OverlayItemData = {
  /** 覆盖物类型 路线(rotue) 区块(block) 场所（site）车位（park）高精地图范围（areaRange）标识区域（tagArea） */
  overlayType:
    | "route"
    | "block"
    | "site"
    | "park"
    | "csvRoute"
    | "customPolygon"
    | "customPolyline"
    | "areaRange"
    | "tagArea";
  /** 场所/路线/区块id */
  overlayId?: string;
  /** 场所/路线/区块名称 */
  overlayName?: string;
  /** 是否清扫 */
  cleanStatus?: boolean;
  /** 清扫次数/执行次数 */
  executeTimes?: number;
  /** 场所/路线/区块经纬度 */
  points: {
    longitude: number | string;
    latitude: number | string;
    x?: number;
    y?: number;
  }[];
  /** 自定义id */
  customId?: number;
  /** 覆盖物实例 overlayType = "customPolygon" | "customPolyline" 时有效 */
  overlayInstance?: any;
  /** 覆盖物是否常驻高亮 解决hover效果的副作用 */
  overlayHighlight?: boolean;
  /** 原始数据 */
  sourceData?: {
    /** park类型的父级[场所（site）]原始数据 */
    site?: Record<string, any>;
    /** park的原始数据 */
    park?: Record<string, any>;
    [x: string]: any;
  };
};

export type GetAreaRangeResponse = {
  id: string;
  /** 区域id */
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  longitude: number;
  latitude: number;
}[];

export type AreaRangeType = {
  id: string;
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  points: { longitude: number; latitude: number }[];
};

/** 标识区域覆盖物数据 */
declare type TagAreaInfo = {
  /** id */
  groupId?: string | number;
  /** 名称 */
  name: string;
  /** 类型 0:临停区 1:禁行区 2:禁扫区 */
  pointType: number;
  /** 原点经纬度 */
  originPoint: number[];
  /** 坐标数组 */
  points: {
    /** gcj02坐标[经度] */
    longitude: number;
    /** gcj02坐标[纬度] */
    latitude: number;
    /** 笛卡尔坐标[x] */
    x: number;
    /** 笛卡尔坐标[y] */
    y: number;
    /** 笛卡尔坐标[z] */
    z: number;
    /** 点位id */
    id?: number;
  }[];
  /** 覆盖物实例 */
  overlayInstance?: AMap.Polygon;
  /** 标识名称Marker实例 */
  nameMarkerInstance?: AMap.Marker;
  /** 箭头实例 */
  arrowLineInstance?: AMap.Polyline;
};

export type CarInfo = {
  /** 设备id */
  deviceId: string;
  /** 电量 */
  electricPoint: number;
  /** 更新时间00:00:00时分秒 */
  updateTime: string;
  /** 更新时间0000:00:00年月日 */
  updateDate: string;
  /** 电量 */
  electric: number;
  /** 充电状态 */
  recharge: boolean;
  /** 电量 */
  electric2: number;
  /** 充电状态 */
  recharge2: boolean;
  /** 驾驶模式,人工驾驶：manual, 自动驾驶：automatic, 遥控驾驶：remote, 急停模式：emergency_braking */
  driveMode: "manual" | "automatic" | "remote" | "emergency_braking";
  /** 详情页在线状态 */
  onlineDetail: "online" | "offline" | "charge";
  /** 速度*/
  speed: number;
  /** 档位*/
  gear: string;
  /** 左前置边刷实际外展距离*/
  leftFrontBrushDistance: number;
  /** 右前置边刷实际外展距离*/
  rightFrontBrushDistance: number;
  /** 执行次数*/
  executeTimes: number;
  /** 车辆里程小计*/
  currentMileage: number;
  /** 车辆里程总计*/
  totalMileage: number;
  /** 照明灯状态 0：false，1:true*/
  headLamp: boolean;
  /** 刹车灯状态 0：false，1:true*/
  brakeLamp: boolean;
  /** 双闪状态 0：false，1:true*/
  doubleFlash: boolean;
  /** 左转向灯状态 0：false，1:true*/
  turnLeftLamp: boolean;
  /** 右左转向灯状态 0：false，1:true*/
  turnRightLamp: boolean;
  /** 倒车灯状态 0：false，1:true*/
  backOffLamp: boolean;
  /** 警灯状态 0：false，1:true*/
  alarmLamp: boolean;
  /** 车辆急停开关，使能:true， 解除:false */
  emergencyBrakeSwitch: boolean;
  /** 车辆电源状态，车辆上电：true,车辆下电：false*/
  power: boolean;
  /** EPB开关状态，未操作：nop, EPB拉起（驻车）:pull, EPB释放：release*/
  EPB: string;
  /** 当前驻车状态, 驻车完成：park_done, 释放完成：release_done, 执行中：operating*/
  park: string;
  /** 扫刷旋转状态，打开：true,关闭：false*/
  broomWork: boolean;
  /** 扫刷喷头状态， 打开：true, 关闭：false*/
  sprayWork: boolean;
  /** 吸盘状态，停止：stop, 上升：up, 下降：down*/
  suctionWork: string;
  /** 垃圾箱门升降开合状态：停止：stop, 打开：open, 关闭：close*/
  boxDoorWork: string;
  /** 水箱*/
  tank: number;
  tankPoint: number;
  /** 垃圾*/
  litter: number;
  /** 人员名称 */
  worker: string;
  /** 人员电话 */
  workerPhone: string;
  /** 速度百分比 */
  speedPercent: string;
  /** 地址*/
  addr: string;
  /** 电池电压*/
  voltage: number;
  /** 电池电流*/
  current: number;
  /** 电池组最高温度*/
  temperatureMax: number;
  /** 最新告警*/
  latestWarn: string;
  /** 清扫面积*/
  cleanArea: number;
  direction: number;
  /** 左前轮胎压，单位bar*/
  leftFrontTirePressure: number;
  /** 左前轮胎温，单位摄氏度*/
  leftFrontTireTemperature: number;
  /** 右前轮胎压，单位bar*/
  rightFrontTirePressure: number;
  /** 右前轮胎温，单位摄氏度*/
  rightFrontTireTemperature: number;
  /** 左后轮胎压，单位bar*/
  leftReverseTirePressure: number;
  /** 左后轮胎温，单位摄氏度*/
  leftReverseTireTemperature: number;
  /** 右后轮胎压，单位bar*/
  rightReverseTirePressure: number;
  /** 右后轮胎温，单位摄氏度*/
  rightReverseTireTemperature: number;
  /** 水泵状态*/
  waterPump: boolean;
  /** 风机状态*/
  fan: boolean;
  /** 风机转速*/
  fanMotorSpeed: number;
  /** 放水阀状态*/
  waterDrainValue: boolean;
  /** 边刷喷水阀状态*/
  brushWaterSparyStatus: string;
  /** 工作状态 */
  workStatus: number;
  /** 在线状态*/
  online: boolean;
  /** 底盘在线状态 0下线， 1在线 ， 2不显示地盘状态 */
  chassisOnline: 2;
  /** 0暂停，1继续 */
  taskStatus: number;
  /** 0下电,1上电*/
  digIoSwitch: number;
  warnNum: number;
  /** 更新时间00:00:00时分秒*/
  gpsTime: string;
  /** 更新时间0000:00:00年月日*/
  gpsDate: string;
  /** 任务名称*/
  taskName: string;
  /** 厂家*/
  manufacturer: string;
  /** 任务工作状态*/
  taskWorkStatus: string;
  /** 0加载结束，1加载中*/
  loadingMark: number;
  /** 加载时间戳*/
  loadingTimeStart: number;
  /** 当前时间戳*/
  loadingTimeCurrent: number;
  /** 风机降尘水阀状态, open:打开，close:关闭*/
  fanDustFallStatus: string;
  /** 吸口降尘水阀状态, open:打开，close:关闭*/
  suctionDustFallStatus: string;
  /** 项目名称*/
  proName: string;
  /** 项目id*/
  proId: string;
  /** 区域名称*/
  areaName: string;
  /** 区域id*/
  areaId: string;
  /** 充电时间*/
  chargeTime: number;
  /** 边刷升降位置状态，invalid: 无效，fall:落下，rise:上升*/
  brushPosition: string;
  faultCacheMap: {
    [x: number]: {
      id: number;
      /** 告警内容*/
      faultContent: string;
      warnLevel: number;
      /** 告警类型，0：无，1，电池告警，2，告警，3报错*/
      faultModule: string;
      /** 告警内容文字*/
      faultDesc: string;
      /** 告警码*/
      code: string;
      /** 创建时间*/
      createdTime: string;
      /** 创建时间*/
      updateTime: string;
      /** 故障地址 保留字段 */
      faultAddress: string;
    };
  };
  /** 1,四轮车，2三轮车 */
  vehType: number;
  /** not_connect:未连接，connect:连接 */
  chargeConnectStatus: string;
  /** 模版id */
  tempId: string;
  /**  站点id */
  stationId: string;
  /** 是否为2.0协议 1.0:false,2.0:true */
  vehicleVersion: boolean;
  /** 车端软件版本 */
  vehicleSoftVersion: number;
  /** 当前任务类型： 0：待机，1：正常泊车，2：充电类型，3：垃圾触发类型，4：水箱触发类型，5：任务中，6：任务规划中 */
  currentTaskType: number;
  /** 自检结果： 1自检成功 2自检失败 3正在进行自检 */
  selfCheckResult: number;
  /** 车端自驾系统版本 */
  sysVersion: string;
  /** 红绿灯图片地址 [如果不为空就需要弹窗展示图片, 反之关闭弹窗] */
  trafficLightPics: string[];
  /** 垃圾箱举升状态 0-Dropped 1-Lifted */
  dustbinLiftPoseStatus: number;
  /** 垃圾箱斗翻转状态 0-Retracted 1-Overturn */
  dustbinOverturnPoseStatus: number;
  /** 维修中 */
  inCharge: string;
  /** 急停原因 0: 非急停状态 1: 急停按键触发 2: 前触边触发 3: 后触边触发 4: 整车故障触发 5: 遥控急停触发 6: 远程急停触发 */
  emgcyStopReason: number;
  /** 允许通过时间 */
  allowPassTime: number;
};
