import { taskType, scheduleType, dayType, speedType, blowType } from "@/assets/ts/config";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

export type TaskItemType = {
  /** 1：定时任务，2：定时倒垃圾，3：定时充电，4：定时开关机(开机) 5: 定时加水 6: 定时开关机(关机) */
  scheduleType: number;
  /** 0:覆盖 1:巡检 2:溜边 */
  taskType: number;
  /** "daily": 每天； "weekly"：星期 */
  executeDayType: string;
  /** 任务类型，0:每天，1-7:周一~周日， */
  executeDays: string[];
  /** 执行开始时间  */
  executeTimeStart: string;
  /** 执行结束时间 */
  executeTimeEnd: string;
  /** 当前车速 0.6:常速 1:中速 1.5:高速 */
  speed: number;
  /** 风机档位 0:关闭 3:一档 4:二档 5:三档 */
  fan: number;
  /** 洒水 0:关 1:开 */
  blowWater: number;
  /** 所选作业范围 1:一键清扫 2: 选区清扫 */
  coverCleanType?: number;
  /** 清扫次数 */
  executeTimes?: number;
  /** 站点类型， -1: 空,1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
  stationType?: number;
  /** 任务模版id/站点id */
  relId: string;
  /** 任务模版/站点名称 */
  relName?: string;
  /** 编辑状态 */
  editStatus: boolean;
  /** 是否为新增任务 */
  readonly isNew?: boolean;
  /** 1自选 2指定 */
  stationMode?: number;
  /** 开关机类型 4:开机 6:关机 【前端字段】需转换字段给后端字段`scheduleType` */
  _switchType: number | null;
  [x: string]: any;
};

/** 创建一个新的任务 */
export const createNewTask = (): TaskItemType => {
  return {
    scheduleType: 1,
    taskType: 0,
    executeDayType: "daily",
    executeDays: ["0"],
    executeTimeStart: "",
    executeTimeEnd: "",
    speed: 2,
    fan: 0,
    blowWater: 1,
    relId: "0",
    relName: "",
    editStatus: true,
    isNew: true,
    coverCleanType: 1,
    executeTimes: 1,
    stationType: -1,
    _switchType: 4,
  };
};
export const getExecuteDaysLabel = (days: string[]) => {
  const weekMap: Record<string, string> = {
    "0": $t("daily"),
    "1": $t("Monday"),
    "2": $t("Tuesday"),
    "3": $t("Wednesday"),
    "4": $t("Thursday"),
    "5": $t("Friday"),
    "6": $t("Saturday"),
    "7": $t("Sunday"),
  };
  if (days.length === 1 && days[0] === "0") {
    return $t("daily");
  } else {
    return `(${days.map((day) => weekMap[day]).join("/")})`;
  }
};
export const getStartTimeLabel = (value: number) => {
  const labelMap: Record<string, string> = {
    1: $t("taskStartTime"),
    2: $t("startTime"),
    3: $t("startTime"),
    4: $t("powerOnTime"),
  };
  return labelMap[value] || "";
};
export const getScheduleTypeLabel = (value: number) => {
  const type = scheduleType.find((item) => item.value === value);
  return type ? type.label : "";
};
export const getTaskTypeLabel = (value: number) => {
  const type = taskType.find((item) => item.value === value);
  return type ? type.label : "";
};
export const getGradientColor = (value: number, colorType: string) => {
  const type = scheduleType.find((item) => item.value === value);
  return type ? (colorType === "light" ? type.bgColorLight : type.bgColorDark) : "";
};
export const getSpeedText = (value: number) => {
  const type = speedType.find((item) => item.prop === value);
  return type ? type.label : "";
};
export const getFanText = (value: number) => {
  const type = blowType.find((item) => item.value === value);
  return type ? type.label : "";
};
export const getRelName = (formOptions: any, relId: string, scheduleType: number) => {
  let options: any[] = [];
  switch (scheduleType) {
    case 1:
      options = formOptions.routeTemplate;
      break;
    case 2:
      options = formOptions.garbageStation;
      break;
    case 3:
      options = formOptions.chargingStation;
      break;
  }
  const option = options.find((item) => item.value === relId);
  return option ? option.label : "";
};
export const validateTask = (task: TaskItemType) => {
  switch (task.scheduleType) {
    case 1:
      if (!task.executeTimeStart || task.executeDays.length === 0) {
        return false;
      }
      if (task.taskType === -1) {
        return false;
      }
      if (task.relId === "0" && task.coverCleanType === 2) {
        return false;
      }
      break;
    case 2:
      if (!task.executeTimeStart || task.executeDays.length === 0) {
        return false;
      }
      break;
    case 3:
      if (!task.executeTimeStart || task.executeDays.length === 0) {
        return false;
      }
      break;
    case 4:
      if (task.executeDays.length === 0 || !task._switchType) {
        return false;
      } else if (task._switchType === 4 && !task.executeTimeStart) {
        return false;
      } else if (task._switchType === 6 && !task.executeTimeEnd) {
        return false;
      }
      break;
    case 5:
      if (!task.executeTimeStart || task.executeDays.length === 0) {
        return false;
      }
      break;
    default:
      break;
  }
  return true;
};
