@charset "UTF-8";
@use "sass:math";

// $fontFamily: -apple-system, sans-serif;
// $fontCn: tahoma, 'PingFang SC', 'Microsoft YaHei', 'Hiragino Sans GB', '\5b8b\4f53', sans-serif;
// $fontEn: -apple-system, 'BlinkMacSystemFont', 'Helvetica Neue', Helvetica, sans-serif;

// $fontTitle1: 12px; // <h1>
// $fontTitle2: 12px; // <h2>
// $fontTitle3: 12px; // <h3>
// $fontTitle4: 12px; // <h4>
// $fontTitle5: 12px; // <h5>
// $fontTitle6: 12px; // <h6>

// $fontLarge: 32px; // 大号内容
// $fontContent: 14px; // 默认内容  container
// $fontSmall: 12px; // 小号内容
// $fontActivity: 28px; // 活动文字大小
// $fontLineHeight: math.div(3, 2); // 文字行高
// $letterSpacing: normal; // 字符间距
// $wordSpacing: normal; // 单词间距

// $colorTheme: #ff3838 !default; // 主题
// $colorBg: #fff !default; // 背景
// $colorBorder: #dbdbdb !default; // 边框
// $colorCover: rgba(0, 0, 0, 0.4) !default; // 遮罩层
// $colorContent: #333 !default; // 内容文字
// $colorInput: #212121 !default; // input文字
// $colorPlaceholder: #666 !default; // placeholder文字
// $colorDisabled: #999 !default; // disabled文字
// $colorLink: #08c !default; // 点击链接文字
// $colorVisited: #800080 !default; // 已点击链接文字

// $viewWidth: 750px; // 全屏宽度
// box-shadow
// transform
// transition
// animation

// $zIndexText: 700 !default;      // 文字遮盖::after/before
// $zIndexCover: 2000 !default;    // 遮盖阴影
// $zIndexPopover: 2000 !default;  // popover
// $zIndexModal: 3000 !default;    // 弹窗
// $zIndexBest: 10000 !default;    // 最高层


%fix {
  &::before,&::after { content: "";display: table; }
  &::after { clear: both;}
  *zoom: 1;
}

@mixin ct-p($direction: both) {
  position: absolute;
  @if $direction==both {
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  } @else if $direction==x {
    left: 50%;
    transform: translateX(-50%);
  } @else if $direction==y {
    top: 50%;
    transform: translateY(-50%);
  }
  @content;
}

@mixin ct-f($direction: both) {
  display: flex;

  @if $direction==both {
    justify-content: center;
    align-items: center;
  } @else if $direction==x {
    justify-content: center;
  } @else if $direction==y {
    align-items: center;
  }
  @content;
}

@mixin fj($type: space-between) {
  display: flex;
  justify-content: $type;
  @content;
}

@mixin wh($width, $height: $width) {
  width: $width;
  height: $height;
  @content;
}

%fullscreen {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

@mixin bis($url,$width:100%,$height:100%) {
	background-image: url($url);
	background-repeat: no-repeat;
	background-size: $width $height;
  @content;
}

@mixin sc($size, $color){
	font-size: $size;
	color: $color;
  @content;
}

@mixin ell {
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}

@mixin ells($lines: 2) {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: $lines;
  -webkit-box-orient: vertical;
}

@mixin triangle($width: 6px, $height: 6px, $borderColor: #000, $direction: top) {
  content: "";
  height: 0;
  width: 0;
  overflow: hidden;

  @if $direction==top {
    border-bottom: $height solid $borderColor;
    border-left: $width solid transparent;
    border-right: $width solid transparent;
  }

  @else if $direction==right {
    border-left: $width solid $borderColor;
    border-top: $height solid transparent;
    border-bottom: $height solid transparent;
  }

  @else if $direction==bottom {
    border-top: $height solid $borderColor;
    border-left: $width solid transparent;
    border-right: $width solid transparent;
  }

  @else if $direction==left {
    border-right: $width solid $borderColor;
    border-top: $height solid transparent;
    border-bottom: $height solid transparent;
  }

  @else if $direction==top-left {
    border-top: $height solid $borderColor;
    border-right: $width solid transparent;
  }

  @else if $direction==top-right {
    border-top: $height solid $borderColor;
    border-left: $width solid transparent;
  }

  @else if $direction==bottom-left {
    border-bottom: $height solid $borderColor;
    border-right: $width solid transparent;
  }

  @else if $direction==bottom-right {
    border-bottom: $height solid $borderColor;
    border-left: $width solid transparent;
  }
}


@mixin v-arrow($direction: right, $borderWidth: 2px, $size: 10px) {
  display: inline-block;
  vertical-align: middle;
  width: $size;
  height: $size;
  @content;
  @if $direction==top {
    border-top: $borderWidth solid currentColor;
    border-right: $borderWidth solid currentColor;
    transform: rotate(-45deg);
  }

  @else if $direction==right {
    border-top: $borderWidth solid currentColor;
    border-right: $borderWidth solid currentColor;
    transform: rotate(45deg);
  }

  @else if $direction==bottom {
    border-left: $borderWidth solid currentColor;
    border-bottom: $borderWidth solid currentColor;
    transform: rotate(-45deg);
  }

  @if $direction==left {
    border-left: $borderWidth solid currentColor;
    border-bottom: $borderWidth solid currentColor;
    transform: rotate(45deg);
  }
}


@mixin scrollbar($direction: y, $thick: 8px, $outColor: #F6F7F8, $inColor: #D3D6DA, $inBorderBadius: 3px ) {
  @if $direction==y {
    overflow-y: scroll;
  }

  @else if $direction==x {
    overflow-x: scroll;
  }

  @else if $direction==both {
    overflow: auto;
  }

  &::-webkit-scrollbar {
    @if $direction==y {
      width: $thick;
    }

    @else if $direction==x {
      height: $thick;
    }

    @else if $direction==both {
      width: $thick;
      height: $thick;
    }
  }

  &::-webkit-scrollbar-track {
    background: transparent;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: $inBorderBadius;
    background: transparent;
  }

  &:hover {

    &::-webkit-scrollbar-thumb {
      background-color: $inColor;
    }
  }

  @content;
}
