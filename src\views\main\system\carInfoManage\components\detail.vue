<template>
  <x-drawer
    :title="$t('carInfo')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="672px"
  >
    <div class="content">
      <div class="content-body">
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vehicleNo") }}：</div>
            <div class="content-body-item-value content-body-item-value_bold">
              {{ detail.info.vehicleNo }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("opDate") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.opDateText }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vehicleType") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.vehicleTypeText }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">
              {{ $t("vehicleModel") }}：
            </div>
            <div class="content-body-item-value">
              {{ detail.info.vehicleModeText }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vinNo") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.vin }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("simNo") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.simId }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">
              {{ $t("dandelionAcc") }}：
            </div>
            <div class="content-body-item-value">
              {{ detail.info.dandelionAccount }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">
              {{ $t("dandelionPWD") }}：
            </div>
            <div class="content-body-item-value">
              {{ detail.info.dandelionPassword ? "******" : "" }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("rtkAccount") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.rtkAccount }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("sn") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.sn }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">运营状态：</div>
            <div class="content-body-item-value">
              {{
                vehOperateStatusType.find(
                  (v) => v.value === detail.info.vehOperateStatus
                )?.label || ""
              }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">远程码：</div>
            <div class="content-body-item-value">
              {{ detail.info.remoteNo || "" }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("remoteSim") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.remoteSim }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">DVR id：</div>
            <div class="content-body-item-value">
              {{ detail.info.dvrId }}
            </div>
          </div>
        </div>
        <div class="content-body-items">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("enterprise") }}：</div>
            <div class="content-body-item-value">{{ detail.info.entName }}</div>
          </div>
        </div>
        <div class="content-foot">
          <x-upload v-model:value="detail.files" view />
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { GetVehicleDetailResponse } from "@/services/type";
import type { FileItem } from "@/components/x-upload/x-upload.vue";
import { parseFilePath } from "@/components/x-upload/file-path";
import { reactive, watch } from "vue";
import { getVehicleDetail } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xUpload from "@/components/x-upload";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { vehOperateStatusType } from "@/assets/ts/config";

const $t = i18nSimpleKey("carInfoManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 详情数据
 */
const detail = reactive({
  info: {} as GetVehicleDetailResponse,
  files: [] as FileItem[],
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = await getVehicleDetail(props.id);

      const { instrUrl } = detail.info;

      if (Array.isArray(instrUrl) && instrUrl.length > 0) {
        detail.files = instrUrl.map((item, index) => {
          const { type } = parseFilePath(item.instrUrl) || {};
          return {
            uid: `-${index + 1}`,
            url: item.instrUrl,
            status: "done",
            name: item.fileName,
            type,
          } as FileItem;
        });
      } else {
        detail.files = [];
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  &-body {
    background: rgb(255, 255, 255);
    border-radius: 8px;
    font-size: 14px;
    padding: 20px;

    &-items {
      display: flex;
    }

    &-item {
      display: flex;
      align-items: center;
      padding: 9px 0;
      flex: 1;

      &-label {
        color: #9f9fa4;
        margin-left: 46px;
        width: 6em;

        &:first-child {
          margin-left: 0;
        }
      }
      &-value {
        color: #383838;

        &_bold {
          color: rgb(16, 22, 55);
          font-size: 18px;
          font-weight: 700;
          line-height: 28px;
        }
      }
    }

    .content-foot {
      margin-top: 10px;
    }
  }
}
</style>
