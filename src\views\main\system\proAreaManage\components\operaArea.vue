<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="props.type === 'view' ? undefined : { position: 'center', confirmDisabled: confirmLoading }"
    :title="`${
      {
        add: $t('add'),
        edit: $t('edit'),
        view: $t('detail'),
      }[props.type]
    }${$t('area')}`"
    width="90%"
    bodyPadding="0"
    @confirm="operaDrawerConfirm"
    @cancel="operaDrawerCancel"
  >
    <div
      v-xloading="loading.value"
      :loading-text="loading.text"
      class="opera-area"
      ref="xOperaAreaRef"
    >
      <div
        class="opera-area-left"
        id="container"
      ></div>
      <div
        class="opera-area-right"
        ref="xOperaDrawerContentRef"
      >
        <x-radio-button
          type="tab"
          :gap="8"
          v-model:value="tabDataTop.tabsEnableType"
          :options="tabDataTop.tabs"
          @change="tabTopChange"
        />
        <template v-if="tabDataTop.tabsEnableType === 'baseInfo'">
          <div class="right-top">
            <div class="right-top-title">{{ $t("baseInfo") }}</div>
            <div class="right-top-form">
              <x-form
                v-if="/^add|edit$/.test(props.type)"
                ref="formRef"
                :model="operaForm"
                :rules="operaFormRules"
                labelFlex="80px"
              >
                <x-form-item
                  :label="$t('areaName')"
                  name="areaName"
                >
                  <x-input
                    v-model:value="operaForm.areaName"
                    :maxlength="20"
                    :placeholder="$t('PEnterAreaName')"
                  />
                </x-form-item>
                <x-form-item
                  :label="$t('areaType')"
                  name="areaType"
                >
                  <x-select
                    v-model:value="operaForm.areaType"
                    :options="operaFormOption.areaType"
                    :popupContainer="xOperaDrawerContentRef"
                    :placeholder="$t('PSelectAreaType')"
                  />
                </x-form-item>
                <x-form-item
                  v-if="/^add$/.test(props.type)"
                  :label="$t('entName')"
                  name="entId"
                >
                  <x-select
                    v-model:value="operaForm.entId"
                    :options="props.entList"
                    :popupContainer="xOperaDrawerContentRef"
                    :placeholder="$t('PSelectEnt')"
                  />
                </x-form-item>
                <x-form-item
                  v-if="/^add$/.test(props.type)"
                  :label="$t('proName')"
                  name="proId"
                >
                  <x-select
                    v-model:value="operaForm.proId"
                    :options="operaFormOption.proId"
                    :popupContainer="xOperaDrawerContentRef"
                    :placeholder="$t('PSelectProName')"
                  />
                </x-form-item>
              </x-form>
              <div
                v-if="/^view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("areaSeq") }}</span>
                <span>{{ operaForm.areaSeq }}</span>
              </div>
              <div
                v-if="/^view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("areaName") }}</span
                ><span>{{ operaForm.areaName }}</span>
              </div>
              <div
                v-if="/^view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("areaType") }}</span>
                <span> {{ operaForm.areaTypeName }} </span>
              </div>
              <div
                v-if="/^edit|view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("entName") }}</span>
                <span>{{ operaForm.entName }}</span>
              </div>
              <div
                v-if="/^view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("proSeq") }}</span
                ><span>{{ operaForm.proSeq }}</span>
              </div>
              <div
                v-if="/^edit|view$/.test(props.type)"
                class="form-view"
              >
                <span>{{ $t("proName") }}</span
                ><span>{{ operaForm.proName }}</span>
              </div>
            </div>
          </div>
          <div class="right-bottom">
            <x-radio-button
              type="tab"
              :gap="8"
              v-model:value="tabData.tabsEnableType"
              :options="tabData.tabs"
              @change="toggleTab"
            />
            <div class="right-bottom-content">
              <!-- 红绿灯 -->
              <trafficLight
                v-if="mapInfo.map && mapInfo.Amap"
                :show="tabData.tabsEnableType === 'trafficLight'"
                :trafficLightList="tabData.trafficLightList"
                :dataLoaded="mapDataLoaded"
                :type="props.type"
                :container="xOperaAreaRef"
                @renderMarker="handleRenderTrafficLightMarker"
                @checkDuplicateTrafficName="handleCheckDuplicateTrafficName"
              />
              <!-- 场所 -->
              <sitesPanel
                v-if="mapInfo.map && mapInfo.Amap"
                :show="tabData.tabsEnableType === 'sites'"
                :type="props.type"
                :entId="operaForm.entId"
                :mapInfo="mapInfo"
                :sites="tabData.sites"
                :container="xOperaAreaRef"
                :dataLoaded="mapDataLoaded"
                :areaId="props.id!"
                @renderMarker="renderMarker"
                @toggleMarker="toggleMarker"
                @togglePolyline="togglePolyline"
                @togglePolygon="togglePolygon"
              />
              <!-- 路线 -->
              <routesPanel
                v-if="mapInfo.map && mapInfo.Amap"
                :show="tabData.tabsEnableType === 'routes'"
                :type="props.type"
                :mapInfo="mapInfo"
                :routes="tabData.routes"
                :container="xOperaAreaRef"
                :dataLoaded="mapDataLoaded"
                @renderMarker="renderMarker"
                @toggleMarker="toggleMarker"
                @togglePolyline="togglePolyline"
                @togglePolygon="togglePolygon"
              />
              <!-- 区块 -->
              <blockPanel
                v-if="mapInfo.map"
                :show="tabData.tabsEnableType === 'blocks'"
                :type="props.type"
                :mapInfo="mapInfo"
                :blocks="tabData.blocks"
                :container="xOperaAreaRef"
                :dataLoaded="mapDataLoaded"
                @toggleMarker="toggleMarker"
                @togglePolyline="togglePolyline"
                @togglePolygon="togglePolygon"
              />
              <!-- 围栏 -->
              <fencesPanel
                v-if="mapInfo.map"
                :show="tabData.tabsEnableType === 'fences'"
                :type="props.type"
                :mapInfo="mapInfo"
                v-model:fences="tabData.fences"
                @clearFence="delFence"
                @toggleMarker="toggleMarker"
                @togglePolyline="togglePolyline"
                @togglePolygon="togglePolygon"
              />
            </div>
          </div>
        </template>
        <template v-else-if="tabDataTop.tabsEnableType === 'attachment'">
          <div class="right-content">
            <x-upload
              v-model:value="attachment.files"
              :headers="headers"
              :multiple="false"
              :showView="false"
              :showUpload="false"
              :allowTypeRepeat="false"
              :autoUpload="false"
              :beforeUpload="beforeUpload"
              :maxCount="1"
              accept=".xml"
              :action="`/cszg/proArea/upload-xml2?id=${props.id}`"
              placeholder="支持上传xml文件,最多支持1个文件上传"
            />
          </div>
        </template>
        <template v-else-if="tabDataTop.tabsEnableType === 'routeRecording'">
          <RouteRecording
            v-model="tabData.csvRoutes"
            :container="xOperaAreaRef"
            :type="props.type"
            :mapInfo="mapInfo"
            :id="props.id"
            :active-id="tabData.activeCsvRouteId"
          />
        </template>
        <template v-else-if="tabDataTop.tabsEnableType === 'point'">
          <pointPage
            :pointList="computedPointList"
            :areaId="props.id!"
            @choosePoint="handleChoosePoint"
            @refresh="fetchPointList"
          ></pointPage>
        </template>
      </div>
      <!-- 右上地址搜索 -->
      <div class="map-search-frame">
        <mapSearch
          v-if="searchInfoLoaded"
          :map="mapInfo.map"
          :placeSearch="mapInfo.placeSearch"
          :popupContainer="xOperaDrawerContentRef"
        />
      </div>
      <!-- 左上地图颜色提示 -->
      <div class="map-tip-frame">
        <div
          class="map-tip-item"
          v-for="(item, i) in mapColorTips"
          :key="i"
        >
          <span
            class="map-tip-item-icon"
            :style="{ backgroundColor: item.color }"
          />
          <span class="map-tip-item-text">{{ item.text }}</span>
        </div>
      </div>
    </div>
  </x-drawer>
</template>
<script lang="tsx" setup>
import pointPage from "./pointPage.vue";
import trafficLight from "./trafficLight.vue";
import { ref, reactive, watch, createVNode, render, nextTick } from "vue";
import type { PropType } from "vue";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import type { OptionsType } from "@/components/x-select.vue";
import SiteMarker from "@/views/main/components/siteMarker.vue";
import mapSearch from "./mapSearch.vue";
import AMapLoader from "@amap/amap-jsapi-loader";
import { areaType } from "@/assets/ts/config";
import {
  topZIndex,
  polylineMarkerStyle,
  polylineEditorStyle,
  polygonStyle,
  polygonEditorStyle,
  blockPolygonEditorStyle,
  polylineHoverMarkerStyle,
  polylineStyle,
} from "@/services/wsconfig";
import {
  getProList,
  addProArea,
  editProArea,
  getProAreaInfo,
  getProAreaRange,
  getMunicipalFacilitiesList,
} from "@/services/api";
import { fixZeroToStr } from "@/assets/ts/dateTime";
import xRadioButton from "@/components/x-radio-button.vue";
import sitesPanel from "./sitesPanel.vue";
import routesPanel from "./routesPanel.vue";
import blockPanel from "./blockPanel.vue";
import fencesPanel from "./fencesPanel.vue";
import RouteRecording, { type RouteFileItem } from "./routeRecording.vue";
import type { FileItem } from "@/components/x-upload/x-upload.vue";
import { getLocalStorage } from "@/assets/ts/storage";
import xUpload from "@/components/x-upload";
import { i18nSimpleKey } from "@/assets/ts/utils";
import type { AxiosRequestConfig } from "axios";
import axios from "axios";
import type {
  AddProAreaRequest,
  GetAreaRangeResponse,
  GetTrafficLightListResponse,
  MunicipalFacilitiesListResponse,
} from "@/services/type";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
import { computed } from "vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
const computedPointList = computed(() => pointList.value);
const $t = i18nSimpleKey("proAreaManage");

type AreaRangeType = {
  id: string;
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  points: { longitude: number; latitude: number }[];
};

const props = defineProps({
  type: {
    type: String as PropType<"add" | "edit" | "view">,
    required: true,
  },
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
  },
  entList: {
    type: Array as PropType<OptionsType>,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);
const pointList = ref<MunicipalFacilitiesListResponse[]>([]);
const xOperaDrawerContentRef = ref<any>();
const xOperaAreaRef = ref<any>();
const loading = reactive({
  value: false,
  text: "正在加载数据中...",
});

/**
 * 页签 - 附件
 */
const headers = { token: getLocalStorage("token") };

const tabDataTop = reactive({
  tabs: computed(() =>
    [
      {
        value: "baseInfo",
        label: "基本信息",
        show: true,
      },
      {
        value: "attachment",
        label: "高精地图",
        show: props.type === "edit",
      },
      {
        value: "routeRecording",
        label: "自录文件",
        show: props.type === "edit",
      },
      {
        value: "point",
        label: "点位",
        show: props.type === "edit",
      },
    ].filter((item) => item.show)
  ),
  tabsEnableType: "baseInfo" as "baseInfo" | "attachment" | "routeRecording",
});
const tabTopChange = (val: typeof tabDataTop.tabsEnableType) => {
  tabDataTop.tabsEnableType = val;
  const list = (mapInfo.markers as any[])
    .concat(mapInfo.blockPolygons, mapInfo.polylines, mapInfo.polylineMarkers)
    .concat([mapInfo.polygon]);
  if (val === "routeRecording") {
    list.forEach((overlay) => overlay && overlay.hide());
    mapInfo.csvRoutePolylines.forEach((polyline: any) => {
      polyline.setOptions(polylineStyle);
      polyline.show();
    });
  } else {
    list.forEach((overlay) => overlay && overlay.show());
    mapInfo.csvRoutePolylines.forEach((polyline: any) => polyline.hide());
  }
  mapInfo.polygonEditor?.setTarget(undefined);
  mapInfo.polygonEditor?.close();
  mapInfo.polylineEditor?.setTarget(undefined);
  mapInfo.polylineEditor?.close();
  mapInfo.blockEditor?.setTarget(undefined);
  mapInfo.blockEditor?.close();

  if (val === "baseInfo") mapInfo.map.setFitView(mapInfo.areaRanges, true);
};

const mapColorTips = [
  {
    color: "#F4151566",
    text: "障碍物",
  },
  {
    color: "#27D4A166",
    text: "可清扫区域",
  },
];

/** 高精地图上传 */
const attachment = reactive({
  files: [] as FileItem[],
  instrUrl: [] as any[],
});

/**
 * 页签 - 基本信息
 */
const formRef = ref<any>();
const operaForm = reactive({
  areaName: "",
  areaSeq: undefined as number | undefined,
  areaType: undefined as number | undefined,
  areaTypeName: "" as string | undefined,
  entId: undefined as string | undefined,
  entName: "",
  proId: "",
  proName: "" as string | undefined,
  proSeq: undefined as number | undefined,
});
const operaFormOption = reactive({
  areaType: areaType.filter((item) => [0, 1].includes(item.value)),
  proId: [] as OptionsType,
});
const operaFormRules = reactive({
  areaName: [["required", $t("PEnterAreaName")]],
  areaType: [["required", $t("PSelectAreaType")]],
  entId: [["required", $t("PEnterEnt")]],
  proId: [["required", $t("PSelectProName")]],
});
watch(
  () => operaForm.entId,
  async (newV) => {
    operaFormOption.proId = (await getProList([Number(newV)])).map((v) => ({
      ...v,
      label: v.proName,
      value: v.id,
    }));
  }
);

/**
 * 场所 / 路线 / 区块 / 围栏
 */
// tabs数据
const tabData = reactive({
  tabs: [
    {
      value: "trafficLight",
      label: "红绿灯",
    },
    {
      value: "sites",
      label: $t("sites"),
    },
    {
      value: "routes",
      label: $t("routes"),
    },
    {
      value: "blocks",
      label: "区块",
    },
    {
      value: "fences",
      label: $t("fences"),
    },
  ],
  tabsEnableType: "sites",
  trafficLightList: [] as GetTrafficLightListResponse[],
  sites: [] as typeof siteDefaultItem[],
  routes: [] as typeof routeDefaultItem[],
  blocks: [] as typeof blockDefaultItem[],
  fences: [] as typeof fenceDefaultItem,
  /** cvs 录制路线 */
  csvRoutes: [] as RouteFileItem[],
  activeCsvRouteId: "",
});

const toggleTab = (type: "trafficLight" | "sites" | "routes" | "blocks" | "fences") => {
  tabData.tabsEnableType = type;
};

// site数据
const siteDefaultItem = {
  id: "",
  wayId: "0" as string,
  parkNo: "",
  stationName: "场所",
  stationType: 4,
  stationTypeText: "",
  longitude: undefined as number | undefined,
  latitude: undefined as number | undefined,
  areaX: "",
  areaY: "",
  areaZ: "",
  yaw: "",
  rainShelter: false,
  stationPic: "",
  stationId: "",
  cleanType: [] as any[],
  parkingList: [] as any[],
  vehicleList: [] as any[],
};

// route数据
const routeDefaultItem = {
  id: "0" as string | undefined,
  routeName: "路线",
  carRouteName: "",
  cleanType: [] as any[],
  points: [] as { longitude: number; latitude: number }[],
};

// blocks数据
const blockDefaultItem = {
  id: undefined as string | undefined,
  proAreaId: undefined as string | undefined,
  blockName: "区块",
  drawMode: "select" as "select" | "draw",
  points: [] as { longitude: number; latitude: number }[],
};

// fence数据
const fenceDefaultItem = [] as { longitude: number; latitude: number }[];
const delFence = () => {
  mapInfo.map.remove([mapInfo.polygon]);
  // mapInfo.polygonEditor.removeAdsorbPolygons([mapInfo.polygon]);
  // mapInfo.polygonEditor.clearAdsorbPolygons();
  mapInfo.polygonEditor.setTarget(null);
  mapInfo.polygonEditor.open();
  mapInfo.polygon = null as any;
  tabData.fences = [];
};

/**
 * 地图相关
 */
const defaultMapInfo = {
  map: null as unknown as AMap.Map,
  Amap: null as unknown as typeof AMap,
  /** 折线编辑器 */
  polylineEditor: null as unknown as AMap.PolylineEditor,
  /** 场所 */
  markers: [] as AMap.Marker[],
  /** 路线 */
  polylines: [] as AMap.Polyline[],
  /** 路线尾部Marker */
  polylineMarkers: [] as AMap.Marker[],
  /** 路线hoverMarker */
  polylineHoverMarkersMap: new Map(),
  /** 围栏编辑器 */
  polygonEditor: null as unknown as AMap.PolygonEditor,
  /** 区块编辑器 */
  blockEditor: null as unknown as AMap.PolygonEditor,
  /** 范围 */
  polygon: null as unknown as AMap.Polygon,
  /** 区块 */
  blockPolygons: [] as AMap.Polygon[],
  /** 区块名称 */
  blockNames: [] as AMap.Marker[],
  /** 地图搜索 */
  placeSearch: null as any,
  /** 高精地图 */
  areaRanges: [] as AMap.Polygon[],
  /** 区块状态：选择/绘制*/
  blockStatus: "select" as "select" | "draw",
  /** 当前区块索引 */
  curBlockIndex: 0,
  /** 录制路线列表 */
  csvRoutePolylines: [] as AMap.Polyline[],
  /** 红绿灯标记 */
  trafficLightMarkers: [] as AMap.Marker[],
  /** 点位标记 */
  pointMarkers: [] as AMap.Marker[],
};
let mapInfo: typeof defaultMapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
mapInfo.polylineHoverMarkersMap = new Map();

const handleChoosePoint = (id: string) => {
  const targetMarker = mapInfo.pointMarkers.find((marker) => marker.getExtData().id === id);
  const markerDiv = targetMarker?.getExtData().markerDiv;
  console.log(targetMarker, "targetMarker");
  console.log(markerDiv, "markerDiv");

  render(
    createVNode(SiteMarker, {
      focus: true,
      icon: "point-marker-icon",
      number: 0,
      labelName: targetMarker?.getExtData().pointName,
    }),
    markerDiv
  );

  mapInfo.map.setFitView(mapInfo.pointMarkers);
};

const handleCheckDuplicateTrafficName = (list: GetTrafficLightListResponse[]) => {
  tabData.trafficLightList = list;
};

const renderMarker = (markerDiv: HTMLElement, focus: Boolean, icon: String, number = 0) => {
  render(
    createVNode(SiteMarker, {
      focus,
      icon,
      number,
    }),
    markerDiv
  );
};

const toggleMarker = (markerIndex: number | "none", isPolyline = false) => {
  if (props.type === "view") markerIndex = "none";
  const markers = isPolyline ? mapInfo.polylineMarkers : mapInfo.markers;
  markers.forEach((marker: AMap.Marker, index: number) => {
    let number = 0;
    isPolyline && (number = marker.getExtData().number);
    const { markerDiv, icon } = marker.getExtData();
    const focus = markerIndex === "none" ? false : markerIndex === index;
    // 编辑路线时需要显示在顶部方便拖拽
    isPolyline && marker.setzIndex(focus ? topZIndex : polylineMarkerStyle.zIndex);
    const _icon = isPolyline ? (focus ? "map_ployline_end_enable" : "map_ployline_end_disable") : icon;
    marker.setExtData({
      ...marker.getExtData(),
      markerFocus: focus,
      icon: _icon,
      number,
    });
    marker.setDraggable(focus);
    marker.setCursor(focus ? "move" : "pointer");
    renderMarker(markerDiv, focus, _icon, number);
  });
};

const togglePolyline = (polylineIndex: number | "none") => {
  // if (props.type === "view") polylineIndex = "none";
  // toggleMarker(polylineIndex, true);
  // if (polylineIndex === "none") {
  //   mapInfo.polylineEditor.close();
  // } else {
  //   mapInfo.polylineEditor.setTarget(mapInfo.polylines[polylineIndex]);
  //   mapInfo.polylines.length && mapInfo.polylineEditor.open();
  // }
};

/** 区块列表的点击 */
const togglePolygon = (polygonIndex: number | "none") => {
  if (props.type === "view") polygonIndex = "none";
  if (polygonIndex === "none") {
    mapInfo.blockEditor.close();
  } else if (mapInfo.blockPolygons[polygonIndex]) {
    mapInfo.blockPolygons.forEach((item) =>
      item.setOptions({
        ...blockPolygonEditorStyle.createOptions,
      })
    );
    if (tabData.blocks[polygonIndex].drawMode === "select") {
      mapInfo.blockEditor.close();
      mapInfo.blockPolygons[polygonIndex].setOptions({
        ...blockPolygonEditorStyle.editOptions,
      });
    } else if (mapInfo.blockPolygons[polygonIndex].getPath()?.length) {
      mapInfo.blockPolygons.length && mapInfo.blockEditor.setTarget(mapInfo.blockPolygons[polygonIndex]);
      mapInfo.blockPolygons.length && mapInfo.blockEditor.open();
    }
  } else {
    mapInfo.blockEditor.setTarget(undefined);
    mapInfo.blockEditor.open();
  }
};

/**
 * 初始化
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      abortController = new AbortController();
      initMap();
    }
  }
);

// 地图数据加载完毕
const mapDataLoaded = ref<boolean>(false);
/** `中断控制器`：用于中断请求(在关闭抽屉时中断请求，避免数据错误渲染) */
let abortController: AbortController | undefined;
const getDataAndRender = () => {
  loading.value = true;
  Promise.all([fetchProAreaInfo(), fetchAreaRangeInfo(), fetchPointList()]).then(() => {
    loading.value = false;
  });
};

/** 获取点位列表 */
const fetchPointList = async () => {
  try {
    loading.value = true;
    const res = await getMunicipalFacilitiesList({ areaId: props.id as string });
    pointList.value = res;
    await renderAllPointMarkers();
  } catch (error) {
    console.log(error);
  } finally {
    loading.value = false;
  }
};

/** 获取详情 */
const fetchProAreaInfo = async () => {
  await getProAreaInfo(props.id, { signal: abortController?.signal }).then((res) => {
    operaForm.entId = res.proAreaEntity.entId;
    operaForm.proId = res.proAreaEntity.proId;
    operaForm.areaName = res.proAreaEntity.areaName;
    operaForm.areaSeq = res.proAreaEntity.areaSeq;
    operaForm.areaType = res.proAreaEntity.areaType;
    operaForm.areaTypeName = operaFormOption.areaType.find((v) => v.value === operaForm.areaType)?.label;
    operaForm.entName = res.proAreaEntity.entName;
    (function waitGetProList() {
      setTimeout(() => {
        if (operaFormOption.proId.length) {
          const cur = operaFormOption.proId.find((v) => v.value === operaForm.proId);
          operaForm.proName = cur?.label;
          operaForm.proSeq = cur?.proSeq;
        } else {
          waitGetProList();
        }
      }, 50);
    })();

    // 红绿灯
    tabData.trafficLightList = res.trafficLightEntityList.map((item: any) => {
      // 从location中提取第一个经纬度点
      const firstPoint = item.location.split(";")[0];
      const [longitude, latitude] = firstPoint.split(",").map(Number);

      return {
        ...item,
        longitude,
        latitude,
      };
    });

    // 场所
    tabData.sites = res.areaStation.map((item) => ({
      ...item,
      parkNo: item.stationParkingRelEntityList?.[0]?.parkNo ?? "",
      stationId: item.id,
      rainShelter: item.rainShelter ?? false,
      cleanType: item.cleanTypeArrays,
      vehicleList: item.stationVehRelEntityList?.map((v) => v.vehicleId) ?? [],
      parkingList: item.stationParkingRelEntityList || [
        {
          parkNo: "",
          parkName: "A1",
          chargingId: null,
        },
      ],
    })) as any;

    // 路线
    tabData.routes = res.areaRoute.map(({ id, gps, routeName, vehRouteNo, cleanTypeArrays }: any) => ({
      id: id || "0",
      routeName,
      carRouteName: vehRouteNo,
      cleanType: cleanTypeArrays,
      points: gps.map(({ longitude, latitude }: any) => ({
        longitude,
        latitude,
      })),
    }));

    // 区块
    tabData.blocks = res.areaBlockDtoList.map((block: any) => ({
      id: block.id,
      proAreaId: block.proAreaId,
      blockName: block.blockName,
      drawMode: block.drawMode || "draw",
      points: block.areaBlockGpsList.map(({ longitude, latitude }: any) => ({
        longitude,
        latitude,
      })),
    }));

    // 围栏
    tabData.fences =
      res.areaFence.gps?.map(({ longitude, latitude }: any, index: number) => ({
        point: fixZeroToStr(index + 1),
        longitude,
        latitude,
      })) || [];
    if (tabData.fences.length) {
      mapInfo.polygon = new mapInfo.Amap.Polygon({
        path: tabData.fences.map(({ longitude, latitude }) => [longitude, latitude]),
        ...polygonStyle,
      });
      mapInfo.map.add([mapInfo.polygon]);
      mapInfo.polygonEditor.setTarget(mapInfo.polygon);
    }

    // 录制路线
    tabData.csvRoutes =
      res.recordingRouteEntities?.map(
        (route: any) =>
          ({
            ...route,
            name: route.routeName,
            url: route.routeFileUrl,
          } as RouteFileItem)
      ) || [];
    drawRecordRoutes();

    mapDataLoaded.value = true;

    tabData.tabsEnableType = "trafficLight";
    renderAllTrafficLightMarkers();
  });
};

/** 获取高精地图信息 */
const fetchAreaRangeInfo = async () => {
  const res = await getProAreaRange({ areaId: props.id }, { signal: abortController?.signal });
  const grouped = res.reduce((accumulator, item) => {
    const key = `${item.xmlId}_${item.pointType}`;
    if (!accumulator[key]) {
      accumulator[key] = [];
    }
    accumulator[key].push(item);
    return accumulator;
  }, {} as any);
  const list: GetAreaRangeResponse[] = Object.values(grouped);

  drawAreaRange(
    list.map((item) => ({
      id: item[0].id,
      proAreaId: item[0].proAreaId,
      xmlId: item[0].xmlId,
      pointType: item[0].pointType,
      points: item.map(({ longitude, latitude }) => ({ longitude, latitude })),
    }))
  );
};

/** 渲染高精地图 */
const drawAreaRange = (list: AreaRangeType[]) => {
  // const barrierRange = list.filter((item) => item.pointType === 1);
  // const cleanRange = list.filter((item) => item.pointType === 0);
  // 带孔多边形，待尝试
  // const polygons = cleanRange.map((item) => {
  //   const polygon = new mapInfo.Amap.Polygon({
  //     path: [item.points.map(({ longitude, latitude }: any) => [
  //       longitude,
  //       latitude,
  //     ]), ...barrierRange.map(item => item.points.map(({ longitude, latitude }: any) => [longitude,latitude,]))],
  //     strokeWeight: 0,
  //     fillOpacity: 0.4,
  //     fillColor: item.pointType === 1 ? "#F41515" : "#27D4A1",
  //     zIndex: 10,
  //     bubble: true,
  //     extData: { ...item, overlayType: "areaRange" },
  //   });
  //   polygon.on("click", toggleAreaRange);
  //   return polygon;
  // });
  const polygons = list.map((item) => {
    const polygon = new mapInfo.Amap.Polygon({
      path: item.points.map(({ longitude, latitude }: any) => [longitude, latitude]),
      strokeWeight: 0,
      fillOpacity: 0.4,
      fillColor: item.pointType === 1 ? "#F41515" : "#27D4A1",
      zIndex: 1,
      bubble: true,
      extData: item,
      cursor: "default",
    });

    polygon.on("click", toggleAreaRange);

    return polygon;
  });
  mapInfo.areaRanges = polygons;
  mapInfo.map.add(polygons);
  mapInfo.map.setFitView(polygons, true, [10, 50, 10, 50], 20);
};

/** 高精地图Polygon点击事件 */
const toggleAreaRange = (e: any) => {
  if (tabData.tabsEnableType === "blocks") {
    if (tabData.blocks[mapInfo.curBlockIndex].drawMode === "draw") return;
    const data: AreaRangeType = e.target.getExtData();
    /** 障碍物区域不可点击 */
    if (data.pointType === 1) return;
    const blockName = tabData.blocks[mapInfo.curBlockIndex].blockName;
    const polygon = new mapInfo.Amap.Polygon({
      ...blockPolygonEditorStyle.editOptions,
      path: data.points.map(({ longitude, latitude }) => [longitude, latitude]),
      extData: {
        name: blockName,
      },
    });
    polygon.on("mouseover", (e: any) => {
      mapInfo.blockNames.forEach((blockName) => {
        if (blockName.getExtData().name === e.target.getExtData().name) {
          blockName.show();
        } else {
          blockName.hide();
        }
      });
    });
    polygon.on("mouseout", () => {
      mapInfo.blockNames.forEach((blockName: any) => blockName.hide());
    });

    const markerDiv = document.createElement("div");
    const _marker = new mapInfo.Amap.Marker({
      ...polylineHoverMarkerStyle,
      content: markerDiv,
      position: polygon.getBounds()?.getCenter(),
      zooms: [12, 20],
      visible: false,
      clickable: false,
      cursor: "default",
      extData: {
        markerDiv: markerDiv,
        name: blockName,
      },
    });
    render(
      createVNode(PolylineNameMarker, {
        name: blockName,
        type: "light",
      }),
      markerDiv
    );

    if (tabData.blocks[mapInfo.curBlockIndex].points.length) {
      mapInfo.map.remove(mapInfo.blockPolygons[mapInfo.curBlockIndex]);
      mapInfo.map.remove(mapInfo.blockNames[mapInfo.curBlockIndex]);
    }
    mapInfo.map.add(polygon);
    mapInfo.map.add(_marker);
    mapInfo.blockPolygons[mapInfo.curBlockIndex] = polygon;
    mapInfo.blockNames[mapInfo.curBlockIndex] = _marker;
    tabData.blocks[mapInfo.curBlockIndex].points = data.points;
  }
};

/** 渲染录制路线并隐藏 */
const drawRecordRoutes = () => {
  const polylines = tabData.csvRoutes.map((route) => {
    const path = route.recordingGpsList?.map(({ longitude, latitude }) => [longitude, latitude]) || [];
    const polyline = new mapInfo.Amap.Polyline({
      ...polylineStyle,
      path: path as [number, number][],
      extData: route,
    });
    polyline.hide(); // 默认隐藏
    return polyline;
  });
  mapInfo.csvRoutePolylines = polylines;
  mapInfo.map.add(mapInfo.csvRoutePolylines);
};

// 搜索信息加载完毕
const searchInfoLoaded = ref<boolean>(false);
const initMap = () => {
  loading.value = true;

  lazyAMapApiLoader().then((AMap) => {
    mapInfo.map = new AMap.Map("container", {
      zooms: [2, 26],
      mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
    });
    mapInfo.Amap = AMap;

    // 地图搜索
    // @ts-ignore
    mapInfo.placeSearch = new AMap.PlaceSearch();
    searchInfoLoaded.value = true;

    // 路线
    mapInfo.polylineEditor = new AMap.PolylineEditor(mapInfo.map, undefined, polylineEditorStyle);

    // 区块
    mapInfo.blockEditor = new AMap.PolygonEditor(mapInfo.map, undefined, blockPolygonEditorStyle);

    // 围栏
    mapInfo.polygonEditor = new AMap.PolygonEditor(mapInfo.map, undefined, polygonEditorStyle);

    ({
      add: () => {
        loading.value = false;
      },
      edit: () => {
        getDataAndRender();
      },
      view: () => {
        getDataAndRender();
      },
    }[props.type]());

    const scale = new AMap.Scale({
      position: {
        left: "10px",
        bottom: "50px",
      },
    });
    mapInfo.map.addControl(scale);
  });
};

/** 附件上传 */
const beforeUpload = async () => {
  let bool = false;
  /** 编辑时判断站点列表中是否有绑定的车辆，如有就要提示用户是否保留上次地图车位与车辆的绑定关系 */
  if (props.type === "edit" && tabData.sites.some((v) => Boolean(v.vehicleList.length))) {
    xModal.confirm({
      title: "是否保留上次地图车位与车辆的绑定关系",
      cancelText: "否",
      confirmText: "是",
      confirm: () => {
        bool = uploadHighFile(true);
      },
      cancel: () => {
        bool = uploadHighFile(false);
      },
      close: () => {
        attachment.files = [];
        bool = false;
      },
    });
  } else {
    bool = uploadHighFile(false);
  }
  return bool;
};

/** 上传高精地图文件 keepBinding 是否保存上次地图车位与车辆的绑定关系 */
const uploadHighFile = (keepBinding: boolean) => {
  loading.value = true;
  loading.text = "正在解析xml文件...";
  const fileItem = attachment.files[0];
  const config = {
    headers: {
      "Content-Type": "multipart/form-data",
      token: getLocalStorage("token"),
    },
    transformRequest: [
      function (file) {
        const form = new FormData();
        form.append("file", file);
        return form;
      },
    ],
    onUploadProgress: (progressEvent) => {
      fileItem.percent = progressEvent.total ? ((progressEvent.loaded / progressEvent.total) * 100) | 0 : 0;
    },
    timeout: 1000 * 60 * 5,
  } as AxiosRequestConfig;
  axios
    .post(
      `${import.meta.env.VITE_BASE_URL}/cszg/proArea/upload-xml2?id=${props.id}&keepBinding=${keepBinding}`,
      fileItem.file,
      config
    )
    .then((response) => {
      if (response.status === 200) {
        Message("success", "xml文件分析成功");
        updateVisible(false);
      }
    })
    .catch((reason) => {
      Message("error", reason?.response?.data?.message || "文件解析失败");
    })
    .finally(() => {
      loading.value = false;
      tabDataTop.tabsEnableType = "baseInfo";
    });

  return false;
};

const confirmLoading = ref<boolean>(false);
/** 抽屉操作*/
const operaDrawerConfirm = async () => {
  const { sites, routes, blocks } = tabData;
  let hasDuplicateId = false;
  const isSitesValid = sites.every((site) => {
    const basicInfoValid = site.stationName && site.longitude !== undefined && site.latitude !== undefined;
    // && site.wayId !== "";
    const parkingIds = new Set();
    const parkingListValid = site.parkingList?.every((park: any) => {
      if (parkingIds.has(park.parkNo)) {
        const duplicateSiteName = site.stationName || $t("unknownSite");
        Message("error", `${duplicateSiteName}${$t($t("duplicateIDExistPEdit"))}`);
        hasDuplicateId = true;
        return false;
      }
      parkingIds.add(park.parkNo);
      return park.parkNo !== "" && park.parkName !== "";
    });
    return basicInfoValid && parkingListValid;
  });
  const isRoutesValid = routes.every((route) => {
    return route.routeName && route.carRouteName;
  });
  const isBaseInfoValid =
    operaForm.areaName !== "" &&
    operaForm.areaType !== undefined &&
    operaForm.entId !== undefined &&
    operaForm.proId !== "";
  if (hasDuplicateId) return false;
  if (!isSitesValid) {
    Message("error", $t("PCompleteFenceInfo"));
    return;
  }
  const isBlockValid = blocks.every((item) => item.blockName && item.points.length);
  // if (tabData.routes.length) { // 取消路线必填
  if (!isRoutesValid) {
    Message("error", $t("PCompleteRouteInfo"));
    return;
  }
  if (!isBlockValid) return Message("error", "请将区块信息补充完整");
  const csvRoute = tabData.csvRoutes.find((item) => !(item.routePath && item.routePath.length));
  if (csvRoute) {
    tabDataTop.tabsEnableType = "routeRecording";
    tabData.activeCsvRouteId = csvRoute.id as string;
    return Message("error", "请将录制路线补充完整");
  }
  if (isBaseInfoValid || formRef.value.validate()) {
    xModal.confirm({
      title: $t("SureToSaveFences"),
      cancelText: $t("No"),
      confirmText: $t("Yes"),
      confirm() {
        const param: AddProAreaRequest = {
          entId: operaForm.entId,
          projectId: operaForm.proId,
          areaName: operaForm.areaName,
          areaType: operaForm.areaType,
          areaRouteDto: tabData.routes.map((item) => ({
            id: item.id || "0",
            routeName: item.routeName,
            vehRouteNo: item.carRouteName,
            cleanType: item.cleanType.join(),
            gps: item.points,
          })),
          sysAreaStationEntityList: tabData.sites.map((item) => {
            const { parkingList, vehicleList, ...rest } = item;
            return {
              ...rest,
              cleanType: item.cleanType?.join(),
              stationParkingRelEntityList: parkingList,
              stationVehRelEntityList: vehicleList.map((vehicleId) => ({
                proAreaId: props.id,
                vehicleId: vehicleId,
              })),
            };
          }),
          areaFenceDto: {
            gps: tabData.fences,
          },
          areaBlockDtoList: tabData.blocks.map((block) => ({
            blockName: block.blockName,
            id: block.id,
            drawMode: block.drawMode,
            proAreaId: block.proAreaId,
            areaBlockGpsList: block.points,
          })),
          recordingRouteEntities: tabData.csvRoutes.map((csv) => ({
            id: csv.id,
            routePath: csv.routePath,
            routeType: csv.routeType,
            routeStatus: csv.routeStatus,
          })),
          trafficLightEntityList: tabData.trafficLightList.map((item: any) => ({
            id: item.id,
            trafficName: item.trafficName,
          })),
        };

        return {
          add: async () => {
            confirmLoading.value = true;
            return addProArea(param)
              .then(() => {
                updateVisible(false);
                Message("success", $t("addSuccess"));
                emits("confirm");
                operaDrawerCancel();
              })
              .finally(() => {
                confirmLoading.value = false;
              });
          },
          edit: async () => {
            confirmLoading.value = true;
            return editProArea({
              ...param,
              id: props.id,
            })
              .then(() => {
                updateVisible(false);
                Message("success", $t("editSuccess"));
                emits("confirm");
                operaDrawerCancel();
              })
              .finally(() => {
                confirmLoading.value = false;
              });
          },
          view: () => {},
        }[props.type]();
      },
    });
  }
};

const operaDrawerCancel = () => {
  tabDataTop.tabsEnableType = "baseInfo";

  // 表单
  operaForm.areaName = "";
  operaForm.areaType = undefined;
  operaForm.entId = undefined;
  operaForm.proId = "";
  operaForm.areaTypeName = "";
  operaForm.entName = "";
  operaForm.proName = "";
  // 附件
  attachment.files = [];
  attachment.instrUrl = [];

  tabData.tabsEnableType = "sites";
  tabData.sites = [];
  tabData.routes = [];
  tabData.fences = [];
  tabData.blocks = [];
  tabData.csvRoutes = [];
  tabData.activeCsvRouteId = "";
  mapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
  mapInfo.polylineHoverMarkersMap = new Map();

  mapDataLoaded.value = false;
  searchInfoLoaded.value = false;
  loading.text = "正在加载数据中...";
  loading.value = false;
  abortController?.abort();
  abortController = undefined;
};

/** 一次性渲染所有红绿灯标记 */
const renderAllTrafficLightMarkers = () => {
  // 清除之前的标记
  mapInfo.trafficLightMarkers.forEach((marker) => marker.setMap(null));
  mapInfo.trafficLightMarkers = [];

  // 为所有红绿灯创建标记
  tabData.trafficLightList.forEach((item: any) => {
    if (!item.longitude || !item.latitude) return;

    const markerDiv = document.createElement("div");
    const marker = new mapInfo.Amap.Marker({
      position: [item.longitude, item.latitude],
      content: markerDiv,
      offset: new mapInfo.Amap.Pixel(-10, -10),
      zIndex: 100,
      extData: {
        markerDiv,
        type: "traffic-light-color-icon",
        id: item.id,
        focus: false,
      },
    });

    // 渲染标记点内容
    render(
      createVNode(SiteMarker, {
        focus: false,
        icon: "traffic-light-color-icon",
        number: 0,
      }),
      markerDiv
    );

    // 添加到地图
    marker.setMap(mapInfo.map);
    mapInfo.trafficLightMarkers.push(marker);
  });
};

/** 渲染所有点位标记 */
const renderAllPointMarkers = () => {
  // 清除之前的标记
  mapInfo.pointMarkers.forEach((marker) => marker.setMap(null));
  mapInfo.pointMarkers = [];
  pointList.value.forEach((item: any) => {
    if (!item.longitude || !item.latitude) return;

    const markerDiv = document.createElement("div");
    const marker = new mapInfo.Amap.Marker({
      position: [item.longitude, item.latitude],
      content: markerDiv,
      offset: new mapInfo.Amap.Pixel(-10, -10),
      zIndex: 100,
      extData: {
        markerDiv,
        type: "point-marker-icon",
        id: item.id,
        focus: false,
        pointName: item.pointName,
      },
    });

    // 渲染标记点内容
    render(
      createVNode(SiteMarker, {
        focus: false,
        icon: "point-marker-icon",
        number: 0,
        labelName: item.pointName,
      }),

      markerDiv
    );

    // 添加到地图
    marker.setMap(mapInfo.map);
    mapInfo.pointMarkers.push(marker);
  });
};

// 修改点击处理函数,只改变显示效果
const handleRenderTrafficLightMarker = (item: GetTrafficLightListResponse) => {
  // 更新所有标记的显示效果
  mapInfo.trafficLightMarkers.forEach((marker) => {
    const extData = marker.getExtData();
    const isCurrentMarker = extData.id === item.id;
    const markerDiv = extData.markerDiv;

    // 更新标记的焦点状态
    extData.focus = isCurrentMarker;

    // 更新标记的层级
    marker.setzIndex(isCurrentMarker ? topZIndex : 100);

    // 重新渲染标记内容
    render(
      createVNode(SiteMarker, {
        focus: isCurrentMarker,
        icon: "traffic-light-color-icon",
        number: 0,
      }),
      markerDiv
    );
  });
};
</script>

<style lang="scss" scoped>
.opera-area {
  position: relative;
  display: flex;
  @include wh(100%);
  &-left {
    flex: 1;
    height: 100%;
  }
  &-right {
    @include fj {
      flex-direction: column;
      justify-content: flex-start;
    }
    flex-basis: 348px;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.9);
    .right-top {
      padding: 0 16px;
      &-title {
        margin-top: 15px;
        @include sc(16px, #9f9fa4);
      }
      &-form {
        margin-top: 15px;
        .form-view {
          display: flex;
          @include wh(100%, 40px) {
            line-height: 32px;
          }
          span:nth-child(1) {
            width: 86px;
            color: #9f9fa4;
          }
          span:nth-child(2) {
            color: #383838;
          }
        }
      }
    }
    .right-bottom {
      @include fj {
        flex-direction: column;
      }
      position: relative;
      flex: 1;
      width: 100%;
      &-content {
        @include wh(100%, 0);
        flex: 1 0 auto;
      }
    }
    .right-content {
      @include wh(100%) {
        padding: 16px;
      }
    }
  }
  .map-search-frame {
    position: absolute;
    right: 366px;
    top: 14px;
    box-shadow: 0px 9px 28px rgba(0, 0, 0, 0.05);
  }
  .map-tip {
    &-frame {
      position: absolute;
      left: 18px;
      top: 14px;
      background-color: #fff;
      border-radius: 4px;
      padding: 12px 14px;
      box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    }
    &-item {
      margin-bottom: 12px;
      &-icon {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 100%;
        margin-right: 5px;
      }
      &-text {
        font-size: 12px;
      }
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}
.content {
  .content-header {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 54px);
    @include sc(16px, #242859);
    .title-icon {
      margin-right: 10px;
    }
    .title-text {
      font-weight: bold;
    }
  }
  .content-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    padding-left: 26px;
    @include sc(14px, #383838);
  }
}
</style>
