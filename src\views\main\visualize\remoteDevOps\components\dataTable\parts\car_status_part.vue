<template>
  <div class="car-status-part">
    <div
      class="car-status-part-info"
      style="width: 30%"
    >
      <el-scrollbar height="100%">
        <div class="car-status-part-info-title">基本状态</div>
        <el-row>
          <el-col
            style="margin-bottom: 10px"
            :span="item.span"
            v-for="item in infoStatus"
            :key="item.label"
          >
            <span class="label">{{ item.label }}：</span>
            <span class="value">{{ item.value || "-" }}&nbsp;&nbsp;</span>
            <span class="unit">{{ item.unit }}</span>
          </el-col>
        </el-row>
      </el-scrollbar>
    </div>

    <div
      class="car-status-part-info"
      style="width: 20%"
    >
      <div class="car-status-part-info-title">导航状态</div>
      <el-row>
        <el-col
          style="margin-bottom: 10px"
          v-for="item in navgetStatus"
          :key="item.label"
        >
          <span class="label">{{ item.label }}：</span>
          <span class="value">{{ item.value || "-" }}&nbsp;&nbsp;</span>
        </el-col>
      </el-row>
    </div>

    <div
      class="car-status-part-info"
      style="width: 30%"
    >
      <el-scrollbar height="100%">
        <div class="car-status-part-info-title">传感器状态</div>
        <el-row>
          <el-col
            style="margin-bottom: 10px"
            v-for="item in sensorStatus"
            :key="item.label"
          >
            <span class="label">{{ item.label }}：</span>
            <span class="value">{{ item.value || "-" }}&nbsp;&nbsp;</span>
            <span class="unit">{{ item.unit }}</span>
          </el-col>
        </el-row>
      </el-scrollbar>
    </div>

    <div
      class="car-status-part-info"
      style="width: 20%"
    >
      <div class="car-status-part-info-title">清扫机构</div>
      <el-row>
        <el-col
          style="margin-bottom: 10px"
          v-for="item in cleaningOrganization"
          :key="item.label"
        >
          <span class="label">{{ item.label }}：</span>
          <span class="value">{{ item.value || "-" }}&nbsp;&nbsp;</span>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { onMounted, computed, inject, type Ref } from "vue";
import type { CarInfo } from "../../../type";
import { formatRoundNum } from "@/assets/ts/utils";

/**
 * 基本状态
 */
const infoStatus = computed(() => {
  return [
    { label: "电量", value: formatRoundNum(carInfo.value?.electric, 0), unit: "%", span: 12 },
    { label: "水量", value: formatRoundNum(carInfo.value?.tank, 0), unit: "%", span: 12 },
    { label: "垃圾量", value: formatRoundNum(carInfo.value?.litter, 0), unit: "%", span: 12 },
    { label: "速度", value: formatRoundNum(carInfo.value?.speed, 1), unit: "km/h", span: 12 },
    { label: "模式", value: carInfo.value?.driveMode ? driveMode[carInfo.value.driveMode] : "-", unit: "", span: 12 },
    {
      label: "档位",
      value: carInfo.value?.gear ? gearEume[carInfo.value?.gear as keyof typeof gearEume] : "-",
      unit: "",
      span: 12,
    },
    { label: "总里程", value: formatRoundNum(carInfo.value?.totalMileage, 1), unit: "km", span: 24 },
    { label: "加水盖", value: "", unit: "", span: 24 },
    { label: "充电电流", value: "", unit: "A", span: 24 },
    { label: "充电电压", value: "", unit: "V", span: 24 },
    { label: "电池电流", value: formatRoundNum(carInfo.value?.current, 0), unit: "A", span: 24 },
    { label: "电池电压", value: formatRoundNum(carInfo.value?.voltage, 0), unit: "V", span: 24 },
    { label: "电池温度", value: formatRoundNum(carInfo.value?.temperatureMax, 0), unit: "°", span: 24 },
    {
      label: "GPS上传时间",
      value:
        carInfo.value?.gpsDate && carInfo.value?.gpsTime ? `${carInfo.value.gpsDate} ${carInfo.value.gpsTime}` : "-",
      unit: "",
      span: 24,
    },
    {
      label: "数据上传时间",
      value:
        carInfo.value?.updateDate && carInfo.value?.updateTime
          ? `${carInfo.value.updateDate} ${carInfo.value.updateTime}`
          : "-",
      unit: "",
      span: 24,
    },
    { label: "GPS位置", value: carInfo.value?.addr, unit: "", span: 24 },
  ];
});

/**
 * 导航状态
 */
const navgetStatus = computed(() => {
  return [
    { label: "定位状态", value: "" },
    { label: "地图状态", value: "" },
    { label: "地图版本", value: "" },
    { label: "自驾版本", value: carInfo.value?.sysVersion },
    { label: "任务状态", value: "" },
    {
      label: "急停原因",
      value: carInfo.value?.emgcyStopReason
        ? emgcyStopReasonEume[carInfo.value.emgcyStopReason as keyof typeof emgcyStopReasonEume]
        : "-",
    },
  ];
});

/**
 * 传感器状态
 */
const sensorStatus = computed(() => {
  return [
    { label: "主激光雷达状态状态", value: "", unit: "" },
    { label: "左前激光雷达状态状态", value: "", unit: "" },
    { label: "左后激光雷达状态状态", value: "", unit: "" },
    { label: "右前激光雷达状态状态", value: "", unit: "" },
    { label: "右后激光雷达状态状态", value: "", unit: "" },
    { label: "摄像头前", value: "", unit: "" },
    { label: "摄像头后", value: "", unit: "" },
    { label: "摄像头左", value: "", unit: "" },
    { label: "摄像头左前", value: "", unit: "" },
    { label: "摄像头右", value: "", unit: "" },
    { label: "摄像头右前", value: "", unit: "" },
    { label: "域控CPU温度", value: "", unit: "C" },
    { label: "域控CPU占用率", value: "", unit: "%" },
    { label: "域控GPU温度", value: "", unit: "C" },
    { label: "域控GPU占用率", value: "", unit: "%" },
  ];
});

/**
 * 清扫机构
 */
const cleaningOrganization = computed(() => {
  return [
    { label: "边刷打开程度", value: "", unit: "%" },
    { label: "边刷转速", value: "", unit: "r/min" },
    { label: "滚刷转速", value: "", unit: "r/min" },
    {
      label: "喷雾状态",
      value:
        carInfo.value?.brushWaterSparyStatus && carInfo.value.brushWaterSparyStatus in sprayStatus
          ? sprayStatus[carInfo.value.brushWaterSparyStatus as keyof typeof sprayStatus]
          : "-",
      unit: "",
    },
    { label: "前挡板状态", value: "", unit: "" },
    {
      label: "垃圾斗",
      value:
        carInfo.value?.dustbinOverturnPoseStatus !== undefined &&
        carInfo.value.dustbinOverturnPoseStatus in dustbinStatus
          ? dustbinStatus[carInfo.value.dustbinOverturnPoseStatus as keyof typeof dustbinStatus]
          : "-",
      unit: "",
    },
    {
      label: "风机状态",
      value:
        carInfo.value?.fanDustFallStatus && carInfo.value.fanDustFallStatus in fanDustFallStatus
          ? fanDustFallStatus[carInfo.value.fanDustFallStatus as keyof typeof fanDustFallStatus]
          : "-",
      unit: "",
    },
  ];
});

/**
 * 模式枚举
 */
const driveMode = {
  manual: "人工驾驶",
  automatic: "自动驾驶",
  remote: "遥控驾驶",
  emergency_braking: "急停模式",
} as const;

/**
 * 垃圾斗状态枚举
 */
const dustbinStatus = {
  0: "原位",
  1: "翻转",
} as const;

/**
 * 风机状态枚举
 */
const fanDustFallStatus = {
  open: "开启",
  close: "关闭",
} as const;

/**
 * 喷雾状态枚举
 */
const sprayStatus = {
  open: "开启",
  close: "关闭",
} as const;
const carInfo = inject("carInfo") as Ref<CarInfo>;
/**
 * 档位枚举
 */
const gearEume = {
  park_done: "P",
  back: "R",
  neutral: "N",
  forward: "D",
} as const;

const emgcyStopReasonEume = {
  0: "未知",
  1: "碰撞",
  2: "遥控器",
  3: "急停按钮",
  4: "遥控器+急停按钮",
} as const;
</script>
<script lang="ts">
export default {
  name: "carStatusPart",
};
</script>
<style scoped lang="scss">
.car-status-part {
  width: 100%;
  height: 100%;
  padding: 10px 15px;
  padding-bottom: 50px;
  display: flex;
  gap: 10px;
  &-info {
    width: 100%;
    height: 100%;
    padding: 15px;
    border-radius: 4px;
    background: rgb(247, 249, 255);
    &-title {
      font-size: 14px;
      color: #2b2f5e;
      margin-bottom: 10px;
    }
    .label {
      font-size: 13px;
      color: #666666;
    }
    .value {
      font-size: 13px;
      color: #333333;
    }
  }
}
</style>
