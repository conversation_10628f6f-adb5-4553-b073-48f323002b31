<template>
  <section
    id="historyAmap"
    ref="historyAmapRef"
  >
    <slot></slot>
  </section>
</template>
<script lang="ts" setup>
import { ref, watch, render, createVNode } from "vue";
import { polylineCarLiveStyle, topZIndex } from "@/services/wsconfig";
import AMapLoader from "@amap/amap-jsapi-loader";
import carMarker from "./carDetailHistoryAmapMarker.vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";

const props = withDefaults(
  defineProps<{
    trackList: any[];
    trackIndex: number;
  }>(),
  {}
);

const mapInfo: any = {
  map: null,
  Amap: null,
  marker: null,
  markerDiv: null,
  curTrackPath: [],
  polyline: null,
  passedPolyline: null,
};
const historyAmapRef = ref<HTMLElement>();

lazyAMapApiLoader().then((AMap) => {
  mapInfo.map = new AMap.Map("historyAmap", {
    zooms: [2, 26],
    mapStyle: "amap://styles/27e17e138bb7b2f02eb26243352889b2",
    viewMode: "3D", //开启3D视图,默认为关闭
    // rotateEnable: false, // 是否开启地图旋转交互
    // pitchEnable: false, // 是否开启地图倾斜交互
    rotation: 0, // 顺时针旋转角度 [0-360]
    pitch: 0, // 地图俯仰角度 [0-83]
  });
  mapInfo.Amap = AMap;

  const scale = new AMap.Scale({
    position: {
      left: "10px",
      bottom: "50px",
    },
  });
  mapInfo.map.addControl(scale);

  watch(
    () => props.trackList,
    (newV) => {
      if (newV.length) {
        mapInfo.curTrackPath = newV.map(({ longitude, latitude }) => [longitude, latitude]);

        !mapInfo.marker && initMarker(newV[props.trackIndex]);
        mapInfo.polyline && mapInfo.map.remove(mapInfo.polyline);
        mapInfo.passedPolyline && mapInfo.map.remove(mapInfo.passedPolyline);

        mapInfo.polyline = new AMap.Polyline({
          map: mapInfo.map,
          path: mapInfo.curTrackPath,
          ...polylineCarLiveStyle,
        });
        mapInfo.passedPolyline = new AMap.Polyline({
          map: mapInfo.map,
          zIndex: topZIndex + 1,
          borderWeight: 0,
          fillOpacity: 1,
          strokeOpacity: 1,
          strokeStyle: "solid",
          strokeWeight: 8,
          strokeColor: "#c5c5c5",
        });
        mapInfo.map.setFitView(mapInfo.polyline, false, [0, 0, 0, 0]);
        // mapInfo.map.setCenter(e.target.getPosition()); // 设置地图中心点
        // mapInfo.map.setRotation(-e.target.getOrientation()); // 设置旋转角
      }
    },
    {
      immediate: true,
    }
  );
  watch(
    () => props.trackIndex,
    (newV) => {
      const trackItem = props.trackList[newV];
      if (trackItem) {
        mapInfo.marker.setPosition(mapInfo.curTrackPath[newV]); // 更新位置
        mapInfo.passedPolyline.setPath(mapInfo.curTrackPath.slice(0, newV)); // 更新已走路线
        renderCarMarker(trackItem); // 更新方向等信息
      }
    },
    {
      immediate: true,
    }
  );
});

const initMarker = (trackItem: typeof props.trackList[0]) => {
  const map: any = mapInfo.map;
  const Amap: any = mapInfo.Amap;
  mapInfo.markerDiv = document.createElement("div");
  mapInfo.marker = new Amap.Marker({
    content: mapInfo.markerDiv,
    position: [trackItem.longitude, trackItem.latitude],
    anchor: "center",
    // offset: [0, 0], // 若设置了anchor，则以anchor设置位置为基准点
  });
  renderCarMarker(trackItem);
  map.add(mapInfo.marker);
};

const renderCarMarker = (info: typeof props.trackList[0]) => {
  render(
    createVNode(carMarker, {
      status: {
        online: true,
        warn: false,
        deviceId: info.deviceId,
      },
      focus: true,
      direction: info.direction,
    }),
    mapInfo.markerDiv
  );
};
</script>

<style lang="scss" scoped></style>
