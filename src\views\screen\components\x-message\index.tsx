import {
  createVNode,
  render,
  defineComponent,
  ref,
  Transition,
  onMounted,
} from "vue";

import "./x-message.scss";

export default (type: "error" | "success", text: string, time = 2000) => {
  let removeTimer: number;
  let hideAnimaTimer: number;

  const Message = defineComponent({
    name: "x-message",
    props: {
      type: {
        type: String,
        required: true,
      },
      option: {
        type: Object,
        required: true,
      },
    },
    setup(props) {
      const vIf = ref(false);
      onMounted(() => {
        vIf.value = true;
        clearTimeout(removeTimer);
        clearTimeout(hideAnimaTimer);
        hideAnimaTimer = window.setTimeout(() => {
          vIf.value = false;
        }, time);
        removeTimer = window.setTimeout(() => {
          render(null, div);
          document.body.removeChild(div);
        }, time + 500);
      });
      return () => {
        return (
          <Transition class="x-screen-message" name="x-screen-message">
            {vIf.value ? (
              <div class="x-screen-message-error">
                <img
                  src={
                    {
                      error: new URL(
                        `@/assets/images/x_message_error.png`,
                        import.meta.url
                      ).href,
                      success: new URL(
                        `@/assets/images/x_message_success.png`,
                        import.meta.url
                      ).href,
                    }[props.type]
                  }
                />
                <span>{props.option.text}</span>
              </div>
            ) : null}
          </Transition>
        );
      };
    },
  });

  const div = document.createElement("div");

  div.setAttribute("class", "x-message-container");

  document.body.appendChild(div);

  render(createVNode(Message, { type, option: { text } }), div); // 渲染vnode到div
};
