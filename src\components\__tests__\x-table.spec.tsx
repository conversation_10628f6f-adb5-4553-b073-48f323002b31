import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import XTable from '@/components/x-table.vue';
import XPagination from "@/components/x-pagination.vue";

const baseProps = { 
  cols: [
    { key: 'colKey', title: 'colTitle'}
  ],
  dataSource: [
    { colKey: 'colKeyValue' }
  ],
} 
const pagination = {
  total: 1,
  current: 1,
  pageSize: 10
}

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XTable,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 cols + dataSource + pagination', () => {
    const wrapper = shallowMount(XTable,{
      props: { 
        ...baseProps,
        pagination,
      }
    });
    const [headTable, bodyTable] = wrapper.findAll('table');
    expect(headTable.find('td').text()).toBe('colTitle');
    expect(bodyTable.find('td').text()).toBe('colKeyValue');
    expect(wrapper.findComponent(XPagination).exists()).toBe(true);
  });

  it('正确渲染 loading和无数据 状态', async () => {
    const wrapper = shallowMount(XTable,{
      props: { 
        loading: true,
      }
    });
    expect(wrapper.find('.x-table-loading').exists()).toBe(true);
    await wrapper.setProps({ loading: false });
    expect(wrapper.find('.x-table-nodata').exists()).toBe(true);
  });

});

describe('Events', () => {
  it('验证customRow', () => {
    let count = 0;
    const wrapper = shallowMount(XTable,{
      props: { 
        ...baseProps,
        customRow: () => {
          return {
            onClick: () => ++count
          }
        }
      }
    });
    wrapper.findAll('table')[1].find('tr').trigger('click');
    expect(count).toBe(1);
  });
});