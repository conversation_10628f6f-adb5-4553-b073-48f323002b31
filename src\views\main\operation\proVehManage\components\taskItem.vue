<template>
  <template v-if="task.editStatus">
    <div class="task">
      <div class="task-header">
        <x-select
          v-model:value="task.scheduleType"
          :options="scheduleType"
          :popupContainer="popupContainer"
          style="width: 400px"
          @update:value="onScheduleTypeChange"
        />
        <x-popover
          trigger="hover"
          placement="bottom"
          :container="popupContainer"
        >
          <x-icon
            name="del_x"
            width="16"
            height="16"
            class="del-icon"
            @click="onDelete"
          />
          <template #content>
            <div class="task-preview-hover-popover">
              {{ $t("deleteTask") }}
            </div>
          </template>
        </x-popover>
      </div>
      <TaskEdit
        :task="task"
        :formOptions="formOptions"
        :popupContainer="popupContainer"
      />
      <div class="task-footer">
        <!-- <x-button
          type="white"
          :text="$t('cancel')"
          @click="onConfirm"
        /> -->
        <x-button
          type="blue"
          :text="$t('Confirm')"
          :disabled="!validateTask(task)"
          @click="onConfirm"
        />
      </div>
    </div>
  </template>
  <template v-else>
    <TaskPreview
      :task="task"
      :showOpera="true"
      :showSwitch="props.showSwitch"
      :popupContainer="popupContainer"
      bgType="light"
      @editTask="onEdit"
      @deleteTask="onDelete"
    />
  </template>
</template>

<script lang="ts" setup>
import xSelect from "@/components/x-select.vue";
import xIcon from "@/components/x-icon.vue";
import xPopover from "@/components/x-popover.vue";
import xButton from "@/components/x-button.vue";
import TaskEdit from "./taskEdit.vue";
import TaskPreview from "./taskPreview.vue";
import { scheduleType } from "@/assets/ts/config";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { validateTask, getRelName, type TaskItemType, createNewTask } from "./utils";
import type { PropType } from "vue";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  formOptions: {
    type: Object as Object as PropType<
      Partial<
        Record<
          | "routeTemplate"
          | "chargingStation"
          | "garbageStation"
          | "parkingStation"
          | "wateringStation"
          | "scheduleTemplate",
          { label: string; value: string }[]
        >
      >
    >,
    required: true,
  },
  task: {
    type: Object as PropType<TaskItemType>,
    required: true,
  },
  showSwitch: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["deleteTask", "editTask", "confirmTask"]);

const onConfirm = () => {
  props.task.relName = getRelName(props.formOptions, props.task.relId, props.task.scheduleType);
  if (props.task.executeDays && Array.isArray(props.task.executeDays)) {
    props.task.executeDays.sort((a, b) => parseInt(a) - parseInt(b));
  }
  emits("confirmTask");
};

const onEdit = () => {
  emits("editTask");
};

const onDelete = () => {
  emits("deleteTask");
};

const onScheduleTypeChange = (value: number) => {
  Object.assign(props.task, createNewTask());
  props.task.scheduleType = value;
  props.task.relId = "0";
  props.task.relName = "";
  props.task.stationType = value === 2 ? 1 : value === 3 ? 2 : value === 5 ? 3 : -1;

  /** 1：定时任务，2：定时倒垃圾，3：定时充电，4：定时开机 5: 定时加水 6：定时关机 */
  const optionsMap: Record<number, { label: string; value: string }[]> = {
    1: props.formOptions.routeTemplate ?? [],
    2: props.formOptions.garbageStation ?? [],
    3: props.formOptions.chargingStation ?? [],
    4: props.formOptions.parkingStation ?? [],
    5: props.formOptions.wateringStation ?? [],
  };
};
</script>

<style lang="scss" scoped>
.task {
  display: flex;
  flex-direction: column;
  margin-top: 10px;
  padding: 10px 20px;
  background: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
  border-radius: 8px;
  &-header {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .del-icon {
      right: 10px;
      top: 10px;
      cursor: pointer;
    }
  }
  &-footer {
    margin-top: 10px;
    display: flex;
    column-gap: 10px;
  }
}
</style>
