<template>
  <x-drawer
    title="任务设置"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="0 0 0 0"
    width="90%"
  >
    <div
      class="task-distribution"
      ref="taskDistributionRef"
    >
      <div
        class="task-distribution-map"
        ref="mapRef"
      />
      <div
        class="task-distribution-modal"
        ref="templateContentRef"
      >
        <div class="modal-top">
          <div class="modal-top-title">
            <x-title>任务设置</x-title>
          </div>
          <div class="modal-top-menus">
            <x-radio-button
              v-model:value="tab.active"
              type="tab"
              :gap="15"
              :options="tab.list"
              @change="tabChange"
            />
          </div>
        </div>
        <div
          class="model-template"
          v-if="tab.active === 2"
        >
          <x-select
            :value="selectedTemplate?.templateId"
            :options="templateOptions"
            :popupContainer="templateContentRef"
            :placeholder="'请选择'"
            style="width: 100%; margin-right: 10px"
            @change="templateSelectChange"
          />
        </div>
        <div class="modal-content">
          <template v-if="tab.active === 3">
            <div class="modal-content-tip">
              <span>{{ "请点击选择清扫区域或区域工具画区块下任务！" }}</span>
              <x-icon
                class="delete-icon"
                name="delete"
                @click.stop="clearSelectedList"
              />
            </div>
            <!-- 自定义列表 -->
            <div class="modal-content-list">
              <div
                :class="{ hg: item.overlayType === 'structurePoints' }"
                class="modal-content-list-item"
                v-for="(item, index) in selectedList"
                :key="index"
                @click="handleItem(item)"
              >
                <template v-if="item.overlayType === 'structurePoints'">
                  <div class="left_point">
                    <text>{{ item.overlayName }}</text>
                    <text>经：{{ item.points[0].longitude }}</text>
                    <text>纬：{{ item.points[0].latitude }}</text>
                  </div>
                </template>
                <template v-else>
                  <div class="modal-content-list-item-left">
                    <div class="modal-content-list-item-left-circle"></div>
                    <div class="modal-content-list-item-left-line"></div>
                  </div>
                  <div class="modal-content-list-item-middle">
                    <div class="modal-content-list-item-middle-row">
                      <span>{{ item.overlayName }}</span>
                    </div>
                    <div
                      class="modal-content-list-item-middle-row"
                      v-if="item.overlayType === 'park'"
                    >
                      {{ item.sourceData?.site?.stationTypeText || "--" }}
                    </div>
                    <div
                      v-else
                      class="modal-content-list-item-middle-row"
                      style="column-gap: 5cap"
                    >
                      <div>
                        <span style="color: #555">次数</span>
                        <span>{{ 1 }}次</span>
                      </div>
                      <xSwitch
                        v-if="
                          carInfo.vehicleVersion &&
                          carInfo.vehicleSoftVersion === 3 &&
                          ['route', 'csvRoute', 'customPolyline'].includes(item.overlayType)
                        "
                        v-model:checked="item.cleanStatus"
                        :checked-value="true"
                        :unchecked-value="false"
                        style="margin: 0"
                      >
                        <template #checkedChildren>
                          <span>{{ "清扫" }}</span>
                        </template>
                        <template #unCheckedChildren>
                          <span style="color: #aaa">{{ "不清扫" }}</span>
                        </template>
                      </xSwitch>
                    </div>
                  </div>
                </template>

                <x-icon
                  class="right-delete"
                  name="del_x_blue"
                  width="18px"
                  height="18px"
                  @click.stop="delOverlay(index)"
                />
              </div>
            </div>
          </template>
          <template v-if="showOptionContent">
            <div
              class="opera-wrapper"
              v-if="false"
            >
              <div class="col">
                <div class="sideTab">
                  <div
                    v-for="item in cleanType.options"
                    :key="item.value"
                    class="sideTab-item"
                    :class="[{ 'is-active': cleanType.index === item.value }]"
                    @click="cleanType.index = item.value"
                  >
                    <text>{{ item.label }}</text>
                  </div>
                </div>
                <p class="label-text">清扫类型</p>
              </div>
              <div class="col">
                <div class="center-round">
                  <roundPercent
                    class="image"
                    :percent="speed.active * 100"
                  />
                  <div class="percent">
                    <span>{{ value.toFixed(1) }}</span>
                    <span>km/h</span>
                  </div>
                </div>
                <div
                  class="water-btn"
                  :class="{ 'is-active': water.index === 2 }"
                  @click="handleWater"
                >
                  洒水
                </div>
                <x-number-box
                  :value="cleanTimes.index"
                  :min="1"
                  :max="20"
                  @change="cleanTimes.index = $event"
                />
                <p class="label-text">清扫次数{{ speed.active }}</p>
              </div>

              <div class="col">
                <div
                  class="bar-container"
                  @mousedown="startDrag"
                  @touchstart="startDrag"
                >
                  <div class="bar-bg">
                    <div
                      class="bar-fill"
                      :style="{ height: fillHeight + '%' }"
                    >
                      <div class="bar-value">{{ value.toFixed(1) }}</div>
                    </div>
                    <div class="speed-marks">
                      <div
                        v-for="mark in speed.options"
                        :key="mark.value"
                        class="speed-mark"
                        :style="{ bottom: ((mark.value - 1.0) / 4.0) * 100 + '%' }"
                      >
                        <span class="speed-mark-value">{{ mark.value.toFixed(1) }}</span>
                      </div>
                    </div>
                  </div>
                </div>
                <p class="label-text">速度设置</p>
              </div>
            </div>
            <div class="modal-content-title">{{ isShowWelt ? "贴边类型" : "清扫类型" }}</div>
            <template v-if="isShowWelt">
              <x-radio
                v-model:value="sweepDirection.index"
                :options="sweepDirection.options"
                optionType="button"
              />
            </template>
            <template v-else>
              <x-radio
                v-model:value="cleanType.index"
                :options="cleanType.options"
                optionType="button"
              />
            </template>
            <div class="modal-content-title">洒水</div>
            <x-radio
              v-model:value="water.index"
              :options="water.options"
              optionType="button"
            />
            <div class="modal-content-title">速度设置</div>
            <x-radio
              v-model:value="speed.active"
              :options="speed.options"
              optionType="button"
            >
              <template #content="{ option }">
                <span>{{ option.label }}</span>
                <span>({{ option.value }}km/h)</span>
              </template>
            </x-radio>
            <template v-if="tab.active !== 3">
              <div class="modal-content-title">清扫次数</div>
              <div class="clean-times-content">
                <x-number-box
                  :value="cleanTimes.index"
                  :min="1"
                  :max="20"
                  @change="cleanTimes.index = $event"
                />
              </div>
            </template>
          </template>
          <div
            class="modal-content-space"
            v-if="tab.active !== 3"
          >
            <div class="space-title">场所</div>
            <div class="space-content">
              <div
                :class="['space-content-item', { enable: item.value === space.index }]"
                v-for="(item, index) in showSpaceOptions"
                :key="index"
                @click="space.index = space.index === item.value ? -1 : item.value"
              >
                <x-icon
                  :name="`${item.icon}${item.value === space.index ? '' : '_disabled'}`"
                  width="28px"
                  height="28px"
                />
                <span>{{ item.label }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="modal-btn">
          <x-button
            type="white"
            text="取消"
            @click="handleCancel"
            style="margin-right: 9px"
          />
          <x-button
            type="blue"
            text="确认"
            :disabled="confirmDisibled"
            :loading="submitLoading"
            @click="handleConfirm"
          />
        </div>
      </div>
      <!-- 左上地图颜色提示 -->
      <div class="map-tip-frame">
        <div
          v-for="(item, i) in mapColorTips"
          :key="i"
          :class="['map-tip-item', { 'is-show': item.visible }]"
          @click="handleMapColorTipsItem(item)"
        >
          <view class="map-tip-item-icon">
            <span
              v-if="item.shape === 'circle'"
              class="circle"
              :style="{ backgroundColor: item.color }"
            />
            <span
              v-if="item.shape === 'polyline'"
              class="polyline"
              :style="{ backgroundColor: item.color }"
            />
            <xIcon
              v-if="item.value === 'structurePoints'"
              name="point-marker-icon"
              width="12px"
              height="12px"
            />
          </view>
          <span class="map-tip-item-text">{{ item.text }}</span>
        </div>
      </div>
      <!-- 区域工具  点位工具 -->
      <div
        class="task-distribution-control"
        v-show="tab.active === 3"
      >
        <div
          class="control-item"
          :class="{ 'is-edit': editorState.add }"
          @click="handleBlockDraw"
        >
          <div class="control-item-left">
            <x-icon
              class="control-item-icon"
              :name="`block_control_icon${editorState.add ? '_active' : ''}`"
              width="12px"
              height="12px"
            />
            <span>{{ editorState.add ? mapInfo.editingPolygon?.overlayName : "区域工具" }}</span>
          </div>
          <div
            class="control-item-right"
            v-show="editorState.add"
          >
            <x-button
              class="control-item-btn"
              type="red"
              text="取消"
              @click.stop="blockToolCancel"
            />
            <x-button
              class="control-item-btn"
              type="blue"
              text="添加"
              @click.stop="blockToolFinish"
            />
          </div>
        </div>

        <div
          class="control-item"
          @click="handlePointDraw"
          :class="{ point_active: isdrawPoint }"
        >
          <div class="control-item-left">
            <x-icon
              v-show="!isdrawPoint"
              class="control-item-icon"
              name="point-icon"
              width="12px"
              height="12px"
            />
            <span>{{ isdrawPoint ? "完成设置" : "点位工具" }}</span>
          </div>
        </div>
      </div>

      <div
        class="x-loading-mask"
        v-if="loading"
      >
        <div class="x-loading-gif" />
      </div>
    </div>
  </x-drawer>
</template>
<script lang="ts" setup>
import roundPercent from "./roundPercent.vue";
import { computed, createVNode, reactive, ref, render, watch } from "vue";
import AMapLoader from "@amap/amap-jsapi-loader";
import xTitle from "@/components/x-title.vue";
import xDrawer from "@/components/x-drawer.vue";
import xButton from "@/components/x-button.vue";
import xRadioButton from "@/components/x-radio-button.vue";
import xRadio from "@/components/x-radio.vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import xSwitch from "@/components/x-switch.vue";
import XNumberBox from "@/components/x-number-box.vue";
import Message from "@/components/x-message";
import { openChargingStatus, task2 } from "@/services/wsapi";
import {
  getProAreaRange,
  getRouteTemplate,
  getVehBlockList,
  getVehRouteList,
  getVehStationList,
  queryVehRecordingRoute,
} from "@/services/api";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
import {
  polygonEditorStyle,
  polylineHoverMarkerStyle,
  polylineStyle,
  polylineTemplateStyle as _polylineTemplateStyle,
  taskTypeMap,
} from "@/services/wsconfig";
import type { GetAreaRangeResponse } from "@/services/type";
import { useMainStore } from "@/stores/main";
import Marker from "../components/marker.vue";
import SiteMarker from "@/views/main/components/siteMarker.vue";
import SiteMarkerInfoWindow from "../components/siteMarkerInfoWindow.vue";
import { getTemplateVersionName, sitesType, speedType } from "@/assets/ts/config";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
import { debounce } from "@/assets/ts/utils";

type OverlayItemData = {
  /** 覆盖物类型 路线(rotue) 区块(block) 场所（site）车位（park）高精地图范围（areaRange） */
  overlayType:
    | "route"
    | "block"
    | "site"
    | "park"
    | "csvRoute"
    | "customPolygon"
    | "customPolyline"
    | "areaRange"
    | "structurePoints";
  /** 场所/路线/区块id */
  overlayId?: string;
  /** 场所/路线/区块名称 */
  overlayName?: string;
  /** 是否清扫 */
  cleanStatus?: boolean;
  /** 清扫次数/执行次数 */
  executeTimes?: number;
  /** 场所/路线/区块经纬度 */
  points: {
    longitude: number | string;
    latitude: number | string;
    x?: number;
    y?: number;
  }[];
  /** 自定义id */
  customId?: number;
  /** 覆盖物实例 overlayType = "customPolygon" | "customPolyline" 时有效 */
  overlayInstance?: any;
  /** 覆盖物的选中状态/覆盖物是否常驻高亮 解决hover效果的副作用 */
  overlayHighlight?: boolean;
  /** 原始数据 */
  sourceData?: {
    /** park类型的父级[场所（site）]原始数据 */
    site?: Record<string, any>;
    /** park的原始数据 */
    park?: Record<string, any>;
    [x: string]: any;
  };
  markerDiv?: any;
};
type TemplateType = {
  templateId: any;
  templateName?: string | null;
  label: string;
  value: string;
  /** 模版版本 1:1.0路线；2:2.0区块；3:2.0自录路线 4:3.0模版  5结构化路线模版，6结构化点位模版*/
  version?: number;
  routeList: {
    vehRouteNo: any;
    routeName: string;
    routeId: any;
    taskType: any;
  }[];
  blockList: {
    blockName: string;
    blockId: any;
  }[];
  /** 自录路线 */
  csvRouteList: {
    recordingId: any;
    routeName: string;
  }[];
  /** 结构化点位模版 */
  structurePointList: {
    pointName: string;
    pointId: any;
    latitude: number;
    longitude: number;
  }[];
};
type AreaRangeType = {
  id: string;
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  points: { longitude: number; latitude: number }[];
};

const props = withDefaults(
  defineProps<{
    show: boolean;
    id: string;
    online: boolean;
  }>(),
  {}
);
const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
const isdrawPoint = ref(false);
let mapClickHandler: any = null;
const value = ref(2.0);
const fillHeight = ref(((value.value - 1.0) / 4.0) * 100);
const isDragging = ref(false);
let startY = 0;
let initialValue = 0;
const submitLoading = ref(false);
/** 点位名称种子 */

const pointNameSeed = ref<number>(1);
/** zIndex: 区块block(default: 10,active: 11) 车位park(default: 12, active: 13) 路线(default: 14, active: 15) 场所site(default: 30, active: 30) 高精地图范围(default: 5) */
const defaultPolygonOptions = {
  strokeWeight: 0,
  fillOpacity: 0.7,
  fillColor: "#9F9FA4",
  bubble: true,
  zIndex: 10,
  cursor: "pointer",
};
/** 自定义路线样式 */
const polylineTemplateStyle = { ..._polylineTemplateStyle, strokeColor: "#F9852E" };
//用来判断自定义是否是结构化点或者结构化路线
const isStructur = ref(false);
const { socket } = useMainStore().$state;
const carInfo = computed(
  () => useMainStore().$state.socket.allStatus?.find((v) => v.deviceId === props.id) ?? ({} as _AllStatusType)
);

const tab = reactive({
  /** 1：一键清扫 2：模板任务 3：自定义 */
  active: 2,
  list: [
    // { label: "一键清扫", value: 1 },
    { label: "模板任务", value: 2 },
    { label: "自定义", value: 3 },
  ],
});
/** 清扫类型 */
const cleanType = reactive({
  index: "complete",
  options: [
    { label: "覆盖清扫", value: "complete" },
    { label: "巡检清扫", value: "cruising" },
    { label: "贴边清扫", value: "border" },
  ],
});
/** 贴边类型 */
const sweepDirection = reactive({
  index: 0,
  options: [
    {
      value: 0,
      label: "靠右行驶",
    },
    {
      value: 1,
      label: "靠左行驶",
    },
    {
      value: 2,
      label: "居中",
    },
  ],
});
/** 洒水 */
const water = reactive({
  index: 2,
  options: [
    { label: "开启", value: 2 },
    { label: "关闭", value: 1 },
  ],
});
/** 速度设置 */
const speed = reactive({
  active: 2.0,
  options: [
    { value: 2.0, label: "低速" },
    { value: 3.0, label: "中速" },
    { value: 4.0, label: "中高速" },
    { value: 5.0, label: "高速" },
  ],
});
/** 清扫次数 */
const cleanTimes = reactive({
  index: 1,
  options: [
    { label: "1次", value: 1 },
    { label: "2次", value: 2 },
    { label: "3次", value: 3 },
  ],
});
const space = reactive({
  index: -1,
  options: [
    { icon: "map_park", label: "停车点", value: 4, relatedType: [4], isShow: false },
    { icon: "map_water", label: "加水点", value: 3, relatedType: [3], isShow: false },
    { icon: "map_garbage", label: "垃圾点", value: 1, relatedType: [1], isShow: false },
    { icon: "map_battery", label: "充电点", value: 2, relatedType: [2], isShow: false },
    { icon: "map_supply", label: "补给点", value: 5, relatedType: [5], isShow: false },
  ],
});

const isShowWelt = computed(() => {
  if (tab.active === 2) {
    const item = templateOptions.value.find((item) => item.value === selectedTemplate.value?.templateId);
    return !!(item && [5, 6].includes(item.version ?? -1));
  } else {
    const isPoint = selectedList.value.some((item) => ["structurePoints", "route"].includes(item.overlayType));
    console.log(isStructur.value, "isStructur.value ");
    console.log(isPoint, "isPoint ");

    return isStructur.value || isPoint;
  }
});

/** 默认隐藏的覆盖物 */
const defaultHideOverlays = ["block", "csvRoute", "structurePoints"];
const showSpaceOptions = computed(() => space.options.filter((item) => item.isShow));
const mapColorTips = ref([
  { color: "#********", text: "障碍物", value: "", visible: true, shape: "circle" },
  { color: "#27D4A166", text: "可运行区域", value: "", visible: true, shape: "circle" },
  { color: "#F9852E", text: "车道线", value: "", visible: true, shape: "polyline" },
  { color: "#9F9FA4B3", text: "自定义区域", value: "block", visible: false, shape: "circle" },
  { color: "#242859", text: "自定义路线", value: "csvRoute", visible: false, shape: "polyline" },
  { color: "#5964FB", text: "自定义点位", value: "structurePoints", visible: false, shape: "point" },
]);
/** 地图颜色提示项 */
const handleMapColorTipsItem = debounce((item: typeof mapColorTips.value[number]) => {
  if (!item.value) return;
  item.visible = !item.visible;
  mapInfo.overlays.concat(mapInfo.overlayNames).forEach((overlay) => {
    const overlayData = overlay.getExtData() as OverlayItemData;
    if (overlayData.overlayType === item.value && !overlayData.overlayHighlight) {
      item.visible ? overlay.show() : overlay.hide();
    }
  });
}, 300);

const taskDistributionRef = ref();
const templateContentRef = ref();
const selectedTemplate = ref<TemplateType>();
const templateOptions = ref<TemplateType[]>([]);
/** 请求获取的数据集合（site&block&route） */
const mapOverlays = ref<OverlayItemData[]>([]);
const selectedList = ref<OverlayItemData[]>([]);
/** 新增区块时的编辑状态 */
const editorState = reactive({ add: false, edit: false });
const tempCustomId = ref<number>(0);

/** 是否展示内容 */
const showOptionContent = computed(
  // () =>
  //   (tab.active === 3 &&
  //     selectedList.value.length &&
  //     selectedList.value.some((item) => ["route", "block", "csvRoute", "customPolygon"].includes(item.overlayType))) ||
  //   tab.active === 2 ||
  //   tab.active === 1
  () => true
);
/** 保存按钮的禁用状态 */
const confirmDisibled = computed(() => {
  if (tab.active === 3) {
    const disabled = selectedList.value.length
      ? selectedList.value[0].overlayType === "park"
        ? false
        : water.index === -1 || cleanTimes.index === -1 // || (!selectedList.value.find((item) => ["route", "csvRoute"].includes(item.overlayType)) && cleanType.index === "") // 非路线相关覆盖物的时候，清扫类型不能为空
      : true;
    return disabled;
  } else {
    const disabled = water.index === -1 || cleanTimes.index === -1;
    if (tab.active === 2) {
      // return space.index === -1 && !selectedTemplate.value?.templateId;
      return !selectedTemplate.value?.templateId;
    } else {
      return disabled;
    }
  }
});

const handleWater = () => {
  water.index = water.index === 2 ? 1 : 2;
};
const startDrag = (e: MouseEvent | TouchEvent) => {
  isDragging.value = true;
  if (e.type === "mousedown") {
    startY = (e as MouseEvent).clientY;
    window.addEventListener("mousemove", onDrag);
    window.addEventListener("mouseup", endDrag);
  } else {
    startY = (e as TouchEvent).touches[0].clientY;
    window.addEventListener("touchmove", onDrag);
    window.addEventListener("touchend", endDrag);
  }
  initialValue = value.value;
};

const onDrag = (e: MouseEvent | TouchEvent) => {
  if (!isDragging.value) return;
  let currentY = e.type.includes("mouse") ? (e as MouseEvent).clientY : (e as TouchEvent).touches[0].clientY;
  const moveDistance = startY - currentY;
  const sensitivity = 0.02;
  let newValue = initialValue + moveDistance * sensitivity;
  newValue = Math.max(1.0, Math.min(5.0, Math.round(newValue * 10) / 10));
  value.value = newValue;
  fillHeight.value = ((newValue - 1.0) / 4.0) * 100;
  speed.active = newValue;
};

const endDrag = () => {
  isDragging.value = false;
  window.removeEventListener("mousemove", onDrag);
  window.removeEventListener("mouseup", endDrag);
  window.removeEventListener("touchmove", onDrag);
  window.removeEventListener("touchend", endDrag);
};

/** tab的选择事件 */
const tabChange = () => {
  isStructur.value = false;
  resetHightlight();
  selectedTemplate.value = undefined;
  cleanType.index = "complete";
  water.index = 2;
  cleanTimes.index = 1;
  space.index = -1;
  clearSelectedList();
};

/** 模版任务的选择事件 */
const templateSelectChange = (val: string) => {
  resetHightlight();
  const template = templateOptions.value.find((item) => item.value === val);
  console.log("template", {
    template,
    overlays: mapInfo.overlays.filter(
      (v) =>
        template?.routeList?.some((item) => item.routeId === v.getExtData().overlayId) ||
        template?.blockList?.some((item) => item.blockId === v.getExtData().overlayId) ||
        template?.csvRouteList?.some((item) => item.recordingId === v.getExtData().overlayId) ||
        template?.structurePointList?.some((item) => item.pointId === v.getExtData().overlayId)
    ),
  });
  selectedTemplate.value = template;
  if (template) {
    mapInfo.overlayNames.forEach((overlay) => {
      defaultHideOverlays.includes(overlay.getExtData().overlayType) && overlay.hide();
    });
    mapInfo.overlays.forEach((overlay) => {
      const overlayData: OverlayItemData = overlay.getExtData();
      if (defaultHideOverlays.includes(overlayData.overlayType)) overlay.hide();
      if (template.routeList && template.routeList.length) {
        template.routeList.forEach((route) => {
          if (route.routeId === overlayData.overlayId && overlayData.overlayType === "route") {
            overlay.show();
            overlay.setOptions({
              strokeColor: "#5964fb",
              strokeOpacity: 0.6,
              zIndex: 15,
            });
            overlay.setExtData({
              ...overlayData,
              overlayHighlight: true,
            } as OverlayItemData);

            const overlayName = mapInfo.overlayNames.find(
              (overlay) =>
                overlay.getExtData().overlayId === overlayData.overlayId && overlay.getExtData().overlayType === "route"
            );
            overlayName?.show();
            overlayName?.setExtData({
              ...overlayName.getExtData(),
              overlayHighlight: true,
            });
          }
        });
      }
      if (template.blockList && template.blockList.length) {
        template.blockList.forEach((block) => {
          if (block.blockId === overlayData.overlayId && overlayData.overlayType === "block") {
            overlay.show();
            overlay.setOptions({
              fillOpacity: 0.6,
              fillColor: "#5964fb",
              zIndex: 11,
            });
            overlay.setExtData({
              ...overlayData,
              overlayHighlight: true,
            } as OverlayItemData);

            const overlayName = mapInfo.overlayNames.find(
              (overlay) =>
                overlay.getExtData().overlayId === overlayData.overlayId && overlay.getExtData().overlayType === "block"
            );
            overlayName?.show();
            overlayName?.setExtData({
              ...overlayName.getExtData(),
              overlayHighlight: true,
            });
          }
        });
      }
      if (template.csvRouteList && template.csvRouteList.length) {
        template.csvRouteList.forEach((csvRoute) => {
          if (csvRoute.recordingId === overlayData.overlayId && overlayData.overlayType === "csvRoute") {
            overlay.show();
            overlay.setOptions({
              strokeColor: "#5964fb",
              strokeOpacity: 0.6,
              zIndex: 15,
            });
            overlay.setExtData({
              ...overlayData,
              overlayHighlight: true,
            } as OverlayItemData);

            const overlayName = mapInfo.overlayNames.find(
              (overlay) =>
                overlay.getExtData().overlayId === overlayData.overlayId &&
                overlay.getExtData().overlayType === "csvRoute"
            );
            overlayName?.show();
            overlayName?.setExtData({
              ...overlayName.getExtData(),
              overlayHighlight: true,
            });
          }
        });
      }

      if (template.structurePointList && template.structurePointList.length) {
        template.structurePointList.forEach((point) => {
          if (point.pointId === overlayData.overlayId && overlayData.overlayType === "structurePoints") {
            overlay.show();
            const markerDiv = overlayData.markerDiv;
            render(
              createVNode(SiteMarker, {
                focus: true,
                icon: "point-marker",
                number: 0,
                index: point.pointName,
                indexColor: "#5964FB",
              }),
              markerDiv
            );
            overlay.setExtData({
              ...overlayData,
              overlayHighlight: true,
            });
          }
        });
      }
    });
  }
};

/** 获取模版列表 */
const getTemplateOptions = async () => {
  const templateList = await getRouteTemplate({ vehNo: props.id });
  templateOptions.value = templateList.map(
    (temp) =>
      ({
        templateName: temp.templateName,
        templateId: temp.id,
        label: temp.templateName + getTemplateVersionName(temp.version),
        value: temp.id,
        version: temp.version,
        routeList:
          temp.routeTemplateRelEntities?.map(
            (route) =>
              ({
                vehRouteNo: route.vehRouteNo,
                routeName: route.routeName,
                routeId: route.routeId,
                taskType: route.cleanType,
              } as TemplateType["routeList"][0])
          ) || [],
        blockList:
          temp.sysAreaTemplateBlockRelEntities?.map(
            (block) =>
              ({
                blockId: block.blockId,
                blockName: block.blockName,
              } as TemplateType["blockList"][0])
          ) || [],
        csvRouteList:
          temp.sysAreaTemplateRecordingRouteEntities?.map(
            (csvRoute) =>
              ({
                recordingId: csvRoute.recordingRouteEntity?.id,
                routeName: csvRoute.recordingRouteEntity?.routeName,
              } as TemplateType["csvRouteList"][0])
          ) || [],
        structurePointList:
          temp.sysRouteTemplatePointsEntities?.map(
            (point: any) =>
              ({
                pointName: point.orders,
                pointId: point.id,
                latitude: point.latitude,
                longitude: point.longitude,
              } as TemplateType["structurePointList"][0])
          ) || [],
      } as TemplateType)
  );

  await renderAllPointMarkers();
};

/** 清空自定义列表 */
const clearSelectedList = () => {
  isStructur.value = false;
  editorState.add = false;
  resetHightlight();
  // 删除自定义绘制的区块
  for (let i = mapInfo.overlays.length - 1; i >= 0; i--) {
    const overlay = mapInfo.overlays[i];
    const overlayData = overlay.getExtData();
    overlay.setExtData({
      ...overlayData,
      overlayHighlight: false,
    });
    if (overlayData.overlayType === "customPolygon") {
      mapInfo.map.remove(overlay);
      mapInfo.overlays.splice(i, 1);
    }
  }
  // 删除自定义绘制生成的区块名称
  for (let i = mapInfo.overlayNames.length - 1; i >= 0; i--) {
    const overlay = mapInfo.overlayNames[i];
    const overlayData = overlay.getExtData();
    if (overlayData.overlayType === "customPolygon") {
      mapInfo.map.remove(overlay);
      mapInfo.overlayNames.splice(i, 1);
    }
  }

  // 删除自定义绘制的点位
  for (let i = mapInfo.drawPointMarkers.length - 1; i >= 0; i--) {
    const overlay = mapInfo.drawPointMarkers[i];
    const overlayData = overlay.getExtData();
    overlay.setExtData({
      ...overlayData,
      overlayHighlight: false,
    });
    if (overlayData.overlayType === "structurePoints") {
      mapInfo.map.remove(overlay);
      mapInfo.drawPointMarkers.splice(i, 1);
    }
  }
  tempCustomId.value = 0;
  selectedList.value = [];
  clearPolygonEditor();
  pointNameSeed.value = 1;

  if (isdrawPoint.value) {
    isdrawPoint.value = false;
    if (mapInfo.map && mapClickHandler) {
      mapInfo.map.off("click", mapClickHandler);

      mapClickHandler = null;
    }
  }
};

/** 自定义列表移出事件 */
const delOverlay = (index: number) => {
  const overlay = mapInfo.overlays.find((item) => item.getExtData().overlayId === selectedList.value[index].overlayId);
  if (overlay) {
    const overlayData: OverlayItemData = overlay.getExtData();
    overlay.setExtData({
      ...overlayData,
      overlayHighlight: false,
    });
    if (overlayData.overlayType === "block") {
      overlay.setOptions({ ...defaultPolygonOptions });
    } else if (overlayData.overlayType === "route") {
      overlay.setOptions({ strokeColor: "#F9852E", zIndex: 14 });
    } else if (overlayData.overlayType === "park") {
      overlay.setOptions({
        ...defaultPolygonOptions,
        strokeWeight: 1,
        strokeColor: "#5964FB",
        strokeOpacity: 0.8,
        zIndex: 12,
      });
    } else if (overlayData.overlayType === "csvRoute") {
      overlay.setOptions({ ...polylineStyle, zIndex: 14 });
    }
  }

  const pointOverlay = mapInfo.drawPointMarkers.find(
    (item) => item.getExtData().overlayId === selectedList.value[index].overlayId
  );
  const pointOverlayIndex = mapInfo.drawPointMarkers.findIndex(
    (item) => item.getExtData().overlayId === selectedList.value[index].overlayId
  );
  if (pointOverlay?.getExtData().overlayType === "structurePoints") {
    mapInfo.map.remove(pointOverlay);
    mapInfo.drawPointMarkers.splice(pointOverlayIndex, 1);
  }
  selectedList.value.splice(index, 1);
};
/** 自定义列表的点击事件,表示列表可点击 */
const handleItem = (row: OverlayItemData) => {
  resetEditorState();
  if (row.overlayType === "customPolygon") {
    mapInfo.polygonEditor.setTarget(row.overlayInstance);
    mapInfo.polygonEditor.open();
    mapInfo.editingPolygon = row;
    editorState.edit = true;
  } else {
    clearPolygonEditor();
  }
};

const handleCancel = () => {
  updateVisible(false);
};

/** 任务下发 */
const handleConfirm = () => {
  submitLoading.value = true;
  if (carInfo.value.online) {
    if (carInfo.value.dustbinLiftPoseStatus || carInfo.value.dustbinOverturnPoseStatus) {
      return Message("error", "垃圾箱未关闭，任务无法执行");
    }
    if (carInfo.value.manufacturer !== "falcon" && [2, 3].includes(carInfo.value.selfCheckResult ?? -1)) {
      return carInfo.value.selfCheckResult === 2
        ? Message("error", "车辆自检失败")
        : Message("error", "车辆正在进行自检");
    }
    // if ([7, 8, 9, 10].includes(carInfo.value.currentTaskType ?? -1)) {
    //   const errorMsg = taskTypeMap.find((item) => item.value === carInfo.value.currentTaskType)?.tip;
    //   return Message("error", `${errorMsg}，任务暂无法下发`);
    // } else if (carInfo.value.currentTaskType === 6) {
    //   return Message("error", "车辆任务规划中，任务暂无法下发");
    // }
    let taskParams = {} as Parameters<typeof task2>[0];

    if (tab.active === 1) {
      taskParams = {
        deviceId: props.id,
        executeTimes: cleanTimes.index === -1 ? undefined : cleanTimes.index,
        sweepMode: water.index,
        cleanType: cleanType.index || undefined,
        stationType: space.index === -1 ? undefined : space.index,
        speed: Number(speed.active),
      };
    } else if (tab.active === 2) {
      taskParams = {
        deviceId: props.id,
        executeTimes: cleanTimes.index === -1 ? undefined : cleanTimes.index,
        sweepMode: water.index,
        cleanType: selectedTemplate.value?.version !== 3 ? cleanType.index : undefined,
        stationType: space.index === -1 ? undefined : space.index,
        tempId: selectedTemplate.value?.templateId,
        speed: Number(speed.active),
        sweepDirection: sweepDirection.index,
      };
    } else if (tab.active === 3) {
      taskParams = {
        deviceId: props.id,
        executeTimes: 1,
        sweepMode: water.index === -1 ? undefined : water.index,
        cleanType: cleanType.index || undefined,
        speed: Number(speed.active),
      };
      if (carInfo.value.vehicleVersion && carInfo.value.vehicleSoftVersion === 2) {
        // 2.0 车辆任务下发
        const statoionData = selectedList.value.find((item) => item.overlayType === "park");
        if (statoionData) {
          taskParams.stationType = statoionData.sourceData?.site?.stationType;
          taskParams.stationId = statoionData.sourceData?.site?.id;
        }

        const csvRouteList = selectedList.value.filter(
          (item) => item.overlayType === "csvRoute" && item.sourceData?.routePath
        );
        if (csvRouteList.length) {
          taskParams.routePathList = csvRouteList.map((item) => item.sourceData?.routePath);
        }
        taskParams.points = selectedList.value
          .filter((item) => !["park", "csvRoute"].includes(item.overlayType))
          .map((item) =>
            item.points.map(
              (point) =>
                ({
                  x: point.x || point.longitude,
                  y: point.y || point.latitude,
                  pointType: Boolean(!point.x),
                  pointGroupType: Boolean(item.overlayType === "route"),
                } as Required<typeof taskParams>["points"][0][0])
            )
          );
      } else if (carInfo.value.vehicleVersion && carInfo.value.vehicleSoftVersion === 3) {
        const structurePointsIndex =
          selectedList.value.findIndex((item) => item.overlayType === "structurePoints") !== -1;

        // 3.0 车辆任务下发
        taskParams.customCompleteSingleTaskList = selectedList.value.map((item) => {
          if (["customPolygon", "route", "block", "customPolyline"].includes(item.overlayType)) {
            return {
              points: item.points.map((point) => {
                return {
                  x: point.x || point.longitude,
                  y: point.y || point.latitude,
                  heading: 0,
                  pointType: Boolean(!point.x),
                  pointGroupType: Boolean(["route", "customPolyline"].includes(item.overlayType)),
                } as Required<Required<typeof taskParams>["customCompleteSingleTaskList"][0]>["points"][0];
              }),
            } as Required<typeof taskParams>["customCompleteSingleTaskList"][0];
          } else if (["park", "site"].includes(item.overlayType)) {
            return {
              stationId: item.sourceData?.site?.id,
              parkId: item.sourceData?.park?.parkNo,
              stationType: item.sourceData?.site?.stationType,
              pathPoints: false,
            };
          } else if (item.overlayType === "csvRoute") {
            return {
              routePath: item.sourceData?.routePath,
              routeClean: item.cleanStatus,
            };
          } else {
            return { unknown: item.overlayType };
          }
        });

        //结构化点位处理
        if (structurePointsIndex) {
          const convertPoint = (point: any, needXY = true) => {
            const base: any = {
              x: point.x || point.longitude,
              y: point.y || point.latitude,
              z: 0,
              heading: 0,
              pointType: Boolean(!point.x),
              pointGroupType: ["structurePoints"].includes(point.overlayType),
              pathPoints: ["structurePoints"].includes(point.overlayType),
            };
            if (base.x && base.y) {
              // 只在有 x/y/longitude/latitude 时赋值
              base.x = point.x || point.longitude;
              base.y = point.y || point.latitude;
            }
            return base as Required<Required<typeof taskParams>["customCompleteSingleTaskList"][0]>["points"][0];
          };
          const parkIndex = selectedList.value.findIndex((item) => item.overlayType === "park");
          const beforePark = parkIndex > 0 ? selectedList.value.slice(0, parkIndex) : [];
          const parkItem = parkIndex > -1 ? [selectedList.value[parkIndex]] : [];
          const afterPark =
            parkIndex > -1 && parkIndex < selectedList.value.length - 1 ? selectedList.value.slice(parkIndex + 1) : [];
          if (parkIndex > -1) {
            const arr1 = beforePark.map((point) => convertPoint(point, true));
            // const arr2 = parkItem.map((point) => convertPoint(point, false));
            const arr3 = afterPark.map((point) => convertPoint(point, true));
            const customList = [];
            if (arr1.length) customList.push({ points: arr1, pathPoints: true });
            if (parkItem.length)
              customList.push({
                stationId: parkItem[0].sourceData?.site?.id,
                parkId: parkItem[0].sourceData?.park?.parkNo,
                stationType: parkItem[0].sourceData?.site?.stationType,
                pathPoints: false,
              });
            if (arr3.length) customList.push({ points: arr3, pathPoints: true });
            taskParams.customCompleteSingleTaskList = customList;
          } else {
            const allPoints = selectedList.value.flatMap((item) =>
              item.points.map((point) => ({
                x: point.longitude,
                y: point.latitude,
                z: 0,
                heading: 0,
                pointType: Boolean(!point.x),
                pointGroupType: ["structurePoints"].includes(item.overlayType),
              }))
            );
            taskParams.customCompleteSingleTaskList = [{ points: allPoints, pathPoints: true }];
          }
        }
      } else {
        return Message("error", "车辆软件版本过低，暂不支持下发任务");
      }
    }
    task2(taskParams)
      .then((result) => {
        Message("success", "任务下发成功");
        updateVisible(false);
      })
      .catch((error) => {
        Message("error", `任务下发失败: ${error.message || error}`);
      })
      .finally(() => {
        submitLoading.value = false;
      });
  } else {
    Message("error", "车辆已下线，任务无法下发！");
    submitLoading.value = false;
  }
};

const resetAll = () => {
  tab.active = 2;
  selectedTemplate.value = undefined;
  cleanType.index = "complete";
  water.index = 2;
  cleanTimes.index = 1;
  space.index = -1;
};

/** 地图 */
const defaultMapInfo = {
  map: null as unknown as AMap.Map,
  Amap: null as unknown as typeof AMap,
  /** 车辆marker */
  carMarker: null as unknown as AMap.Marker,
  /** 覆盖物(场所&路线&区块&自定义区块) */
  overlays: [] as any[],
  /** 覆盖物名称(场所&路线&区块&自定义区块) */
  overlayNames: [] as any[],
  /** 高精地图范围 */
  areaRanges: [] as any[],
  /** 多边形编辑器 */
  polygonEditor: null as unknown as AMap.PolygonEditor,
  /** 正在编辑的多边形 */
  editingPolygon: null as OverlayItemData | null,
  /** 自定义绘制的多边形 */
  customPolygons: [] as any[],
  pointMarkers: [] as any[], // 渲染的点位
  drawPointMarkers: [] as AMap.Marker[], // 手动绘制的点位
};
let mapInfo: typeof defaultMapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
const mapRef = ref();
const initMap = async () => {
  lazyAMapApiLoader().then((AMap) => {
    mapInfo.map = new AMap.Map(mapRef.value, {
      zooms: [2, 26],
      zoom: 16,
      mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
    });
    mapInfo.Amap = AMap;

    mapInfo.polygonEditor = new AMap.PolygonEditor(mapInfo.map, undefined, {
      createOptions: {
        fillColor: "#27D4A1",
        fillOpacity: 0.6,
        strokeWeight: 0,
        bubble: true,
      },
      editOptions: polygonEditorStyle.editOptions,
    });
    initPolygonEditor();
    const scale = new AMap.Scale({
      position: {
        left: "10px",
        bottom: "50px",
      },
    });
    mapInfo.map.addControl(scale);
  });
};

/** 绘制车辆 */
const drawCarMarker = (data: _BaseStatusType) => {
  if (mapInfo.carMarker) {
    const { markerDiv } = mapInfo.carMarker.getExtData();
    mapInfo.carMarker.setPosition([data.longitude, data.latitude]);
    renderCarMarker(data, markerDiv);
  } else {
    const markerDiv = document.createElement("div");
    const carMarker = new mapInfo.Amap.Marker({
      content: markerDiv,
      position: [data.longitude, data.latitude],
      anchor: "center",
    });
    carMarker.setExtData({
      ...data,
      markerDiv,
      instance: carMarker,
    });
    renderCarMarker(data, markerDiv);
    mapInfo.map.add(carMarker);
    mapInfo.carMarker = carMarker;
  }
};
const renderCarMarker = (data: _BaseStatusType, markerDiv: HTMLDivElement) => {
  render(
    createVNode(Marker, {
      status: {
        online: data.online,
        warn: false,
        deviceId: data.deviceId,
      },
      focus: false,
      direction: data.direction,
      disabledClick: true,
    }),
    markerDiv
  );
};

/** 创建覆盖物名称 */
const createOverlayName = (overlay: any, name?: string) => {
  const overlayData: OverlayItemData = overlay.getExtData();
  const position =
    overlay.className === "Overlay.Polygon"
      ? overlay.getBounds().getCenter()
      : [overlayData.points[0].longitude, overlayData.points[0].latitude];
  const markerDiv = document.createElement("div");
  const _marker = new mapInfo.Amap.Marker({
    ...polylineHoverMarkerStyle,
    content: markerDiv,
    position: position,
    clickable: true,
    cursor: "pointer",
    visible: true,
    extData: overlayData,
  });
  render(
    createVNode(PolylineNameMarker, {
      name: name ?? "",
      type: "light",
    }),
    markerDiv
  );
  _marker.on("mouseover", () => overlayMouseover({ target: overlay }));
  _marker.on("mouseout", () => overlayMouseout({ target: overlay }));
  _marker.on("click", () => overlayClick({ target: overlay }));
  return _marker;
};
/** 绘制场所|车位 */
const drawSiteAndParkOverlay = async () => {
  let polygons: any[] = [];
  const markers = mapOverlays.value
    .filter((item) => ["park"].includes(item.overlayType))
    .map((item) => {
      /** 停车位的标志marker */
      let marker: any;
      let position = [item.sourceData?.site?.longitude, item.sourceData?.site?.latitude] as any;
      const path = item.points.map(({ longitude, latitude }: any) => [longitude, latitude]);

      // 车位无坐标时仅显示场所
      if (path.length) {
        /** 停车位的多边形区域 */
        const polygon = new mapInfo.Amap.Polygon({
          ...defaultPolygonOptions,
          path,
          strokeWeight: 1,
          strokeColor: "#5964FB",
          strokeOpacity: 0.8,
          extData: item,
          zIndex: 12,
        } as AMap.PolygonOptions);
        polygon.on("click", overlayClick); // BUG:不知何原因，此处无法触发事件，而且连鼠标样式(cursor)都无法生效
        polygons.push(polygon);

        position = polygon.getBounds()?.getCenter();
      }

      const markerDiv = document.createElement("div");
      marker = new mapInfo.Amap.Marker({
        content: markerDiv,
        position: position,
        title: item.overlayName,
        extData: item,
        zIndex: 30,
        cursor: "pointer",
        bubble: true,
        anchor: "bottom-center",
      });
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: sitesType.find((site) => site.value === item.sourceData?.site?.stationType || "")?.icon,
        }),
        markerDiv
      );
      marker.on("click", overlayClick);

      return marker;
    });
  mapInfo.overlays.push(...polygons, ...markers);
  mapInfo.map.add([...polygons, ...markers]);
};

/** 渲染所有结构化点位标记 */
const renderAllPointMarkers = () => {
  const list = templateOptions.value.find((item) => item.version === 6)?.structurePointList ?? [];
  // 清除之前的标记
  // mapInfo.pointMarkers.forEach((marker) => marker.setMap(null));
  // mapInfo.pointMarkers = [];
  // 只渲染当前编辑模板的点
  list.forEach((item, index) => {
    if (item.longitude && item.latitude) {
      const markerDiv = document.createElement("div");
      const marker = new mapInfo.Amap.Marker({
        position: [item.longitude, item.latitude],
        content: markerDiv,
        offset: new mapInfo.Amap.Pixel(-10, -10),
        zIndex: 100,
        extData: {
          markerDiv,
          overlayId: item.pointId,
          overlayName: item.pointName,
          overlayHighlight: false,
          overlayType: "structurePoints",
          sourceData: item,
          points: [],
        } as OverlayItemData,
      });
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: "point-marker-black",
          number: 0,
          index: item.pointName,
          indexColor: "#383838",
        }),
        markerDiv
      );
      defaultHideOverlays.includes("structurePoints") && marker.hide();
      marker.setMap(mapInfo.map);
      mapInfo.overlays.push(marker);
    }
  });
};
/** 绘制路线|区块|录制路线 */
const drawRouteAndBlockOverlay = () => {
  const overlays = mapOverlays.value
    .filter((item) => ["block", "route", "csvRoute"].includes(item.overlayType))
    .map((item) => {
      let overlay: any;
      const path = item.points.map(({ longitude, latitude }: any) => [longitude, latitude]);
      if (item.overlayType === "block") {
        // 区块
        overlay = new mapInfo.Amap.Polygon({
          ...defaultPolygonOptions,
          path,
          extData: item,
        } as AMap.PolygonOptions);
        const overlayName = createOverlayName(overlay, item.overlayName);
        mapInfo.overlayNames.push(overlayName);
      } else if (item.overlayType === "route") {
        // 结构化路线
        overlay = new mapInfo.Amap.Polyline({
          ...polylineTemplateStyle,
          path,
          zIndex: 14,
          cursor: "pointer",
          extData: item,
        });
        const overlayName = createOverlayName(overlay, item.overlayName);
        mapInfo.overlayNames.push(overlayName);
      } else if (item.overlayType === "site") {
        // 场所
        const markerDiv = document.createElement("div");
        overlay = new mapInfo.Amap.Marker({
          content: markerDiv,
          position: [item.points[0].longitude, item.points[0].latitude] as any,
          title: item.overlayName,
          zIndex: 30,
          cursor: "pointer",
          draggable: false,
          bubble: true,
          extData: item,
          anchor: "bottom-center",
        });
        render(
          createVNode(SiteMarker, {
            focus: false,
            icon: sitesType.find((site) => site.value === item.sourceData?.stationType || "")?.icon,
          }),
          markerDiv
        );
      } else if (item.overlayType === "csvRoute") {
        // 录制路线
        overlay = new mapInfo.Amap.Polyline({
          ...polylineStyle,
          path,
          zIndex: 14,
          cursor: "pointer",
          extData: item,
        });
        const overlayName = createOverlayName(overlay, item.overlayName);
        mapInfo.overlayNames.push(overlayName);
      }
      defaultHideOverlays.includes(item.overlayType) && overlay.hide();
      overlay.on("click", overlayClick);
      overlay.on("mouseover", overlayMouseover);
      overlay.on("mouseout", overlayMouseout);
      return overlay;
    });

  mapInfo.overlays.push(...overlays);
  mapInfo.overlayNames.forEach(
    (overlay) => defaultHideOverlays.includes(overlay.getExtData().overlayType) && overlay.hide()
  );
  mapInfo.map.add([...mapInfo.overlays, ...mapInfo.overlayNames]);
};
/** 覆盖物的点击事件 */
const overlayClick = (e: any) => {
  if (Object.values(editorState).some(Boolean)) return;
  if (tab.active === 3) {
    if (isdrawPoint.value) return Message("error", "请先完成点位设置");
    const overlay = e.target;
    const overlayData: OverlayItemData = overlay.getExtData();

    // 列表里场所不超过1个
    if ((["park"].includes(overlayData.overlayType), selectedList.value.some((item) => item.overlayType === "park"))) {
      return Message("error", "只能添加一个场所");
    } else if (
      carInfo.value.vehicleVersion &&
      [2, 3].includes(carInfo.value.vehicleSoftVersion)
      // 当前选择的是route，但已选列表中包含了csvRoute或block
      // && ((["route"].includes(overlayData.overlayType) &&
      //   selectedList.value.some((item) => ["csvRoute", "block", "structurePoints"].includes(item.overlayType))) ||
      //   // 当前选择的是csvRoute，但已选列表中包含了route
      //   (["csvRoute"].includes(overlayData.overlayType) &&
      //     selectedList.value.some((item) => ["route", "structurePoints"].includes(item.overlayType))) ||
      //   // 当前选择的是block，但已选列表中包含了route, structurePoints
      //   (["block"].includes(overlayData.overlayType) &&
      //     selectedList.value.some((item) => ["route", "structurePoints"].includes(item.overlayType))))
    ) {
      // 根据不同情况显示不同的错误提示
      if (["route"].includes(overlayData.overlayType)) {
        if (selectedList.value.some((item) => ["csvRoute"].includes(item.overlayType))) {
          return Message("error", "选择普通路线后不能选择录制路线或区块");
        } else if (selectedList.value.some((item) => ["block"].includes(item.overlayType))) {
          return Message("error", "选择区块后不能选择普通路线");
        } else if (selectedList.value.some((item) => ["structurePoints"].includes(item.overlayType))) {
          return Message("error", "选择结构化点位后不能选择普通路线");
        }
      } else if (["block"].includes(overlayData.overlayType)) {
        if (selectedList.value.some((item) => ["route"].includes(item.overlayType))) {
          return Message("error", "选择路线后不能选择区块");
        } else if (selectedList.value.some((item) => ["structurePoints"].includes(item.overlayType))) {
          return Message("error", "选择结构化点位后不能选择区块");
        }
      } else if (["csvRoute"].includes(overlayData.overlayType)) {
        if (selectedList.value.some((item) => ["route"].includes(item.overlayType))) {
          return Message("error", "选择普通路线后不能选择录制路线");
        } else if (selectedList.value.some((item) => ["structurePoints"].includes(item.overlayType))) {
          return Message("error", "选择结构化点位后不能选择录制路线");
        }
      }
    } else if (["csvRoute"].includes(overlayData.overlayType) && !overlayData.sourceData?.routePath) {
      return Message("error", "请在【项目区域管理】将该录制路线信息补充完整");
    } else if (
      // 相同的区块不超过3次
      selectedList.value.filter(
        (item) => item.overlayId === overlayData.overlayId && item.overlayType === overlayData.overlayType
      ).length >= 3
    ) {
      return Message("error", "同区块添加数量不能超过3次");
    }
    isStructur.value = ["structurePoints", "route"].includes(overlayData.overlayType);
    // 高亮覆盖物并添加到自定义列表中
    overlay.setExtData({
      ...overlayData,
      overlayHighlight: true,
    } as OverlayItemData);
    if (overlayData.overlayType === "block") {
      overlay.setOptions({ fillColor: "#27D4A1", fillOpacity: 0.6, zIndex: 11 });
      selectedList.value.push(overlayData);
    } else if (["route", "csvRoute"].includes(overlayData.overlayType)) {
      overlay.setOptions({ strokeColor: "#27D4A1", zIndex: 15 });
      selectedList.value.push({ ...overlayData, cleanStatus: true });
    } else if (overlayData.overlayType === "park") {
      siteMarkerClick(overlay, overlayData);
    }
  }
};
/** 场所（site）的点击事件 */
const siteMarkerClick = (overlay: any, row: OverlayItemData) => {
  if (!row.sourceData) return;
  const { site, park } = row.sourceData;
  const siteIcon = sitesType.find((item) => item.value === site?.stationType)?.icon || "";
  const infoWindowDiv = document.createElement("div");
  const infoWindow = new mapInfo.Amap.InfoWindow({
    isCustom: true,
    content: infoWindowDiv,
    position: overlay.getBounds().getCenter(),
    offset: [0, -38],
  });
  render(
    createVNode(SiteMarkerInfoWindow, {
      name: `${site?.stationName ?? ""} - ${park?.parkName ?? ""}`,
      popupContainer: taskDistributionRef.value,
      window: infoWindow,
      type: siteIcon,
      id: site?.id,
      areaId: site?.proAreaId,
      stationPic: site?.stationPic,
      stationType: site?.stationType,
      stationList:
        site?.stationParkingRelEntityList?.map((station: any) => ({
          ...station,
          status: "idle",
          warn: false,
        })) || [],
      distributeTask2Click: () => {
        selectedList.value.push(row);
        // 高亮停车位
        const parkPolygon = mapInfo.overlays.find(
          (item) =>
            item.getExtData().overlayType === "park" &&
            item.getExtData().overlayId === row.overlayId &&
            item.getExtData().sourceData.site.id === row.sourceData?.site?.id
        );
        if (parkPolygon) {
          parkPolygon.setOptions({ fillColor: "#27D4A1", fillOpacity: 0.6, zIndex: 13 });
        }
      },
    }),
    infoWindowDiv
  );
  infoWindow.open(mapInfo.map, overlay.getBounds().getCenter());
  if (siteIcon === "map_battery") {
    const stationNoArr = site?.stationParkingRelEntityList?.map((v: any) => v.chargingStationNo) || [];
    openChargingStatus(stationNoArr);
  }
};

/** 覆盖物的移入事件 */
const overlayMouseover = (e: { target: any }) => {
  if (Object.values(editorState).some(Boolean)) return;
  const overlay = e.target;
  const overlayData: OverlayItemData = overlay.getExtData();

  if (["block"].includes(overlayData.overlayType)) {
    overlay.setOptions({ fillColor: "#27D4A1", fillOpacity: 0.6, zIndex: 11 });
  } else if (["route"].includes(overlayData.overlayType)) {
    overlay.setOptions({ strokeColor: "#27D4A1", strokeWeight: 3, zIndex: 15 });
    // mapInfo.overlayNames.find((item) => item.getExtData().overlayId === overlayData.overlayId)?.show(); // 名称悬浮显示逻辑
  } else if (["csvRoute"].includes(overlayData.overlayType)) {
    overlay.setOptions({ strokeColor: "#27D4A1", strokeWeight: 3, zIndex: 15 });
  }

  // mapInfo.overlayNames.forEach((item: any) => {
  //   const itemData: OverlayItemData = item.getExtData();
  //   if (
  //     (itemData.overlayType === "customPolygon" &&
  //       itemData.customId === overlayData.customId) ||
  //     (itemData.overlayType !== "customPolygon" &&
  //       itemData.overlayId === overlayData.overlayId)
  //   ) {
  //     item.show();
  //   } else {
  //     item.hide();
  //   }
  // });
};
/** 覆盖物移出事件 */
const overlayMouseout = (e: { target: any }) => {
  // mapInfo.overlayNames.forEach((item) => item.hide());
  const overlayData: OverlayItemData = e.target.getExtData();
  if (overlayData.overlayHighlight) return;

  if (["block"].includes(overlayData.overlayType)) {
    e.target.setOptions({ fillOpacity: 0.7, fillColor: "#9F9FA4", zindex: 10 });
  } else if (["route"].includes(overlayData.overlayType)) {
    e.target.setOptions({ ...polylineTemplateStyle, zIndex: 14 });
    // mapInfo.overlayNames.find((item) => item.getExtData().overlayId === overlayData.overlayId)?.hide(); // 名称悬浮显示逻辑
  } else if (["csvRoute"].includes(overlayData.overlayType)) {
    e.target.setOptions({ ...polylineStyle, zIndex: 14 });
  }
};

/** 清空 清扫区域 等覆盖物 */
const clearMapOverlay = () => {
  mapInfo.map.remove([...mapInfo.overlays, ...mapInfo.overlayNames]);
  mapInfo.overlays = [];
  mapInfo.overlayNames = [];
};

/** 重置高亮状态 */
const resetHightlight = () => {
  mapInfo.overlays.forEach((overlay) => {
    const overlayData: OverlayItemData = overlay.getExtData();
    if (overlayData.overlayType === "block") {
      overlay.setOptions({
        fillColor: "#9F9FA4",
        fillOpacity: 0.7,
        zIndex: 10,
      });
    }
    if (overlayData.overlayType === "route") {
      overlay.setOptions({
        strokeColor: "#F9852E",
        zIndex: 14,
      });
    }
    if (overlayData.overlayType === "csvRoute") {
      overlay.setOptions({ ...polylineStyle, zIndex: 14 });
    }
    if (overlayData.overlayType === "structurePoints") {
      const markerDiv = overlayData.markerDiv;
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: "point-marker-black",
          number: 0,
          index: overlayData.overlayName,
          indexColor: "#383838",
        }),
        markerDiv
      );
    }
  });
};

/** 获取地图数据 */
const getAreaOverlayData = async () => {
  await Promise.all([
    getVehStationList(props.id, { signal: abortController?.signal }),
    getVehRouteList(props.id, { signal: abortController?.signal }),
    getVehBlockList(props.id, { signal: abortController?.signal }),
    queryVehRecordingRoute({ id: props.id }, { signal: abortController?.signal }),
  ]).then(([siteList, routeList, blockList, csvRouteList]) => {
    const pointTypeList = siteList
      .map((item) => item.stationType)
      .filter((value, index, self) => self.indexOf(value) === index) as number[];
    space.options.forEach((item) => {
      item.isShow = pointTypeList.some((v) => item.relatedType.includes(v));
    });
    const sites = siteList.map(
      (site) =>
        ({
          overlayType: "site",
          overlayId: site.id,
          overlayName: site.stationName,
          points: [{ longitude: site.longitude, latitude: site.latitude }],
          sourceData: site,
        } as OverlayItemData)
    );
    const parks = siteList.flatMap(
      (site) =>
        site.stationParkingRelEntityList?.map(
          (park) =>
            ({
              overlayType: "park",
              overlayId: park.id,
              overlayName: park.parkName,
              points:
                park.location
                  .split(";")
                  .slice(0, -1)
                  .map((item) => item.split(","))
                  .map(([longitude, latitude]) => ({ longitude, latitude })) || [],
              sourceData: { site, park },
            } as OverlayItemData)
        ) || []
    );
    const routes = routeList.map(
      (route) =>
        ({
          overlayType: "route",
          overlayId: route.id,
          overlayName: route.routeName,
          points: route.gps,
          sourceData: route,
        } as OverlayItemData)
    );
    const blocks = blockList.map(
      (block) =>
        ({
          overlayType: "block",
          overlayId: block.id,
          overlayName: block.blockName,
          points: block.areaBlockGpsList,
          sourceData: block,
        } as OverlayItemData)
    );
    const csvRoutes = csvRouteList.map(
      (csvRoute) =>
        ({
          overlayType: "csvRoute",
          overlayId: csvRoute.id,
          overlayName: csvRoute.routeName,
          points: csvRoute.recordingGpsList,
          sourceData: csvRoute,
        } as OverlayItemData)
    );
    mapOverlays.value = routes.concat(blocks, routes, sites, parks, csvRoutes);
    drawRouteAndBlockOverlay();
    drawSiteAndParkOverlay();
  });
};
/** 获取高精地图信息 */
const getAreaRange = async () => {
  const res = await getProAreaRange({ vehNo: props.id });
  const grouped = res.reduce((accumulator, item) => {
    const key = `${item.xmlId}_${item.pointType}`;
    if (!accumulator[key]) {
      accumulator[key] = [];
    }
    accumulator[key].push(item);
    return accumulator;
  }, {} as any);
  const list: GetAreaRangeResponse[] = Object.values(grouped);

  drawAreaRange(
    list.map((item) => ({
      id: item[0].id,
      proAreaId: item[0].proAreaId,
      xmlId: item[0].xmlId,
      pointType: item[0].pointType,
      points: item.map(({ longitude, latitude }) => ({ longitude, latitude })),
    }))
  );
};
/** 渲染高精地图范围 */
const drawAreaRange = (list: AreaRangeType[]) => {
  const polygons = list.map((item) => {
    const polygon = new mapInfo.Amap.Polygon({
      path: item.points.map(({ longitude, latitude }: any) => [longitude, latitude]),
      strokeWeight: 0,
      fillOpacity: 0.4,
      fillColor: item.pointType === 1 ? "#F41515" : "#27D4A1",
      zIndex: 5,
      bubble: true,
      extData: { ...item, overlayType: "areaRange" },
    });
    polygon.on("click", toggleAreaRange);
    return polygon;
  });
  mapInfo.areaRanges = polygons;
  mapInfo.map.add(polygons);
  mapInfo.map.setFitView(polygons, true, [10, 50, 10, 50], 20);
};
/** 高精地图的区块点击 */
const toggleAreaRange = (e: any) => {
  if (Object.values(editorState).some(Boolean)) return;
  // 高精地图不可点击
  if (mapInfo.areaRanges.length) return;
  const overlay = e.target;
  const overlayData: AreaRangeType = overlay.getExtData();
  if (tab.active === 3 && overlayData.pointType === 0) {
    overlay.setOptions({
      fillColor: "#27D4A1",
      fillOpacity: 0.6,
    });
    selectedList.value.push({
      overlayId: overlayData.xmlId,
      overlayName: overlayData.xmlId,
      overlayType: "areaRange",
      points: overlayData.points,
    });
  }
};

/** 初始化多边形编辑器 */
const initPolygonEditor = () => {
  const polygonEditorHandler = (e: any) => {
    const polygon = e.target;
    const points = polygon.getPath().map(({ lng, lat }: any) => ({
      longitude: lng,
      latitude: lat,
    }));
    if (editorState.add && mapInfo.editingPolygon) {
      mapInfo.editingPolygon.points = points;
    }
    if (editorState.edit && mapInfo.editingPolygon) {
      const cur = selectedList.value.find((item) => item.customId === mapInfo.editingPolygon?.customId);
      cur && (cur.points = points);
    }
  };

  mapInfo.polygonEditor.on("addnode", polygonEditorHandler);
  mapInfo.polygonEditor.on("removenode", polygonEditorHandler);
  mapInfo.polygonEditor.on("adjust", polygonEditorHandler);
  mapInfo.polygonEditor.on("add", (e: any) => {
    if (mapInfo.editingPolygon) {
      const polygon = e.target;
      polygon.setExtData(mapInfo.editingPolygon);
      polygon.on("mouseover", overlayMouseover);
      polygon.on("mouseout", overlayMouseout);
      mapInfo.editingPolygon.overlayInstance = polygon;
    }
  });
};
/** 下方[区域绘制] 点击事件 */
const handleBlockDraw = () => {
  if (selectedList.value.some((item) => ["route"].includes(item.overlayType)))
    return Message("error", "选择普通路线后不能选择录制路线或区块");

  if (selectedList.value.some((item) => item.overlayType === "structurePoints"))
    return Message("error", "选择结构化点位后不能选择录制路线或区块");
  if (Object.values(editorState).some(Boolean)) return;
  resetEditorState();
  editorState.add = true;
  tempCustomId.value++;
  mapInfo.editingPolygon = {
    overlayName: `自定义区域${tempCustomId.value}`,
    customId: tempCustomId.value,
    overlayType: "customPolygon",
    points: [],
  };
  mapInfo.polygonEditor.open();
};

/** 下方[自定义区域] 取消按钮 */
const blockToolCancel = () => {
  editorState.add = false;
  const tempPloygon = mapInfo.polygonEditor.getTarget();
  if (tempPloygon) {
    const tempData: OverlayItemData = tempPloygon.getExtData();
    // 自定义列表中无相同自定义区块的时候，删除该自定义区块
    if (!selectedList.value.some((v) => v.customId === tempData.customId)) {
      mapInfo.map.remove(tempPloygon);
      tempCustomId.value--;
    }
  } else {
    tempCustomId.value--;
  }
  clearPolygonEditor();
};
/** 下方自定义区域 完成按钮 */
const blockToolFinish = () => {
  if (mapInfo.editingPolygon) {
    if (mapInfo.editingPolygon.points.length < 3) {
      Message("error", "自定义区域最少需要三个点");
      return false;
    }
    if (!mapInfo.editingPolygon.overlayInstance) {
      Message("error", "请点击鼠标右键完成绘制后再添加");
      return false;
    }
    selectedList.value.push(mapInfo.editingPolygon);
    mapInfo.overlays.push(mapInfo.editingPolygon.overlayInstance);
    const overlayName = createOverlayName(mapInfo.editingPolygon.overlayInstance, mapInfo.editingPolygon.overlayName);
    mapInfo.overlayNames.push(overlayName);

    mapInfo.map.add([mapInfo.editingPolygon.overlayInstance, overlayName]);

    editorState.add = false;
    clearPolygonEditor();
  }
};

/** 下方[点位绘制] 点击事件 */
const handlePointDraw = () => {
  if (editorState.add) return;
  if (selectedList.value.length > 0 && !["structurePoints", "park"].includes(selectedList.value[0].overlayType)) {
    return Message("error", "选择其他后不可选择结构化点位");
  }
  isdrawPoint.value = !isdrawPoint.value;
  if (isdrawPoint.value) {
    if (mapInfo.map && mapClickHandler) {
      mapInfo.map.off("click", mapClickHandler);
      mapClickHandler = null;
    }
    mapClickHandler = function (e: any) {
      const { lng, lat } = e.lnglat;
      const newPoint = {
        id: Date.now() + "",
        overlayName: String(pointNameSeed.value++),
        overlayType: "structurePoints",
        overlayId: Date.now() + "",
        points: [{ longitude: lng, latitude: lat }],
        longitude: lng,
        latitude: lat,
      } as OverlayItemData;
      selectedList.value.push(newPoint);
      renderPointMarkers();
    };

    mapInfo.map.on("click", mapClickHandler);
  } else {
    if (mapInfo.map && mapClickHandler) {
      mapInfo.map.off("click", mapClickHandler);
      mapClickHandler = null;
    }
  }
};

/** 渲染点位标记 */
const renderPointMarkers = () => {
  // 清除之前的标记
  mapInfo.drawPointMarkers.forEach((marker) => marker.setMap(null));
  mapInfo.drawPointMarkers = [];
  let list = selectedList.value;
  list.forEach((item) => {
    if (item.overlayType === "structurePoints") {
      const markerDiv = document.createElement("div");
      const marker = new mapInfo.Amap.Marker({
        position: [item.points[0].longitude, item.points[0].latitude] as any,
        content: markerDiv,
        offset: new mapInfo.Amap.Pixel(-10, -10),
        zIndex: 100,
        extData: {
          ...item,
          markerDiv,
          type: "point-marker",
          focus: false,
          pointName: item.overlayName,
        },
      });
      render(
        createVNode(SiteMarker, {
          focus: false,
          icon: "point-marker",
          number: 0,
          index: item.overlayName,
        }),
        markerDiv
      );
      marker.setMap(mapInfo.map);
      mapInfo.drawPointMarkers.push(marker);
    }
  });
};

/** 重置editorState状态 */
const resetEditorState = () => {
  Object.keys(editorState).forEach((key) => ((editorState as any)[key] = false));
};
/** 情况 polygonEditor 中的编辑对象 */
const clearPolygonEditor = () => {
  mapInfo.polygonEditor && mapInfo.polygonEditor.setTarget(undefined);
  mapInfo.polygonEditor && mapInfo.polygonEditor.close();
  mapInfo.editingPolygon = null;
};

/** 加载状态 */
const loading = ref<boolean>(false);
/** 请求中断器 */
let abortController: AbortController | undefined;
/** 组件初始化 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      abortController = new AbortController();
      loading.value = true;
      await initMap();
      resetAll();
      clearMapOverlay();
      Promise.all([getAreaRange(), getAreaOverlayData(), getTemplateOptions()]).then(() => {
        loading.value = false;
      });
    } else {
      abortController && abortController.abort();
      abortController = undefined;
      tabChange();
      mapInfo = JSON.parse(JSON.stringify(defaultMapInfo));
      isStructur.value = false;
    }
  }
);
watch(
  () => socket.baseStatus,
  (newV) => {
    if (props.show && newV && newV.length) {
      const target = newV.find((item) => item.deviceId === props.id);
      if (target) {
        drawCarMarker(target);
      }
    }
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.task-distribution {
  width: 100%;
  height: 100%;
  position: relative;
  user-select: none;
  &-map {
    width: 100%;
    height: 100%;
  }
  &-modal {
    position: absolute;
    top: 50%;
    right: 20px;
    transform: translateY(-50%);
    display: flex;
    flex-direction: column;
    @include wh(330px, 600px) {
      box-shadow: 0 0 10px 0 rgba(72, 111, 245, 0.14);
      padding-bottom: 18px;
      background-color: #fff;
      border-radius: 10px;
    }
    .modal-top {
      @include wh(100%, 88px) {
        border-radius: 10px 10px 0 0;
        background: linear-gradient(to bottom, rgba(90, 118, 252, 0.22), rgba(244, 247, 254, 0));
        // filter: blur(30px);
      }
      &-title {
        @include ct-f(y);
        @include wh(100%, 48px) {
          padding-left: 4px;
        }
      }
      &-menus {
        @include wh(100%, 40px);
        :deep(.x-radio-button-list-tab) {
          background-color: rgba(0, 0, 0, 0);
        }
      }
    }
    .model-template {
      margin: 10px 15px 0;
    }
    .modal-content {
      flex: 1;
      width: 100%;
      padding: 0 16px;
      height: 50vh;
      overflow-y: scroll;
      position: relative;
      &::-webkit-scrollbar {
        display: none;
      }
      &-tip {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 13px;
        position: sticky;
        top: 0;
        left: 0;
        padding: 10px 0;
        background-color: #fff;
        span {
          color: #9f9fa4;
        }
        .delete-icon {
          color: #555555;
          cursor: pointer;
          font-size: 16px;
        }
      }
      &-title {
        margin-bottom: 8px;
        margin-top: 16px;
        @include sc(14px, #555555);
      }
      &-space {
        margin-top: 30px;
        .space-title {
          @include sc(14px, #555555) {
            font-weight: 700;
          }
        }
        .space-content {
          @include fj {
            margin-top: 10px;
            gap: 10px 7px;
            flex-wrap: wrap;
            justify-content: initial;
          }
          &-item {
            cursor: pointer;
            @include fj {
              flex-direction: column;
              align-items: center;
            }
            @include wh(54px, 57px) {
              padding: 4px 0;
              border-radius: 8px;
              background-color: rgb(244, 247, 254);
              @include sc(12px, #9f9fa4);
            }
            &.enable {
              box-shadow: 0 0 10px 0 rgba(72, 111, 245, 0.14);
              background-color: rgb(255, 255, 255);
              color: #555555;
            }
          }
        }
      }
      &-list {
        margin-bottom: 10px;
        &:last-child {
          margin-bottom: 0;
        }
        &-item {
          border-radius: 4px;
          padding: 4px 10px;
          display: flex;
          align-items: center;
          height: 60px;
          cursor: pointer;
          .left_point {
            display: flex;
            align-items: center;
            gap: 20px;
            width: 100%;
          }
          .right-delete {
            display: none;
          }
          &:hover {
            background-color: #f5f8ff;
            .right-delete {
              display: block;
            }
          }
          &.hg {
            height: 50px !important;
          }
          &-left {
            margin-right: 6px;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding-top: 7px;
            &-circle {
              width: 8px;
              height: 8px;
              border-radius: 100%;
              border: 1px solid #cbcece;
              margin-bottom: 4px;
            }
            &-line {
              width: 0;
              height: 31px;
              border-left: 1px dashed #cbcece;
            }
          }
          &-middle {
            flex: 1;
            height: 100%;
            &-row {
              margin-bottom: 10px;
              display: flex;
              &:last-child {
                margin-bottom: 0;
              }
            }
          }
        }
      }
      .clean-times-content {
        background-color: rgb(244, 247, 254);
        width: max-content;
        :deep(.x-number-box) {
          column-gap: 2px;
        }
        :deep(.x-number-box-btn) {
          font-size: 16px;
          font-weight: bold;
          background-color: transparent;
          background: transparent;
          cursor: pointer;
        }
        // :deep(.x-number-box-input input) {
        //   background-color: transparent;
        // }
      }
      .opera-wrapper {
        margin-top: 20px;
        border-radius: 8px;
        background: rgba(245, 248, 255, 1);
        padding: 10px;
        width: 100%;
        height: 200px;
        display: flex;
        align-items: center;
        justify-content: space-between;

        .col {
          height: 100%;
          &:nth-child(2) {
            display: flex;
            align-items: center;
            flex-direction: column;
            gap: 10px;
          }
          .sideTab {
            box-sizing: border-box;
            width: 75px;
            height: 155px;
            border-radius: 8px;
            padding: 10px 7px;
            background-color: #fff;
            border-radius: 10px;
            box-shadow: 0px 0px 10px 0px rgba(155, 162, 190, 0.2);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            &-item {
              box-sizing: border-box;
              width: 100%;
              height: 33px;
              text-align: center;
              line-height: 33px;
              font-size: 12px;
              color: rgba(85, 85, 85, 1);
              border-radius: 8px;
              &.is-active {
                color: #fff;
                box-shadow: 0px 0px 10px 0px rgba(0, 0, 0, 0.1);
                background: linear-gradient(180deg, rgba(124, 132, 252, 1), rgba(89, 100, 251, 1) 100%);
              }
            }
          }
          .bar-container {
            width: 66px;
            height: 155px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            user-select: none;
          }
          .bar-bg {
            width: 100%;
            height: 100%;
            border-radius: 10px;
            box-shadow: 0 0 10px 0 rgba(155, 162, 190, 0.25);
            background: #e4e4ec;
            position: relative;
            overflow: visible;
          }
          .bar-fill {
            position: absolute;
            bottom: 0;
            width: 100%;
            background: linear-gradient(180deg, #7c84fc, #5964fb 100%);
            border-radius: 10px;
            transition: height 0.2s;
            z-index: 1;
            overflow: hidden;
            cursor: pointer;
          }
          .bar-value {
            padding-top: 10px;
            width: 100%;
            text-align: center;
            color: white;
            font-size: 14px;
          }
          .speed-marks {
            position: absolute;
            left: 0px;
            top: 10px;
            bottom: 0;
            width: 40px;
            user-select: none;
          }
          .speed-mark {
            position: absolute;
            display: flex;
            align-items: center;
            transform: translateY(50%);
          }
          .speed-mark-value {
            font-size: 14px;
            color: #bcbfed;
          }
          .label-text {
            color: #9f9fa4;
            font-size: 12px;
            margin-top: 10px;
            text-align: center;
          }

          .center-round {
            overflow: hidden;
            position: relative;
            @include ct-f(x);
            @include wh(104px, 94px);
            .image {
              position: absolute;
              top: 5px;
            }
            .percent {
              span:nth-child(1) {
                @include ct-p(x) {
                  top: 35px;
                }
                @include sc(18px, #242859) {
                  font-weight: bolder;
                }
              }
              span:nth-child(2) {
                @include ct-p(x) {
                  top: 57px;
                }
                @include sc(12px, #242859);
              }
            }
          }
          .water-btn {
            width: 46px;
            height: 34px;
            text-align: center;
            line-height: 34px;
            font-size: 12px;
            color: #555555;
            border-radius: 8px;
            box-shadow: 0px 0px 10px 0px rgba(155, 162, 190, 0.25);
            background: rgba(255, 255, 255, 1);
            &.is-active {
              background: linear-gradient(180deg, rgba(124, 132, 252, 1), rgba(89, 100, 251, 1) 100%);
              color: #fff;
            }
          }
        }
      }
    }
    .modal-btn {
      @include fj;
      @include wh(100%, auto) {
        padding: 10px 0px 0;
        margin: 0 auto;
        border-radius: 0 0 10px 10px;
        justify-content: center;
        gap: 10px;
      }
    }
  }
  &-control {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    margin: 0 auto;
    width: fit-content; // 或者具体宽度
    height: 28px;
    display: flex;
    align-items: center;
    justify-content: space-around;
    gap: 20px;
    .control-item {
      // position: absolute;
      // bottom: 10px;
      // left: 0;
      // right: 0;
      // margin: 0 auto;
      height: 28px;
      width: max-content;
      border-radius: 4px;
      background-color: #01051fb3;
      color: #fff;
      font-size: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 0 10px;
      cursor: pointer;
      &.is-edit {
        background-color: #fff;
        width: 225px;
        height: 42px;
        cursor: default;
        color: #383838;
      }
      &.point_active {
        background-color: #5964fb !important;
      }
      &-icon {
        margin-right: 7px;
      }
      &.isPoint {
        background-color: #5964fb !important;
      }
      &-btn {
        margin-left: 10px;
        display: inline-flex;
        width: 40px;
        height: 24px !important;
        align-items: center;
        font-size: 12px;
        padding: 0;
      }
    }
  }
  .map-tip {
    &-frame {
      position: absolute;
      left: 18px;
      top: 24px;
      background-color: #fff;
      border-radius: 4px;
      padding: 12px 14px;
    }
    &-item {
      margin-top: 12px;
      display: flex;
      align-items: center;
      cursor: pointer;
      color: #9f9fa4;
      &.is-show {
        color: #000;
      }
      &-icon {
        display: inline-flex;
        align-items: center;
        width: 12px;
        height: 12px;
        margin-right: 5px;
      }
      .polyline {
        display: inline-block;
        width: 12px;
        height: 4px;
      }
      .circle {
        display: inline-block;
        width: 12px;
        height: 12px;
        border-radius: 100%;
      }
      &-text {
        font-size: 12px;
      }
      &:first-child {
        margin-top: 0;
      }
    }
  }
}
</style>
