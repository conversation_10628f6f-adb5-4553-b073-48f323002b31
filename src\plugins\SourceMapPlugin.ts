import { unlinkSync, readdirSync, readFile, writeFile } from "fs";

export const sourceMapDelete = () => {
  return {
    enforce: "post",
    writeBundle: {
      sequential: true,
      order: "post",
      async handler({ dir }) {
        const files = readdirSync(`${dir}/js/`);
        files
          .filter((str) => str.endsWith(".map"))
          .forEach((str) => unlinkSync(`${dir}/js/${str}`));

        files
          .filter((str) => str.endsWith(".js"))
          .forEach((str, index) => {
            const path = `${dir}/js/${str}`;
            readFile(path, "utf-8", (err, data) => {
              if (err) throw err;
              const updateData = data.replace(/\/\/# sourceMap\S+/, "");
              writeFile(path, updateData, (err) => {
                if (err) throw err;
              });
            });
          });
      },
    },
  };
};
