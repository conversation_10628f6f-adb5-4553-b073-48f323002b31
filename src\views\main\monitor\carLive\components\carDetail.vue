<template>
  <section
    v-show="socket.statusPop"
    class="car-detail"
  >
    <div
      class="car-detail-close"
      v-html="closeText"
      @click="socket.statusPop = false"
    ></div>
    <div class="car-detail-content">
      <div class="content-left">
        <div class="content-left-top">
          <div :class="['car-image', { online: carImage.status.origin === 'charge' }]">
            <img
              v-show="carImage.status.imgUrl"
              :src="carImage.status.imgUrl"
              :class="{
                'cover-image': carImage.status.origin !== 'charge',
              }"
              ref="carImageRef"
            />
            <capsuleIcon
              v-show="!isOffline"
              class="car-image-work"
              :config="carImage.workStatus"
              height="34px"
              style="width: 90px"
            />
            <capsuleIcon
              v-show="isOffline"
              class="car-image-work"
              :config="{
                text: offlineText,
                icon: offlineSvg,
                color: '#999',
              }"
              height="34px"
              style="width: 90px"
            />
            <capsuleIcon
              class="car-image-drive"
              :config="carImage.driveMode"
              height="34px"
              style="width: 102px"
            />
            <div class="car-image-id">
              <span>
                {{ socket.statusPop }}
              </span>
            </div>
          </div>
          <div class="car-speed">
            <div class="car-speed-top">
              <roundPercent
                class="car-speed-top-round"
                :percent="carSpeedStatus.speed * 10"
                :offline="isOffline"
              />
              <div class="round-content-top">
                <span>{{ carSpeedStatus.speedText }}</span>
                <span>km/h</span>
              </div>
              <div class="round-content-bottom">
                <span>10km/h</span>
              </div>
            </div>
            <div class="car-speed-bottom">
              <div
                v-for="(option, index) in carSpeedStatus.gears"
                :key="index"
                :class="['gear', { active: carSpeedStatus.gear === option.value }]"
              >
                <span>{{ option.label }}</span>
              </div>
            </div>
          </div>
        </div>
        <div class="content-left-center">
          <div class="car-battery">
            <batteryPercent
              :percent="Number(carStatus.battery)"
              :offline="isOffline"
              style="transform: scale(2.2)"
            />
            <div class="percent">{{ carStatus.battery }}%</div>
            <span class="title">{{ $t("electricNumber") }}</span>
          </div>

          <div class="car-water">
            <waterPercent
              :percent="Number(carStatus.water)"
              :offline="isOffline"
              style="transform: scale(2.2)"
            />
            <div class="percent">{{ carStatus.water }}%</div>
            <span class="title">{{ $t("waterTank") }}</span>
          </div>
          <div class="car-garbage">
            <garbagePercent
              :percent="Number(carStatus.garbage)"
              :offline="isOffline"
              style="transform: scale(2.2)"
            />
            <div class="percent">{{ carStatus.garbage }}%</div>
            <span class="title">{{ $t("garbageCan") }}</span>
          </div>
        </div>
        <div class="content-left-bottom">
          <div class="car-battery-detail">
            <div class="car-battery-detail-warp">
              <div class="car-battery-detail-warp-item">
                <div class="top">
                  <semiRoundPercent
                    class="top-panel"
                    :percent="batteryStatus.temperatureMax"
                    :offline="isOffline"
                    :start="-30"
                  />
                  <div class="top-content">
                    <span>{{ batteryStatus.temperatureMaxText || "--" }}°</span>
                  </div>
                </div>
                <x-icon
                  class="center"
                  name="battery_temperature"
                ></x-icon>
                <span class="bottom">{{ $t("batteryTemperature") }}</span>
              </div>
              <div class="car-battery-detail-warp-item">
                <div class="top">
                  <div class="top">
                    <semiRoundDashPercent
                      class="top-panel"
                      :percent="batteryStatus.current"
                      :offline="isOffline"
                      :end="150"
                    />
                    <div class="top-content">
                      <span>{{ batteryStatus.currentText || "--" }}</span>
                    </div>
                  </div>
                </div>
                <x-icon
                  class="center"
                  name="battery_current"
                ></x-icon>
                <span class="bottom">{{ $t("batteryCurrent") }}</span>
              </div>
              <div class="car-battery-detail-warp-item">
                <div class="top">
                  <div class="top">
                    <semiRoundDashPercent
                      class="top-panel"
                      :percent="batteryStatus.voltage"
                      :offline="isOffline"
                      :end="150"
                    />
                    <div class="top-content">
                      <span>{{ batteryStatus.voltageText || "--" }}</span>
                    </div>
                  </div>
                </div>
                <x-icon
                  class="center"
                  name="battery_voltage"
                ></x-icon>
                <span class="bottom">{{ $t("batteryVoltage") }}</span>
              </div>
            </div>
          </div>
          <div class="car-miles">
            <div class="car-miles-top">
              <div class="car-miles-top-title">
                <x-title icon="triangle">{{ $t("currentMileage") }}</x-title>
              </div>
              <div class="car-miles-top-content">
                <ul>
                  <li
                    v-for="(item, index) in currentMileage"
                    :key="index"
                  >
                    <span>{{ item }}</span>
                  </li>
                </ul>
                <span>km</span>
              </div>
            </div>
            <div class="car-miles-bottom">
              <div class="car-miles-bottom-title">
                <x-title icon="triangle">{{ $t("totalMileage") }}</x-title>
              </div>
              <div class="car-miles-bottom-content">
                <span>{{ carMileageInfo.totalMileage }}</span>
                <span>km</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="content-right">
        <div class="car-light-status">
          <div class="light-status-title">
            <x-title>
              <template
                v-for="(item, index) in carStatusTabs.tabs"
                :key="index"
              >
                <span
                  :class="['title-label', { active: item.value === carStatusTabs.active }]"
                  @click="carStatusTabs.active = item.value"
                  >{{ item.label }}</span
                >
              </template>
              <x-icon
                v-if="isTirePressureWarning"
                name="map_warn"
                width="12px"
                height="11.6px"
                class="tire-warn-icon"
              />
            </x-title>
          </div>
          <div class="light-status-image">
            <div class="image-car"></div>
            <!-- <x-icon
              class="left blue-short-light"
              :style="{
                opacity: carLightStatus[1].status && isLightActive ? 1 : 0,
              }"
              name="blue_short_light"
            /> -->
            <x-icon
              class="right-top blue-light"
              :style="{
                opacity: carLightStatus[0].status && isLightActive ? 1 : 0,
              }"
              name="blue_light"
            />
            <x-icon
              class="right-bottom blue-light"
              :style="{
                opacity: carLightStatus[0].status && isLightActive ? 1 : 0,
              }"
              name="blue_light"
            />
            <x-icon
              class="left-top orange-light"
              :style="{
                opacity: (carLightStatus[2].status || carLightStatus[3].status) && isLightActive ? 1 : 0,
              }"
              name="orange_light"
            />
            <x-icon
              class="right-top orange-light"
              :style="{
                opacity: (carLightStatus[2].status || carLightStatus[3].status) && isLightActive ? 1 : 0,
              }"
              name="orange_light"
            />
            <x-icon
              class="left-bottom orange-light"
              :style="{
                opacity: (carLightStatus[2].status || carLightStatus[4].status) && isLightActive ? 1 : 0,
              }"
              name="orange_light"
            />
            <x-icon
              class="right-bottom orange-light"
              :style="{
                opacity: (carLightStatus[2].status || carLightStatus[4].status) && isLightActive ? 1 : 0,
              }"
              name="orange_light"
            />
            <template
              v-for="(item, index) in carTireStatus"
              :key="index"
            >
              <tirePressureInfo
                :pressure="item.pressure"
                :temperature="item.temperature"
                :position="item.position"
                :status="item.status"
                v-show="isTireActive"
              />
            </template>
          </div>
          <div class="light-status-list">
            <template
              v-for="(item, index) in carLightStatus"
              :key="index"
            >
              <div
                class="list-item"
                v-show="isLightActive && item.title !== $t('operaLight')"
              >
                <div :class="['item-icon', { enable: item.status }]">
                  <x-icon
                    :name="item.status ? `${item.icon}_enable` : item.icon"
                    width="20px"
                    height="20px"
                  />
                </div>
                <div class="item-title">{{ item.title }}</div>
              </div>
            </template>
          </div>
        </div>
        <div class="car-device-status">
          <div class="car-device-status-title">
            <x-title>{{ $t("operaEquipmentStatus") }}</x-title>
          </div>
          <div class="car-device-status-image">
            <div class="device-left">
              <div :class="['image', carDeviceStatus.cleanerStatus ? 'cleaner-open' : 'cleaner-close']"></div>
              <div class="title">
                {{ $t("sweep") }} （
                <span :class="{ open: carDeviceStatus.cleanerStatus }">{{
                  carDeviceStatus.cleanerStatus ? "开" : "关"
                }}</span>
                ）
              </div>
            </div>
            <div class="device-center">
              <div :class="['image', carDeviceStatus.garbageStatus ? 'garbage-open' : 'garbage-close']"></div>
              <div class="title">
                {{ $t("garbageCan") }}（<span :class="{ open: carDeviceStatus.garbageStatus }">{{
                  carDeviceStatus.garbageStatus ? $t("on") : $t("off")
                }}</span
                >）
              </div>
            </div>
            <div class="device-right">
              <div class="wrapper">
                <cleaningAreaRound
                  class="inner-bg"
                  :class="{
                    'cleaning-animate': carDeviceStatus.cleanerStatus,
                  }"
                  :offline="isOffline"
                />
                <div class="inner-content">
                  {{ carDeviceStatus.cleanArea }}
                </div>
              </div>
              <div class="title">{{ $t("sweepArea") }}(㎡)</div>
            </div>
          </div>
        </div>
        <div class="car-time-line">
          <div class="car-time-line-top">
            <div
              v-if="carUploadInfo.chassisOnline !== 2"
              class="car-time-line-top-status"
            >
              <div>
                车辆底盘状态：<span>{{ ["未上电", "已上电"][carUploadInfo.chassisOnline] }}</span>
              </div>
              <div>
                自动驾驶版本：<span>{{ carUploadInfo.sysVersion }}</span>
              </div>
            </div>
            <div class="car-time-line-top-upload">
              <div class="upload-data">
                <div class="upload-data-label">{{ $t("dataUpload") }}</div>
                <div class="upload-data-content">
                  <div class="time">{{ carUploadInfo.updateTime }}</div>
                  <div class="date">{{ carUploadInfo.updateDate }}</div>
                </div>
              </div>
              <div class="upload-gps">
                <div class="upload-gps-label">GPS{{ $t("upload") }}</div>
                <div class="upload-gps-content">
                  <div class="time">{{ carUploadInfo.gpsTime }}</div>
                  <div class="date">{{ carUploadInfo.gpsDate }}</div>
                </div>
              </div>
            </div>
          </div>
          <div class="car-time-line-bottom">
            <div class="car-time-line-bottom-item">
              <x-icon
                class="car-time-line-bottom-item-icon"
                name="locate"
              />
              <div class="content">
                {{ carUploadInfo.addr }}
              </div>
            </div>
          </div>
        </div>
        <div class="car-warn">
          <template v-if="carWarnStatus.warningCount">
            <div
              class="car-warn-wrap"
              @click="toAlarmStatistics"
              style="cursor: pointer"
            >
              <div class="left">
                <x-icon
                  name="warn_radiation"
                  width="30"
                  height="30"
                />
                <span>{{ carWarnStatus.warningCount }}</span>
              </div>
              <div class="center">
                {{ carWarnStatus.latestWarn }}
              </div>
              <x-icon
                class="right"
                name="right"
                width="12"
                height="12"
              />
            </div>
          </template>
          <template v-else>
            <div style="color: #999">{{ $t("noAlarm") }}</div>
          </template>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed } from "vue";
import capsuleIcon from "./capsuleIcon.vue";
import batteryPercent from "./batteryPercent.vue";
import waterPercent from "./waterPercent.vue";
import garbagePercent from "./garbagePercent.vue";
import roundPercent from "./roundPercent.vue";
import semiRoundPercent from "./semiRoundPercent.vue";
import semiRoundDashPercent from "./semiRoundDashPercent.vue";
import cleaningAreaRound from "./cleaningAreaRound.vue";
import tirePressureInfo from "./tirePressureInfo.vue";
import xIcon from "@/components/x-icon.vue";
import xTitle from "@/components/x-title.vue";
import {
  workStatusMap,
  driveModeMap,
  parseBattery,
  parseWater,
  parseSpeed,
  parseTemperature,
  parseGarbage,
  parseCurrent,
  parseVoltage,
  parseCurrentMileage,
  parseTotalMileage,
  parseCleaningArea,
  offlineText,
  offlineSvg,
  undefinedOrNullToZero,
} from "@/services/wsconfig";
import { useMainStore } from "@/stores/main";
import { useRouter } from "vue-router";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

const router = useRouter();
const closeText = ref("&#10005");
const { socket } = useMainStore();

const carImage = reactive({
  workStatus: {
    icon: "",
    text: "",
    color: "",
  },
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
  status: {
    origin: "",
    imgUrl: "",
  },
});
const carStatus = reactive({
  battery: "0",
  water: "0",
  garbage: "0",
});
const carLightStatus = reactive([
  {
    title: $t("normalLight"),
    icon: "normal_light",
    status: false,
  },
  {
    title: $t("workLight"),
    icon: "work_light",
    status: false,
  },
  {
    title: $t("doubleLight"),
    icon: "double_light",
    status: false,
  },
  {
    title: $t("leftLight"),
    icon: "left_light",
    status: false,
  },
  {
    title: $t("rightLight"),
    icon: "right_light",
    status: false,
  },
]);
const carTireStatus = reactive([
  {
    title: $t("frontLeftWheel"),
    position: "FL",
    status: 0, // 0---下线或无数据 1---正常 2---告警
    pressure: undefined,
    temperature: undefined,
    pressureKey: "leftFrontTirePressure",
    temperatureKey: "leftFrontTireTemperature",
  },
  {
    title: $t("frontRightWheel"),
    position: "FR",
    status: 0,
    pressure: undefined,
    temperature: undefined,
    pressureKey: "rightFrontTirePressure",
    temperatureKey: "rightFrontTireTemperature",
  },
  {
    title: $t("backLeftWheel"),
    position: "RL",
    status: 0,
    pressure: undefined,
    temperature: undefined,
    pressureKey: "leftReverseTirePressure",
    temperatureKey: "leftReverseTireTemperature",
  },
  {
    title: $t("backRightWheel"),
    position: "RR",
    status: 0,
    pressure: undefined,
    temperature: undefined,
    pressureKey: "rightReverseTirePressure",
    temperatureKey: "rightReverseTireTemperature",
  },
]);
const carStatusTabs = reactive({
  tabs: [
    {
      value: "light",
      label: $t("lightStatus"),
    },
    {
      value: "tire",
      label: $t("tirePressureState"),
    },
  ],
  active: "light",
});
const carSpeedStatus = reactive({
  speed: 0,
  speedText: "0.0",
  gears: [
    {
      value: "park_done",
      label: "P",
    },
    {
      value: "back",
      label: "R",
    },
    {
      value: "neutral",
      label: "N",
    },
    {
      value: "forward",
      label: "D",
    },
  ],
  gear: "park_done",
});
const batteryStatus = reactive({
  temperatureMax: 0.0,
  temperatureMaxText: "0.0",
  current: 0.0,
  currentText: "0.0",
  voltage: 0.0,
  voltageText: "0.0",
});
const carMileageInfo = reactive({
  currentMileage: "0.0",
  totalMileage: "0.0",
});
const carDeviceStatus = reactive({
  cleanerStatus: false,
  garbageStatus: false,
  cleanArea: "",
});
const carUploadInfo = reactive({
  updateTime: "",
  updateDate: "",
  gpsTime: "",
  gpsDate: "",
  addr: "",
  chassisOnline: 2, // 0 未上电  1 已上电  2 无状态隐藏
  sysVersion: "",
});
const carWarnStatus = reactive({
  warningCount: 0,
  latestWarn: "",
});
const currentMileage = computed(() => {
  const miles = carMileageInfo.currentMileage;
  if (miles === "--") {
    return ["-", "-", "", "", "", "", ""];
  } else {
    return miles.toString().padStart(7, "0").split("");
  }
});
const isOffline = computed(() => carImage.status.origin === "offline");
const isLightActive = computed(() => carStatusTabs.active === "light");
const isTireActive = computed(() => carStatusTabs.active === "tire");
const isTirePressureWarning = ref(false);
const carImageRef = ref<any>();
const carImagePause = ref(false);
watch(
  () => socket.allStatus,
  (newV) => {
    if (newV) {
      const target = newV.find((v) => v.deviceId === socket.statusPop);
      if (target) {
        carImage.status = {
          origin: target.onlineDetail,
          imgUrl: {
            online: new URL(`@/assets/images/car_online.gif`, import.meta.url).href,
            offline: new URL(`@/assets/images/car_offline.png`, import.meta.url).href,
            charge: new URL(`@/assets/images/car_charge.gif`, import.meta.url).href,
          }[target.onlineDetail],
        };
        if (target.onlineDetail === "online") {
          const tempImage = new Image();
          tempImage.src = carImage.status.imgUrl;
          tempImage.onload = () => {
            if (target.speed === 0) {
              carImageRef.value.stopGif();
              carImagePause.value = true;
            } else if (carImagePause.value) {
              carImageRef.value.playGif();
              carImagePause.value = false;
            }
          };
        }
        carImage.workStatus = workStatusMap[target.workStatus];
        carImage.driveMode = JSON.parse(JSON.stringify(driveModeMap[target.driveMode]));
        if (isOffline.value) {
          carImage.driveMode.icon += "_offline";
          carImage.driveMode.color = "#999";
        }
        carLightStatus[0].status = !isOffline.value && target.headLamp;
        // carLightStatus[1].status = !isOffline.value && target.alarmLamp;
        // carLightStatus[1].status = !isOffline.value;
        carLightStatus[2].status = !isOffline.value && target.doubleFlash;
        carLightStatus[3].status = !isOffline.value && target.turnLeftLamp;
        carLightStatus[4].status = !isOffline.value && target.turnRightLamp;

        for (const tire of carTireStatus) {
          // @ts-ignore
          tire.pressure = target[tire.pressureKey]; // 胎压
          // @ts-ignore
          tire.temperature = target[tire.temperatureKey]; // 胎温
          // 胎压状态
          if (isOffline.value) {
            tire.status = 0;
          } else {
            tire.status = tire.pressure! < 3 || tire?.pressure! > 4.5 ? 2 : 1;
          }
        }
        // 胎压告警
        isTirePressureWarning.value = carTireStatus.some((tire) => tire.status === 2);

        carStatus.battery = parseBattery(target.electric);
        carStatus.water = parseWater(target.tank);
        carStatus.garbage = parseGarbage(target.litter);
        carSpeedStatus.speed = target.speed;
        carSpeedStatus.speedText = parseSpeed(target.speed);
        carSpeedStatus.gear = isOffline.value ? "park_done" : target.gear;
        batteryStatus.temperatureMaxText = parseTemperature(target.temperatureMax);
        batteryStatus.temperatureMax = undefinedOrNullToZero(target.temperatureMax);
        batteryStatus.currentText = parseCurrent(target.current);
        batteryStatus.current = undefinedOrNullToZero(target.current);
        batteryStatus.voltageText = parseVoltage(target.voltage);
        batteryStatus.voltage = undefinedOrNullToZero(target.voltage);
        carMileageInfo.totalMileage = parseTotalMileage(target.totalMileage);
        carMileageInfo.currentMileage = parseCurrentMileage(target.currentMileage);
        carDeviceStatus.cleanerStatus = !isOffline.value && target.broomWork;
        carDeviceStatus.garbageStatus = !isOffline.value && target.boxDoorWork === "open";
        carDeviceStatus.cleanArea = parseCleaningArea(target.cleanArea);
        carUploadInfo.gpsDate = target.gpsDate && target.gpsDate.replace(/-/g, "/");
        carUploadInfo.gpsTime = target.gpsTime;
        carUploadInfo.updateDate = target.updateDate && target.updateDate.replace(/-/g, "/");
        carUploadInfo.updateTime = target.updateTime;
        carUploadInfo.addr = target.addr;
        carWarnStatus.warningCount = !isOffline.value ? target.warnNum : 0;
        carWarnStatus.latestWarn = target.latestWarn;
        carUploadInfo.chassisOnline = target.chassisOnline;
        carUploadInfo.sysVersion = target.sysVersion;
      }
    }
  }
);
const toAlarmStatistics = () => {
  router.push({
    name: "alarmStatistics",
    query: {
      deviceId: socket.statusPop as string,
    },
  });
};

// 操作gif
if ("getContext" in document.createElement("canvas")) {
  // @ts-ignore
  HTMLImageElement.prototype.playGif = function () {
    // @ts-ignore
    if (this.storeCanvas) {
      // @ts-ignore
      this.storeCanvas.parentElement.removeChild(this.storeCanvas);
      // @ts-ignore
      this.storeCanvas = null;
      this.style.opacity = "";
    }
    // @ts-ignore
    if (this.storeUrl) this.src = this.storeUrl;
  };
  // @ts-ignore
  HTMLImageElement.prototype.stopGif = function () {
    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const { width, height } = this;
    if (width && height) {
      // @ts-ignore
      if (!this.storeUrl) this.storeUrl = this.src;
      canvas.width = width;
      canvas.height = height;
      ctx?.drawImage(this, 0, 0, width, height);
      try {
        this.src = canvas.toDataURL("image/gif");
      } catch (e) {
        this.removeAttribute("src");
        canvas.style.position = "absolute";
        this.parentElement?.insertBefore(canvas, this);
        this.style.opacity = "0";
        // @ts-ignore
        this.storeCanvas = canvas;
      }
    }
  };
}
</script>

<style lang="scss" scoped>
.car-detail {
  z-index: var(--z-index-popover);
  position: absolute;
  left: 0;
  top: 0;
  @include wh(100%) {
    padding: 48px 32px 22px 32px;
    background-color: #f5f5f5;
  }
  &-close {
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 32px;
    @include ct-f;
    @include wh(24px) {
      background-color: rgba(5, 10, 39, 0.4);
      border-radius: 50%;
    }
    @include sc(16px, #fff);
  }
  &-content {
    display: flex;
    @include wh(100%);
    .content-left {
      flex: 720;
      height: 100%;
      display: flex;
      flex-direction: column;
      &-top {
        flex: 230;
        width: 100%;
        display: flex;
        .car-image {
          position: relative;
          flex: 476;
          height: 100%;
          border-radius: 8px;
          background-color: rgb(229, 230, 232);
          overflow: hidden;
          &.online {
            @include bis("@/assets/images/car_online_bg.jpg");
          }
          &-work {
            width: 90px;
            left: 20px;
          }
          &-drive {
            width: 100px;
            right: 20px;
          }
          &-work,
          &-drive {
            position: absolute;
            top: 18px;
          }
          &-id {
            position: absolute;
            right: 20px;
            bottom: 18px;
            @include sc(18px, #383838) {
              font-weight: bolder;
            }
          }
          img {
            position: absolute;
            left: 50%;
            top: 50%;
            transform: translate(-50%, -50%);
            @include wh(320px, 180px);
            // animation-duration: 6s;
            // animation-name: carImage;
            // animation-iteration-count: infinite;
            // animation-timing-function: linear;
            // @keyframes carImage {
            //   0% {
            //     transform: translate(-200%, -50%);
            //   }
            //   100% {
            //     transform: translate(100%, -50%);
            //   }
            // }
            &.cover-image {
              @include wh(100%, 100%);
            }
          }

          // background-image: url(@/assets/images/car_online.gif);
          // background-repeat: no-repeat;
          // background-size: cover;
          // background-position: center;
        }
        .car-speed {
          @include ct-f(both) {
            flex-direction: column;
            flex: 230;
          }
          height: 100%;
          margin-left: 15px;
          background-color: #fff;
          border-radius: 4px;
          &-top {
            position: relative;
            &-round {
              @include wh(140px, 140px);
            }
            .round-content-top {
              span:nth-child(1) {
                @include ct-p(x) {
                  top: 50px;
                }
                @include sc(18px, #242859) {
                  font-weight: bolder;
                }
              }
              span:nth-child(2) {
                @include ct-p(x) {
                  top: 70px;
                }
                @include sc(12px, #999);
              }
            }
            .round-content-bottom {
              span {
                @include ct-p(x) {
                  top: 100px;
                }
                @include sc(12px, #999);
              }
            }
          }
          &-bottom {
            display: flex;
            padding: 5px;
            @include sc(14px, #c5c5c5) {
              border-radius: 30px;
              background: #f5f5f6;
            }
            .gear {
              margin: 0 7px;
              border: none;
              font-weight: bolder;
              &.active {
                @include sc(14px, #242859) {
                  font-weight: bolder;
                  border-radius: 50%;
                  background: #fff;
                }
              }
              span {
                display: inline-block;
                @include wh(27px, 27px) {
                  line-height: 27px;
                  text-align: center;
                }
              }
            }
          }
        }
      }
      &-center {
        flex: 200;
        width: 100%;
        margin-top: 15px;
        display: flex;
        .car-garbage,
        .car-water,
        .car-battery {
          position: relative;
          @include fj {
            flex-direction: column;
            align-items: center;
          }
          flex: 1;
          height: 100%;
          padding: 9.6vh 0 3vh 0;
          background-color: #fff;
          border-radius: 4px;
          .title {
            color: #999999;
          }
          .percent {
            @include ct-p;
            @include sc(18px, #242859) {
              font-weight: bolder;
            }
          }
        }
        .car-water,
        .car-garbage {
          margin-left: 15px;
        }
      }
      &-bottom {
        flex: 140;
        width: 100%;
        margin-top: 15px;
        display: flex;
        .car-battery-detail,
        .car-miles {
          height: 100%;
          background-color: #fff;
          border-radius: 4px;
        }
        .car-battery-detail {
          flex: 476;
          display: flex;
          &-warp {
            @include ct-f(y) {
              justify-content: space-around;
              flex: 1;
            }
            &-item {
              position: relative;
              @include ct-f(y) {
                flex-direction: column;
                text-align: center;
              }
              .top {
                &-panel {
                  margin-top: -65px;
                  @include wh(140px, 140px);
                }
                &-content {
                  @include ct-p(both);
                  @include sc(18px, #242859) {
                    font-weight: bolder;
                  }
                }
              }
              .center {
                margin: 10px 0;
              }
              .bottom {
                @include sc(12px, #999999);
              }
            }
          }
        }
        .car-miles {
          display: flex;
          flex-direction: column;
          flex: 230;
          margin-left: 15px;
          padding: 7px 0 0 8px;
          &-top,
          &-bottom {
            height: 50%;
            &-content {
              margin-left: 12px;
            }
          }
          &-top {
            &-content {
              ul {
                display: inline-block;
              }
              li {
                display: inline-block;
                margin: 1px;
                @include wh(24px, 32px) {
                  line-height: 32px;
                  text-align: center;
                  vertical-align: middle;
                }
                background: rgba(229, 230, 232, 0.4);
                border-radius: 4px;
                span {
                  @include sc(20px, #383838);
                }
                &:nth-child(6) {
                  background: none;
                  width: auto;
                }
              }
              span {
                margin-left: 5px;
                vertical-align: bottom;
                @include sc(12px, #999999);
              }
            }
          }
          &-bottom {
            &-content {
              span:nth-child(1) {
                margin-right: 5px;
                @include sc(16px, #242859) {
                  font-weight: bolder;
                }
              }
              span:nth-child(2) {
                @include sc(12px, #999999);
              }
            }
          }
        }
      }
    }
    .content-right {
      flex: 395;
      height: 100%;
      margin-left: 15px;
      display: flex;
      flex-direction: column;
      .car-light-status,
      .car-device-status,
      .car-time-line,
      .car-warn {
        background-color: #fff;
        border-radius: 4px;
      }
      .car-light-status {
        flex: 220;
        width: 100%;
        display: flex;
        flex-direction: column;
        padding: 7px 8px 10px 8px;
        .light-status-title {
          .title-label {
            display: inline-block;
            margin-right: 15px;
            color: #9f9fa4;
            cursor: pointer;
            &.active {
              color: #383838;
            }
          }
          .tire-warn-icon {
            margin: 1px 0 0 -5px;
            animation-duration: 0.8s;
            animation-name: flicker;
            animation-iteration-count: infinite;
            animation-timing-function: linear;
            @keyframes flicker {
              0% {
                opacity: 1;
              }
              100% {
                opacity: 0.4;
              }
            }
          }
        }
        .light-status-image {
          position: relative;
          flex: 3;
          width: 100%;
          .image-car {
            @include ct-p {
              transform: translate(-50%, calc(-50% + 20px));
            }
            @include bis("@/assets/images/light_status_car.png");
            @include wh(325px, 120px);
            // @include wh(60%, 0) {
            //   padding-bottom: 50%;
            // }
          }
          .blue-light {
            transition: opacity 1s;
            @include wh(60px, 30px);
            &.right-top,
            &.right-bottom {
              position: absolute;
              top: 50%;
              left: 50%;
            }
            &.right-top {
              transform: translate(calc(131px - 50%), calc(12px - 50%));
            }
            &.right-bottom {
              transform: translate(calc(131px - 50%), calc(28px - 50%));
            }
          }
          .blue-short-light {
            transition: opacity 1s;
            @include wh(40px);
            &.left {
              position: absolute;
              top: 50%;
              left: 50%;
              transform: translate(calc(-160px - 50%), calc(-50% + 20px));
            }
          }
          .orange-light {
            transition: opacity 1s;
            width: 40px;
            &.left-top,
            &.left-bottom,
            &.right-top,
            &.right-bottom {
              position: absolute;
              top: 50%;
              left: 50%;
            }
            &.left-top {
              transform: translate(calc(-168px - 50%), calc(-4px - 50%)) rotate(180deg);
            }
            &.left-bottom {
              transform: translate(calc(-168px - 50%), calc(44px - 50%)) rotate(180deg);
            }
            &.right-top {
              transform: translate(calc(126px - 50%), calc(-4px - 50%));
            }
            &.right-bottom {
              transform: translate(calc(126px - 50%), calc(44px - 50%));
            }
          }
        }
        .light-status-list {
          flex: 1;
          width: 80%;
          margin: 0 auto;
          @include fj;
          .list-item {
            @include fj {
              flex-direction: column;
              align-items: center;
            }
            @include wh(36px, 48px);
            .item-icon {
              @include ct-f;
              @include wh(26px) {
                border-radius: 50%;
                background-color: rgba(196, 196, 196, 0.3);
              }
              transition: background-color 0.6s;
              &.enable {
                background-color: #5964fb;
              }
            }
            .item-title {
              @include sc(12px, #5e5e5e);
            }
          }
        }
      }
      .car-device-status {
        display: flex;
        flex-direction: column;
        flex: 150;
        width: 100%;
        margin-top: 15px;
        padding: 7px 8px 10px 8px;
        &-image {
          @include ct-f(y) {
            justify-content: space-around;
            flex: 1;
            text-align: center;
          }
          .device-left,
          .device-center {
            .title {
              margin-top: 5px;
              @include sc(12px, #5e5e5e);
            }
            .open {
              color: #5964fb;
            }
            .image {
              @include wh(144px, 98px);
            }
            .cleaner-open {
              @include bis("@/assets/images/device_cleaner_open.gif");
            }
            .cleaner-close {
              @include bis("@/assets/images/device_cleaner_close.png");
            }
            .garbage-open {
              @include bis("@/assets/images/device_garbage_open.gif");
            }
            .garbage-close {
              @include bis("@/assets/images/device_garbage_close.png");
            }
          }
          .device-right {
            .title {
              margin-top: 5px;
              @include sc(12px, #5e5e5e);
            }
            .wrapper {
              position: relative;
              @include wh(98px, 98px);
              .inner-content {
                @include ct-p(both);
                @include sc(18px, #242859) {
                  font-weight: bold;
                }
              }
              .cleaning-animate {
                animation: rotateCircle 2s linear infinite;
              }
            }
          }
        }
      }
      .car-time-line {
        display: flex;
        flex-direction: column;
        flex: 1;
        width: 100%;
        margin-top: 15px;
        &-top {
          padding-top: 6px;
          margin: 0 10px;
          &-status {
            @include sc(14px, #555555);
            @include fj();
            span {
              color: #5964fb;
            }
          }
          &-upload {
            margin: 8px 0;
            @include fj;
            .upload-data,
            .upload-gps {
              flex: 1;
              height: 46px;
              padding-left: 10px;
              padding-top: 4px;
              background-color: rgb(245, 245, 245);
              border-radius: 4px;
              &-label {
                @include sc(12px, #9f9fa4);
              }
              &-content {
                display: flex;
                .time {
                  @include sc(16px, #242859);
                }
                .date {
                  margin-left: 4px;
                  margin-top: 3px;
                  @include sc(12px, #9f9fa4);
                }
              }
            }
          }
        }
        &-bottom {
          height: 45px;
          &-item {
            @include ct-f(y);
            margin: 0 10px;
            height: 38px;
            background: #f5f5f6;
            border-radius: 4px;
            &-icon {
              padding: 10px;
              @include wh(20, 20);
            }
            .content {
              max-width: 500px;
              @include sc(14px, #383838);
              @include ell;
            }
          }
        }
      }
      .car-warn {
        @include ct-f(y);
        flex: 42;
        margin-top: 15px;
        padding: 0 10px;
        &-wrap {
          @include ct-f(y);
          width: 100%;
          .left {
            position: relative;
            span {
              @include ct-p(both) {
                padding-bottom: 5px;
              }
              @include sc(12px, #fff);
            }
          }
          .center {
            margin-left: 10px;
            @include sc(14px, #e24562);
            @include ell;
          }
          .right {
            margin-left: auto;
          }
        }
      }
    }
  }
}
</style>
