<template>
  <template v-if="props.show">
    <!-- Tab内面板内容 -->
    <div class="content-fences">
      <x-table
        v-show="fences.length"
        style="margin-top: 10px"
        :cols="table.cols"
        :dataSource="props.fences"
        :loading="table.loading"
      >
      </x-table>
      <x-icon
        v-if="/^add|edit$/.test(props.type)"
        v-show="fences.length"
        class="content-fences-delete"
        name="del_x_blue"
        width="18px"
        height="18px"
        @click.stop="delFence"
      />
      <div
        v-show="!props.fences.length"
        class="content-fences-tip"
      >
        {{ $t("LeftClickToDrawFence") }}<br />
        {{ $t("RightClicktoFinish") }}
      </div>
    </div>
  </template>
</template>
<script lang="tsx" setup>
import { nextTick, onMounted, reactive, watch } from "vue";
import type { PropType } from "vue";
import xTable from "@/components/x-table.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { fixZeroToStr } from "@/assets/ts/dateTime";
const $t = i18nSimpleKey("proAreaManage");

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String as PropType<"add" | "edit" | "view">,
    default: "add",
  },
  mapInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  fences: {
    type: Array as PropType<typeof fenceDefaultItem>,
    required: true,
  },
});

/**
 * Tab内面板内容
 */
const fenceDefaultItem = [] as { longitude: number; latitude: number }[];

const table = reactive({
  cols: [
    {
      key: "point",
      title: $t("point"),
      width: "56",
    },
    {
      key: "longitude",
      title: $t("longitude"),
      width: "130",
    },
    {
      key: "latitude",
      title: $t("latitude"),
      width: "130",
    },
  ],
  loading: false,
});

const emits = defineEmits(["clearFence", "toggleMarker", "togglePolyline", "update:fences", "togglePolygon"]);

const delFence = () => {
  emits("clearFence");
};

onMounted(() => {
  const polygonEditorHandle = (event: any) => {
    const polygon = event.target;
    if (!polygon.getPath().length) {
      return Message("error", "围栏不能少于3个点");
    }
    const fences = polygon.getPath().map(({ lng, lat }: any, index: number) => ({
      point: fixZeroToStr(index + 1),
      longitude: lng,
      latitude: lat,
    }));
    emits("update:fences", fences);
  };

  props.mapInfo.polygonEditor.on("addnode", polygonEditorHandle);
  props.mapInfo.polygonEditor.on("removenode", polygonEditorHandle);
  props.mapInfo.polygonEditor.on("adjust", polygonEditorHandle);
  props.mapInfo.polygonEditor.on("add", (event: any) => {
    props.mapInfo.polygon = event.target;
  });
});

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      emits("toggleMarker", "none");
      emits("togglePolyline", "none");
      emits("togglePolygon", "none");
      nextTick(() => {
        props.mapInfo.polygon && props.mapInfo.polygonEditor.setTarget(props.mapInfo.polygon);
        props.type !== "view" && props.mapInfo.polygonEditor.open();
      });
    }
  },
  { immediate: true }
);
</script>

<style scoped lang="scss">
.content-fences {
  position: relative;
  @include wh(100%);
  overflow: hidden;
  padding-bottom: 56px;
  &-delete {
    cursor: pointer;
    position: absolute;
    top: 20px;
    right: 10px;
  }
  &-tip {
    padding-left: 20px;
    padding-top: 10px;
    @include sc(12px, rgb(127, 131, 190));
  }
}
</style>
