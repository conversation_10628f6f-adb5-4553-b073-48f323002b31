<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="{ position: 'end' }"
    :title="$t('carAuth')"
    width="800px"
    @cancel="carDrawerCancel"
    @confirm="carDrawerConfirm"
  >
    <x-transfer
      :titles="carDrawer.transfer.titles"
      :dataSource="carDrawer.transfer.dataSource"
      :targetSource="carDrawer.transfer.targetSource"
      :locale="carDrawer.transfer.locale"
      showSearch
    />
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, watch } from "vue";
import {
  resTreeToXTree,
  xTreeToXTransferTree,
  i18nSimpleKey,
} from "@/assets/ts/utils";
import { getCarTree, carAuth } from "@/services/api";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xTransfer from "@/components/x-transfer.vue";
import Message from "@/components/x-message";
const $t = i18nSimpleKey("userManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const carDrawer = reactive({
  transfer: {
    locale: {
      itemUnit: $t("vehicle"),
      searchPlaceholder: $t("PEnterVehNo"),
    },
    titles: [$t("allCars"), $t("authorizedCars")],
    dataSource: [] as TreeItemType[],
    targetSource: [] as TreeItemType[],
  },
});
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const resTree = await getCarTree(props.id);
      carDrawer.transfer.dataSource = xTreeToXTransferTree(
        resTreeToXTree(resTree.entId ? [resTree] : []),
        carDrawer.transfer.targetSource
      );
    }
  }
);
const carDrawerCancel = () => {
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
};
const carDrawerConfirm = async () => {
  await carAuth({
    userId: props.id,
    vehicleIdList: carDrawer.transfer.targetSource.map(
      (v) => v.value
    ) as number[],
  });
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
  emits("update:show", false);
  Message("success", $t("saveSuccess"));
  emits("confirm");
};
</script>

<style lang="scss" scoped></style>
