// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 内部快照 1`] = `
<div
  class="x-popover-container"
  style="display: none;"
>
  <!--v-if-->
  <div
    class="x-popover-content-default"
  >
    
    <!-- 卡片：有搜索框 -->
    
    <!-- 卡片：无搜索框 -->
    <div
      data-v-a2b71f8c=""
    >
      <div
        class="x-select-normal-list"
        data-v-a2b71f8c=""
        style="width: 0px;"
      >
        
        <div
          class="list-item"
          data-v-a2b71f8c=""
        >
          
          label-1
          
        </div>
        <div
          class="list-item"
          data-v-a2b71f8c=""
        >
          
          label-2
          
        </div>
        <div
          class="list-item"
          data-v-a2b71f8c=""
        >
          
          label-3
          
        </div>
        
      </div>
    </div>
    
    
  </div>
</div>
`;

exports[`Snapshot > 正确渲染 外部快照 1`] = `
"<section class="x-select" data-v-a2b71f8c="">
  <section class="x-popover" data-v-a2b71f8c="">
    <div class="x-select-normal-selector" data-v-a2b71f8c=""><span class="selector-label" data-v-a2b71f8c="">label-1</span><span class="selector-arrow" data-v-a2b71f8c=""></span></div>
    <!--teleport start-->
    <!--teleport end-->
  </section>
</section>"
`;
