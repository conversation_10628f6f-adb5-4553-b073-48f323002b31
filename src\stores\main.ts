import { defineStore } from "pinia";
import { login, logout, getPub<PERSON><PERSON>ey } from "@/services/api";
import type { LoginRequest, LoginResponse } from "@/services/type";
import { enRSACrypt } from "@/assets/ts/encrypt";
import { getLocalStorage, setLocalStorage } from "@/assets/ts/storage";
import { router } from "@/router/router";
import { number } from "echarts";

export const useMainStore = defineStore("main", {
  state: () => ({
    isJieyaPlatform: location.href.includes("autocity.szjieya.com"),
    userInfo: {} as LoginResponse["userInfo"],
    pwdModalStatus: true as boolean | true,
    tabs: [] as _TabItem[],
    audioStatus: false as boolean | false,
    floatBallPosition: {
      bottom: "240px",
      right: 0,
    } as _PositionType,
    menuControl: {
      menuShow: true,
    },
    socket: {
      instance: null as (WebSocket & { eventMap?: Map<string, any> }) | null,
      /**type === 1 gps信息 */
      baseStatus: null as _BaseStatusType[] | null,
      /**type === 4 消息提醒 */
      alarmMessageStatus: null as _AlarmMessageStatusType | null,
      /**type === 3 系统实时告警 */
      systemWarnStatus: null as _SystemWarnNumType | null,
      /**type === 6 充电桩状态 */
      chargingStationStatus: null as _ChargingStationStatusType[] | null,
      /**type === 0 车辆信息*/
      allStatus: null as _AllStatusType[] | null,
      /**type === 2 状态数据 */
      statsStatus: null as _StatsStatusType | null,
      /**type === 10 车辆列表 */
      vehicleList: [] as _VehicleListInfo[],
      /** type === 11 车辆行车路线 */
      fahrtroutes: undefined as _FahrtrouteInfo | undefined,
      /** type === 12 站点信息 */
      siteInformation: undefined as _SiteInformation | undefined,
      statusPop: false as string | false,
      alarmStatusPop: false as string | false,
      // 所有订阅的车辆 = infoWindow/carDetail的车辆id + 右侧列表打勾车辆id
      activeCarId: null as null | string, // infoWindow/carDetail的车辆id
      activeCarIdVideo: null as null | string, // infoWindow/carDetail的车辆id(打开视频)
      enableCarIds: [] as string[], // 右侧列表所有打勾的车辆id
      enableCarTree: undefined as any[] | undefined,
      selectedCarItem: [] as any[],
      showRoutes: false,
      showFences: false,
      taskContinueConfirm: false as string | false,
      // 大屏相关
      screenActiveCarId: null as null | string, // infoWindow/carDetail的车辆id
      screenWorkView: null as any,
      screenAlarm: null as _ScreenAlarmType[] | null,
      screenWorkers: null as _ScreenWorkerType[] | null,
      // uav相关
      activeUavId: null as null | string,
      uavVideoVisible: false as boolean | false,
    },
    /** 红绿灯通过时间 */
    passTime: null as number | null,
  }),
  getters: {},
  actions: {
    getPassTime() {
      return this.$state.passTime;
    },
    setPassTime(time: number) {
      this.$state.passTime = time;
    },
    clearPassTime() {
      this.$state.passTime = null;
    },
    loadUserInfo() {
      this.$patch({
        userInfo: getLocalStorage("userInfo"),
      });
    },
    loadTabs() {
      this.$patch({
        tabs: getLocalStorage("tabs") || [],
      });
    },
    loadMenuControl() {
      this.$patch({
        menuControl: getLocalStorage("menuControl"),
      });
    },
    loadEnableCarIds() {
      this.$state.socket.enableCarIds = getLocalStorage("enableCarIds");
    },
    loadPwdModalStatus() {
      this.$patch({
        pwdModalStatus: getLocalStorage("pwdModalStatus"),
      });
    },
    updatePwdModalStatus(status: any) {
      this.$state.pwdModalStatus = status;
      setLocalStorage("pwdModalStatus", status);
    },
    updateActiveCarId(id: typeof this.$state.socket.activeCarId) {
      this.$state.socket.activeCarId = id;
    },
    updateActiveUavId(id: typeof this.$state.socket.activeUavId) {
      this.$state.socket.activeUavId = id;
    },
    updateUavVideoVisible(status: typeof this.$state.socket.uavVideoVisible) {
      this.$state.socket.uavVideoVisible = status;
    },
    updateScreenActiveCarId(id: typeof this.$state.socket.screenActiveCarId) {
      this.$state.socket.screenActiveCarId = id;
    },
    updateActiveCarIdVideo(id: typeof this.$state.socket.activeCarIdVideo) {
      this.$state.socket.activeCarIdVideo = id;
    },
    updateEnableCarIds(ids: string[]) {
      this.$state.socket.enableCarIds = ids;
      setLocalStorage("enableCarIds", ids);
    },
    loadShowRoutes() {
      this.$state.socket.showRoutes = getLocalStorage("showRoutes");
    },
    updateShowRoutes(status: boolean) {
      this.$state.socket.showRoutes = status;
      setLocalStorage("showRoutes", status);
    },
    loadShowFences() {
      this.$state.socket.showFences = getLocalStorage("showFences");
    },
    updateShowFences(status: boolean) {
      this.$state.socket.showFences = status;
      setLocalStorage("showFences", status);
    },
    autoLogin({ token, userInfo }: LoginResponse) {
      setLocalStorage("token", token);
      setLocalStorage("userInfo", userInfo);
      setLocalStorage("pwdModalStatus", true);
      setLocalStorage("enableCarIds", []);
      setLocalStorage("tabs", []);
      setLocalStorage("menuControl", {
        menuShow: true,
      });
      setLocalStorage("showRoutes", false);
      setLocalStorage("showFences", false);
      this.$state.socket.baseStatus = null;
      this.$state.socket.enableCarTree = [];
      this.$state.socket.selectedCarItem = [];
    },
    autoLogout() {
      localStorage.removeItem("token");
      localStorage.removeItem("userInfo");
      localStorage.removeItem("enableCarIds");
      localStorage.removeItem("tabs");
      localStorage.removeItem("pwdModalStatus");
      this.$state.socket.instance?.close();
      this.$state.socket.instance = null;
    },
    login(param: LoginRequest) {
      return new Promise((resolve, reject) => {
        (async () => {
          try {
            const publicKey = (await getPublicKey()).publicKey;
            const loginRes = await login({
              ...param,
              ...{
                password: enRSACrypt(param.password, publicKey),
              },
            });
            this.autoLogin(loginRes);
            resolve({});
          } catch {
            reject({});
          }
        })();
      });
    },
    logout() {
      return new Promise((resolve, reject) => {
        (async () => {
          try {
            await logout();
            this.autoLogout();
            router.push("/login");
            resolve({});
          } catch {
            reject();
          }
        })();
      });
    },
  },
});
