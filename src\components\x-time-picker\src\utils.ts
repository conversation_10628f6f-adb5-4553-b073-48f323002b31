import { formatDateTime } from "@/assets/ts/dateTime";

/**
 * 解析时间格式化占位符
 */
export function parseDateFormat(format: string) {
  // 匹配
  const matchs = format.match(/YYYY|YY|MM|M|DD|D|HH|H|hh|h|mm|m|ss|s/g) || [];

  // 结果
  const result = {
    year: matchs.includes("YYYY") || matchs.includes("YY"),
    month:
      matchs.includes("MMMM") ||
      matchs.includes("MMM") ||
      matchs.includes("MM") ||
      matchs.includes("M"),
    day: matchs.includes("DD") || matchs.includes("D"),
    hour:
      matchs.includes("HH") ||
      matchs.includes("H") ||
      matchs.includes("hh") ||
      matchs.includes("h"),
    minute: matchs.includes("mm") || matchs.includes("m"),
    second: matchs.includes("ss") || matchs.includes("s"),
    year_month_day: false,
    hour_minute_second: false,
    hour_minute: false,
  };

  // 时不存在，分，秒也设置为不存在
  if (result.hour === false) {
    result.minute = false;
    result.second = false;
  }

  // 分不存在，秒也设置为不存在
  if (result.minute === false) {
    result.second = false;
  }

  // 年月日都存在
  result.year_month_day = result.year && result.month && result.day;

  // 时分存在
  result.hour_minute = result.hour && result.minute;

  // 时分秒存在
  result.hour_minute_second = result.hour && result.minute && result.second;

  return result;
}

/**
 * 解析时间
 */
export function parseTime(time: string) {
  const result = {
    hour: undefined as number | undefined,
    minute: undefined as number | undefined,
    second: undefined as number | undefined,
  };

  // 赋值的键
  let key = "";

  for (let i = 0; i < time.length; i++) {
    const char = time[i];
    const num = parseInt(char);
    if (Number.isNaN(num)) {
      key = "";
    } else {
      if (result.hour === undefined) {
        result.hour = num;
        key = "hour";
      } else if (key === "hour") {
        result.hour = Number(result.hour + "" + num);
        key = "";
      } else if (result.minute === undefined) {
        result.minute = num;
        key = "minute";
      } else if (key === "minute") {
        result.minute = Number(result.minute + "" + num);
        key = "";
      } else if (result.second === undefined) {
        result.second = num;
        key = "second";
      } else if (key === "second") {
        result.second = Number(result.second + "" + num);
        key = "";
      }
    }
  }

  return [result.hour, result.minute, result.second];
}

/**
 * 值转换为小时分钟秒
 */
export function valueToTime(value: string | undefined, format: string) {
  const result = {} as any;

  if (!value) return result;

  const formatParse = parseDateFormat(format);

  const [hour, minute, second] = parseTime(value);

  if (formatParse.hour) {
    result.hour = hour === undefined ? 0 : hour;
  }
  if (formatParse.minute) {
    result.minute = minute === undefined ? 0 : minute;
  }
  if (formatParse.second) {
    result.second = second === undefined ? 0 : second;
  }

  return result;
}

/**
 * 合并小时分钟秒
 */
export function mergeHourMinuteSecond(time: any, format: string) {
  let value = "1970-01-01 ";

  if (time.hour === undefined) {
    value = value + "00";
  } else {
    value = value + time.hour.toString().padStart(2, "0");
  }

  if (time.minute === undefined) {
    value = value + ":00";
  } else {
    value = value + ":" + time.minute.toString().padStart(2, "0");
  }

  if (time.second === undefined) {
    value = value + ":00";
  } else {
    value = value + ":" + time.second.toString().padStart(2, "0");
  }

  return formatDateTime(value, format);
}

/**
 * 比较时间
 */
export function compareTime(t1: string, t2: string) {
  const v1 = parseTime(t1);
  const v2 = parseTime(t2);
  const len = Math.max(v1.length, v2.length);

  while (v1.length < len) {
    v1.push(0);
  }
  while (v2.length < len) {
    v2.push(0);
  }

  for (let i = 0; i < len; i++) {
    const num1 = v1[i] || 0;
    const num2 = v2[i] || 0;

    if (num1 > num2) {
      return 1;
    } else if (num1 < num2) {
      return -1;
    }
  }

  return 0;
}

/**
 * 生成时间列
 */
export function genTimeColumns({
  data,
  value,
  partial,
  format,
  disabledTime,
  hourStep,
  minuteStep,
  secondStep,
  hideDisabledOptions,
}: any) {
  const formatParse = parseDateFormat(format);

  const disabled = {
    hours: [],
    minutes: [],
    seconds: [],
  };

  if (disabledTime) {
    const { disabledHours, disabledMinutes, disabledSeconds } = disabledTime(
      value,
      partial
    );
    if (disabledHours) {
      disabled.hours = disabledHours();
    }
    if (disabledMinutes) {
      disabled.minutes = disabledMinutes();
    }
    if (disabledSeconds) {
      disabled.seconds = disabledSeconds();
    }
  }

  if (formatParse.hour) {
    genColumn({
      data,
      disabled,
      step: hourStep,
      hideDisabledOptions,
      key: "hours",
      steps: 24,
    });
  }
  if (formatParse.minute) {
    genColumn({
      data,
      disabled,
      step: minuteStep,
      hideDisabledOptions,
      key: "minutes",
      steps: 60,
    });
  }
  if (formatParse.second) {
    genColumn({
      data,
      disabled,
      step: secondStep,
      hideDisabledOptions,
      key: "seconds",
      steps: 60,
    });
  }
}

/**
 * 生成列
 */
export function genColumn({
  data,
  disabled,
  step,
  hideDisabledOptions,
  key,
  steps,
}: any) {
  const length = Math.ceil(steps / step);
  const items = Array.from({ length }, (_, i) => i);

  if (hideDisabledOptions) {
    data[key] = items
      .filter((item) => !disabled[key].includes(item))
      .map((item: any) => ({
        value: item,
        label: item.toString().padStart(2, "0"),
      }));
  } else {
    data[key] = items.map((item: any) => ({
      value: item,
      label: item.toString().padStart(2, "0"),
      disabled: disabled[key].includes(item),
    }));
  }
}
