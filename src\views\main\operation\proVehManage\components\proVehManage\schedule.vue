<template>
  <x-drawer
    :title="$t('scheduleSetting')"
    :visible="props.show"
    :btnOption="{
      position: 'center',
      confirm: $t('syncVehicle'),
    }"
    bodyPadding="0"
    :maskClosable="false"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    width="85%"
  >
    <div
      class="schedule"
      ref="scheduleRef"
    >
      <div class="schedule-top">
        <x-button
          type="highlightWhite"
          icon="button_add"
          disabledIcon="button_add_disabled"
          :text="$t('addTask')"
          :disabled="templateId !== '' || isEditing"
          @click="addTask"
          style="margin-right: 12px"
        />
        <div class="schedule-top-right">
          <div class="selector-label">{{ $t("selectTemplate") }}</div>
          <x-select
            v-model:value="templateId"
            :options="formOptions.scheduleTemplate"
            :popupContainer="scheduleRef"
            allow-clear
            :placeholder="$t('PSelectTemplate')"
            style="width: 280px"
            @update:value="onChangeTemplate"
          />
        </div>
      </div>
      <div class="schedule-bottom">
        <!-- 模板任务 -->
        <template v-if="templateId">
          <div class="schedule-bottom-title">{{ $t("templateTask") }}</div>
          <div class="schedule-bottom-content">
            <template
              v-for="(task, index) in templateTaskList"
              :key="index"
            >
              <TaskPreview
                :task="task"
                :showSwitchStatus="true"
                :popupContainer="scheduleRef"
                bgType="light"
              />
            </template>
          </div>
        </template>
        <!-- 现有任务/新增任务 -->
        <template v-else>
          <template v-if="newTaskList.length > 0">
            <div class="schedule-bottom-title">{{ $t("addTask") }}</div>
            <div class="schedule-bottom-content">
              <template
                v-for="(task, index) in newTaskList"
                :key="index"
              >
                <TaskItem
                  :task="task"
                  :showSwitch="true"
                  :formOptions="formOptions"
                  :popupContainer="scheduleRef"
                  @deleteTask="deleteTask(index, 'new')"
                  @editTask="editTask(index, 'new')"
                  @confirmTask="confirmTask(index, 'new')"
                />
              </template>
            </div>
          </template>
          <template v-if="existingTaskList.length > 0">
            <div class="schedule-bottom-title">{{ $t("existingTasks") }}</div>
            <div class="schedule-bottom-content">
              <template
                v-for="(task, index) in existingTaskList"
                :key="index"
              >
                <TaskItem
                  :task="task"
                  :formOptions="formOptions"
                  :popupContainer="scheduleRef"
                  :showSwitch="true"
                  @deleteTask="deleteTask(index, 'existing')"
                  @editTask="editTask(index, 'existing')"
                  @confirmTask="confirmTask(index, 'existing')"
                />
              </template>
            </div>
          </template>
        </template>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  getCurrentSchedule,
  getRouteTempListByArea,
  getStationListByArea,
  getScheduleTempDetail,
  saveCarSchedule,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import xModal from "@/components/x-modal";
import TaskPreview from "../taskPreview.vue";
import TaskItem from "../taskItem.vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { createNewTask, getRelName, type TaskItemType } from "../utils";
import { getTemplateVersionName } from "@/assets/ts/config";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  vehicleId: {
    type: String,
    required: true,
  },
  proAreaId: {
    type: String,
    required: true,
  },
});

const scheduleRef = ref<any>();

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const templateId = ref("");

// 车上现有任务
const existingTaskList = ref([] as TaskItemType[]);
// 新增任务
const newTaskList = ref([] as TaskItemType[]);
// 模板任务
const templateTaskList = ref([] as TaskItemType[]);

const formOptions = reactive({
  scheduleTemplate: [] as any[],
  routeTemplate: [] as any[],
  chargingStation: [] as any[],
  garbageStation: [] as any[],
  wateringStation: [] as any[],
  parkingStation: [] as any[],
});

// 控制只能有一个任务处于编辑状态
const isEditing = ref(false);

// 添加任务
const addTask = debounce(() => {
  newTaskList.value.push({ ...createNewTask(), switchOn: 0 });
  isEditing.value = true;
});

// 保存任务
const confirmTask = debounce((index: number, type: string) => {
  const task = type === "new" ? newTaskList.value[index] : existingTaskList.value[index];
  task.editStatus = false;
  isEditing.value = false;
});

// 编辑任务
const editTask = (index: number, type: string) => {
  const task = type === "new" ? newTaskList.value[index] : existingTaskList.value[index];
  if (isEditing.value) {
    Message("error", $t("PEditBeforeConfirm"));
    return;
  }
  task.editStatus = true;
  isEditing.value = true;
};

// 删除任务
const deleteTask = (index: number, type: string) => {
  const taskList = type === "new" ? newTaskList.value : existingTaskList.value;
  if (taskList[index].editStatus) {
    isEditing.value = false;
  }
  taskList.splice(index, 1);
};

// 选择模板
const onChangeTemplate = (tempId: string) => {
  const selectedTemplate = formOptions.scheduleTemplate.find((template) => template.value === tempId);
  templateTaskList.value = selectedTemplate?.taskList || [];
};

// 同步车
const formSubmit = () => {
  if (!templateId.value && isEditing.value) {
    Message("error", $t("PSaveTaskBeforeSync"));
    return;
  }
  xModal.confirm({
    title: $t("confirmSyncTaskToVehicle"),
    confirm() {
      const _arrangement = templateId.value ? templateTaskList.value : newTaskList.value.concat(existingTaskList.value);
      const param = {
        proAreaId: props.proAreaId,
        vehicleId: props.vehicleId,
        arrangement: _arrangement.map((v) => ({
          ...v,
          stationType: v.stationType === -1 ? null : v.stationType,
          scheduleType: v.scheduleType === 4 ? v._switchType : v.scheduleType,
          switchOn: v.switchOn ?? 0,
          _switchType: null,
        })),
      };
      return saveCarSchedule(param).then(() => {
        Message("success", $t("syncSuccess"));
        emits("update:show", false);
      });
    },
  });
};

/**
 * 基础数据获取
 */
// 获取当前已有任务列表
const getScheduleList = async () => {
  const result = await getCurrentSchedule({
    id: props.vehicleId,
  });
  existingTaskList.value =
    result.scheduleList?.map((task: any) => {
      return {
        ...task,
        relName: getRelName(formOptions, task.relId, task.scheduleType),
        executeDayType: task.executeDays && !task.executeDays.includes("0") ? "weekly" : "daily",
        editStatus: false,
        isNew: false,
        coverCleanType: task.relId !== "0" ? 2 : 1,
        executeTimes: task.executeTimes ?? 1,
        scheduleType: task.scheduleType === 6 ? 4 : task.scheduleType,
        stationType: task.stationType ? task.stationType : -1,
        _switchType: [4, 6].includes(task.scheduleType ?? 1) ? task.scheduleType : null,
      } as TaskItemType;
    }) || [];
};

/** 获取站点列表及模版列表 */
const getOptinsDataList = async () => {
  const resArr = await Promise.all([
    getRouteTempListByArea({ proAreaId: props.proAreaId }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 1 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 2 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 3 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 4 }),
  ]);
  const [routeTemplateList, garbageStationList, chargingStationList, wateringStationList, parkingStationList] =
    resArr.map((v) =>
      v.map((item) => ({ ...item, label: item.relName + getTemplateVersionName(item.version), value: item.relId }))
    );
  formOptions.routeTemplate = routeTemplateList;
  formOptions.garbageStation = garbageStationList;
  formOptions.chargingStation = chargingStationList;
  formOptions.wateringStation = wateringStationList;
  formOptions.parkingStation = parkingStationList;
};

/**
 * 初始化
 */
watch(
  () => props.show,
  (newV) => {
    if (newV) {
      templateId.value = "";
      existingTaskList.value = [];
      newTaskList.value = [];
      templateTaskList.value = [];
      isEditing.value = false;
      getScheduleList();
      getOptinsDataList();
    }
  }
);
</script>

<style lang="scss" scoped>
.schedule {
  padding: 20px;
  &-top {
    @include ct-f(y);
    &-right {
      display: flex;
      background: #e0e4fc;
      border-radius: 4px 0 0 4px;
      .selector-label {
        @include wh(70px, 32px);
        @include sc(14px, #383838);
        line-height: 32px;
        text-align: center;
      }
    }
  }
  &-bottom {
    display: flex;
    flex-direction: column;
    padding-bottom: 50px;
    &-title {
      margin: 10px 0 5px 0;
      @include sc(14px, #555555);
    }
  }
  :deep(.button-confirm) {
    margin: 0 auto;
  }
  :deep(.popover__body) {
    width: 140px;
    justify-content: center;
  }
  :deep(.time-panel) {
    height: 180px;
  }
}
</style>
