<template>
  <div class="empty-state">
    <el-empty
      :image="emptyBg"
      :description="description"
    >
      <slot />
    </el-empty>
  </div>
</template>

<script lang="ts" setup>
import emptyBg from "@/assets/images/table_nodata.png";
defineProps({
  description: {
    type: String,
    default: "暂无数据",
  },
});
</script>

<style lang="scss" scoped>
.empty-state {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  box-sizing: border-box;
  padding: 0 20px;
  :deep(.el-empty__image) {
    width: 100% !important;
  }
}
</style>
