<template>
  <div
    v-if="true"
    ref="rootRef"
    class="component"
  >
    <div class="component-left">
      <div
        :class="['component-left-item', { 'is-active': componentModel.active === item.value }]"
        v-for="item in componentModel.options"
        :key="item.value"
        @click="handleLeftItem(item)"
      >
        {{ item.label }}
      </div>
    </div>
    <div class="component-right">
      <h1>组件-[{{ componentModel.active }}]</h1>
      <div
        v-if="componentModel.active === 'datetime'"
        class="component-right"
        ref="pageTableLayoutRef"
      >
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 760px; display: flex"
          >
            <div class="middle-left-item-label">日期【DatePicker】</div>
            <div class="middle-left-item-value">
              <DatePicker
                :popupContainer="pageTableLayoutRef"
                v-model:value="searchForm.date1"
                format="YYYY-MM-DD"
                allowClear
                showSelect
              />
            </div>
            <div class="middle-left-item-value">
              <DatePicker
                :popupContainer="pageTableLayoutRef"
                v-model:value="searchForm.date2"
                format="YYYY-MM-DD"
                allowClear
                showToday
              />
            </div>
            <div class="middle-left-item-value">
              <DatePicker
                :popupContainer="pageTableLayoutRef"
                v-model:value="searchForm.date3"
                format="YYYY-MM-DD HH:mm:ss"
                allowClear
                showTime
                showNow
              />
            </div>
          </div>
        </div>
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 1000px; display: flex"
          >
            <div class="middle-left-item-label">日期范围【DateRangePicker】</div>
            <div class="middle-left-item-value">
              <DateRangePicker
                :popupContainer="pageTableLayoutRef"
                v-model:value="searchForm.dateRange1"
                format="YYYY年MM月DD日 HH小时mm分钟ss秒"
                allowClear
                showTime
                showNow
              />
            </div>
            <div class="middle-left-item-value">
              <DateRangePicker
                :popupContainer="pageTableLayoutRef"
                v-model:value="searchForm.dateRange2"
                format="YYYY-MM-DD"
                :disabledTime="disabledTimeFn"
                :disabledDate="disabledDateFn"
                allowClear
                showTime
              />
            </div>
          </div>
        </div>
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 480px; display: flex"
          >
            <div class="middle-left-item-label">时间【TimePicker】</div>
            <div class="middle-left-item-value">
              <TimePicker
                v-model:value="searchForm.time"
                :popupContainer="pageTableLayoutRef"
                format="HH小时mm分钟ss秒"
              />
            </div>
          </div>
        </div>
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 480px; display: flex"
          >
            <div class="middle-left-item-label">时间范围【TimeRangePicker】</div>
            <div class="middle-left-item-value">
              <TimeRangePicker
                v-model:value="searchForm.timeRange"
                :popupContainer="pageTableLayoutRef"
              />
            </div>
          </div>
        </div>
      </div>
      <div
        v-else-if="componentModel.active === 'icon'"
        class="icon-wrapper"
      >
        <div
          class="icon-wrapper-item"
          v-for="icon in iconList"
          :key="icon"
          @click="handleIconClick(icon)"
        >
          <div style="width: 50px; height: 50px; display: flex; align-items: center; justify-content: center">
            <XIcon
              :width="50"
              :height="50"
              :name="icon"
            />
          </div>
          <div style="margin-top: 5px; line-break: anywhere; text-align: center">{{ icon }}</div>
        </div>
      </div>
      <ButtonDemo v-else-if="componentModel.active === 'button'"></ButtonDemo>
      <xSwitch
        v-else-if="componentModel.active === 'switch'"
        :modelValue="true"
      >
        <template #checkedChildren>
          <span>{{ "清扫" }}</span>
        </template>
        <template #unCheckedChildren>
          <span style="color: #aaa">{{ "不清扫" }}</span>
        </template>
      </xSwitch>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { TimePicker, TimeRangePicker } from "@/components/x-time-picker";
import { DatePicker, DateRangePicker } from "@/components/x-date-picker";
import type { DateType } from "@/assets/ts/dateTime";
import { reactive, ref } from "vue";
import XIcon from "@/components/x-icon.vue";
import xMessage from "@/components/x-message";
import ButtonDemo from "./buttonDemo.vue";
import xSwitch from "@/components/x-switch.vue";
import { debounce } from "@/assets/ts/utils";

const rootRef = ref();
const componentModel = reactive({
  active: "button",
  options: [
    { label: "按钮", value: "button" },
    { label: "日期时间选择器", value: "datetime" },
    { label: "图标", value: "icon" },
    { label: "开关", value: "switch" },
  ],
});
const handleLeftItem = (item: typeof componentModel.options[number]) => {
  componentModel.active = item.value;
};

/** 获取本地所有icon */
const iconList = Object.keys(import.meta.glob("/src/assets/icons/**"))
  .map((v) => v.match(/\/([^/]+)\.svg$/)?.at(1) || "")
  .filter(Boolean);

const handleIconClick = debounce(async (icon: string) => {
  if (navigator.clipboard && navigator.permissions) {
    await navigator.clipboard.writeText(icon);
  } else {
    const textArea = document.createElement("textArea") as HTMLTextAreaElement;
    textArea.value = icon;
    textArea.style.width = "0";
    textArea.style.position = "fixed";
    textArea.style.left = "-999px";
    textArea.style.top = "10px";
    textArea.setAttribute("readonly", "readonly");
    document.body.appendChild(textArea);

    textArea.select();
    document.execCommand("copy", true);
    document.body.removeChild(textArea);
  }
  xMessage("success", "复制成功！");
  console.log("%c 复制成功:", "color:orange;", icon);
});

const pageTableLayoutRef = ref();
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  opDate: ["", ""],
  time: "11小时55分钟50秒",
  timeRange: ["", ""],
  date1: "",
  date2: "",
  date3: "",
  dateRange1: ["", ""],
  dateRange2: ["", ""],
  selectOne: "",
  selectTwo: "",
  selectThree: "",
});

const disabledTimeFn = () => {
  return {
    disabledHours: () => [22],
    disabledMinutes: () => [22],
    disabledSeconds: () => [22],
  };
};

const disabledDateFn = (day: DateType) => {
  if (day.value === 25) {
    return true;
  }
  return false;
};
</script>

<style lang="scss" scoped>
.component {
  min-height: 100vh;
  display: flex;
  column-gap: 10px;
  h1 {
    font-size: 20px;
    margin-bottom: 10px;
  }
  &-left {
    width: 10vw;
    border-right: 1px solid #aaa;
    padding: 10px;
    box-sizing: border-box;
    @include scrollbar(y, 4px) {
      overflow-y: auto;
      height: 100vh;
    }
    &-item {
      border-radius: 5px;
      height: 30px;
      display: flex;
      align-items: center;
      padding: 5px;
      cursor: pointer;
      &.is-active,
      &:hover {
        background-color: #5964fb;
        color: #fff;
      }
    }
  }
  &-right {
    flex: 1;
  }
}
.icon-wrapper {
  display: flex;
  flex-wrap: wrap;
  border-left: 1px solid #aaa;
  border-top: 1px solid #aaa;
  @include scrollbar(y, 4px) {
    overflow-y: auto;
    max-height: 90vh;
  }
  &-item {
    width: calc(100% / 20);
    min-height: 100px;
    padding: 10px;
    border-right: 1px solid #aaa;
    border-bottom: 1px solid #aaa;
    display: flex;
    align-items: center;
    flex-direction: column;
    flex-wrap: wrap;
    cursor: pointer;
    background-color: rgba(135, 207, 235, 0.5);
  }
}
.middle-left-items {
  display: flex;
  margin-top: 14px;

  &:first-child {
    margin-top: 0;
  }
}
.middle-left-item {
  margin-left: 16px;

  &:first-child {
    margin-left: 0;
  }

  &-label {
    @include sc(14px, #5e5e5e) {
      line-height: 20px;
      padding: 6px 8px 6px 0;
    }
  }

  &-value {
    flex: 1;
  }
}
</style>
