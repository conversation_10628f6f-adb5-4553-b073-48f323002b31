import AMapLoader from "@amap/amap-jsapi-loader";

type AMapLoaderOptions = Partial<Parameters<typeof AMapLoader.load>[0] & { securityJsCode?: string }>;

/**
 * @desc 加载高德地图 loader, 有 options 时会重新加载AMap实例
 * @param options.plugins 插件, 默认值为 `["AMap.PolygonEditor",""]`
 */
export function lazyAMapApiLoader(options?: AMapLoaderOptions) {
  return new Promise<typeof AMap>((resolve, reject) => {
    const isReload = !!Object.keys(options ?? {}).length; // 针对有 options 有属性时, 重新加载AMap实例
    if (window.AMap && !isReload) {
      resolve(window.AMap);
      return;
    }
    // @ts-ignore
    window._AMapSecurityConfig = {
      /** 安全密钥 */
      securityJsCode: options?.securityJsCode ?? import.meta.env.VITE_APP_AMAP_SECURITY_JS_CODE,
    };
    /** 插件 */
    const plugins = Array.from(
      new Set([
        "AMap.PolygonEditor",
        "AMap.PolylineEditor",
        "AMap.MouseTool",
        "AMap.MoveAnimation",
        "AMap.PlaceSearch",
        "AMap.AutoComplete",
        "AMap.Geocoder",
        "AMap.Scale",
        "AMap.HeatMap",
        ...(options?.plugins ?? []),
      ])
    );
    AMapLoader.load({
      key: import.meta.env.VITE_APP_AMAP_KEY,
      version: "2.0",
      plugins: plugins,
      ...options,
    })
      .then(resolve)
      .catch(reject);
  });
}
