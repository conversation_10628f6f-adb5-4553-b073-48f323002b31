import { describe, it, expect } from "vitest";
import { mount } from '@vue/test-utils';
import XTitle from '@/components/x-title.vue';

const wrapper = mount(XTitle);

describe('XTitle.vue', () => {
  it('正确渲染组件生成快照', () => {
    expect(wrapper.html()).toMatchSnapshot();
  });

  it('当icon属性值为默认值时正确渲染', () => {
    expect(wrapper.find('h3 use').attributes('href')).toBe('#icon-title_flag');
    expect(wrapper.find('.triangle').exists()).toBe(false);
  });

  it('当icon属性值为flag时正确渲染', async () => {
    await wrapper.setProps({ icon: 'flag' });
    expect(wrapper.find('h3 use').attributes('href')).toBe('#icon-title_flag');
    expect(wrapper.find('.triangle').exists()).toBe(false);
  });

  it('当icon属性值为triangle时正确渲染', async () => {
    await wrapper.setProps({ icon: 'triangle' });
    const triangleIcon = wrapper.find('.triangle');
    expect(triangleIcon.exists()).toBe(true);
    expect(wrapper.find('.title_flag').exists()).toBe(false);
  });

  it('正确渲染插槽内容', () => {
    const text = 'Test Content';
    const wrapper = mount(XTitle, { 
      slots: { 
        default: text,
      } 
    });
    expect(wrapper.text()).toMatch(text);
  });
});
