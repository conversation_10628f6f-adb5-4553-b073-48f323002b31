<template>
  <section class="button">
    <h2>基础</h2>
    <div class="button-content">
      <x-button
        text="按钮"
        v-for="type in buttonTypes"
        :key="type"
        :type="type"
      />
    </div>
    <h2>禁用</h2>
    <div class="button-content">
      <x-button
        text="按钮"
        v-for="type in buttonTypes"
        :key="type"
        disabled
        :type="type"
      />
    </div>
    <h2>加载中【loading】</h2>
    <div class="button-content">
      <x-button
        text="按钮"
        v-for="type in buttonTypes"
        :key="type"
        loading
        :type="type"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import XButton from "@/components/x-button.vue";

const buttonTypes = [
  "paleBlue",
  "blue",
  "linearBlue",
  "paleGreen",
  "green",
  "white",
  "highlightWhite",
  "orange",
  "red",
] as any;
</script>
<style lang="scss" scoped>
.button {
  h2 {
    font-size: 16px;
    margin: 10px 0;
  }
  &-content {
    display: flex;
    column-gap: 10px;
  }
}
</style>
