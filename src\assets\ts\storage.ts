import CryptoJS from "crypto-js";

const config = {
  isEncrypt: true,
};

const SECRET_KEY = CryptoJS.enc.Utf8.parse("1234123412ABCDEF");
const SECRET_IV = CryptoJS.enc.Utf8.parse("ABCDEF1234123412");

const encrypt = (data: string) => {
  const parseData = CryptoJS.enc.Utf8.parse(data);
  const encrypted = CryptoJS.AES.encrypt(parseData, SECRET_KEY, {
    iv: SECRET_IV,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return encrypted.ciphertext.toString(CryptoJS.enc.Base64);
};

const decrypt = (data: string) => {
  const decrypt = CryptoJS.AES.decrypt(data, SECRET_KEY, {
    iv: SECRET_IV,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypt.toString(CryptoJS.enc.Utf8);
};

const isJson = (value: any) => {
  if (Object.prototype.toString.call(value) === "[object String]") {
    try {
      const obj = JSON.parse(value);
      const objType = Object.prototype.toString.call(obj);
      return objType === "[object Object]" || objType === "[object Array]";
    } catch (e) {
      return false;
    }
  }
  return false;
};

export const setLocalStorage = (key: string, value: any, expire = 0) => {
  const data = JSON.stringify({
    data: value,
    time: Date.now(),
    expire,
  });
  localStorage.setItem(key, config.isEncrypt ? encrypt(data) : data);
};

export const getLocalStorage = (key: string) => {
  const value = localStorage.getItem(key);
  if (!value || JSON.stringify(value) === "null") {
    return null;
  }

  const storage = config.isEncrypt
    ? JSON.parse(decrypt(value))
    : JSON.parse(value);

  if (
    Date.now() > storage.time + storage.expire * 1000 &&
    storage.expire !== 0
  ) {
    localStorage.removeItem(key);
    return null;
  }

  return isJson(storage.data) ? JSON.parse(storage.data) : storage.data;
};
