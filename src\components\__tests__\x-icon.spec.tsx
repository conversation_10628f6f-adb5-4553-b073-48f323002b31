import { describe, it, expect, afterEach, vi } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue'
import XIcon from '@/components/x-icon.vue';

const baseProps = { name: 'loading' }

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = mount(XIcon,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = mount(XIcon,{
      props: {
        ...baseProps,
        className: 'className',
        width: '40px',
        height: '20px',
        color: 'red',
      },
    });
    expect(wrapper.classes().includes('className')).toBe(true);
    expect(wrapper.attributes().style).toBe('width: 40px; height: 20px; color: red;');
  });
});

describe('Events', () => {
  it('验证activeName', () => {
    const wrapper = mount(XIcon, {
      props: {
        ...baseProps,
        activeName: 'activeName',
      }
    });
    wrapper.trigger('mousedown');
    expect(wrapper.vm.isActive).toBe(true);
    expect(wrapper.find('use').attributes().href).toBe('#icon-loading');
    nextTick(() => {
      expect(wrapper.find('use').attributes().href).toBe('#icon-activeName');
    });
  });
});