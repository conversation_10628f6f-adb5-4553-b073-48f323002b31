// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 xModal.function 快照 1`] = `
<div
  class="Modal"
>
  <transition-stub
    appear="false"
    css="true"
    data-v-95d2109e=""
    name="modal"
    persisted="true"
  >
    <div
      class="modal"
      data-v-95d2109e=""
      style="display: none;"
    >
      <transition-stub
        appear="false"
        css="true"
        data-v-95d2109e=""
        name="modal-mask"
        persisted="true"
      >
        <div
          class="modal-mask"
          data-v-95d2109e=""
          style="display: none;"
        />
      </transition-stub>
      <transition-stub
        appear="false"
        css="true"
        data-v-95d2109e=""
        name="modal-content"
        persisted="true"
      >
        <div
          class="modal-content"
          data-v-95d2109e=""
          style="width: 424px; display: none;"
        >
          <div
            class="content-header"
            data-v-95d2109e=""
          >
            <div
              class="content-header-title"
              data-v-95d2109e=""
            >
              <svg
                aria-hidden="true"
                class="x-icon title-icon"
                data-v-6c3de310=""
                data-v-95d2109e=""
              >
                <use
                  data-v-6c3de310=""
                  xlink:href="#icon-modal_warn"
                />
              </svg>
              <span
                class="title-text"
                data-v-95d2109e=""
              >
                confirm的标题
              </span>
            </div>
            <span
              class="content-header-close"
              data-v-95d2109e=""
            >
              ✕
            </span>
          </div>
          <div
            class="content-body"
            data-v-95d2109e=""
          >
            <div
              class="content-body-slot"
              data-v-95d2109e=""
            >
              <!--v-if-->
            </div>
            <!--v-if-->
          </div>
        </div>
      </transition-stub>
    </div>
  </transition-stub>
</div>
`;

exports[`Snapshot > 正确渲染 默认快照 1`] = `
<div
  class="x-modal"
  data-v-248baf58=""
>
  <transition-stub
    appear="false"
    css="true"
    data-v-248baf58=""
    name="x-modal-mask"
    persisted="true"
  >
    <div
      class="x-modal-mask"
      data-v-248baf58=""
    />
  </transition-stub>
  <transition-stub
    appear="false"
    css="true"
    data-v-248baf58=""
    name="x-modal-content"
    persisted="true"
  >
    <div
      class="x-modal-content"
      data-v-248baf58=""
      style=""
    >
      <div
        class="content-header"
        data-v-248baf58=""
      >
        <div
          class="content-header-title"
          data-v-248baf58=""
        >
          titleStr
        </div>
        <span
          class="content-header-close"
          data-v-248baf58=""
        >
          ✕
        </span>
      </div>
      <div
        class="content-body"
        data-v-248baf58=""
        style=""
      >
        <div
          class="content-body-slot"
          data-v-248baf58=""
        >
          <!--v-if-->
          
          
        </div>
        <div
          class="content-body-button"
          data-v-248baf58=""
          style="justify-content: end;"
        >
          <button
            class="x-button white"
            data-v-248baf58=""
            data-v-e1f75faa=""
            style="height: 32px; line-height: 32px; margin-right: 9px;"
            type="button"
          >
            
            <!--v-if-->
            <span
              class="x-button-text"
              data-v-e1f75faa=""
            >
              cancelStr
            </span>
            
          </button>
          <button
            class="x-button blue"
            data-v-248baf58=""
            data-v-e1f75faa=""
            style="height: 32px; line-height: 32px;"
            type="button"
          >
            
            <!--v-if-->
            <span
              class="x-button-text"
              data-v-e1f75faa=""
            >
              confirmStr
            </span>
            
          </button>
        </div>
      </div>
    </div>
  </transition-stub>
</div>
`;
