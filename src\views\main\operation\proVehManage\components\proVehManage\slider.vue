<template>
  <section class="x-slider" ref="contentRef">
    <div class="x-slider-container">
      <div
        class="slider"
        @mousedown="onHandleSliderClick"
        :style="{
          width: `${barWidth + 10}px`,
        }"
      >
        <div class="slider-rail"></div>
        <div
          class="slider-track"
          :style="{
            width: `${
              barWidth * ((base.percent - props.min) / (props.max - props.min))
            }px`,
          }"
        />
        <div
          class="slider-handle"
          :style="{
            top: '-3px',
            left: `${
              barWidth *
                ((base.percent - props.min) / (props.max - props.min)) -
              2
            }px`,
          }"
          @mousedown="onHandleDragStart"
        />
      </div>
    </div>
    <div class="x-slider-info">{{ Math.round(base.percent) }}%</div>
  </section>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";

const props = defineProps({
  // 百分比 0-100
  value: {
    type: Number,
    default: 0,
  },
  // 最小值
  min: {
    type: Number,
    default: 0,
  },
  // 最大值
  max: {
    type: Number,
    default: 100,
  },
  // 滑条宽度
  barWidth: {
    type: Number,
    default: 200,
  },
});

const contentRef = ref<any>();
const barWidth = ref(props.barWidth);

const base = reactive({
  percent: props.value,
  isDragging: false,
});

watch(
  () => props.value,
  (newV) => {
    if (newV < props.min || newV > props.max) {
      base.percent = props.min;
    } else {
      base.percent = newV;
    }
  },
  {
    immediate: true,
  }
);

const emits = defineEmits(["change"]);

// 点击调整进度
const onHandleSliderClick = (event) => {
  const pageZoom = Number(
    getComputedStyle(document.querySelector(".main")!).zoom
  );
  const clientX = event.clientX / pageZoom;
  const railRect = contentRef.value.getBoundingClientRect();
  const clickX = clientX - railRect.left;
  let newPercent =
    (clickX / barWidth.value) * (props.max - props.min) + props.min;
  newPercent = Math.min(props.max, Math.max(props.min, newPercent));
  updateProgress(newPercent);
};

// 拖动调整进度
const onHandleDragStart = () => {
  base.isDragging = true;
  document.addEventListener("mousemove", onHandleDrag);
  document.addEventListener("mouseup", onHandleDragEnd);
};
const onHandleDragEnd = () => {
  base.isDragging = false;
  document.removeEventListener("mousemove", onHandleDrag);
  document.removeEventListener("mouseup", onHandleDragEnd);
};
const onHandleDrag = (event) => {
  if (base.isDragging) {
    const pageZoom = Number(
      getComputedStyle(document.querySelector(".main")!).zoom
    );
    const clientX = event.clientX / pageZoom;
    const railRect = contentRef.value.getBoundingClientRect();
    const dragX = clientX - railRect.left;
    let newPercent =
      (dragX / barWidth.value) * (props.max - props.min) + props.min;
    newPercent = Math.min(props.max, Math.max(props.min, newPercent));
    updateProgress(newPercent);
  }
};
const updateProgress = (newPercent: number) => {
  base.percent = newPercent;
  emits("change", Math.round(base.percent));
};
</script>

<style lang="scss" scoped>
.x-slider {
  display: flex;
  @include wh(100%, 30px);
  &-container {
    .slider {
      position: relative;
      display: inline-block;
      width: 100%;
      cursor: pointer;
      &-rail {
        @include wh(100%, 4px);
        border-radius: 10px;
        background: rgba(196, 196, 196, 0.4);
      }
      &-track {
        position: absolute;
        top: 0;
        height: 100%;
        border-radius: 10px;
        background: #5964fb;
      }
      &-handle {
        position: absolute;
        @include wh(10px, 10px);
        border-radius: 50%;
        background: #5964fb;
      }
    }
  }
  &-info {
    margin-left: 5px;
    line-height: 30px;
    @include sc(14px, #5062ec) {
      font-weight: 500;
    }
  }
}
</style>
