<template>
  <section
    id="amap"
    ref="amapRef"
  >
    <slot></slot>
    <carDetail
      v-model:show="carDetailReactive.show"
      :id="carDetailReactive.deviceId"
    />
  </section>
</template>
<script lang="ts" setup>
import { ref, onMounted, onUnmounted, watch, createVNode, render, reactive } from "vue";
import { useMainStore } from "@/stores/main";
import { connect } from "@/services/ws";
import {
  onBaseStatus,
  offBaseStatus,
  toggleProject,
  addCar,
  delCar,
  openChargingStatus,
  closeChargingStatus,
} from "@/services/wsapi";
import { liveCarSites } from "@/services/api";
import { sitesType } from "@/assets/ts/config";
import { mapZoom } from "@/services/wsconfig";
import AMapLoader from "@amap/amap-jsapi-loader";
import carMarker from "./carMarker.vue";
import InfoWindow from "./infoWindow.vue";
import workerMarker from "./workerMarker.vue";
import workerInfoWindow from "./workerInfoWindow.vue";
import SiteMarker from "./siteMarker.vue";
import SiteMarkerInfoWindow from "./siteMarkerInfoWindow.vue";
import carDetail from "./carDetail.vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
const props = withDefaults(
  defineProps<{
    proId: string;
    mapCenterId: string; // 用于当点击列表后定位到对应的 人员或车辆
  }>(),
  {}
);
const amapRef = ref<HTMLElement>();
useMainStore().loadUserInfo();

let isFirst = true;
onMounted(() => {
  // 使用onMounted原因：当从实时切回来时，需等实时地图onUnmounted执行完
  connect(() => {
    onBaseStatus();
    if (isFirst) {
      isFirst = false;
      watch(
        () => props.proId,
        (newV) => {
          toggleProject(newV);
        },
        { immediate: true }
      );
    } else {
      toggleProject(props.proId);
    }
  });
});
onUnmounted(() => {
  closeChargingStatus();
  socket.instance && offBaseStatus();
  // socket.instance && offAllStatus();
  socket.statusPop = false;
  socket.alarmStatusPop = false;
  // stopReq = true;
});

const mapInfo = {
  map: null as null | AMap.Map,
  Amap: undefined as unknown as typeof AMap,
  carMarkers: [] as AMap.Marker[], // 小车
  workerMarkers: [] as AMap.Marker[], // 人员
  siteMarkers: [] as AMap.Marker[], // 垃圾点 / 充电点 / 加水点 / 停车点
};
const { socket, updateScreenActiveCarId } = useMainStore();

lazyAMapApiLoader().then((AMap) => {
  mapInfo.map = new AMap.Map("amap", {
    zooms: [2, 26],
    mapStyle: "amap://styles/27e17e138bb7b2f02eb26243352889b2",
  });
  mapInfo.Amap = AMap;
  const scale = new AMap.Scale({
    position: {
      left: "10px",
      bottom: "50px",
    },
  });
  // @ts-ignore
  mapInfo.Amap.addControl(scale);
  watch(
    () => props.proId,
    (newV) => {
      if (mapInfo.siteMarkers.length) {
        mapInfo.siteMarkers.forEach((site) => mapInfo.map?.remove(site));
        mapInfo.siteMarkers = [];
      }
      liveCarSites(newV).then((res) => {
        addSiteMarker([
          ...res.map(
            ({
              stationType,
              latitude,
              longitude,
              stationName,
              stationPic,
              id,
              proAreaId,
              stationParkingRelEntityList,
            }) => ({
              icon: sitesType.find((site) => site.value === stationType)?.icon || "",
              point: [longitude, latitude],
              name: stationName,
              stationPic,
              id,
              areaId: proAreaId,
              stationList: stationParkingRelEntityList,
            })
          ),
        ]);
      });
    },
    {
      immediate: true,
    }
  );
  watch(
    () => socket.baseStatus,
    (newV) => {
      if (newV) {
        updateCarMarker(newV);
      }
    }
  );
  watch(
    () => socket.screenWorkers,
    (newV) => {
      if (newV) {
        updateWorkerMarker(newV);
      }
    }
  );
  watch(
    () => props.mapCenterId,
    (newV) => {
      if (newV) {
        const allItem = [
          ...(socket.baseStatus?.map(({ deviceId, longitude, latitude }) => ({
            key: deviceId,
            longitude,
            latitude,
          })) || []),
          ...(socket.screenWorkers?.map(({ userName, lon, lat }) => ({
            key: userName,
            longitude: lon,
            latitude: lat,
          })) || []),
        ];
        const target = allItem.find(({ key }) => key == newV);
        mapInfo.map?.setZoomAndCenter(mapZoom, [target?.longitude, target?.latitude]);
      }
    }
  );
});

const addSiteMarker = (sites: any[]) => {
  sites.forEach((site) => {
    const markerDiv = document.createElement("div");
    const newMarker = new mapInfo.Amap.Marker({
      content: markerDiv,
      zIndex: 10,
      anchor: "bottom-center",
      // offset: [0, -15],
      position: site.point,
      cursor: "pointer",
      draggable: false,
      bubble: true,
    });
    render(
      createVNode(SiteMarker, {
        focus: false,
        icon: site.icon,
      }),
      markerDiv
    );
    newMarker.on("click", async () => {
      const infoWindowDiv = document.createElement("div");
      const infoWindow = new mapInfo.Amap.InfoWindow({
        isCustom: true,
        content: infoWindowDiv,
        position: site.point,
        offset: [0, -38],
      });
      render(
        createVNode(SiteMarkerInfoWindow, {
          name: site.name,
          popupContainer: amapRef.value,
          window: infoWindow,
          type: site.icon,
          id: site.id,
          areaId: site.areaId,
          stationPic: site.stationPic,
          stationType: site.stationType,
          stationList: site.stationList.map((station) => ({
            ...station,
            status: "idle",
            warn: false,
          })),
        }),
        infoWindowDiv
      );
      infoWindow.open(mapInfo.map, site.point);
      if (site.icone === "map_battery") {
        const stationNoArr = site.stationList.map((v) => v.chargingStationNo);
        openChargingStatus(stationNoArr);
      }
    });
    mapInfo.map.add(newMarker);
    mapInfo.siteMarkers.push(newMarker);
  });
};

const updateWorkerMarker = (data: typeof socket.screenWorkers) => {
  const map: any = mapInfo.map;
  const Amap: any = mapInfo.Amap;

  // 当切换项目时，将原项目有的人员 但切换项目没的人员去掉
  let newWorkerMarkers = [] as typeof mapInfo.workerMarkers;
  for (const marker of mapInfo.workerMarkers) {
    if (data?.some((v) => v.userName === marker.getExtData().key)) {
      newWorkerMarkers.push(marker);
    } else {
      map.remove(marker);
    }
  }
  mapInfo.workerMarkers = newWorkerMarkers;

  data.forEach((v) => {
    let marker = mapInfo.workerMarkers.find((item) => item.getExtData().key === v.userName);
    if (marker) {
      const { markerDiv, markerFocus } = marker.getExtData();
      marker.setPosition([v.lon, v.lat]);
      renderWorkerMarker(marker, markerDiv, v, markerFocus);
    } else {
      // 添加InfoWindow
      const infoWindowDiv = document.createElement("div");
      const infoWindow = new Amap.InfoWindow({
        isCustom: true,
        content: infoWindowDiv,
        position: [v.lon, v.lat], // 基点位置
        offset: [20, -78], // 位置偏移量 默认[0,0] 基准点在信息窗体的底部中心
        anchor: "top-left",
      });

      // 添加carMarker
      const markerDiv = document.createElement("div");
      marker = new Amap.Marker({
        content: markerDiv,
        position: [v.lon, v.lat],
        anchor: "bottom-center",
        // offset: [0, 0], // 若设置了anchor，则以anchor设置位置为基准点
        extData: {
          key: v.userName,
          markerDiv: markerDiv,
          markerFocus: false,
        },
      });
      updateExtData(marker, {
        marker: marker,
      });

      renderWorkerMarker(marker, markerDiv, v, false);

      map.add(marker);
      mapInfo.workerMarkers.push(marker);

      // 辅助看中心点
      // const _marker = new Amap.Marker({
      //   position: [v.lon, v.lat],
      // });
      // map.add(_marker);

      marker.on("mouseover", () => {
        renderWorkerInfoWindow(infoWindow, infoWindowDiv, marker, markerDiv, v);
        infoWindow.open(map, marker.getPosition());
        renderWorkerMarker(marker, markerDiv, v, true); // 隐藏顶部名称
      });
      marker.on("mouseout", () => {
        infoWindow.close();
        renderWorkerMarker(marker, markerDiv, v, false); // 显示顶部名称
      });
    }
  });
};

const renderWorkerInfoWindow = (
  infoWindow: typeof mapInfo.Amap.InfoWindow,
  infoWindowDiv: HTMLElement,
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: typeof socket.screenWorkers[0]
) => {
  render(
    createVNode(workerInfoWindow, {
      userName: info.userName,
      mobile: info.mobile,
    }),
    infoWindowDiv
  );
};

const renderWorkerMarker = (
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: typeof socket.screenWorkers[0],
  focus: boolean
) => {
  render(
    createVNode(workerMarker, {
      online: info.onlineStatus,
      userName: info.userName,
      focus,
    }),
    markerDiv
  );
};

let delOtherMarker: Function | undefined;
const updateCarMarker = (data: typeof socket.baseStatus) => {
  const map: any = mapInfo.map;
  const Amap: any = mapInfo.Amap;

  // 当切换项目时，将原项目有的车但切换项目没的车去掉
  let newCarMarkers = [] as typeof mapInfo.carMarkers;
  for (const marker of mapInfo.carMarkers) {
    if (data?.some((v) => v.deviceId === marker.getExtData().key)) {
      newCarMarkers.push(marker);
    } else {
      map.remove(marker);
    }
  }
  mapInfo.carMarkers = newCarMarkers;

  data?.forEach((v) => {
    let marker = mapInfo.carMarkers.find((item) => item.getExtData().key === v.deviceId);
    if (marker) {
      const { markerDiv, markerFocus } = marker.getExtData();
      marker.setPosition([v.longitude, v.latitude]);
      renderCarMarker(marker, markerDiv, v, markerFocus);
    } else {
      // 添加InfoWindow
      const infoWindowDiv = document.createElement("div");
      const infoWindow = new Amap.InfoWindow({
        isCustom: true,
        content: infoWindowDiv,
        position: [v.longitude, v.latitude], // 基点位置
        offset: [0, -38], // 位置偏移量 默认[0,0] 基准点在信息窗体的底部中心
      });

      // 添加carMarker
      const markerDiv = document.createElement("div");
      marker = new Amap.Marker({
        content: markerDiv,
        position: [v.longitude, v.latitude],
        anchor: "center",
        // offset: [0, 0], // 若设置了anchor，则以anchor设置位置为基准点
        extData: {
          key: v.deviceId,
          markerDiv: markerDiv,
          infoWindowDiv: infoWindowDiv,
          infoWindow: infoWindow,
          markerFocus: false,
        },
      });
      updateExtData(marker, {
        marker: marker,
      });

      renderCarMarker(marker, markerDiv, v, false);

      map.add(marker);
      mapInfo.carMarkers.push(marker);

      // 辅助看中心点
      // const _marker = new Amap.Marker({
      //   position: [v.longitude, v.latitude],
      // });
      // map.add(_marker);

      const showInfoWindow = (marker, markerDiv, infoWindow, infoWindowDiv, deviceId) => {
        delOtherMarker && delOtherMarker();
        addCar([deviceId]);
        updateScreenActiveCarId(deviceId);
        toggleMarkerFocus(marker, markerDiv, v, true);
        renderInfoWindow(infoWindow, infoWindowDiv, marker, markerDiv, v);
        infoWindow.open(map, marker.getPosition());
      };

      marker.on("click", () => {
        carDetailReactive.deviceId = v.deviceId;
        carDetailReactive.show = true;
      });
      marker.on("mouseover", (e: { originEvent: MouseEvent }) => {
        const { top } = markerDiv.getBoundingClientRect();
        if (e.originEvent.pageY > top - 46) {
          showInfoWindow(marker, markerDiv, infoWindow, infoWindowDiv, v.deviceId);
          closeChargingStatus();
        }
      });
      marker.on("mouseout", () => {
        delOtherMarker = () => {
          // infoWindow.close();
          // map.clearInfoWindow();
          toggleMarkerFocus(marker, markerDiv, v, false);
          delCar([v.deviceId]);
        };
      });

      // 根据地图上添加的覆盖物分布情况，自动缩放地图到合适的视野级别
      mapInfo.map.setFitView(mapInfo.carMarkers, false, [100, 0, 540, 540]);
    }
  });
};

const updateExtData = (target: any, extData: object) => {
  target.setExtData({
    ...target.getExtData(),
    ...extData,
  });
};

const renderCarMarker = (
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: typeof socket.baseStatus[0],
  focus: boolean
) => {
  render(
    createVNode(carMarker, {
      status: {
        online: info.online,
        warn: info.warn,
        deviceId: info.deviceId,
      },
      focus,
      direction: info.direction,
    }),
    markerDiv
  );
};

const toggleMarkerFocus = (
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: typeof socket.baseStatus[0],
  status: boolean
) => {
  updateExtData(marker, {
    markerFocus: status,
  });
  //renderMarker(marker, markerDiv, info, status);
};

const renderInfoWindow = (
  infoWindow: typeof mapInfo.Amap.InfoWindow,
  infoWindowDiv: HTMLElement,
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: typeof socket.baseStatus[0]
) => {
  render(
    createVNode(InfoWindow, {
      window: infoWindow,
      map: mapInfo.map,
      Amap: mapInfo.Amap,
      id: info.deviceId,
      markerBlur: () => toggleMarkerFocus(marker, markerDiv, info, false),
    }),
    infoWindowDiv
  );
};

const carDetailReactive = reactive({
  show: false,
  deviceId: "",
});
</script>

<style lang="scss" scoped></style>
