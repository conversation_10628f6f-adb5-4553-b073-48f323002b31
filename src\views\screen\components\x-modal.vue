<template>
  <Teleport to="body">
    <Transition name="x-modal">
      <div v-show="props.visible" class="x-modal">
        <Transition name="x-modal-mask">
          <div
            v-show="props.visible"
            class="x-modal-mask"
            @click="handleMask"
          ></div>
        </Transition>
        <Transition name="x-modal-content">
          <div
            v-show="props.visible"
            class="x-modal-content"
            :style="contentStyle"
          >
            <slot name="topTitle"></slot>
            <div class="content-header" v-if="props.title">
              <sub-title class="content-header-title">{{
                props.title
              }}</sub-title>
              <span
                class="content-header-close"
                v-html="closeText"
                @click="handleCancel"
              ></span>
            </div>
            <div class="content-body" :style="bodyStyle">
              <div class="content-body-slot">
                <slot></slot>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import subTitle from "./subTitle.vue";
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    required: false,
  },
  width: {
    type: String,
  },
  height: {
    type: String,
  },
  maskClosable: {
    type: Boolean,
    default: true,
  },
  bodyStyle: {
    type: Object,
  },
  radius: {
    type: String,
  },
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);
const contentStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width};`;
  if (props.height) style += `height: ${props.height};`;
  if (props.radius) style += `border-radius: ${props.radius};`;
  return style;
});
const bodyStyle = computed(() => {
  let style = "";
  if (props.bodyStyle) {
    for (const key of Object.keys(props.bodyStyle)) {
      style += `${key}:${props.bodyStyle[key]};`;
    }
  }
  if (!props.title) {
    style += "border-radius: 10px;";
  }
  return style;
});
const closeText = ref("&#10005");
const handleCancel = () => {
  emits("update:visible", false);
  emits("cancel");
};
const handleMask = () => {
  if (props.maskClosable) {
    handleCancel();
  }
};
</script>

<style lang="scss" scoped>
.x-modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  &-enter {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-mask {
    @include wh(100%) {
      background-color: rgba(0, 0, 0, 0.54);
    }
    &-enter {
      &-from {
        opacity: 0;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        opacity: 0;
      }
    }
  }
  &-content {
    &-enter {
      &-from {
        transform: scale(0.2);
        transform-origin: left top;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: scale(0.2);
        transform-origin: left top;
      }
    }
    display: flex;
    flex-direction: column;
    @include ct-p;
    @include wh(100%);
    @include bis("@/assets/images/screen_modal_bg.png");
    .content-header {
      position: relative;
      width: calc(100% - 40px);
      &-title {
        margin: 2vh 0 0 40px;
      }
      &-close {
        position: absolute;
        display: block;
        right: 0;
        top: 2.5vh;
        color: rgb(221, 229, 250);
        cursor: pointer;
      }
    }
    .content-body {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex: 1;
      padding: 0 10px;
    }
  }
}
</style>
