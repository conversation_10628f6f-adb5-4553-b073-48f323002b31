<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { genDays, parseDateFormat, valueToDateTime, valueToRange, valueToSelecting } from "./utils";
import {
  formatDateTime,
  getNextYearMonth,
  getPrevYearMonth,
  getWeeksInMonth,
  getWeekInDay,
} from "@/assets/ts/dateTime";
import TimePanel from "@/components/x-time-picker/src/time-panel.vue";
import xPopover from "@/components/x-popover.vue";
import type { DateType } from "@/assets/ts/dateTime";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");
/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    format?: string;
    value: string | string[];
    previewValue?: string;
    visible?: boolean;
    partial?: string;
    focus?: number;
    year: number;
    month: number;
    todaystamp: number;
    popupContainer: HTMLElement;
    showTime: boolean | any;
    disabledTime?: Function;
    disabledDate?: Function;
    range?: boolean;
    touch?: any;
    spotList?: string[];
    dateType?: "day" | "week" | "month";
  }>(),
  {
    showTime: false,
    visible: false,
    partial: "",
    range: false,
    dateType: "day",
  }
);

/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["updateTempValue", "changeYearMonth", "updatePreviewValue"]);

/**
 * 引用
 */
const panelRef = ref();

/**
 * 固定值
 */
const weekList = [$t("one"), $t("two"), $t("three"), $t("four"), $t("five"), $t("six"), $t("day")];

/**
 * 动态值
 */
const data = reactive({
  date: 0, // 通过valueToDateTime
  time: "", // 通过valueToDateTime
  days: [[], [], [], [], [], []] as DateType[][],
  weeks: [] as { index: number; weekRange: string[] }[][],
  months: [
    [1, 2, 3, 4],
    [5, 6, 7, 8],
    [9, 10, 11, 12],
  ],
  currDate: formatDateTime(props.todaystamp, "YYYY-MM-DD"),
  currWeekIndex: getWeekInDay(new Date(), "weekIndex"),
  popoverVisible: false,
  dateFormat: "YYYY-MM-DD",
  timeFormat: "HH:mm:ss",
  range: [0, 0],
  ranging: [0, 0],
});

// 格式
if (props.format) {
  const formatParse = parseDateFormat(props.format);
  data.timeFormat = formatParse.hour_minute_second
    ? "HH:mm:ss"
    : formatParse.hour_minute
    ? "HH:mm"
    : formatParse.hour
    ? "ss"
    : "HH:mm:ss";
}

// 值转换为日期时间
props.focus === undefined && valueToDateTime({ data, value: props.value });

// 范围
valueToRange({ data, value: props.value, format: data.dateFormat });

// 年月变化
watch(
  () => [props.year, props.month],
  (newV) => {
    // console.log("[props.year, props.month]变化");
    // 生成日期
    genDays({
      data,
      year: props.year,
      month: props.month,
      disabledDate: props.disabledDate,
    });
    data.weeks = (() => {
      const weeks = getWeeksInMonth(+new Date(...newV)).map((item, index) => ({
        index,
        weekRange: item,
      }));
      return [weeks.slice(0, 3), weeks.slice(3)];
    })();
  },
  {
    immediate: true,
  }
);

// 值变化
watch(
  () => [props.value, props.focus],
  () => {
    // console.log("[props.value, props.focus]变化");
    if (props.focus === undefined) {
      valueToDateTime({ data, value: props.value });
    } else if (props.focus > -1) {
      valueToDateTime({ data, value: props.value[props.focus] });
    }
    // 范围
    valueToRange({ data, value: props.value, format: data.dateFormat });
  }
);

// 预览值
watch(
  () => props.previewValue,
  () => {
    // console.log("props.previewValue变化");
    // 选择中范围
    valueToSelecting(props, data);
  }
);

// 二层popover日期列表逻辑
const updateVisible = (visible: boolean) => {
  data.popoverVisible = visible;
};
const selectMonth = (value: number) => {
  changeYearMonth({ month: value - 1 });
  data.popoverVisible = false;
};

/**
 * 改变月份
 */
const changeYearMonth = ({ type, month, year }: any) => {
  const change = {
    year: year === undefined ? props.year : year,
    month: month === undefined ? props.month : month,
    partial: props.partial,
  };

  if (type === "prev") {
    const [prevYear, prevMonth] = getPrevYearMonth(change.year, change.month);
    change.year = prevYear;
    change.month = prevMonth;
  } else if (type === "next") {
    const [nextYear, nextMonth] = getNextYearMonth(change.year, change.month);
    change.year = nextYear;
    change.month = nextMonth;
  }

  emits("changeYearMonth", change);
};

/**
 * 禁用的日期
 */
const isDisabledDay = (day: DateType) => {
  if (day.disabled) return true;
  if (props.touch?.has(0) && day.timestamp < data.range[0]) return true;
  if (props.touch?.has(1) && day.timestamp > data.range[1]) return true;
  return false;
};
/**
 * 鼠标进入日期
 */
const mouseenterDay = (day: DateType) => {
  // 禁用的日期
  if (isDisabledDay(day)) {
    // 不出现预览效果
    emits("updatePreviewValue");
    return;
  }
  // 发出事件
  emits("updatePreviewValue", {
    date: formatDateTime(day.timestamp, data.dateFormat),
  });
};

const mouseenterWeek = (week: string[]) => {
  emits("updatePreviewValue", {
    date: week.toString(),
  });
};
const mouseenterMonth = (month: number) => {
  emits("updatePreviewValue", {
    date: `${props.year}-${month}`,
  });
};

/**
 * 点击日期
 */
const onClickDay = (day: DateType) => {
  // 禁用的日期
  if (isDisabledDay(day)) return;
  // 发出事件
  emits("updateTempValue", {
    date: formatDateTime(day.timestamp, data.dateFormat),
  });
  // 选中的瞬间不再出现预览效果
  emits("updatePreviewValue");

  // 如果选择的是临近月的日期
  if (["prev", "next"].includes(day.type)) {
    changeYearMonth({ type: day.type, month: props.month });
  }
};
const onClickweek = (week: string[]) => {
  emits("updateTempValue", {
    date: week.toString(),
  });
  emits("updatePreviewValue");
};

const onClickMonth = (month: number) => {
  emits("updateTempValue", {
    date: `${props.year}-${month}`,
  });
  emits("updatePreviewValue");
};

/**
 * 更新时间
 */
const updateTime = (time: string) => {
  // 发出事件
  emits("updateTempValue", {
    time,
  });
};

// 日期圆点标记
const showSpot = (value) => {
  let flag;
  props.spotList?.map((date) => {
    if (date === formatDateTime(value.timestamp, data.dateFormat)) {
      flag = true;
    }
  });
  return flag;
};
</script>

<template>
  <div
    class="panel"
    ref="panelRef"
  >
    <div class="panel-day">
      <!-- 年月 -->
      <div class="panel-day__head">
        <div
          class="year-icon"
          @click="changeYearMonth({ year: props.year - 1 })"
        >
          <x-icon
            name="calendar_left_double"
            width="12"
            height="12"
          />
        </div>
        <div
          v-show="props.dateType !== 'month'"
          class="month-icon"
          @click="changeYearMonth({ month: props.month, type: 'prev' })"
        >
          <x-icon
            name="calendar_left"
            width="12"
            height="12"
          />
        </div>
        <div class="year-month">
          <template v-if="props.dateType === 'month'">
            <div>
              {{ props.year + $t("year") }}
            </div>
          </template>
          <template v-else-if="props.dateType === 'week'">
            <div>
              {{ props.year + $t("year") }}

              {{ (props.month + 1).toString().padStart(2, "0") + $t("month") }}
            </div>
          </template>
          <template v-else>
            <x-popover
              trigger="click"
              :visible="data.popoverVisible"
              @update:visible="updateVisible"
              placement="bottom"
              :triangle="false"
              contentType="none"
              :zIndex="3000"
              :gap="9"
            >
              <div class="year-month-trigger">
                {{ props.year + $t("year") }}

                {{ (props.month + 1).toString().padStart(2, "0") + $t("month") }}
              </div>
              <template #content>
                <div class="month-list">
                  <div
                    :class="['month-list-item', { enable: props.month === value - 1 }]"
                    v-for="(value, index) in 12"
                    @click="selectMonth(value)"
                    :key="index"
                  >
                    {{ ["一", "二", "三", "四", "五", "六", "七", "八", "九", "十", "十一", "十二"][value - 1] + "月" }}
                  </div>
                </div>
              </template>
            </x-popover>
          </template>
        </div>
        <div
          v-show="props.dateType !== 'month'"
          class="month-icon"
          @click="changeYearMonth({ month: props.month, type: 'next' })"
        >
          <x-icon
            name="calendar_right"
            width="12"
            height="12"
          />
        </div>
        <div
          class="year-icon"
          @click="changeYearMonth({ year: props.year + 1 })"
        >
          <x-icon
            name="calendar_right_double"
            width="12"
            height="12"
          />
        </div>
      </div>
      <!-- 日期 -->
      <div class="panel-day__body">
        <table class="day-table">
          <thead v-show="props.dateType === 'day'">
            <tr>
              <th
                v-for="day in weekList"
                :key="day"
              >
                {{ day }}
              </th>
            </tr>
          </thead>
          <tbody @mouseleave="emits('updatePreviewValue')">
            <template v-if="props.dateType === 'day'">
              <tr
                v-for="(line, index) in data.days"
                :key="index"
              >
                <td
                  v-for="(day, i) in line"
                  :key="i"
                  class="day-cell"
                  :class="[
                    {
                      'day-cell_near': day.type !== 'curr',
                      'day-cell_today': day.timestamp === props.todaystamp,
                      'day-cell_selected': props.range
                        ? data.range.includes(day.timestamp)
                        : day.timestamp === data.date,
                      'day-cell_disabled':
                        day.disabled ||
                        (props.touch?.has(0) && day.timestamp < data.range[0]) ||
                        (props.touch?.has(1) && day.timestamp > data.range[1]),
                      'day-cell_range':
                        data.range.includes(0) === false &&
                        day.timestamp > data.range[0] &&
                        day.timestamp < data.range[1],
                      'day-cell_range-start': day.timestamp === data.range[0],
                      'day-cell_range-end': day.timestamp === data.range[1],
                      'day-cell_range-alone':
                        (data.range[0] === 0 || data.range[1] === 0) &&
                        (data.range[0] !== 0 || data.range[1] !== 0) &&
                        (day.timestamp === data.range[0] || day.timestamp === data.range[1]),
                      'day-cell_ranging':
                        data.ranging.includes(0) === false &&
                        day.timestamp > data.ranging[0] &&
                        day.timestamp < data.ranging[1],
                      'day-cell_ranging-start': data.ranging.includes(0) === false && day.timestamp === data.ranging[0],
                      'day-cell_ranging-end': data.ranging.includes(0) === false && day.timestamp === data.ranging[1],
                    },
                  ]"
                  @click="onClickDay(day)"
                  @mouseenter="mouseenterDay(day)"
                >
                  <div class="day-cell__inner">
                    {{ day.value }}
                    <span
                      v-if="showSpot(day)"
                      class="day-cell__inner-spot"
                      :class="{
                        near: day.type !== 'curr',
                      }"
                    ></span>
                  </div>
                </td>
              </tr>
            </template>

            <template v-if="props.dateType === 'week'">
              <tr
                v-for="(line, index) in data.weeks"
                :key="index"
              >
                <td
                  v-for="(week, i) in line"
                  :key="i"
                  class="day-cell"
                  :class="[
                    {
                      'day-cell_selected': props.value.split(' ~ ')[0] == week.weekRange[0],
                    },
                  ]"
                  @click="onClickweek(week.weekRange)"
                  @mouseenter="mouseenterWeek(week.weekRange)"
                >
                  <div
                    :class="[
                      'day-cell__inner',
                      {
                        today:
                          data.currDate.slice(0, 4) == props.year &&
                          data.currDate.slice(5, 7) == props.month + 1 &&
                          data.currWeekIndex == i,
                      },
                    ]"
                  >
                    第{{ ["一", "二", "三", "四", "五", "六"][week.index] }}周
                  </div>
                </td>
              </tr>
            </template>

            <template v-if="props.dateType === 'month'">
              <tr
                v-for="(line, index) in data.months"
                :key="index"
              >
                <td
                  v-for="(month, i) in line"
                  :key="i"
                  class="day-cell"
                  :class="[
                    {
                      'day-cell_selected': props.value.slice(0, 4) == props.year && props.value.slice(5, 7) == month,
                    },
                  ]"
                  @click="onClickMonth(month)"
                  @mouseenter="mouseenterMonth(month)"
                >
                  <div
                    :class="[
                      'day-cell__inner',
                      {
                        today: data.currDate.slice(0, 4) == props.year && props.month + 1 === month,
                      },
                    ]"
                  >
                    {{ month + "月" }}
                  </div>
                </td>
              </tr>
            </template>
          </tbody>
        </table>
      </div>
    </div>
    <!-- 时间 -->
    <div
      class="panel-time"
      v-if="props.showTime"
    >
      <div class="panel-time__head">{{ $t("selectTime") }}</div>
      <div class="panel-time__body">
        <TimePanel
          :value="data.time"
          :format="data.timeFormat"
          :visible="props.visible"
          :disabledTime="props.disabledTime"
          @updateTempValue="updateTime"
        ></TimePanel>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.panel {
  display: flex;
}
.panel-time {
  border-left: 1px solid #e5e6eb;

  &__head {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 41px;
    padding: 0 8px;
    border-bottom: 1px solid #e5e6eb;
    font-weight: 600;
  }

  &__body {
    padding: 8px 0;
  }
}
.panel-day {
  width: 288px;

  &__head {
    display: flex;
    align-items: center;
    height: 41px;
    padding: 0 8px;
    border-bottom: 1px solid #e5e6eb;
  }
  &__body {
    padding: 8px 18px;
  }
}

.year-icon,
.month-icon {
  width: 24px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;

  &:hover {
    background-color: #f2f3f5;
  }

  &_state-hidden {
    visibility: hidden;
  }
}
.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
}
.year-month-trigger {
  cursor: pointer;
  padding: 0 16px;
  border-radius: 4px;
  line-height: 24px;

  &:hover {
    color: #5964fb;
    background-color: #f2f3f5;
  }
}

.month-list {
  display: flex;
  flex-wrap: wrap;
  @include wh(288px, 309px) {
    padding: 18px 10px;
    background-color: #fff;
    box-shadow: 0px 9px 28px rgba(0, 0, 0, 0.05);
  }
  &-item {
    cursor: pointer;
    @include wh(48px, 32px) {
      border-radius: 4px;
      margin: 14px 20px;
      background-color: #fff;
    }
    line-height: 32px;
    text-align: center;
    &:hover,
    &.enable {
      color: #fff;
      background-color: #5964fb;
    }
  }
}

.day-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;

  th {
    height: 36px;
    color: #86909c;
    font-weight: 400;
    vertical-align: middle;
  }
  td {
    height: 36px;
    vertical-align: middle;
    box-sizing: border-box;
    text-align: center;
    padding: 5px 0;
  }
}
.day-cell {
  position: relative;
  cursor: pointer;

  &::before {
    position: absolute;
    top: 50%;
    inset-inline-start: 0;
    inset-inline-end: 0;
    z-index: 1;
    height: 32px;
    transform: translateY(-50%);
    transition: all 0.3s;
    content: "";
  }

  &::after {
    position: absolute;
    top: 50%;
    z-index: 0;
    height: 32px;
    border-top: 1px dashed #7cb3ff;
    border-bottom: 1px dashed #7cb3ff;
    transform: translateY(-50%);
    box-sizing: border-box;
    content: "";
  }

  &__inner {
    position: relative;
    z-index: 2;
    display: inline-block;
    min-width: 32px;
    height: 32px;
    line-height: 32px;
    border-radius: 50%;
    transition: background 0.3s, color 0.3s, border 0.3s;
    &-spot {
      display: inline-block;
      position: absolute;
      @include ct-p(x);
      bottom: 0;
      @include wh(6px, 6px);
      background: #5964fb;
      border-radius: 50px;
      &.near {
        background: #c9cdd4;
      }
    }
  }

  &:hover:not(&_disabled) &__inner {
    background-color: rgb(89, 100, 251);
    color: rgb(255, 255, 255);
    animation-duration: 1.3s;
    animation-name: heartBeat1;
    animation-timing-function: ease-in-out;
  }

  // 范围
  &_range:not(&_near)::before {
    background: #e5edff;
  }

  // 范围-开始
  &_range-start:not(&_near)::before {
    background: #e5edff;
    inset-inline-start: 2px;
    border-radius: 50% 0 0 50%;
  }

  // 范围-结束
  &_range-end:not(&_near)::before {
    background: #e5edff;
    inset-inline-end: 2px;
    border-radius: 0 50% 50% 0;
  }

  // 范围-开始 + 范围-结束
  @at-root #{& + "_range-start" + & + "_range-end"}:not(&_near)::before {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }

  // 范围-单独
  &_range-alone:not(&_near)::before {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }

  // 范围中
  &_ranging:not(&_near)::after {
    inset-inline-start: 2px;
    inset-inline-end: 0;
  }
  // 范围中开始
  &_ranging-start:not(&_near)::after {
    inset-inline-start: 2px;
    inset-inline-end: 0;
    border-left: 1px dashed #7cb3ff;
    border-radius: 50% 0 0 50%;
  }
  // 范围中结束
  &_ranging-end:not(&_near)::after {
    inset-inline-start: 2px;
    inset-inline-end: 2px;
    border-right: 1px dashed #7cb3ff;
    border-radius: 0 50% 50% 0;
  }
  // 范围中-开始 + 范围中-结束
  @at-root #{& + "_ranging-start" + & + "_ranging-end"}:not(&_near)::after {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }

  // 范围 + 范围中
  @at-root #{& + "_range" + & + "_ranging"}:not(&_near)::before,
    #{& + "_range-start" + & + "_ranging"}:not(&_near)::before,
    #{& + "_range-end" + & + "_ranging"}:not(&_near)::before,
    #{& + "_range" + & + "_ranging-start"}:not(&_near)::before,
    #{& + "_range-start" + & + "_ranging-start"}:not(&_near)::before,
    #{& + "_range-end" + & + "_ranging-start"}:not(&_near)::before,
    #{& + "_range" + & + "_ranging-end"}:not(&_near)::before,
    #{& + "_range-start" + & + "_ranging-end"}:not(&_near)::before,
    #{& + "_range-end" + & + "_ranging-end"}:not(&_near)::before {
    background: rgb(217, 224, 251);
  }

  // 今天
  &_today:not(&_near) &__inner::before,
  &__inner.today::before {
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
    z-index: 1;
    border: 1px solid #5964fb;
    border-radius: 50%;
    content: "";
  }

  // 选中
  &_selected:not(&_near) &__inner {
    color: #fff;
    background: #5964fb;
  }

  // 禁用
  &_disabled {
    cursor: default;
  }
  &_disabled::before {
    background: rgba(0, 0, 0, 0.04);
  }
  &_disabled &__inner {
    color: #c9cdd4;
    background: transparent;
  }

  // 临近月份的日期
  &_near {
    color: #c9cdd4;
  }
}
</style>
