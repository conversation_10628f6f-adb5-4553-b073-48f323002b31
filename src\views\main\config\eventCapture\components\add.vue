<template>
  <x-drawer
    :title="$t('add')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    :btnOption="{
      position: 'center',
      confirmDisabled: !isValidate,
    }"
    width="564px"
  >
    <div ref="contentRef" class="container">
      <div class="form-item">
        <div class="form-item-label required" style="width: 95px">
          {{ $t("deviceId") }}
        </div>
        <div class="form-item-value">
          <x-select
            v-model:value="form.deviceNo"
            :options="formOptions.deviceOptions"
            :popupContainer="contentRef"
            @update:value="changeDeviceNo"
            :placeholder="$t('PEnterDeviceId')"
          />
        </div>
      </div>
      <div class="inner" v-for="(item, index) in form.contents" :key="index">
        <div class="inner-title">
          <div class="inner-title-left">
            <span class="inner-title-left-text">
              {{ index < 9 ? `0${index + 1}` : `${index + 1}` }}
            </span>
          </div>
          <span
            v-if="form.contents.length !== 1"
            v-html="closeText"
            @mousedown="delList(index)"
            class="inner-title-right"
          ></span>
        </div>
        <div class="inner-content">
          <div class="form-item">
            <div class="form-item-label required">{{ $t("type") }}</div>
            <div class="form-item-value">
              <x-select
                v-model:value="item.captureType"
                :options="formOptions.captureType"
                :popupContainer="contentRef"
                @update:value="changeCaptureType($event, index)"
                :placeholder="$t('PSelectType')"
              />
            </div>
          </div>
          <div class="form-item">
            <div class="form-item-label required">{{ $t("camera") }}</div>
            <div class="form-item-value">
              <div class="checkbox-container">
                <div
                  v-for="(camera, i) in formOptions.cameraStand"
                  :key="i"
                  class="checkbox"
                >
                  <x-checkbox
                    :text="camera.label"
                    :checked="
                      form.contents[index].cameraStand?.includes(camera.value)
                    "
                    @update:checked="
                      changeCameraStand($event, camera.value, index)
                    "
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="cycle-container">
            <span class="cycle-text">{{ $t("captureInterval") }}(s)</span>
            <x-number-box
              :value="item.captureDuration"
              :min="1"
              :max="99"
              @change="item.captureDuration = $event"
            />
          </div>
        </div>
      </div>
      <x-button
        v-if="form.contents.length < 3"
        type="paleBlue"
        :text="$t('addType')"
        icon="button_add"
        @click="addList"
        style="margin-top: 10px"
      />
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, computed } from "vue";
import {
  getVehList,
  getVehTypeById,
  saveEventCaptureConfig,
} from "@/services/api";
import {
  captureEventType,
  fourCameraType,
  threeCameraType,
} from "@/assets/ts/config";
import xDrawer from "@/components/x-drawer.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import xNumberBox from "@/components/x-number-box.vue";
import xButton from "@/components/x-button.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("eventCapture");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});

const contentRef = ref<any>();

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

const formOptions = reactive({
  deviceOptions: [] as { label: string; value: string }[],
  captureType: captureEventType,
  cameraStand: fourCameraType,
});

/**
 * 表单项
 */
const form = reactive({
  deviceNo: "",
  contents: [
    {
      captureType: "",
      cameraStand: [],
      captureDuration: 1,
    },
  ],
});

// 切换车牌号
const changeDeviceNo = async (deviceNo: string) => {
  resetContent();
  const { vehType } = await getVehTypeById({ deviceNo });
  formOptions.cameraStand = vehType === "1" ? threeCameraType : fourCameraType;
};

// 添加类型
const addList = () => {
  form.contents.push({
    captureType: "",
    cameraStand: [],
    captureDuration: 1,
  });
};

// 删除类型
const closeText = ref("&#10005");
const delList = (index: number) => {
  form.contents.splice(index, 1);
};

// 修改类型
const changeCaptureType = (event: any, index: number) => {
  const captureTypeList = form.contents.map((item) => item.captureType);
  const currentParkId = form.contents[index].captureType;
  const duplicateIndex = captureTypeList.findIndex(
    (id, i) => id === currentParkId && i !== index
  );
  if (duplicateIndex !== -1) {
    Message("error", $t("typeCannotRepeat"));
    form.contents[index].captureType = "";
  }
};

// 修改摄像头
const changeCameraStand = (checked: boolean, value: any, index: number) => {
  const cameraList = new Set(form.contents[index].cameraStand);
  if (checked) {
    cameraList.add(value);
  } else {
    cameraList.delete(value);
  }
  form.contents[index].cameraStand = Array.from(cameraList);
};

/**
 * 表单提交
 */
// 表单校验
const isValidate = computed(() => {
  return (
    form.deviceNo &&
    form.contents.every(
      (item) => item.captureType && item.cameraStand.length > 0
    )
  );
});

// 表单提交
const formSubmit = async () => {
  const params = {
    deviceNo: form.deviceNo,
    type: form.contents,
  };
  await saveEventCaptureConfig(params);
  emits("update:show", false);
  Message("success", $t("captureSettingSaveSuccess"));
  emits("confirm");
  resetAll();
};

// 重置内容
const resetAll = () => {
  form.deviceNo = "";
  resetContent();
};

// 重置
const resetContent = () => {
  form.contents = [
    {
      captureType: "",
      cameraStand: [],
      captureDuration: 1,
    },
  ];
};

watch(
  () => props.show,
  async (newV) => {
    resetAll();
    if (newV) {
      formOptions.deviceOptions = (await getVehList()).map((item) => ({
        label: item!,
        value: item!,
      }));
    }
  }
);
</script>

<style lang="scss" scoped>
.container {
  padding: 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
  .inner {
    margin-bottom: 15px;
    border-radius: 10px;
    background: #f4f7fe;
    &-title {
      display: flex;
      justify-content: space-between;
      height: 22px;
      line-height: 22px;
      &-left {
        width: 72px;
        background: linear-gradient(to right, #5964fb, #f4f7fe);
        border-radius: 10px 0 0 0;
        &-text {
          margin-left: 10px;
          @include sc(12px, #fff);
        }
      }
      &-right {
        font-size: 12px;
        display: block;
        margin-right: 15px;
        cursor: pointer;
      }
    }
    &-content {
      padding: 10px 20px;
      .cycle-container {
        @include ct-f(y) {
          justify-content: space-between;
        }
        padding: 5px 0;
        .cycle-text {
          @include sc(14px, #383838) {
            font-weight: 500;
          }
        }
        border-radius: 8px;
      }
    }
  }
  .form-item {
    display: flex;
    margin-bottom: 10px;
    &-label {
      @include wh(80px, 30px) {
        line-height: 30px;
      }
      @include sc(14px, #555);
      &.required::before {
        content: "* ";
        color: red;
      }
    }
    &-value {
      width: calc(100% - 80px);
      .checkbox-container {
        @include ct-f(y) {
          flex-wrap: wrap;
        }
        .checkbox {
          margin: 5px 20px 10px 0;
          :deep(.x-checkbox-text) {
            @include sc(14px, #555);
            width: 50px;
          }
        }
      }
    }
  }
}
</style>
