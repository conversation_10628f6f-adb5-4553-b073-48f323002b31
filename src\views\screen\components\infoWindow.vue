<template>
  <section
    :class="[
      'info-window',
      {
        disable: !carStatus.online,
        bottomOpera: false,
      },
    ]"
  >
    <div class="info-window-loading" v-if="isEcar && carStatus.loading">
      <img src="@/assets/images/loading.gif" alt="" />
      <span
        >{{ carStatus.machineStatus ? $t("closeMachine") : $t("start")
        }}{{ $t("in") }}...</span
      >
    </div>
    <span
      class="info-window-close"
      v-html="closeText"
      @click="clickHandle"
    ></span>
    <div class="info-window-container">
      <div class="top">
        <capsuleIcon
          v-show="!isOffline"
          :config="carStatus.workStatus"
          style="margin-right: 10px"
        />
        <capsuleIcon
          v-show="isOffline"
          :config="{
            text: offlineText,
            icon: offlineSvg,
            color: '#999',
          }"
          style="margin-right: 10px"
        />
        <capsuleIcon :config="carStatus.driveMode" style="margin-right: 10px" />
        <capsuleIcon
          v-show="carStatus.worker.text && carStatus.worker.text !== '--'"
          :config="carStatus.worker"
        />
      </div>
      <div class="center">
        <div class="center-round">
          <roundPercent
            class="image"
            :percent="carStatus.speedPercent * 100"
            :offline="isOffline"
          />
          <div class="percent">
            <span>{{ carStatus.speed }}</span>
            <span>km/h</span>
          </div>
        </div>
        <div class="center-battery">
          <div class="percent">{{ carStatus.battery }}%</div>
          <div class="image">
            <batteryPercent
              :percent="Number(carStatus.battery)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
        <div class="center-water">
          <div class="percent">{{ carStatus.water }}%</div>
          <div class="image">
            <waterPercent
              :percent="Number(carStatus.water)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
        <div class="center-garbage">
          <div class="percent">{{ carStatus.garbage }}%</div>
          <div class="image">
            <garbagePercent
              :percent="Number(carStatus.garbage)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-left">
          <span>{{ $t("dataUploadTime") }}</span>
        </div>
        <div class="bottom-right">
          <span>{{ carStatus.updateDate }}</span>
          <span>{{ carStatus.updateTime }}</span>
        </div>
      </div>
      <div class="footer"></div>
    </div>
    <distributeTask
      v-model:show="taskDrawer.show"
      :id="taskDrawer.id"
      :map="map"
      :Amap="Amap"
    />
    <customSetting
      v-model:show="customDrawer.show"
      :id="customDrawer.id"
      :isThreeEcar="isThreeEcar"
    />
    <x-modal
      :visible="taskConfirm.show"
      @confirm="submitConfirm"
      @cancel="taskConfirm.show = false"
      :bodyStyle="{ padding: '32px 32px 24px 44px' }"
      :btnOption="{
        cancel: $t('close'),
        position: 'right',
        confirmLoading: taskConfirm.modeLoading,
        confirmDisabled: taskConfirm.modeLoading,
      }"
      radius="4px"
      width="424px"
      height="181px"
    >
      <div class="content">
        <div class="content-header">
          <div class="content-header-title">
            <x-icon
              class="title-icon"
              name="modal_warn"
              style="margin-right: 10px"
            />
            <span class="title-text">{{ $t("vehicleSwitchAutoMode") }}</span>
          </div>
        </div>
        <div class="content-body">{{ $t("PCheckAroundAndSafe") }}</div>
      </div>
    </x-modal>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch, onMounted, computed, nextTick } from "vue";
import type { PropType } from "vue";
import { useMainStore } from "@/stores/main";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import {
  workStatusMap,
  driveModeMap,
  parseSpeed,
  parseBattery,
  parseWater,
  parseGarbage,
  offlineText,
  offlineSvg,
} from "@/services/wsconfig";
import { delCar } from "@/services/wsapi";
import { changeDriveMode } from "@/services/api";
import xIcon from "@/components/x-icon.vue";
import capsuleIcon from "./capsuleIcon.vue";
import batteryPercent from "./batteryPercent.vue";
import waterPercent from "./waterPercent.vue";
import garbagePercent from "./garbagePercent.vue";
import roundPercent from "./roundPercent.vue";
import distributeTask from "./distributeTask.vue";
import customSetting from "./customSetting.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
  markerBlur: {
    type: Function,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
const { socket, userInfo, updateActiveCarId } = useMainStore();

const isOffline = computed(() => !carStatus.online);
const headImgUrl = computed(() => {
  if (userInfo.imgUrl) {
    return userInfo.imgUrl;
  } else {
    return new URL("/src/assets/images/user_default_head.png", import.meta.url)
      .href;
  }
});
const isEcar = computed(
  () => carStatus.manufacturer === "ecar" || carStatus.manufacturer === "PA"
); // 三轮车 或 四轮车
const isThreeEcar = computed(() => carStatus?.vehType === 2); // 三轮车
const dataLoaded = ref(false);

onMounted(() => {
  watch(
    () => socket.allStatus,
    (newV) => {
      if (newV) {
        const target = newV.find((v) => v.deviceId === props.id);
        if (target) {
          dataLoaded.value = true;
          // 易咖开关机loading处理
          if (isEcar.value) {
            carStatus.loading = Boolean(target.loadingMark);

            // 加载超过2分钟
            const loadingTimeOut =
              target.loadingTimeCurrent - target.loadingTimeStart >=
              2 * 60 * 1000;
            // 在线状态切换
            const onlinStatusChange = target.online !== carStatus.online;

            if (carStatus.loading && (loadingTimeOut || onlinStatusChange)) {
              carStatus.loading = false;
            }
          }
          carStatus.online = target.online;
          carStatus.digIoSwitch = target.digIoSwitch;
          // 车辆开关机状态
          nextTick(() => {
            carStatus.machineStatus = isEcar.value
              ? carStatus.online
              : carStatus.digIoSwitch;
          });
          carStatus.workStatus = workStatusMap[target.workStatus];
          carStatus.driveMode = JSON.parse(
            JSON.stringify(driveModeMap[target.driveMode])
          );
          if (isOffline.value) {
            carStatus.driveMode.icon += "_offline";
            carStatus.driveMode.color = "#999";
          }
          carStatus.worker.text = target.worker;
          carStatus.updateDate = target.updateDate;
          carStatus.updateTime = target.updateTime;
          carStatus.speed = parseSpeed(target.speed);
          carStatus.speedPercent = Math.min(carStatus.speed / 10, 1);
          carStatus.battery = parseBattery(target.electric);
          carStatus.water = parseWater(target.tank);
          carStatus.garbage = parseGarbage(target.litter);
          carStatus.taskStatus = target.taskStatus;
          carStatus.taskWorkStatus = target.taskWorkStatus;
          carStatus.manufacturer = target.manufacturer;
          carStatus.recharge = target.recharge;
          carStatus.vehType = target.vehType;
          carStatus.chargeConnectStatus = target.chargeConnectStatus;
        }
      }
    }
  );
});

const closeText = ref("&#10005");
const carStatus = reactive<any>({
  workStatus: {
    icon: "",
    text: "",
    color: "",
  },
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
  worker: {
    imgUrl: headImgUrl,
    text: "",
    color: "#5964FB",
  },
  online: false,
  updateDate: "",
  updateTime: "",
  speed: 0.0,
  speedPercent: 0,
  battery: 0,
  water: 0,
  garbage: 0,
  digIoSwitch: "",
  taskStatus: 0,
  taskWorkStatus: "",
  manufacturer: "",
  loading: false,
  machineStatus: false,
  recharge: false,
  vehType: 1,
  chargeConnectStatus: "not_connect",
});

const clickHandle = () => {
  closeInfoWindow();
  updateActiveCarId(null);
  !socket.enableCarIds.includes(props.id) && delCar([props.id]);
};

// 仅关闭弹窗 不断开连接
const closeInfoWindow = () => {
  props.window.close();
  // props.map.clearInfoWindow();
  props.markerBlur();
};

/**
 * 多任务下发
 */
// 待机模式
const taskConfirm = reactive({
  show: false,
  modeLoading: false,
});
const submitConfirm = debounce(() => {
  taskConfirm.modeLoading = true;
  const param = {
    driveMode: 2,
    vehNo: props.id,
  };
  changeDriveMode(param)
    .then(() => {
      Message("success", $t("commandSendSuccess"));
      taskConfirm.show = false;
    })
    .finally(() => {
      taskConfirm.modeLoading = false;
    });
});
// 其他模式
const taskDrawer = reactive({
  show: false,
  id: "",
});

// 自定义设置
const customDrawer = reactive({
  show: false,
  id: "",
});
</script>

<style lang="scss" scoped>
// screen_info_window.png
// screen_info_window_disable.png
.info-window {
  @include wh(368px, 206px);
  @include bis("@/assets/images/map_info_window.png");
  &.disable {
    background-image: url("@/assets/images/map_info_window_disable.png");
  }
  &.bottomOpera {
    @include wh(368px, 290px);
    background-image: url("@/assets/images/map_info_window_task.png");
  }

  position: relative;
  &-loading {
    z-index: 5;
    @include wh(354px, 230px);
    position: absolute;
    top: 10px;
    left: 6px;
    background: rgba(255, 255, 255, 0.8);
    img,
    span {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    img {
      transform: translate(-50%, -70%);
      @include wh(57.6px, 64.2px);
    }
    span {
      transform: translate(-50%, 40px);
      @include sc(16px, #555) {
        font-weight: bold;
      }
    }
  }
  &-container {
    position: absolute;
    top: 20px;
    padding: 0 15px;
    width: 100%;
    .top {
      display: flex;
      @include wh(100%, 28px);
    }
    .center {
      display: flex;
      margin-top: 10px;
      &-round {
        overflow: hidden;
        position: relative;
        @include ct-f(x);
        @include wh(104px, 94px);
        .image {
          position: absolute;
          top: 5px;
        }
        .percent {
          position: absolute;
          top: -22px;
          span:nth-child(1) {
            @include ct-p(x) {
              top: 35px;
            }
            @include sc(18px, #242859) {
              font-weight: bolder;
            }
          }
          span:nth-child(2) {
            @include ct-p(x) {
              top: 57px;
            }
            @include sc(12px, #242859);
          }
        }
      }
      &-battery,
      &-water,
      &-garbage {
        flex: 1;
        .percent {
          text-align: center;
          @include sc(14px, #242859) {
            font-weight: bolder;
          }
        }
        .image {
          @include ct-f(x);
          margin-top: 5px;
        }
      }
    }
    .bottom {
      @include fj;
      height: 32px;
      line-height: 32px;
      background: #f6f6f6;
      border-radius: 4px;
      font-size: 12px;
      padding: 0 10px;
      &-left {
        color: #555;
      }
      &-right {
        color: #383838;
        span:nth-child(2) {
          padding-left: 10px;
        }
      }
    }
    .footer {
      margin-top: 5px;
      &-opera {
        display: flex;
      }
      &-bottom {
        margin-top: 6px;
        @include fj {
          align-items: center;
        }
        &-status {
          @include ct-f(y);
          @include wh(188px, 100%) {
            padding-left: 8px;
            border-radius: 4px;
            background-image: linear-gradient(
              to right,
              rgba(87, 103, 251, 0.22),
              rgba(87, 103, 251, 0)
            );
            color: rgb(87, 103, 251);
          }
          &.charge {
            background-image: linear-gradient(
              to right,
              rgba(39, 212, 161, 0.17),
              rgba(39, 212, 161, 0)
            );
            color: rgb(39, 212, 161);
          }
        }
        &-opera {
          height: 16px;
          display: flex;
          img {
            cursor: pointer;
            margin-right: 10px;
          }
        }
      }
    }
  }
  &-close {
    z-index: 6;
    cursor: pointer;
    display: block;
    @include ct-f;
    @include wh(16px) {
      font-size: 16px;
    }
    position: absolute;
    top: 25px;
    right: 18px;
  }
}
.content {
  .content-header {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 54px);
    @include sc(16px, #242859);
    .title-icon {
      margin-right: 10px;
    }
    .title-text {
      font-weight: bold;
    }
  }
  .content-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    padding-left: 26px;
    @include sc(14px, #383838);
  }
}
</style>
