<template>
  <section class="x-pagination">
    <span class="x-pagination-total">{{
      $t("total") + props.total + $t("line")
    }}</span>
    <ul class="x-pagination-page">
      <li class="page-prev" @click="goPage(current - 1)">
        <span class="page-prev-arrow"></span>
      </li>
      <template v-for="(item, index) in drawArr" :key="index">
        <li v-if="item === '...'" class="page-point">
          <span></span><span></span><span></span>
        </li>
        <li
          v-else
          :class="['page-num', { enable: item === props.current }]"
          @click="props.current !== item && goPage(item as number)"
        >
          {{ item }}
        </li>
      </template>

      <li class="page-next" @click="goPage(current + 1)">
        <span class="page-next-arrow"></span>
      </li>
      <li class="page-size">
        <x-select
          :value="pageSize.value"
          @update:value="pageSizeChange"
          :options="pageSize.options"
          :filter-option="
            (value, options) => options.filter((v) => v.value !== value)
          "
          style="width: 90px"
        />
      </li>
      <li class="page-jump">
        {{ $t("jumpTo") }}
        <input
          type="text"
          oninput="value=value.replace(/[^\d]/g,'')"
          @keyup.enter="
            goPage(Number(($event.target as HTMLInputElement).value))
          "
        />
        {{ $t("page") }}
      </li>
    </ul>
  </section>
</template>
<script lang="ts" setup>
import { computed, reactive } from "vue";
import type { PropType } from "vue";
import type { PageSizeType } from "./types";
import xSelect from "@/components/x-select.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = defineProps({
  total: {
    type: Number,
    required: true,
  },
  current: {
    type: Number,
    required: true,
  },
  pageSize: {
    type: Number as PropType<PageSizeType>,
    required: true,
  },
});
const emits = defineEmits(["update:current", "update:pageSize"]);
const pageSize = reactive({
  value: computed(() => String(props.pageSize)),
  options: [
    {
      value: "10",
      label: `10${$t("line")}/${$t("page")}`,
    },
    {
      value: "15",
      label: `15${$t("line")}/${$t("page")}`,
    },
    {
      value: "20",
      label: `20${$t("line")}/${$t("page")}`,
    },
    {
      value: "25",
      label: `25${$t("line")}/${$t("page")}`,
    },
  ],
});
const pageSizeChange = (_pageSize: string) => {
  goPage(1, false);
  emits("update:pageSize", Number(_pageSize));
};

const totalPage = computed(() => Math.ceil(props.total / props.pageSize));
const drawArr = computed(() => {
  if (totalPage.value < 8) {
    return new Array(totalPage.value).fill(0).map((v, i) => i + 1);
  } else {
    // 以当前为中心的5个必须显示
    const overLeft = props.current - 2;
    const overRight = totalPage.value - (props.current + 2);
    const mainStartNumber =
      overLeft < 1 ? 1 : overRight < 0 ? totalPage.value - 4 : overLeft;
    let res: (string | number)[] = [
      mainStartNumber,
      mainStartNumber + 1,
      mainStartNumber + 2,
      mainStartNumber + 3,
      mainStartNumber + 4,
    ];
    if (res[0] === 2) {
      res = [1, ...res];
    } else if (res[0] > 2) {
      res = [1, "...", ...res];
    }
    if (res[res.length - 1] === totalPage.value - 1) {
      res = [...res, totalPage.value];
    } else if (res[res.length - 1] < totalPage.value - 1) {
      res = [...res, "...", totalPage.value];
    }
    return res;
  }
});

const goPage = (num: Number, isEmit = true) => {
  if (num > 0 && num <= totalPage.value && isEmit) {
    emits("update:current", num);
  }
};
</script>

<style lang="scss" scoped>
.x-pagination {
  @include ct-f(y);
  &-total {
    color: #555;
  }
  &-page {
    display: flex;
    .page-point {
      @include fj {
        align-items: center;
      }
      @include wh(32px) {
        padding: 0 8px;
        margin-left: 8px;
      }
      span {
        display: block;
        @include wh(3px) {
          border-radius: 50%;
          background-color: #d4d4d4;
        }
      }
    }
    .page-num {
      cursor: pointer;
      @include ct-f;
      @include wh(32px) {
        margin-left: 16px;
        color: #555;
        border-radius: 2px;
        border: 1px solid #dcdcdc;
      }

      &:hover:not(.enable) {
        background-color: rgba(89, 100, 251, 0.1);
        border: none;
      }
      &.enable {
        color: #fafafa;
        background-color: #5964fb;
        border: none;
      }
      .page-prev-arrow {
        @include v-arrow(left, 1px, 6px);
      }
      .page-next-arrow {
        @include v-arrow(right, 1px, 6px);
      }
    }
    .page-size {
      margin-left: 16px;
    }
    .page-jump {
      display: flex;
      height: 32px;
      margin-left: 16px;
      color: #555;
      line-height: 32px;
      input {
        @include wh(48px, 100%) {
          margin-left: 8px;
          margin-right: 8px;
          border-radius: 4px;
          border: 1px solid rgb(220, 220, 220);
        }
        text-align: center;
        color: #555;
      }
    }
  }
}
</style>
