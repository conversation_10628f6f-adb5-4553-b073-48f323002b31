<template>
  <section
    class="template-detail"
    ref="templateDetailRef"
    v-if="props.show"
  >
    <div class="template-detail-top">{{ props.proAreaName }}—{{ props.proName }}</div>
    <div class="template-detail-middle">
      <x-checkbox
        :class="['checkbox', { active: all() }]"
        :text="$t('selectAll')"
        :checked="all()"
        :indeterminate="!all() && half()"
        :stopPropagation="false"
        @update:checked="toggleAllChecked"
      />
      <div
        v-for="(item, index) in scheduleConfig"
        :key="index"
      >
        <x-checkbox
          class="checkbox"
          :text="item.label"
          :checked="item.checkStatus"
          @update:checked="scheduleConfig[index].checkStatus = $event"
        />
      </div>
    </div>
    <div class="template-detail-bottom">
      <div
        class="template"
        v-for="(item, index) in filteredData"
        :key="index"
      >
        <div class="template-name">{{ item.tempName }}</div>
        <div
          v-for="(task, i) in item.scheduleList"
          :key="i"
        >
          <TaskPreview
            :task="task"
            bgType="light"
          />
        </div>
      </div>
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive, computed } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
import xCheckbox from "@/components/x-checkbox.vue";
import { getScheduleTempDetail } from "@/services/api";
import TaskPreview from "../taskPreview.vue";
import { scheduleType } from "@/assets/ts/config";
import type { GetScheduleTempDetailResponse } from "@/services/type";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  proAreaId: {
    type: String,
    required: true,
  },
  proName: {
    type: String,
    required: true,
  },
  proAreaName: {
    type: String,
    required: true,
  },
});

const templateDetailRef = ref<any>();

const scheduleConfig = reactive(
  scheduleType.map((item: any) => ({
    ...item,
    checkStatus: true,
  }))
);

// 半选
const half = () => {
  return scheduleConfig.some((item) => item.checkStatus);
};
// 全选
const all = () => {
  return scheduleConfig.every((item) => item.checkStatus);
};
// 切换全选
const toggleAllChecked = (checked: boolean) => {
  scheduleConfig.forEach((item) => {
    item.checkStatus = checked;
  });
};

const filteredData = computed(() => {
  const checkedTypes = scheduleConfig.filter((item) => item.checkStatus).map((item) => item.value);
  return detail.templateList.map((template) => {
    const filteredTasks = template.scheduleList.filter((task) => checkedTypes.includes(task.scheduleType));
    return { ...template, scheduleList: filteredTasks };
  });
});

const detail = reactive({
  proAreaName: "",
  proName: "",
  templateList: [] as GetScheduleTempDetailResponse["scheduleTemplateList"],
});

(async () => {
  const { proAreaName, proName, scheduleTemplateList } = await getScheduleTempDetail({ proAreaId: props.proAreaId });
  detail.proAreaName = proAreaName || "";
  detail.proName = proName || "";
  detail.templateList = scheduleTemplateList.map((v) => ({
    ...v,
    scheduleList: v.scheduleList.map((t) => ({
      ...t,
      scheduleType: t.scheduleType === 6 ? 4 : t.scheduleType,
      _switchType: [4, 6].includes(t.scheduleType ?? 1) ? t.scheduleType : null,
    })),
  }));
})();
</script>

<style lang="scss" scoped>
.template-detail {
  height: 100%;
  &-top {
    width: 100%;
    margin: 15px 0;
    @include sc(12px, #383838);
  }
  &-middle {
    display: flex;
    margin-bottom: 10px;
    .checkbox {
      margin-right: 20px;
      :deep(.x-checkbox-text) {
        @include sc(14px, #555);
      }
    }
  }
  &-bottom {
    @include wh(100%, calc(100% - 100px));
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    .template {
      padding: 10px 15px;
      margin: 10px 0 20px 0;
      background: #f5f8fe;
      border-radius: 8px;
      &-name {
        @include sc(14px, #383838) {
          font-weight: 700;
        }
      }
    }
  }
}
</style>
