export default {
  common: {
    entAdmin: "企业管理员",
    proAdmin: "项目管理员",
    custService: "客服员",
    operator: "操作员",
    other: "其他",
    unsolved: "未解决",
    solved: "已解决",
    ordinaryAlarm: "普通告警",
    mildAlarm: "轻微告警",
    urgentAlarm: "紧急告警",
    deadlyAlarm: "致命告警",
    receive: "接收",
    notReceive: "不接收",
    ordinary: "普通",
    mild: "轻微",
    urgent: "紧急",
    deadly: "致命",
    havePopUp: "有弹窗",
    notPopUp: "无弹窗",
    unused: "未使用",
    used: "已使用",
    ChinaMobile: "中国移动",
    QianXun: "千寻",
    full: "全部",
    park: "园区",
    street: "街道",
    parkPlace: "停车处",
    chargePlace: "充电处",
    addWaterPlace: "加水处",
    garbageCollectPlace: "垃圾回收处",
    supplyPoint: "补给点",
    normalClean: "正常清扫",
    edgeClean: "沿边清扫",
    transfer: "转场",
    travel: "行驶",
    fullCoverClean: "全覆盖清扫",
    garbageRecognizeClean: "垃圾识别清扫",
    garbageRecognize: "垃圾识别",
    parkCar: "泊车",
    enabled: "已开启",
    closed: "已关闭",
    notTurnedOff: "不关机",
    turnedOff: "关机",
    normalSpeed: "常速",
    mediumSpeed: "中速",
    highSpeed: "高速",
    success: "成功",
    fail: "失败",
    bind: "已绑定",
    unBind: "未绑定",
    YiChuangZhiLian: "亿创智联",
    standard: "标准",
    soft: "柔和",
    power: "强力",
    missManholeCover: "井盖缺失",
    FallRecognize: "摔倒识别",
    garbageFull: "垃圾箱满",
    garbageDetection: "垃圾检测",
    pitDetection: "凹坑检测",
    barePicture: "裸图片",
    frontSide: "前置",
    leftSide: "左侧",
    rightSide: "右侧",
    backSide: "后置",
    front: "前",
    left: "左",
    right: "右",
    back: "后",
    AC: "交流",
    DC: "直连",
    wireless: "无线",
    // provincialName: "省级地名",
    // geographicalName: "地市级地名",
    // districtName: "区县级地名",
    nowork: "未作业",
    working: "作业中",
    unknown: "未知",
    charging: "充电中",
    standby: "待机",
    break: "中断",
    remoteDriving: "远程驾驶",
    manualDriving: "手动驾驶",
    autoDriving: "自动驾驶",
    controlDriving: "遥控驾驶",
    emergencyStop: "急停",
    waterNumber: "水量",
    waterShortage: "缺水",
    electricNumber: "电量",
    electricShortage: "缺电",
    garbageCan: "垃圾箱",
    dumpGarbage: "倒垃圾",
    task: "任务",
    event: "事件",
    coverClean: "覆盖清扫",
    recognizeClean: "识别清扫",
    inspectionClean: "巡检清扫",
    weltClean: "贴边清扫",
    scheduledTask: "定时作业",
    scheduledGarbageDisposal: "定时倒垃圾",
    scheduledCharging: "定时充电",
    scheduledPowerOnOff: "定时开关机",
    scheduledWatering: "定时加水",
    close: "关闭",
    firstGear: "一档",
    secondGear: "二档",
    thirdGear: "三档",
    daily: "每天",
    weekly: "星期",
    Monday: "周一",
    Tuesday: "周二",
    Wednesday: "周三",
    Thursday: "周四",
    Friday: "周五",
    Saturday: "周六",
    Sunday: "周日",
  },
  xComps: {
    // withDefaults / defineProps 待处理
    one: "一",
    two: "二",
    three: "三",
    four: "四",
    five: "五",
    six: "六",
    day: "日",
    month: "月",
    year: "年",
    startDate: "开始日期",
    endDate: "结束日期",
    startTime: "开始时间",
    endTime: "结束时间",
    selectTime: "选择时间",
    PSelect: "请选择",
    PEnterContent: "请输入内容",
    PEntSearchContent: "请输入搜索内容",
    PSelectDate: "请选择日期",
    noData: "暂无数据",
    sorryNoData: "实在抱歉！目前没有相关数据",
    now: "此刻",
    today: "今天",
    confirm: "确定",
    recognize: "确认",
    cancel: "取消",
    start: "开始",
    end: "结束",
    uploadSuccess: "上传成功",
    uploadFail: "上传失败",
    view: "查看",
    preview: "预览",
    download: "下载",
    delete: "删除",
    releaseMouse: "释放鼠标",
    dragToArea: "拖拽到此区域",
    fileUploadFail: "文件上传失败",
    loadFail: "加载失败",
    total: "共",
    page: "页",
    line: "条",
    item: "项",
    jumpTo: "跳至",
    fullSelect: "全选",
    alreadySelect: "已选",
    listEmpty: "列表为空",
  },
  login: {
    cszg: "城市之光 · 无人驾驶",
    accountLogin: "账号登录",
    enterAccount: "请输入账号",
    enterPassword: "请输入密码",
    enterCode: "请输入验证码",
    refresh: "刷新",
    login: "登录",
    rememberPassword: "记住密码",
    loginSuccess: "登录成功",
  },
  main: {
    jieya: "深圳市洁亚环保产业有限公司",
    cszg: "无人驾驶智慧城市服务平台",
    alarmSound: "告警声音",
    alarmLight: "告警灯",
    personalCenter: "个人中心",
    putAway: "收起",
    changePassword: "修改密码",
    logout: "退出登录",
    DoYouLogout: "确认要退出登录吗？",
  },
  mainComps: {
    imageLineDesc: "图像标线说明",
    garbage: "垃圾",
    pipeline: "管线",
    clearedGarbage: "已清理垃圾",
    newGarbage: "新垃圾",
    unclearGarbage: "未处理垃圾",
    changePassword: "修改密码",
    originalPassword: "原密码",
    PEntOriPassword: "请输入原密码",
    newPassword: "新密码",
    PEntCharNumberLetter: "请输入6-15字符，数字、字母组合",
    confirmPassword: "确认密码",
    PEntNewPassword: "请输入新密码",
    oriPasswordError: "原密码输入错误",
    PEntCharNumberLetterAcc: "请输入6-15个字符，字母 + 数字组成的账号",
    cannotSamePassword: "新密码与原密码不能相同",
    notMatchPassword: "确认密码与新密码输入不一致",
    passwordChangeSuccess: "密码修改成功",
    account: "账号",
    phoneNumber: "手机号",
    positionType: "岗位类型",
    ofRole: "所属角色",
    ofCompany: "所属公司",
    loginInfo: "登录信息",
    lastLogin: "上次登录",
    dragImageToThis: "将图片拖拽到此区域",
    dragImageCutAvatar: "拖拽图片剪切头像",
    uploadPicture: "上传图片",
    dragImageSelCropRange: "拖拽图片选择裁剪范围",
    save: "保存",
    filePreview: "文件预览",
    notPreviewPleaseDownload: "该文件类型不支持预览，请下载后查看",
    PSelectRoute: "请选择路线",
    PSelectTaskType: "请选择任务类型",
    addRoute: "添加路线",
    taskType: "任务类型",
    unknown: "未知",
    detail: "详情",
    location: "位置",
    selectChargingPosition: "选择充电位",
    fault: "故障",
    opera: "操作",
    auto: "自动",
    taskSet: "任务已设置",
    define: "定义",
  },
  mainMenus: {
    visualize: "可视化看板",
    companyData: "企业数据看板",
    projectData: "项目数据看板",
    monitor: "车辆监控",
    carLive: "车辆实时查看",
    trackReplay: "轨迹回放",
    alarmStatistics: "告警统计",
    system: "系统管理",
    companyManage: "企业管理",
    roleManage: "角色管理",
    userManage: "用户管理",
    simManage: "SIM卡管理",
    rtkManage: "RTK账号管理",
    carInfoManage: "车辆信息管理",
    chargingStationManage: "充电桩信息管理",
    projectManage: "项目管理",
    proAreaManage: "项目区域管理",
    operation: "运营管理",
    proUserManage: "项目人员管理",
    proVehManage: "项目车辆管理",
    vehOperationRecord: "车辆操作记录",
    report: "报表管理",
    vehTaskStatistics: "车辆任务数据统计",
    vehicleCleanReport: "车辆任务数据详情",
    vehTaskSummary: "车辆任务汇总统计",
    proAreaSummary: "项目区域汇总统计",
    vehEventStatistics: "车辆事件处理统计",
    securityEventStatistics: "安全事件上报统计",
    config: "配置管理",
    carType: "车辆型号管理",
    alarmManage: "告警配置管理",
    eventCapture: "事件抓拍设置",
  },
  visualize: {},
  companyData: {},
  projectData: {},
  monitor: {},
  carLive: {
    showRoute: "显示路线",
    showArea: "显示区域",
    front: "前",
    back: "后",
    left: "左",
    right: "右",
    DoCarContinueTask: "车是否继续任务？",
    continue: "继续",
    notNeed: "不用",
    carContinueTaskSuccess: "车继续任务成功！",
    realTimeAlarms: "实时告警",
    individual: "个",
    dataUploadTime: "数据上传时间",
    alarmStatistics: "告警统计",
    noPermission: "暂无权限",
    noContent: "暂无内容",
    view: "查看",
    unread: "未读",
    read: "已读",
    all: "全部",
    electricNumber: "电量",
    waterTank: "水箱",
    garbageCan: "垃圾箱",
    batteryTemperature: "电池温度",
    batteryCurrent: "电池电流",
    batteryVoltage: "电池电压",
    currentMileage: "当前里程",
    totalMileage: "总里程",
    operaLight: "作业灯",
    operaEquipmentStatus: "作业设备状态",
    sweep: "清扫",
    on: "开",
    off: "关",
    sweepArea: "清扫面积",
    dataUpload: "数据上传",
    upload: "上传",
    noAlarm: "无告警",
    normalLight: "照明灯",
    workLight: "作业灯",
    doubleLight: "双闪灯",
    leftLight: "左转灯",
    rightLight: "右转灯",
    frontLeftWheel: "前左轮",
    frontRightWheel: "前右轮",
    backLeftWheel: "后左轮",
    backRightWheel: "后右轮",
    lightStatus: "灯光状态",
    tirePressureState: "胎压状态",
    carId: "车牌号",
    workStatus: "工作状态",
    speed: "速度",
    uploadTime: "上传时间",
    currentFault: "当前故障",
    carRealTimeStatus: "车辆实时状态",
    online: "在线",
    offline: "离线",
    alarm: "告警",
    controlSetting: "控制设置",
    switchSetting: "开关设置",
    sweepBrush: "扫刷",
    blower: "风机",
    permissionRelease: "权限释放",
    selectAll: "全选",
    setting: "设置",
    speedSetting: "速度设置",
    sprinkler: "洒水",
    suctionNozzleDustReduction: "吸口降尘",
    boxDustReduction: "箱体降尘",
    close: "关闭",
    firstGear: "一档",
    secondGear: "二档",
    thirdGear: "三档",
    openActive: "开启",
    descending: "降落",
    lift: "抬升",
    narrowGear: "窄档",
    middleGear: "中档",
    wideGear: "宽档",
    open: "打开",
    commandSendSuccess: "指令下发成功",
    failed: "失败",
    makeSureVehicleSpeedTo: "确定要将该车辆速度设置为",
    isItOk: "吗？",
    vehicle: "车",
    commandSetSuccess: "指令设置成功",
    makeSurePermissionRelease: "确定释放云端控制权限",
    controlAuthReleasedSuccess: "控制权限释放成功",
    taskSetting: "任务设置",
    save: "保存",
    reset: "重置",
    PSelectTemplate: "请选择模板",
    routeNameMiss: "路线名缺失",
    sweepTypeMiss: "清扫类型缺失",
    putAway: "收起",
    more: "更多",
    cyclesTimes: "循环次数",
    endPoint: "结束点",
    PSelectSite: "请选择站点",
    PSelectTaskType: "请选择任务类型",
    PSelectParkSpace: "请选择车位",
    isRunCurrentTask: "是否执行当前任务？",
    vehicleScheduleTask: "车辆有排班任务，将在",
    minAfterRun: "分钟后执行",
    customRoute: "自定义路线",
    templateRoute: "模板路线",
    uniformType: "统一型",
    multiType: "多类型",
    vehicleOffTaskCannotSend: "车辆已下线，任务无法下发！",
    taskSendSuccess: "任务下发成功",
    auto: "自动",
    closeMachine: "关机",
    start: "启动",
    openMachine: "开机",
    task: "任务",
    control: "控制",
    camera: "摄像头",
    cockpit: "驾驶舱",
    pause: "暂停",
    execute: "执行",
    in: "中",
    charging: "充电中",
    vehicleSwitchAutoMode: "确定将车辆切换至自动驾驶模式",
    PCheckAroundAndSafe: "请检查车辆周边环境，安全驾驶",
    autoDrive: "自动驾驶",
    confirm: "确认",
    success: "成功",
    standby: "待机",
    confirmExecSweepTask: "确认执行清扫任务吗？",
    confirmEndCharging: "确定结束充电吗？",
    ifConfirmEndBreakCharge: "如确定结束，车辆断开充电",
    endChargingSuccess: "结束充电成功",
    cancel: "取消",
    confirmWant: "确定要",
    notInstallCockpit: "未安装驾驶舱客户端",
    vehicleNotEnterSN: "车辆未录入远程SN码",
    smallScreen: "小屏",
    bigScreen: "大屏",
    fullScreen: "全屏",
    PClickOpenVideo: "请点击打开视频",
    nowPlaying: "正在播放",
    flatten: "平铺",
  },
  trackReplay: {
    currentTimeNoVehicle: "当前时段无车辆轨迹，请重新选择！",
    currentEnvNotSupport: "当前环境不支持",
    node: "节点",
    coordinate: "坐标",
    time: "时间",
    addresse: "地址",
    route: "路线",
    dataUploadTime: "数据上传时间",
    PSelect: "请选择",
    deviceId: "车牌号",
    startTime: "开始时间",
    endTime: "结束时间",
    loadRoutePoint: "加载轨迹点",
    search: "查询",
    PSelectValidTimeRange: "请选择有效时间段！",
  },
  alarmStatistics: {
    alarmStatistics: "告警统计",
    PEnterDeviceId: "请输入车牌号",
    PEnterFaultCode: "请输入故障编码",
    startTime: "开始时间",
    endTime: "结束时间",
    search: "查询",
    reset: "重置",
    unsolved: "未解决",
    solved: "已解决",
    process: "处理",
    detail: "详情",
    orderId: "序号",
    faultName: "告警名称",
    faultCode: "故障编码",
    faultInfo: "告警信息",
    faultLevel: "告警等级",
    faultTime: "告警时间",
    faultDetail: "告警详情",
    status: "状态",
    level: "等级",
    deviceId: "车牌号",
    deviceInfo: "车辆信息",
    deviceNumber: "车牌号码",
    deviceType: "车辆类型",
    time: "时间",
    opera: "操作",
    total: "总计",
    realTime: "实时",
    today: "当天",
    yesterday: "昨天",
    lastSevenDays: "近7天",
    editProcess: "编辑处理",
    reason: "理由",
    problemDesc: "问题描述",
    PEnter: "请输入",
    vehicleStillAlarm: "车辆还处于警报中，不能选择已解决",
    dataSaveSuccess: "数据保存成功",
    ofProject: "所属项目",
    ofEnterprise: "所属企业",
    recordInfo: "记录信息",
  },
  system: {},
  companyManage: {
    companyManage: "企业管理",
    add: "新增",
    PEnterEntName: "请输入企业名",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderNumber: "序号",
    entName: "企业名称",
    parentEnt: "上级企业",
    createdTime: "创建日期",
    userName: "联系人",
    userAccount: "帐号",
    mobile: "联系电话",
    opera: "操作",
    sureToDelet: "您确定要删除该企业？",
    PDeleteRelatedCarAccountSimRoleProjectFirst: "需先删除企业关联的车辆，账号，SIM卡，角色，项目信息",
    editCompany: "编辑企业信息",
    deleteSuccess: "删除成功",
    baseInfo: "基本信息",
    PEnterUserName: "请输入联系人",
    PEnterMobile: "请输入联系电话",
    account: "登录账号",
    entAddress: "企业地址",
    PEnterEntAddress: "请输入企业地址",
    functionConfig: "功能配置",
    PSelectParentEnt: "请选择上级企业",
    PSelectProvinceCityDistrict: "请选择省市区",
    mobileIncorrect: "您输入的联系电话有误",
    mobileExist: "您输入的联系电话已存在",
    saveSuccess: "数据保存成功",
    addCompany: "新增企业",
    PEntCharNumberLetterAcc: "请输入6~15个字符，字母，数字组成的账号",
    PEnterAccount: "请输入登录帐号",
    accountExist: "您输入的登录帐号已存在",
    loginPassword: "登录密码",
    PEntCharNumberLetterPWD: "请输入6-15个字符，数字，字母组合的密码",
    PEnterPassword: "请输入登录密码",
    confirmationOWD: "确认密码",
    PEnterConfirmPWD: "请输入确认密码",
    notMatchPassword: "确认密码必须与登录密码一致",
    addSuccess: "新增企业成功",
    companyDetail: "企业详情",
    functionSetting: "功能设置",
    interface: "界面",
    function: "功能",
    mainAccount: "主帐号",
    address: "地址：",
  },
  roleManage: {
    roleManage: "角色管理",
    add: "新增",
    PEnterEntName: "请输入企业名",
    PEnterRoleName: "请输入角色名称",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderNumber: "序号",
    roleName: "角色名称",
    entName: "企业名称",
    opera: "操作",
    sureToDelete: "确认要删除该角色？",
    willRemoveAccPermission: "如账号已分配该角色，将移除对应的角色权限！",
    deleteSuccess: "删除角色成功",
    staffList: "人员名单",
    account: "帐号",
    userName: "姓名",
    mobile: "手机号",
    roleDetail: "角色详情",
    noData: "字段未输出",
    staff: "人员",
    functionSetting: "功能设置",
    interface: "界面",
    function: "功能",
    addRole: "新增角色",
    baseInfo: "基本信息",
    enterprise: "企业",
    PSelectEnt: "请选择企业",
    functionConfig: "功能配置",
    roleExist: "您输入的角色名称已存在",
    addSuccess: "新增角色成功",
    editRole: "编辑角色",
    saveSuccess: "数据保存成功",
  },
  userManage: {
    userManage: "用户管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    PEnterAccNamePhone: "请输入账号/姓名/手机号",
    search: "查询",
    reset: "重置",
    carAuth: "车辆授权",
    edit: "编辑",
    detail: "详情",
    enableUser: "解禁",
    resetPWD: "重置密码",
    disableUser: "禁用",
    delete: "删除",
    orderNumber: "序号",
    account: "账号",
    name: "姓名",
    phone: "手机号",
    company: "企业",
    type: "类型",
    status: "状态",
    opera: "操作",
    sureToEnableAcc: "确认解禁该账号吗？",
    enableSuccess: "解禁用户成功",
    sureToDelAcc: "确认删除该账号吗？",
    willDisbleCarAndRole: "将解绑账号下车辆，角色关系",
    deleteSuccess: "删除用户成功",
    sureToDisableAcc: "确认禁用该账号吗？",
    disableSuccess: "禁用用户成功",
    sureToResetPWD: "确认要重置该账号密码 ？",
    resetPWDSuccess: "密码重置成功",
    newPWD: "新密码",
    copySuccess: "复制成功",
    vehicle: "辆",
    PEnterVehNo: "请输入车牌号进行搜索",
    allCars: "所有车辆",
    authorizedCars: "已授权车辆",
    saveSuccess: "数据保存成功",
    baseInfo: "基本信息",
    role: "所属角色",
    accountType: "账号类型",
    stationType: "岗位类型",
    addUser: "新增用户",
    entName: "企业名称",
    loginAcc: "登录账号",
    loginPWD: "登录密码",
    confirmationPWD: "确认密码",
    PEnterUserName: "请输入该用户姓名",
    PEnterPhone: "请输入手机号",
    phoneIncorrect: "您输入的手机号码有误",
    phoneExist: "您输入的手机号码已存在",
    PSelectCompany: "请选择企业",
    PEntCharNumberLetterAcc: "请输入6~15个字符，字母，数字组成的账号",
    PSetLoginAcc: "请设置该登录账号",
    PEntCharNumberLetterPWD: "请输入6-15个字符，数字，字母组合的密码",
    PEnterPWD: "请输入该账号密码",
    notMatchPassword: "登陆密码和确认密码不一致",
    PSelectStationType: "请选择岗位类型",
    PConfirmPWD: "请确认密码",
    PEnterRoleName: "请输入角色名称",
    PEnterCompany: "请输入企业名称",
    createSuccess: "创建用户成功",
    editInfo: "编辑信息",
  },
  simManage: {
    simManage: "SIM卡管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    PEnterSim: "请输SIM卡号",
    search: "查询",
    reset: "重置",
    startDate: "开始日期",
    endDate: "结束日期",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderNumber: "序号",
    simId: "SIM卡号",
    status: "状态",
    entName: "企业名",
    vehicleNo: "车牌号",
    registerDate: "登记日期",
    netOperator: "网络运营商",
    operator: "运营商",
    opera: "操作",
    unUsed: "未使用",
    inUse: "使用中",
    all: "全部",
    sureToDelSim: "确认删除该SIM卡吗",
    deleteSuccess: "删除SIM卡成功",
    simDetail: "SIM卡详情",
    simNo: "卡号",
    enterprise: "企业",
    editSimInfo: "编辑SIM卡信息",
    PSelect: "请选择",
    editSuccess: "编辑SIM卡成功",
    PSelectEnt: "请选择企业",
    simNoIncorrect: "您输入的SIM卡号有误",
    ChinaMobile: "中国移动",
    ChinaTelecom: "中国电信",
    ChinaUnicom: "中国联通",
    addSim: "新增SIM卡",
    addSuccess: "创建SIM卡成功",
  },
  rtkManage: {
    rtkManage: "RTK账号管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    PEnterRTK: "请输入RTK账号",
    startDate: "开始日期",
    endDate: "结束日期",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    unUsed: "未使用",
    inUse: "使用中",
    all: "全部",
    orderNumber: "序号",
    rtkAccount: "RTK账号",
    status: "状态",
    vehicleNo: "车牌号",
    entName: "企业名",
    registerDate: "登记日期",
    netOperator: "提供商",
    operator: "供应商",
    opera: "操作",
    sureToDelRTK: "确认要删除该RTK账号？",
    deleteSuccess: "删除RTK账号成功",
    editRTK: "编辑RTK账号",
    addRTK: "新增RTK账号",
    enterprise: "企业",
    rtkPassword: "RTK密码",
    PEnterRTKPassword: "请输入RTK密码",
    PSelectOperator: "请选择供应商",
    PSelectRegisterStartDate: "请选择登记开始日期",
    entNameRequired: "企业名称不能为空",
    rtkAccountRequired: "RTK账号不能为空",
    rtkAccountExist: "您输入的RTK账号已存在",
    rtkPasswordRequired: "RTK密码不能为空",
    editSuccess: "编辑RTK账号成功",
    createSuccess: "创建RTK账号成功",
    rtkDetail: "查看详情",
    baseInfo: "基本信息",
  },
  carInfoManage: {
    carInfoManage: "车辆信息管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    opDate: "投放日期",
    startDate: "开始日期",
    endDate: "结束日期",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    allModels: "全部型号",
    allTypes: "全部类型",
    vehicleNo: "车牌号",
    vinNo: "车辆VIN",
    simNo: "SIM卡号",
    PEnter: "请输入",
    orderNumber: "序号",
    entName: "企业名",
    type: "类型",
    model: "型号",
    opera: "操作",
    sureToDelCarInfo: "确认要删除车辆信息？",
    willUnbindCarAndSim: "将解除车辆与SIM卡绑定关系",
    deleteSuccess: "删除车辆信息成功",
    editCarInfo: "编辑车辆信息",
    confirm: "确认",
    baseInfo: "基本信息",
    enterprise: "企业",
    vehicleType: "车辆类型",
    vehicleModel: "车辆型号",
    sn: "远程SN码",
    remoteSim: "远程SIM",
    PEnterVehicleNo: "请输入车牌号",
    PEnterVinNo: "请输入VIN码",
    PSelectvehicleType: "请选择车辆类型",
    PSelectVehicleModel: "请选择车辆型号",
    PSelectSimNo: "请选择SIM卡",
    PEnterSN: "请输入远程SN码",
    PEnterRemoteSim: "请输入远程5GCPE SIM卡",
    dandelionAcc: "蒲公英账号",
    PEnterDandelionAcc: "请输入蒲公英账号",
    dandelionPWD: "蒲公英密码",
    PEnterDandelionPWD: "请输入蒲公英密码",
    rtkAccount: "RTK账号",
    PSelectRtkAcc: "请选择RTK账号",
    carConfigList: "车辆配置清单",
    supportsWordPDForExcel: "支持word,pdf,excel单个文件最大支持10M,最多支持3个文件上传",
    vinNoRequired: "VIN码不能为空",
    vinNoExist: "您输入的VIN码已存在",
    vehicleNoRequired: "车牌号不能为空",
    vehicleNoExist: "您输入的车牌号已存在",
    vehicleTypeRequired: "车辆类型不能为空",
    simNoRequired: "SIM卡不能为空",
    dandelionAccExist: "您输入的蒲公英账号已存在",
    saveSuccess: "数据保存成功",
    fileSizeExceedsLimit: "文件大小超出限制",
    addCar: "新增车辆",
    PSelectEnterprise: "请选择企业",
    createSuccess: "创建车辆成功",
    carInfo: "车辆信息",
  },
  chargingStationManage: {
    chargingStationManage: "充电桩信息管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    serialNumber: "充电桩序列号",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    bound: "已绑定",
    unbound: "未绑定",
    all: "全部",
    orderNumber: "序号",
    status: "状态",
    entName: "企业名称",
    opera: "操作",
    sureToDelChargingPile: "确定删除充电桩信息？",
    willUnbindChargingPileAndArea: "将解除充电桩与项目区域的绑定关系",
    deleteSuccess: "删除充电桩信息成功",
    editChargingPile: "编辑充电桩",
    serialNo: "序列号",
    chargingPileNo: "编号",
    ratedPower: "额定功率",
    type: "类型",
    model: "型号",
    supplier: "品牌",
    notes: "备注",
    PEnter: "请输入",
    PSelect: "请选择",
    PEnterSerialNo: "请输入序列号",
    PSelectTaskType: "请选择任务类型",
    PSelectType: "请选择类型",
    saveSuccess: "数据保存成功",
    addChargingPile: "新增充电桩",
    addSuccess: "新增充电桩成功",
    chargingPileDetail: "查看详情",
    project: "项目",
    area: "区域",
    enterprise: "企业",
  },
  projectManage: {
    projectManage: "项目管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    PSelectEnt: "请选择企业",
    PEnterProName: "请输入项目名称",
    PEnterUserName: "请输入负责人姓名",
    PSelectUserName: "请选择负责人",
    opDate: "投放日期",
    startDate: "开始日期",
    endDate: "结束日期",
    search: "查询",
    reset: "重置",
    staffAllocation: "人员分配",
    vehicleAllocation: "车辆分配",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderNumber: "序号",
    entName: "企业名称",
    proName: "项目名称",
    proSeq: "项目编号",
    userName: "负责人",
    opera: "操作",
    sureToDelProject: "确认要删除该项目吗？",
    willUnbindMemberCarAndProject: "将解除成员，车辆与项目的绑定关系",
    deleteSuccess: "删除项目成功",
    person: "人",
    PEnterStaff: "输入人员搜索",
    allStaffs: "所有人员",
    assigned: "已分配",
    vehicle: "辆",
    PEnterVehicleNo: "请输入车牌号进行搜索",
    allVehicles: "所有车辆",
    saveSuccess: "数据保存成功",
    editProInfo: "编辑项目信息",
    addProject: "新增项目",
    createSuccess: "创建项目成功",
    projectDetail: "项目详情",
    incorrectOpDateOutput: "开放日期字段后端输出不正确",
    incorrectMobileOutput: "手机号字段后端未输出",
    collapse: "收起",
    more: "更多",
  },
  proAreaManage: {
    proAreaManage: "项目区域管理",
    add: "新增",
    PEnterEnt: "请输入企业名",
    PSelectEnt: "请输入企业名",
    areaName: "区域名称",
    areaSeq: "区域编号",
    proName: "项目名称",
    proSeq: "项目编号",
    areaType: "区域类型",
    search: "查询",
    reset: "重置",
    carAuth: "车辆授权",
    taskTemplate: "任务模板",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderNumber: "序号",
    entName: "企业名称",
    opera: "操作",
    sureToDelProject: "确认要删除该项目区域吗？",
    willUnbindCarAndProArea: "将解除车辆与项目区域的绑定关系",
    deleteSuccess: "删除项目区域成功",
    vehicle: "辆",
    PEnterVehicleNo: "请输入车牌号进行搜索",
    allVehicles: "所有车辆",
    authorizedVehicles: "已授权车辆",
    saveSuccess: "数据保存成功",
    point: "点位",
    longitude: "经度",
    PEnterLongitude: "请输入经度",
    latitude: "纬度",
    PEnterLatitude: "请输入纬度",
    LeftClickToDrawFence: "鼠标左键点击地图可直接画围栏范围。",
    RightClicktoFinish: "鼠标右键点击完成绘制。",
    PEnterAddress: "请输入地名",
    area: "区域",
    baseInfo: "基本信息",
    PEnterAreaName: "请输入区域名称",
    PSelectAreaType: "请选择区域类型",
    PSelectProName: "请选择项目名",
    sites: "场所",
    routes: "路线",
    fences: "围栏",
    unknownSite: "未知场所",
    duplicateIDExist: "中存在重复ID，请修改后保存",
    PCompleteFenceInfo: "请将场所信息补充完整",
    PCompleteRouteInfo: "请将路线信息补充完整",
    SureToSaveFences: "保存所画区域点位信息嘛？",
    No: "否",
    Yes: "是，保存",
    addSuccess: "新增区域成功",
    editSuccess: "编辑区域成功",
    AtLeastOneRoute: "至少需要一条路线",
    AdjustTemplateIfRouteModified: "修改路线注意调整相关模板路线",
    addRoute: "添加路线",
    total: "共",
    points: "个点",
    routeName: "路线名称",
    PEnterRouteName: "请输入路线名称",
    cleanType: "清扫类型",
    PSelectCleanType: "请选择清扫类型",
    routeNameOfCar: "车端对应路线名称",
    PEnterRouteNameOfCar: "请输入车端对应路线名称",
    ClickMapToBegin: "点击地图可直接画路线",
    DragPointToAdd: "拖动描点可以增加描点",
    ClickPointToDel: "点击描点可删除描点",
    edgewise: "沿边清扫",
    editAreaRouteTemplate: "编辑区域路线模版",
    save: "保存",
    vehicleTaskTemplate: "车辆工作任务模板",
    LongPressDragAdjustOrder: "长按拖动可调整模板顺序",
    templateName: "模板名",
    PEnterName: "请输入名称",
    route: "路线",
    cancel: "取消",
    confirm: "确定",
    show: "查看",
    hide: "不查看",
    routeNameMissing: "路线名缺失",
    cleanTypeMissing: "清扫类型缺失",
    templateBeingEdited: "还有模板在编辑中",
    PConfirmBeforeSaving: "请先确定再保存",
    PConfirmBeforeAdding: "请先确定再新增",
    PConfirmBeforeEditing: "请先确定再编辑",
    PEnterTemplateName: "请填写模板名",
    PCompleteRoute: "请将路线补充完整",
    addSite: "添加场所",
    parkingSiteInTotal: "个泊车位",
    stationName: "名称",
    PEnterStationName: "请输入场所名称",
    stationType: "类型",
    PSelectStationType: "请选择类型",
    wayId: "路线ID",
    PEnter: "请输入",
    picture: "图片",
    addPicture: "添加图片",
    parkingSpot: "车位",
    PEnterID: "请输入ID",
    chargingPile: "充电桩",
    PSelectChargingPile: "请选择充电桩",
    addParkingSpot: "添加泊车位",
    parkingIDsCanNotBeDuplicated: "场所内车位ID不能重复",
    chargingStationAlreadyBeenAssociate: "此充电桩已被绑定，请重新选择",
    PCompleteParkingInfo: "请将泊车位信息补充完整",
    PEnterStationType: "请选择场所类型",
    range: "范围",
    PEnterRouteID: "请输入路线ID",
  },
  operation: {},
  proUserManage: {
    proUserManage: "项目人员管理",
    PEnterAccName: "请输入账号/姓名",
    allProject: "所有项目",
    search: "查询",
    reset: "重置",
    carAuth: "车辆授权",
    detail: "详情",
    resetPwd: "重置密码",
    orderNumber: "序号",
    userName: "姓名",
    account: "账号",
    mobile: "手机号",
    proName: "项目名",
    opera: "操作",
    sureToResetPWD: "确认要重置该账号密码？",
    resetSuccess: "密码重置成功",
    newPWD: "新密码",
    copySuccess: "复制成功",
    detailInfo: "查看详情",
    stationType: "岗位类型",
    vehicleInCharge: "负责车辆",
    authorizedVehicles: "已授权车辆",
    save: "保存",
    vehicle: "辆",
    PEnterAreaVehNo: "输入区域/车牌号进行搜索",
    allCars: "所有车辆",
    authorizedCars: "已授权车辆",
    saveSuccess: "数据保存成功",
  },
  proVehManage: {
    proVehManage: "项目车辆管理",
    scheduleTemplate: "排班模板",
    templateDetail: "模板详情",
    templateAdd: "新增模板",
    templateEdit: "编辑模板",
    PEnterVehNo: "请输入车牌号码",
    PEnterUser: "请输入负责人",
    allProjects: "全部项目",
    allAreas: "全部区域",
    allPlans: "全部计划",
    search: "查询",
    reset: "重置",
    off: "关",
    on: "开",
    edit: "编辑",
    setUser: "设置负责人",
    PSelectUser: "请选择负责人",
    cancel: "取消",
    confirm: "确定",
    setting: "设置",
    withOrWithoutSchedule: "有/无排班",
    schedule: "排班",
    detail: "详情",
    all: "全部",
    orderNumber: "序号",
    vehicleNo: "车牌号",
    schedulePlan: "排班计划",
    userName: "负责人",
    areaName: "区域名称",
    projectName: "项目名称",
    opera: "操作",
    prompt: "提示",
    vehicleHasNotBeenScheduled: "该车还未排班，请先设置排班计划",
    close: "关闭",
    open: "开启",
    success: "成功",
    settingItem: "设置项",
    thresholdBattery: "阀值（电池电量小于或等于）",
    status: "状态",
    automaticCharging: "自动充电",
    automaticShutdown: "自动关机",
    lowBatteryReminder: "电量低提醒（云端）",
    automaticWater: "自动加水",
    save: "保存",
    automatic: "自动",
    batteryLevelBelow: "电量低于",
    waterLevelBelow: "水量低于",
    range: "区间",
    to: "至",
    saveSuccess: "保存成功",
    viewDetails: "查看详情",
    vehicleModel: "车辆型号",
    vehicleType: "车辆类型",
    vehicleVinNo: "车辆VIN码",
    simNo: "SIM卡",
    configList: "配置清单",
    view: "查看",
    download: "下载",
    launchTime: "投放时间",
    hmiSetting: "HMI端设置",
    resetPWD: "密码重置",
    car: "车辆",
    sureToResetHMIPWD: "确认要重置该HMI端密码吗？",
    resetPWDSuccess: "端密码重置成功",
    newPWD: "新密码",
    copySuccess: "复制成功",
    online: "在线",
    offline: "离线",
    templateCount: "模板数",
    add: "新增",
    selectAll: "全选",
    addRoute: "加路线",
    speedSetting: "速度设置",
    fanSetting: "风机设置",
    blowWaterSetting: "洒水设置",
    templateName: "模板名称",
    PEnterTemplateName: "请输入模板名称",
    addTask: "新增任务",
    taskType: "任务类型",
    cycleTime: "循环周期",
    offTime: "关机时间",
    cleanTimes: "清扫次数",
    routeTemplate: "路线模板",
    fan: "风机",
    blowWater: "洒水",
    selectedJobScope: "所选作业范围",
    selectGarbageStation: "选择垃圾点",
    selectChargingStation: "选择充电点",
    selectWateringStation: "选择加水点",
    selectShutdownStation: "选择场所",
    PSaveBeforeAdd: "请先保存再新增",
    PConfirmBeforeAdd: "请先确认再新增",
    PEditBeforeConfirm: "请先确认再编辑",
    saveTemplateSuccess: "模板保存成功",
    sureToDeletTemplate: "你确定要删除该排班模板吗？",
    canNotRecoverDeleted: "删除后就不可恢复",
    Confirm: "确认",
    ok: "好的",
    initialSettingRestored: "已恢复初始数据",
    delete: "删除",
    PSelectTime: "请选择时间",
    deleteTemplate: "删除模板",
    deleteTask: "删除任务",
    taskStartTime: "作业开始时间",
    startTime: "开始时间",
    powerOnTime: "开机时间",
    daily: "每天",
    Monday: "周一",
    Tuesday: "周二",
    Wednesday: "周三",
    Thursday: "周四",
    Friday: "周五",
    Saturday: "周六",
    Sunday: "周日",
    scheduleSetting: "定时设置",
    syncVehicle: "同步车",
    selectTemplate: "选用模板",
    PSelectTemplate: "请选择模板",
    templateTask: "模板任务",
    existingTasks: "车上现有任务",
    PSaveTaskBeforeSync: "请先保存任务再同步",
    confirmSyncTaskToVehicle: "确定将任务同步到车吗？",
    syncSuccess: "同步成功",
    cloudSyncTime: "云端下发时间",
    vehicleSyncTime: "车端同步时间",
    synchronized: "已同步",
    unsynchronized: "未同步",
    scheduleLog: "排班操作记录",
    vehicleInfo: "车辆信息",
    tempNameCannotRepeat: "模板名不能重复",
  },
  vehOperationRecord: {
    vehOperationRecord: "车辆操作记录",
    PEnterEntVehNo: "请输入车牌号码",
    PEnterEntUser: "请输入操作者",
    startDate: "开始日期",
    endDate: "结束日期",
    search: "查询",
    reset: "重置",
    detail: "详情",
    allResults: "全部结果",
    orderNumber: "序号",
    vehicleNo: "车牌号",
    operationItem: "操作事项",
    operationResult: "操作结果",
    operator: "操作人",
    operationTime: "操作时间",
    operationSource: "操作来源",
    operationType: "操作类型",
    opera: "操作",
    allItems: "全部事项",
    viewDetails: "查看详情",
    operationInfo: "操作信息",
    operationItemDetails: "操作事项详情",
    taskName: "任务名称",
    taskType: "任务类型",
    loopCount: "循环次数",
    vehicleInfo: "车辆信息",
    projectArea: "项目区域",
    belongingProject: "所属项目",
    belongingCompany: "所属公司",
    operatorInfo: "操作者信息",
    operationAccount: "操作账号",
  },
  report: {},
  vehTaskStatistics: {
    vehTaskStatistics: "车辆任务数据统计",
    vehNumber: "车辆编号",
    startTime: "开始时间",
    endTime: "结束时间",
    search: "查询",
    reset: "重置",
    PEntVehNumber: "请在搜索框内输入车辆编号查询",
    PEntTime: "请输入时间",
    detail: "详情",
    proInfo: "项目信息",
    proName: "项目名称",
    proArea: "项目区域",
    taskTimes: "任务次数",
    times: "次",
    taskDuration: "任务时长",
    traveMiles: "行驶里程",
    cleanArea: "清扫面积",
    useWater: "用水",
    useElectric: "用电",
    garbageNumber: "垃圾量",
    date: "日",
    month: "月",
    cannotExce365Day: "日期范围不能超过365天",
    PfillSearchCriteria: "请将查询条件填写完整",
  },
  vehicleCleanReport: {
    vehicleCleanReport: "车辆任务数据详情",
    PEntVehNo: "请输入车牌号码",
    taskTime: "任务时间",
    startTime: "开始时间",
    endTime: "结束时间",
    export: "导出",
    search: "查询",
    reset: "重置",
    orderNumber: "序号",
    deviceId: "车牌号码",
    workNo: "任务编号",
    workTime: "任务时间",
    taskDuration: "任务时长",
    traveMiles: "行驶里程",
    cleanArea: "清扫面积",
    useWater: "用水",
    useElectric: "用电",
    garbageCleanNumber: "垃圾清扫量",
    now: "现在",
    exportSuccess: "导出成功",
    exportFail: "导出失败",
    cannotExportExce18Month: "导出日期范围不能超过18个月",
    PSelectCorrectTimeRange: "请选择正确的时间范围",
  },
  vehTaskSummary: {
    vehTaskSummary: "车辆任务汇总统计",
    export: "导出",
    search: "查询",
    reset: "重置",
    orderNumber: "序号",
    deviceId: "车牌号",
    now: "现在",
    exportSuccess: "导出成功",
    exportFail: "导出失败",
  },
  vehEventStatistics: {
    vehEventStatistics: "车辆事件处理统计",
    vehNumber: "车辆编号",
    startTime: "开始时间",
    endTime: "结束时间",
    search: "查询",
    reset: "重置",
    orderNumber: "序号",
    deviceId: "车牌号",
    status: "状态",
    camera: "摄像头",
    time: "时间",
    ofArea: "所属区域",
    ofProject: "所属项目",
    location: "位置",
    image: "图像",
    full: "全部",
  },
  securityEventStatistics: {
    securityEventStatistics: "安全事件上报统计",
    vehNumber: "车辆编号",
    startTime: "开始时间",
    endTime: "结束时间",
    search: "查询",
    reset: "重置",
    full: "全部",
    orderNumber: "序号",
    deviceId: "车牌号",
    type: "类别",
    camera: "摄像头",
    uploadTime: "上传时间",
    ofArea: "所属区域",
    ofProject: "所属项目",
    location: "位置",
    image: "图像",
  },
  proAreaSummary: {
    proSummary: "项目汇总",
    areaSummary: "区域汇总",
    PEntEntName: "请输入企业名",
    allProjects: "全部项目",
    allAreas: "全部区域",
    export: "导出",
    search: "查询",
    reset: "重置",
    orderNumber: "序号",
    project: "项目",
    area: "区域",
    company: "企业",
    taskDate: "日期",
    taskTimes: "任务数",
    times: "次",
    taskDuration: "时长",
    traveMiles: "里程",
    cleanArea: "面积",
    useWater: "用水",
    useElectric: "用电",
    garbageCleanNumber: "垃圾量",
    exportSuccess: "导出成功",
    exportFail: "导出失败",
    cannotExportExce90Day: "导出日期范围不能超过90天",
    PSelectCorrectTimeRange: "请选择正确的时间范围",
  },
  config: {},
  carType: {
    carType: "车辆型号管理",
    add: "新增",
    PEnterVehicleModelName: "请输入车辆型号名称",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderId: "序号",
    vehicleModelName: "车辆型号名",
    vehicleTypeName: "车辆类型名称",
    opera: "操作",
    allType: "全部类型",
    confirmDeleteVehicleModel: "确认要删除该车辆型号？",
    willDebondedVehicleSim: "将解除车辆与SIM卡绑定关系",
    deleteVehicleModelSuccess: "删除车辆型号成功",
    addVehicleModel: "新增车辆型号",
    modelName: "型号名",
    PEnterModelName: "请输入型号名",
    type: "类型",
    PSelectType: "请选择类型",
    remark: "备注",
    PEnterRemark: "请输入备注",
    youEnterModelExists: "您输入的型号已存在",
    createVehicleModelSuccess: "创建车辆型号成功",
    vehicleModelDetail: "车辆型号详情",
    modelNumber: "型号",
    editVehicleModel: "编辑车辆型号",
    dataSaveSuccess: "数据保存成功",
  },
  alarmManage: {
    alarmManage: "告警配置管理",
    setting: "设置",
    add: "新增",
    all: "全部",
    search: "查询",
    reset: "重置",
    edit: "编辑",
    detail: "详情",
    delete: "删除",
    orderId: "序号",
    opera: "操作",
    vehicleType: "车辆类别",
    alarmVersion: "告警版本",
    alarmModule: "告警模块",
    PEnterAlarmName: "请输入告警名称",
    alarmLevel: "告警等级",
    receiveOrNot: "是否接收",
    alarmName: "告警名称",
    level: "等级",
    interfaceDescription: "接口对应描述",
    confirmDeleteAlarmConfig: "确认要删除告警配置吗？",
    deleteAlarmConfigSuccess: "删除告警配置成功",
    receive: "接收",
    notReceive: "未接收",
    received: "已接收",
    have: "有",
    save: "保存",
    PEnterAlarmNameSearch: "请输入告警名称搜索",
    system: "系统",
    globalPopup: "全局弹窗",
    noPopup: "无弹窗",
    hasPopup: "有弹窗",
    setAlarmConfigSuccess: "设置告警配置成功",
    PEnter: "请输入",
    PSelect: "请选择",
    addAlarm: "新增告警",
    platformReceive: "平台接收",
    platformGlobalPopup: "平台全局弹窗",
    popupOrNot: "是否弹窗",
    interfaceDocking: "接口对接",
    interface: "接口",
    alarmDescription: "警情描述",
    createAlarmConfigSuccess: "创建告警配置成功",
    viewAlarmDetail: "查看告警详情",
    editAlarm: "编辑告警",
    modifyAlarmConfigSuccess: "修改告警配置成功",
  },
  eventCapture: {
    eventCapture: "事件抓拍设置",
    add: "新增",
    vehNumber: "车辆编号",
    search: "查询",
    reset: "重置",
    on: "开",
    off: "关",
    read: "读取",
    orderId: "序号",
    deviceId: "车牌号",
    PEnterDeviceId: "请输入车牌号",
    settingContent: "设置内容",
    settingTime: "设置时间",
    switch: "开关",
    opera: "操作",
    captureSetting: "抓拍设置",
    close: "关闭",
    open: "开启",
    success: "成功",
    type: "类型",
    PSelectType: "请选择类型",
    camera: "摄像头",
    captureInterval: "抓拍间隔",
    addType: "添加类型",
    typeCannotRepeat: "类型不能重复",
    captureSettingSaveSuccess: "抓拍设置保存成功",
    readMore: "读取详情",
    readTime: "读取时间",
  },
};
