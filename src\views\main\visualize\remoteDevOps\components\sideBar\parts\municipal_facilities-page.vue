<!-- 管线和碰撞 -->
<template>
  <div
    class="municipal_facilities-page"
    ref="containerRef"
  >
    <div
      class="municipal_facilities-page-item"
      v-for="item in listData"
      :key="item.id"
    >
      <div class="image_wrapper">
        <el-image
          class="image_item"
          :src="item.picUrl"
          fit="cover"
          :preview-src-list="[item.picUrl]"
          :preview-teleported="true"
        />

        <span class="image_wrapper_time">{{ item.createdTime }}</span>
      </div>
      <div class="municipal_facilities-page-item-result">
        <span class="label">人工识别结果：</span>
        <span class="municipal_facilities-page-item-result-pass">
          <template v-if="item.recognizeResult">
            <span v-if="item.recognizeResult === 1">已解除</span>
            <span
              v-else
              style="color: #f9852e"
              >未解除</span
            >
          </template>
          <template v-else>
            <span>未识别</span>
          </template>
        </span>
      </div>
      <div class="municipal_facilities-page-item-result">
        <span class="label">校验人：</span>
        <span
          class="municipal_facilities-page-item-result-pass"
          style="color: #242859"
          >{{ item.updatedUser ?? "-" }}</span
        >
      </div>
      <div class="municipal_facilities-page-item-result">
        <span class="label">时间：</span>
        <span
          class="municipal_facilities-page-item-result-pass"
          style="color: #242859"
          >{{ item.updatedTime }}</span
        >
      </div>
    </div>
    <div
      v-if="loading"
      class="loading-more"
    >
      加载中...
    </div>
    <div
      v-if="!hasMore"
      class="no-more"
    >
      没有更多数据了
    </div>
    <XEmpty
      v-if="listData.length === 0"
      description="暂无数据"
    ></XEmpty>
  </div>
</template>
<script setup lang="ts">
import { getMunicipalFacilitiesList } from "@/services/api";
import XEmpty from "@/components/x-empty.vue";
import { ref, inject } from "vue";
import type { PropType, Ref } from "vue";
import type { CarInfo } from "../../../type";

const props = defineProps({
  activeTab: {
    type: String as PropType<"pipeline" | "collision" | any>,
    default: "pipeline",
  },
});
const carInfo = inject("carInfo") as Ref<CarInfo>;
const listData = ref<any[]>([]);
const containerRef = ref<HTMLElement | null>(null);
const loading = ref(false);
const hasMore = ref(true);

const load = async () => {
  const res = await getMunicipalFacilitiesList({ areaId: carInfo.value.areaId });
  listData.value = res.data;
};

defineExpose({
  load,
});
</script>

<script lang="ts">
export default {
  name: "MunicipalFacilitiesPage",
};
</script>

<style scoped lang="scss">
.municipal_facilities-page {
  width: 100%;
  height: 100%;
  padding: 10px;
  padding-bottom: 100px;
  overflow-y: auto;

  .loading-more,
  .no-more {
    text-align: center;
    padding: 10px 0;
    color: #999;
    font-size: 12px;
  }

  &-item {
    width: 100%;
    height: 200px;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    .image_wrapper {
      position: relative;
      width: 100%;
      height: 140px;
      border-radius: 4px;
      .image_item {
        border-radius: 4px;
      }
      &_time {
        position: absolute;
        bottom: 5px;
        left: 5px;
        font-size: 10px;
        color: #ffffff;
      }
    }

    &-result {
      font-size: 12px;
      color: #666666;
      &-pass {
        display: inline-block;
        text-align: right;
        width: calc(100% - 85px);
      }
      &-location {
        color: #242859;
      }
      .label {
        display: inline-block;
        width: 85px;
        text-align: left;
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #909399;

    .el-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}
</style>
