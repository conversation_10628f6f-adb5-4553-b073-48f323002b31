<template>
  <section
    :class="[
      'info-window',
      {
        disable: !carStatus.online,
        bottomOpera: showTaskStatus || showStopCharge,
      },
    ]"
  >
    <div
      class="info-window-loading"
      v-if="carStatus.loading"
    >
      <img src="@/assets/images/loading.gif" />
      <span> {{ carStatus.machineStatus ? $t("closeMachine") : $t("start") }}{{ $t("in") }}... </span>
    </div>
    <span
      class="info-window-close"
      v-html="closeText"
      @click="clickHandle"
    ></span>
    <div class="info-window-container">
      <div
        class="top"
        :class="{ offline_bg: !carStatus.online }"
      >
        <div
          class="left"
          :class="{ offline_text: !carStatus.online }"
        >
          {{ carStatus.deviceId }}
        </div>
        <div
          class="right"
          :class="{ offline_text: !carStatus.online }"
        >
          {{ carStatus.online ? "工作中" : "已关机" }}
        </div>
      </div>
      <div class="center">
        <div class="center-round">
          <roundPercent
            class="image"
            :percent="carStatus.speedPercent * 100"
            :offline="isOffline"
          />
          <div class="percent">
            <span>{{ carStatus.speed }}</span>
            <span class="unit">km/h</span>
            <span class="label">速度</span>
          </div>
        </div>
        <div class="center-round">
          <roundPercent
            class="image"
            :percent="carStatus.speedPercent * 100"
            :offline="isOffline"
          />
          <div class="percent">
            <span>{{ carStatus.electric }}</span>
            <x-icon
              class="unit"
              name="map_battery_icon"
              width="12"
              height="12"
            />
            <span class="label">电量</span>
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-left">
          <span>{{ $t("dataUploadTime") }}</span>
        </div>
        <div class="bottom-right">
          <span>{{ carStatus.updateDate }}</span>
          <span>{{ carStatus.updateTime }}</span>
        </div>
      </div>
      <div class="footer">
        <div class="footer-opera">
          <div
            class="item"
            v-for="item in btnList"
            :key="item.key"
            @click="handleBtnClick(item.key)"
          >
            <x-icon
              :name="item.icon"
              width="20"
              height="20"
              :color="isHover ? '#fff' : '#383838'"
            />
            <span class="label">{{ item.label }}</span>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="taskVisible"
      title="任务"
      width="20%"
      :append-to-body="true"
      @close="handleTaskCancel"
    >
      <div class="task-center">
        <label>航线</label>
        <el-select
          v-model="taskId"
          clearable
          style="width: 240px"
          placeholder="请选择航线"
        >
          <el-option
            v-for="item in uavData.routeList"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </div>
      <template #footer>
        <div class="task-footer">
          <el-button
            type="info"
            @click="handleTaskCancel"
            >取消</el-button
          >
          <el-button
            type="primary"
            @click="handleTaskConfirm"
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch, onMounted, computed } from "vue";
import type { PropType } from "vue";
import { useMainStore } from "@/stores/main";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { workStatusMap, driveModeMap, parseSpeed, parseBattery } from "@/services/wsconfig";
import { delCar } from "@/services/wsapi";
import {
  takeoff,
  getUavRouteList,
  getUavExecuteId,
  executeTask,
  getUavAirportInfo,
  feachUavHover,
  feachUavContinue,
  feachUavReturn,
  getUavCabinInfo,
  getUavToken,
} from "@/services/api";
import xIcon from "@/components/x-icon.vue";
import roundPercent from "./roundPercent.vue";
import Message from "@/components/x-message";
import type { Router } from "vue-router";
import { ElMessageBox } from "element-plus";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  router: {
    type: Object as PropType<Router>,
    required: true,
  },
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
  markerBlur: {
    type: Function,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  vin: {
    type: String,
    required: true,
  },
});
const {
  socket,
  userInfo,
  updateActiveCarId,
  updateActiveUavId,
  updateUavVideoVisible,
  userInfo: { permitList },
} = useMainStore();

const taskId = ref("");
const taskVisible = ref(false);
const btnList = ref([
  {
    label: "起飞",
    icon: "takeoff_icon",
    key: "takeoff",
  },
  {
    label: "任务",
    icon: "task_icon",
    key: "task",
  },
  {
    label: "悬停",
    icon: "hover_icon",
    key: "hover",
  },
  {
    label: "继续",
    icon: "continue_icon",
    key: "continue",
  },
  {
    label: "返航",
    icon: "return_icon",
    key: "return",
  },
  {
    label: "视频",
    icon: "video_icon",
    key: "video",
  },
]);

const isHover = ref(false);
const uavData = ref({} as any);
const isOffline = computed(() => !carStatus.online);
const headImgUrl = computed(() => {
  if (userInfo.imgUrl) {
    return userInfo.imgUrl;
  } else {
    return new URL("/src/assets/images/user_default_head.png", import.meta.url).href;
  }
});
const dataLoaded = ref(false);
const showStopCharge = computed(
  () => carStatus.online && dataLoaded.value && carStatus.recharge && carStatus.chargeConnectStatus === "not_connect"
);

const closeText = ref("&#10005");
const carStatus = reactive<Record<string, any>>({
  workStatus: {
    icon: "",
    text: "",
    color: "",
  },
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
  worker: {
    imgUrl: headImgUrl,
    text: "",
    color: "#5964FB",
  },
  online: false,
  updateDate: "",
  updateTime: "",
  speed: 0.0,
  speedPercent: 0,
  battery: 0,
  taskStatus: 0,
  loading: false,
  machineStatus: false,
  recharge: false,
  chargeConnectStatus: "not_connect",
});

const handleBtnClick = debounce(async (key: string) => {
  if (!["takeoff", "task"].includes(key) && !carStatus.online) return Message("error", "设备不在线，无法操作");
  switch (key) {
    case "takeoff":
      // 起飞
      try {
        await takeoff({
          nodeId: uavData.value.airports[0].nodeId,
          droneId: props.vin,
          airportId: uavData.value.airports[0].platformId,
        }).then(() => {
          Message("success", "起飞成功");
        });
      } catch (error) {
        Message("error", "起飞失败");
      }
      break;
    case "task":
      // 任务
      taskVisible.value = true;
      break;
    case "hover":
      // 悬停
      ElMessageBox.confirm(`悬停：飞机停留空中不动`, `确定要${props.id}悬停吗？`, { type: "warning" }).then(
        async () => {
          try {
            await feachUavHover({ nodeId: uavData.value.airports[0].nodeId, droneId: props.vin }).then(() => {
              Message("success", "悬停成功");
            });
          } catch (error) {
            Message("error", "悬停失败");
          }
        }
      );
      break;
    case "continue":
      // 继续
      ElMessageBox.confirm(`继续：飞机继续执行任务`, `确定要${props.id}继续吗？`, { type: "warning" }).then(
        async () => {
          try {
            await feachUavContinue({ nodeId: uavData.value.airports[0].nodeId, droneId: props.vin }).then(() => {
              Message("success", "继续成功");
            });
          } catch (error) {
            Message("error", "继续失败");
          }
        }
      );
      break;
    case "return":
      // 返航
      ElMessageBox.confirm(`返航：飞机返回起飞点`, `确定要${props.id}返航吗？`, { type: "warning" }).then(async () => {
        try {
          await feachUavReturn({ nodeId: uavData.value.airports[0].nodeId, droneId: props.vin }).then(() => {
            Message("success", "返航成功");
          });
        } catch (error) {
          Message("error", "返航失败");
        }
      });
      break;
    case "video":
      // 视频
      updateUavVideoVisible(true);
      console.log(carStatus.vin, "carStatus.vin");
      updateActiveUavId(carStatus.vin);
      break;
    default:
      break;
  }
});

const handleTaskConfirm = async () => {
  try {
    await executeTask({
      nodeId: uavData.value.airports[0].nodeId,
      airportId: uavData.value.drones[0].platformId,
      missionId: taskId.value,
      executionId: uavData.value.executionId,
      skipFlightCheck: true,
    });
  } catch (error) {
    Message("error", "执行任务失败");
    return;
  }
};

const handleTaskCancel = () => {
  taskVisible.value = false;
  taskId.value = "";
};

const clickHandle = () => {
  closeInfoWindow();
  updateActiveCarId(null);
  !socket.enableCarIds.includes(props.id) && delCar([props.id]);
};

// 仅关闭弹窗 不断开连接
const closeInfoWindow = () => {
  props.window.close();
  props.markerBlur();
};

const init = async () => {
  try {
    // 并行请求执行ID和机场信息
    const [executionId, drone, routeList] = await Promise.all([
      getUavExecuteId({ droneId: props.vin }),
      getUavAirportInfo(),
      getUavRouteList(),
    ]);

    // 更新uavData
    uavData.value = {
      executionId,
      ...drone,
    };

    console.log(uavData.value, "uavData.value");

    uavData.value.routeList = routeList.map((item: any) => {
      return {
        label: item.missionName,
        value: item.missionId,
      };
    });
  } catch (error) {
    // 统一错误处理
    const errorMessage = error?.message || "操作失败";
    Message("error", errorMessage);
  }
};
onMounted(() => {
  init();
});

watch(
  () => socket.allStatus,
  (newV) => {
    if (newV) {
      const target = newV.find((v) => v.deviceId === props.id);
      if (target) {
        dataLoaded.value = true;

        Object.assign(carStatus, target);
        carStatus.online = target.online;
        carStatus.workStatus = workStatusMap[target.workStatus];
        carStatus.driveMode = JSON.parse(JSON.stringify(driveModeMap[target.driveMode]));
        if (isOffline.value) {
          carStatus.driveMode.icon += "_offline";
          carStatus.driveMode.color = "#999";
        }

        carStatus.updateDate = target.gpsDate;
        carStatus.updateTime = target.gpsTime;
        carStatus.speed = parseSpeed(target.speed);
        carStatus.speedPercent = Math.min(carStatus.speed / 10, 1);
        carStatus.battery = parseBattery(target.electric);
        carStatus.taskStatus = target.taskStatus;
        carStatus.recharge = target.recharge;
        carStatus.chargeConnectStatus = target.chargeConnectStatus;
      }
    }
  },
  { immediate: true }
);

watch(
  () => socket.baseStatus,
  (newV) => {
    const target = newV.find((v) => v.deviceId === props.id);
    carStatus.vin = target.vin;
  },
  { immediate: true }
);
</script>

<style lang="scss" scoped>
.info-window {
  @include wh(368px, 260px);
  @include bis("@/assets/images/map_info_window.png");
  &.disable {
    background-image: url("@/assets/images/map_info_window_disable.png");
  }
  &.bottomOpera {
    @include wh(368px, 290px);
    background-image: url("@/assets/images/map_info_window_task.png");
  }

  position: relative;
  &-loading {
    z-index: 5;
    @include wh(354px, 230px);
    position: absolute;
    top: 10px;
    left: 6px;
    background: rgba(255, 255, 255, 0.8);
    img,
    span {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    img {
      transform: translate(-50%, -70%);
      @include wh(57.6px, 64.2px);
    }
    span {
      width: max-content;
      transform: translate(-50%, 40px);
      @include sc(16px, #555) {
        font-weight: bold;
      }
    }
  }
  &-container {
    position: absolute;
    top: 20px;
    padding: 0 15px;
    width: 100%;
    .top {
      display: flex;
      border-radius: 60px;
      width: fit-content;
      min-width: 150px;
      background: rgb(229, 237, 255);
      .left {
        position: relative;
        border: 1px solid rgb(229, 237, 255);
        font-size: 14px;
        font-weight: 700;
        height: 100%;
        background-color: #fff;
        color: #242859;
        padding-left: 9px;
        padding-right: 20px;
        border-radius: 60px;
        white-space: nowrap;
      }
      .left::before {
        content: "";
        position: absolute;
        top: 50%;
        right: 5px;
        transform: translate(0%, -50%);
        border: 3px solid v-bind('carStatus.online ? "rgb(89, 100, 251)" : "rgb(141, 139, 139)"');
        border-radius: 50%;
        width: 5px;
        height: 5px;
      }
      .right {
        height: 23px;
        font-size: 10px;
        font-weight: 400;
        color: #5964fb;
        line-height: 23px;
        padding: 0 10px;
        white-space: nowrap;
      }
    }
    .center {
      display: flex;
      margin-top: 10px;
      justify-content: space-around;
      &-round {
        overflow: hidden;
        position: relative;
        @include ct-f(x);
        @include wh(104px, 94px);
        .image {
          position: absolute;
          top: 5px;
        }
        .percent {
          span:nth-child(1) {
            @include ct-p(x) {
              top: 35px;
            }
            @include sc(18px, #242859) {
              font-weight: bolder;
            }
          }
          .unit {
            @include ct-p(x) {
              top: 57px;
            }
            @include sc(12px, #9f9fa4);
          }
          .label {
            @include ct-p(x) {
              top: 70px;
            }
            @include sc(12px, #242859);
          }
        }
      }
    }
    .bottom {
      @include fj;
      height: 32px;
      line-height: 32px;
      background: #f6f6f6;
      border-radius: 4px;
      font-size: 12px;
      padding: 0 10px;
      &-left {
        color: #555;
      }
      &-right {
        color: #383838;
        span:nth-child(2) {
          padding-left: 10px;
        }
      }
    }
    .footer {
      margin-top: 12px;
      &-opera {
        display: flex;
        align-items: center;
        justify-content: space-around;
        user-select: none;
        .item {
          cursor: pointer;
          display: flex;
          flex-direction: column;
          align-items: center;
          padding: 3px;
          color: #383838;
          .label {
            font-size: 12px;
            font-weight: 400;
          }
          &:hover {
            background-color: #5964fb;
            border-radius: 4px;
            color: #fff;
            :deep(.x-icon) {
              fill: #fff;
              stroke: #fff;
            }
          }
        }
      }
    }
  }
  &-close {
    z-index: 6;
    cursor: pointer;
    display: block;
    @include ct-f;
    @include wh(16px) {
      font-size: 16px;
    }
    position: absolute;
    top: 25px;
    right: 18px;
  }
}

.offline_bg {
  background-color: rgb(223, 224, 224) !important;
}
.offline_text {
  color: #9f9fa4 !important;
}

.task-center {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
}
.task-footer {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  margin-top: 20px;
}
</style>
