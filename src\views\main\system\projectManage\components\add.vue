<template>
  <x-drawer
    :title="$t('addProject')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    :btnOption="{ position: 'center' }"
    bodyPadding="20px 0 20px 20px"
    footerType="following"
    width="672px"
  >
    <div
      ref="contentRef"
      class="content"
    >
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
      >
        <x-form-item
          :label="$t('entName')"
          name="entId"
        >
          <x-tree-select
            v-model:value="form.entId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectEnt')"
            showSearch
          />
        </x-form-item>
        <x-form-item
          :label="$t('proName')"
          name="proName"
        >
          <x-input
            v-model:value="form.proName"
            :placeholder="$t('PEnterProName')"
            :maxlength="25"
          />
        </x-form-item>
        <x-form-item
          :label="$t('userName')"
          name="userId"
        >
          <x-select
            v-model:value="form.userId"
            :options="formOptions.users"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectUserName')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('opDate')"
          name="openDate"
        >
          <DatePicker
            v-model:value="form.openDate"
            format="YYYY-MM-DD"
            :popupContainer="contentRef"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { ref, reactive, watch } from "vue";
import { userList, compTree, addPro } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import xSelect from "@/components/x-select.vue";
import { DatePicker } from "@/components/x-date-picker";
import Message from "@/components/x-message";
import { resTreeToXTree, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("projectManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    // 重置
    formRef.value && formRef.value.resetFields();
    formOptions.users = [];
  }
};

/**
 * 表单项
 */
const form = reactive({
  entId: "",
  proName: "",
  openDate: "",
  userId: "",
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  proName: [["required", $t("PEnterProName")]],
  entId: [[(v: any) => v !== 0, $t("PSelectEnt")]],
  // userId: [["required", $t("PSelectUserName")]],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    await addPro(form);
    emits("update:show", false);
    Message("success", $t("createSuccess"));
    emits("confirm");
    formRef.value.resetFields();
    formOptions.users = [];
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  company: [] as TreeItemType[],
  users: [] as any[],
});

/**
 * 用户列表
 */
const getUserList = async () => {
  if (!form.entId) return;

  // 用户
  const users = await userList({
    page: 1,
    limit: 500,
    entIdList: [form.entId],
    station: 2, // 后台要求固定传参
  });
  formOptions.users = (users?.list || []).map((item) => ({
    value: item.userId,
    label: item.userName,
  }));
};

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      // 企业树
      formOptions.company = reactive(resTreeToXTree([await compTree()]));
      // 用户列表
      getUserList();
    }
  }
);
watch(
  () => form.entId,
  () => {
    // 清除选择
    form.userId = "";
    // 用户列表
    getUserList();
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  background: #fff;
  border-radius: 8px;
}
</style>
