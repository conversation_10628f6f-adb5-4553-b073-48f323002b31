# !/bin/bash
webhook='https://oapi.dingtalk.com/robot/send?access_token=764676b602dda3c4f6c8e0a644d8028598b568d1b7447f9569627c1169b59a7a'
function sendDingTalkNotifications() {
  local title="最新部署"
  local text="\n### ${title}\n#### 部署分支：[${CI_COMMIT_REF_NAME}分支](${CI_PROJECT_URL}/commits/${CI_COMMIT_BRANCH})\n#### 部署状态：${DEPLOY_STATUS}\n#### 部署者：${GITLAB_USER_LOGIN}\n##### ${CI_COMMIT_MESSAGE} ${CI_COMMIT_SHORT_SHA}
  "
  curl "$webhook" -H 'Content-Type: application/json' -d "{\"msgtype\": \"markdown\",\"markdown\": {\"title\":\"$title\",\"text\": \"$text\"}}"
}

if [ "$?" -eq "0" ];then  # "$?" -eq "0" 表示上一句脚本执行成功，1的话表示失败
  DEPLOY_STATUS='部署成功！'
  sendDingTalkNotifications
else
  DEPLOY_STATUS='部署失败！'
  sendDingTalkNotifications
fi