<template>
  <button
    :class="[`x-button ${props.type}`, { 'is-loading': props.loading }]"
    :style="buttonStyle"
    :type="props.htmlType"
    @click="buttonClick"
  >
    <span
      v-if="props.loading"
      class="x-button-loading"
    >
      <x-icon :name="['blue', 'green', 'orange', 'red'].includes(props.type) ? 'loading_white' : 'loading'" />
    </span>
    <span
      v-else-if="props.icon"
      class="x-button-icon"
    >
      <x-icon :name="props.disabled && props.disabledIcon ? props.disabledIcon : props.icon" />
    </span>
    <span class="x-button-text">{{ props.text }}</span>
  </button>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import type { PropType } from "vue";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  type: {
    type: String as PropType<
      "paleBlue" | "blue" | "linearBlue" | "paleGreen" | "green" | "white" | "highlightWhite" | "orange" | "red"
    >,
    default: "blue",
  },
  text: {
    type: String,
    required: true,
  },
  size: {
    // large | middle | small
    type: String as PropType<"middle" | "small" | "large">,
    default: "middle",
  },
  color: {
    type: String,
  },
  icon: {
    type: String,
  },
  htmlType: {
    type: String as PropType<"button" | "reset" | "submit">,
    default: "button",
  },
  disabled: {
    type: Boolean,
    default: () => false,
  },
  disabledIcon: {
    type: String,
  },
  loading: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["click"]);
const buttonStyle = computed(() => {
  let style = "";
  if (props.color) style += `background-color: ${props.color};`;
  if (props.icon) style += `padding-left: 12px; padding-right: 12px;`;
  const height = {
    middle: 32,
    small: undefined,
    large: undefined,
  }[props.size];
  const padding = {
    middle: undefined,
    small: 8,
    large: undefined,
  }[props.size];
  const fontSize = {
    middle: undefined,
    small: 12,
    large: undefined,
  }[props.size];
  if (props.disabled) {
    style += `${
      {
        paleBlue: "background-color: #E5EDFF;",
        blue: "background-color: #E5EDFF;color:#9f9fa4;",
        linearBlue: "background-color: #FFF;color:rgba(89, 100, 251, 0.3);border: 1px solid rgb(172, 177, 253);",
        paleGreen: "background-color: #BFF6E2;",
        green: "background-color: #BFF6E2;",
        white: "background-color: #ACB1FD;color: #00000088;",
        highlightWhite: "background-color: rgba(255, 255, 255, 0.98);color:rgba(89, 100, 251, 0.3);",
        orange: "background-color: #FDDBC1;",
        red: "background-color: #E24562;",
      }[props.type]
    }cursor: not-allowed;`;
  }
  return (style += `height: ${height}px; line-height: ${height}px; padding: 3px ${padding}px; font-size: ${fontSize}px;`);
});
const buttonClick = (event: Event) => {
  if (props.disabled || props.loading) {
    event.stopPropagation();
    return;
  }
  emit("click", event);
};
</script>

<style lang="scss" scoped>
.x-button {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  padding-left: 18px;
  padding-right: 18px;
  border-radius: 4px;
  color: #fff;
  &.is-loading {
    cursor: not-allowed;
    opacity: 0.7;
  }
  &-loading {
    @include ct-f;
    height: 100%;
    margin-right: 5px;
    svg {
      animation: rotateCircle 2s linear infinite;
    }
  }
  &-icon {
    margin-right: 6px;
  }
  &.paleBlue {
    background-color: rgb(245, 249, 255);
    &:hover {
      background-color: rgb(245, 248, 255);
    }
    &:active {
      background-color: rgb(236, 239, 255);
    }
    .x-button-text {
      color: rgb(89, 100, 251);
    }
  }
  &.blue {
    background-color: #5964fb;
    &:hover {
      background-color: rgb(125, 134, 253);
    }
    &:active {
      background-color: rgb(58, 67, 203);
    }
  }
  &.linearBlue {
    color: #5964fb;
    background-color: #fff;
    border: 1px solid #5964fb;
    &:hover {
      background-color: #fff;
      border: 1px solid #5964fb;
    }
    &:active {
      background-color: #ecefff;
      border: 1px solid #5964fb;
    }
  }
  &.paleGreen {
    background-color: rgb(234, 254, 247);
    &:hover {
      background-color: rgb(241, 249, 247);
    }
    &:active {
      background-color: rgb(225, 247, 239);
    }
    .x-button-text {
      color: rgb(39, 212, 161);
    }
  }
  &.green {
    background-color: rgb(40, 224, 159);
    &:hover {
      background-color: rgb(104, 234, 188);
    }
    &:active {
      background-color: rgb(46, 185, 136);
    }
  }
  &.white {
    color: #000;
    background-color: rgb(255, 255, 255);
    &:hover {
      background-color: rgb(245, 248, 255);
    }
    &:active {
      background-color: rgb(236, 239, 255);
    }
  }
  &.highlightWhite {
    color: rgb(89, 100, 251);
    background-color: rgb(255, 255, 255);
    &:hover {
      background-color: rgb(245, 248, 255);
    }
    &:active {
      background-color: rgb(236, 239, 255);
    }
  }
  &.orange {
    background-color: rgb(249, 133, 46);
    &:hover {
      background-color: rgb(250, 174, 117);
    }
    &:active {
      background-color: rgb(232, 116, 29);
    }
  }
  &.red {
    background-color: rgb(226, 69, 98);
    &:hover {
      background-color: rgba(226, 69, 98, 0.8);
    }
    &:active {
      background-color: rgba(226, 69, 98, 0.8);
    }
  }
}
</style>
