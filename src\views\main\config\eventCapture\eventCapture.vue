<template>
  <section class="event-capture" ref="eventCaptureRef">
    <div class="event-capture-top">
      <div class="top-title">{{ $t("eventCapture") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:capture:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addModal.show = true"
        />
        <Add v-model:show="addModal.show" @confirm="addModal.confirm" />
      </div>
    </div>
    <div class="event-capture-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.deviceNo"
          :options="formOptions.deviceOptions"
          :popupContainer="eventCaptureRef"
          :placeholder="$t('vehNumber')"
          showSearch
          style="width: 240px"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:capture:list')"
          type="blue"
          :text="$t('search')"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button type="green" :text="$t('reset')" @click="resetSearchForm" />
      </div>
    </div>
    <div class="event-capture-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 设置内容 -->
        <template #contents="{ record }">
          <template v-for="(item, index) in record.contents" :key="index">
            <div class="setting-content-item">
              <span>{{ item.captureTypeText }}-</span>
              <span>{{ getCameraText(item.cameraStand) }}-</span>
              <span>{{ item.captureDuration }}s</span>
            </div>
          </template>
        </template>
        <!-- 开关 -->
        <template #captureOperation="{ record }">
          <x-switch
            :disabled="!permitList.includes('sys:capture:operate')"
            :checkedValue="1"
            :unCheckedValue="0"
            :unCheckedChildren="$t('off')"
            :checkedChildren="$t('on')"
            :checked="Boolean(record.captureOperation)"
            @mousedown="beforeChangeSwitch(record)"
          />
        </template>
        <!-- 操作 -->
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:capture:info')"
                @click="openDetail(record.deviceNo)"
              >
                {{ $t("read") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Detail
        v-model:show="detailModal.show"
        :deviceNo="detailModal.deviceNo"
      />
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive } from "vue";
import type { PageSizeType } from "@/components/types";
import {
  getVehList,
  getEventCaptureList,
  setEventCaptureStatus,
} from "@/services/api";
import type { GetEventCaptureListRequest } from "@/services/type";
import { fourCameraType, threeCameraType } from "@/assets/ts/config";
import { useMainStore } from "@/stores/main";
import xSwitch from "@/components/x-switch.vue";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import Message from "@/components/x-message";
import Add from "./components/add.vue";
import Detail from "./components/detail.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("eventCapture");

const {
  userInfo: { permitList },
} = useMainStore();

const eventCaptureRef = ref<any>();

/**
 * 表单
 */
// 搜索表单字段
const searchForm = reactive({
  deviceNo: "",
});
// 组件数据支持
const formOptions = reactive({
  deviceOptions: [] as { label: string; value: string }[],
  cameraStand: fourCameraType.concat(threeCameraType),
});

/**
 * 表格
 */
// 字段
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderId"),
      width: "30",
    },
    {
      key: "deviceNo",
      title: $t("deviceId"),
      width: "40",
    },
    {
      key: "contents",
      title: $t("settingContent"),
      width: "60",
      slots: "contents",
      slotsWrap: true,
    },
    {
      key: "createdTime",
      title: $t("settingTime"),
      width: "40",
    },
    {
      key: "captureOperation",
      title: $t("switch"),
      width: "40",
      slots: "captureOperation",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "40",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
// 摄像头文本
const getCameraText = (cameraStand: Array<string>) => {
  return cameraStand
    .map((type) => {
      const cameraType = formOptions.cameraStand.find(
        (item) => item.value === type
      );
      return cameraType ? cameraType.label : type;
    })
    .join("/");
};
// 搜索
const submitSearch = async () => {
  table.loading = true;

  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceNo: searchForm.deviceNo,
  } as GetEventCaptureListRequest;
  const { totalCount, list } = await getEventCaptureList(params);

  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 500);
};
// 重新搜索
const reSearch = (currentPage: number = 1) => {
  table.pagination["current"] = currentPage;
  submitSearch();
};
// 重置
const resetSearchForm = () => {
  searchForm.deviceNo = "";
  table.pagination["current"] = 1;
  submitSearch();
};
// 分页
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};

/**
 * 相关操作
 *
 */
// 切换开关
const beforeChangeSwitch = async (record: any) => {
  const param = {
    deviceNo: record.deviceNo,
    operation: record.captureOperation ? 0 : 1,
  };
  await setEventCaptureStatus(param);
  Message(
    "success",
    `${$t("captureSetting")}${
      record.captureOperation ? $t("close") : $t("open")
    }${$t("success")}`
  );
  reSearch(table.pagination.current);
};
// 新增
const addModal = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
// 读取
const detailModal = reactive({
  show: false,
  deviceNo: "",
});
const openDetail = (deviceNo: string) => {
  detailModal.deviceNo = String(deviceNo);
  detailModal.show = true;
};

// 初始化车辆列表
(async () => {
  resetSearchForm();
  formOptions.deviceOptions = (await getVehList()).map((item) => ({
    label: item!,
    value: item!,
  }));
})();
</script>

<style lang="scss">
.event-capture-table-setting-content-hover-popover {
  max-width: 400px;
  padding: 0 6px;
}
</style>
<style lang="scss" scoped>
.event-capture {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
    .middle-left {
      &-input {
        width: 200px;
        margin-right: 16px;
      }
      &-select {
        width: 150px;
        margin-right: 16px;
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .setting-content-item {
      display: inline-block;
      background: #f5f8fd;
      border-radius: 4px;
      padding: 4px;
      margin: 2px 5px;
    }
    .table-setting-content-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
