import areaOptions from "@/assets/ts/area";
import { treeBfsParse } from "@/assets/ts/utils";

const areaHash = {} as any;
treeBfsParse(areaOptions, "children", (item: any) => {
  areaHash[item.value] = item;
});

/** 验证是否大陆和香港的手机号 */
export const isPhoneNumber = (str: string) => /^(1[3-9]\d{9}|[4-9]\d{7})$/.test(str);

export const includeNumberCharacter = (str: string) =>
  /^[A-Za-z0-9]{6,15}$/.test(str) && /[A-Za-z]{1}/.test(str) && /[0-9]{1}/.test(str);

export const isArea = (areas: string) => {
  if (Array.isArray(areas) && areas.length > 0) {
    for (let i = 0; i < areas.length; i++) {
      const item = areaHash[areas[i]];
      if (item) {
        if (item.children) {
          if (areas[i + 1]) {
            continue;
          }
          return false;
        }
        continue;
      }
      return false;
    }
  }
  return true;
};

export const isSimId = (str: string) => /^[^\u4e00-\u9fa5]{1,30}$/.test(str);

export const isLongitude = (v: string | number) => Number(v) >= -180 && Number(v) <= 180;

export const isLatitude = (v: string | number) => Number(v) >= -85 && Number(v) <= 85;

export const isChecked = (v: any[] | null) => v && v.length > 0;
