.date-picker{
  display: flex;
  &-select{
    width: 50px;
    margin-right: 0;
    :deep(.x-select-normal-selector){
      background-color:#e9eafe;
      border-radius: 4px 0 0 4px;
      border-right: 0;
    }
  }
  &-popover{
    flex: 1;
    .x-date-picker-trigger.show-select{
      border-radius: 0 4px 4px 0;
      border-left: 0;
    }
  }
}


.x-date-picker-trigger {
  position: relative;
  height: 32px;
  display: flex;
  align-items: center;
  border-radius: 4px;
  border: 1px solid rgb(220, 220, 220);
  transition: border 0.2s;

  &_state-actived {
    border-color: #5964fb;
  }

  &__item {
    flex: 1;
    padding: 0 4px;
  }

  &__input {
    width: 100%;
    font-size: 14px;
    line-height: 24px;
    font-family: Arial;
    padding: 0 8px;
    border-radius: 4px;
    background-color: transparent;
    transition: background 0.2s, color 0.2s;

    &_state-focus {
      background-color: #f2f3f5;
    }
    &_state-preview {
      color: #888;
    }
  }

  &__suffix {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-basis: 30px;
    flex-grow: 0;
    flex-shrink: 0;
    height: 30px;
  }

  &__clear {
    position: absolute;
    top: 0;
    right: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #fff;
    border-radius: 6px;
    width: 30px;
    height: 30px;
    opacity: 0;
    cursor: pointer;
    transition: opacity 0.2s;
  }

  &:hover {
    border-color: #5964fb;
  }
  &:hover &__clear {
    opacity: 1;
  }
  
  &__split {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-basis: 30px;
    flex-grow: 0;
    flex-shrink: 0;
    height: 30px;
  }
}
.popover {
  background: rgb(255, 255, 255);
  box-shadow: 0px 4px 10px rgba(0, 0, 0, 0.1);
  border: 1px solid rgb(229, 230, 235);
  border-radius: 10px;
  user-select: none;
}
.popover__body {
  display: flex;
}
.popover__foot {
  display: flex;
  align-items: center;
  height: 41px;
  border-top: 1px solid #e5e6eb;
}
.panel-split {
  width: 1px;
  background-color: #e5e6eb;
}
.button-confirm {
  height: 24px;
  text-align: center;
  line-height: 24px;
  font-size: 14px;
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;
  color: #fff;
  background-color: #5964fb;
  margin-left: auto;
  margin-right: 8px;
  &.disabled{
    color: #9f9fa4;
    background-color: #E5EDFF;
    cursor: not-allowed;
  }
}
.button-side {
  height: 24px;
  text-align: center;
  line-height: 24px;
  font-size: 14px;
  border-radius: 4px;
  padding: 0 10px;
  cursor: pointer;
  color: #5964FB;
  background-color: #D9E0FB;
  margin-left: 8px;
}