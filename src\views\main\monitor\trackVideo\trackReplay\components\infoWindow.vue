<template>
  <section class="info-window">
    <div class="info-window-container">
      <span
        class="info-window-close"
        v-html="closeText"
        @mousedown="mousedownHandle"
      ></span>
      <div class="top">
        <capsuleIcon
          :config="carStatus.workStatus"
          style="margin-right: 10px"
        />
        <capsuleIcon
          :config="carStatus.driveMode"
          style="margin-right: 10px"
        />
        <capsuleIcon
          v-show="carStatus.worker.text && carStatus.worker.text !== '--'"
          :config="carStatus.worker"
        />
      </div>
      <div class="center">
        <div class="center-round">
          <roundPercent
            class="image"
            :percent="carStatus.speedPercent * 100"
          />
          <div class="percent">
            <span>{{ carStatus.speed }}</span>
            <span>km/h</span>
          </div>
        </div>
        <div class="center-battery">
          <div class="percent">{{ carStatus.battery }}%</div>
          <div class="image">
            <batteryPercent
              :percent="Number(carStatus.battery)"
              showIcon
            />
          </div>
        </div>
        <div class="center-water">
          <div class="percent">{{ carStatus.water }}%</div>
          <div class="image">
            <waterPercent
              :percent="Number(carStatus.water)"
              showIcon
            />
          </div>
        </div>
        <div class="center-garbage">
          <div class="percent">{{ carStatus.garbage }}%</div>
          <div class="image">
            <garbagePercent
              :percent="Number(carStatus.garbage)"
              showIcon
            />
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-left">
          <span>{{ $t("dataUploadTime") }}</span>
        </div>
        <div class="bottom-right">
          <span>{{ carStatus.dataTime }}</span>
        </div>
      </div>
      <div class="footer">
        <x-icon
          name="locate_blue"
          width="24"
          height="24"
        />
        <div class="content">
          {{ carStatus.addr }}
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch, computed } from "vue";
import type { PropType } from "vue";
import { workStatusMap, driveModeMap, parseSpeed, parseBattery, parseWater, parseGarbage } from "@/services/wsconfig";
import xIcon from "@/components/x-icon.vue";
import capsuleIcon from "@/views/main/monitor/carLive/components/capsuleIcon.vue";
import batteryPercent from "@/views/main/monitor/carLive/components/batteryPercent.vue";
import waterPercent from "@/views/main/monitor/carLive/components/waterPercent.vue";
import garbagePercent from "@/views/main/monitor/carLive/components/garbagePercent.vue";
import roundPercent from "@/views/main/monitor/carLive/components/roundPercent.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("trackReplay");

const props = defineProps({
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  info: {
    type: Object as PropType<any>,
    required: true,
  },
});
const headImgUrl = computed(() => {
  return new URL("/src/assets/images/user_default_head.png", import.meta.url).href;
});
const closeText = ref("&#10005");
const carStatus = reactive<any>({
  workStatus: {
    icon: "",
    text: "",
    color: "",
  },
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
  worker: {
    imgUrl: headImgUrl,
    text: "",
    color: "#5964FB",
  },
  speed: 0.0,
  speedPercent: 0,
  battery: 0,
  water: 0,
  garbage: 0,
  dataTime: "",
  addr: "",
});

watch(
  () => props.info,
  (newV) => {
    if (newV) {
      carStatus.workStatus = workStatusMap[newV.workStatus];
      // @ts-ignore
      carStatus.driveMode = JSON.parse(JSON.stringify(driveModeMap[newV.driveMode]));
      carStatus.worker.text = newV.worker;
      carStatus.speed = parseSpeed(newV.speed);
      carStatus.speedPercent = Math.min(carStatus.speed / 10, 1);
      carStatus.battery = parseBattery(newV.electric);
      carStatus.water = parseWater(newV.tank);
      carStatus.garbage = parseGarbage(newV.litter);
      carStatus.dataTime = newV.dataTime;
      carStatus.addr = newV.addr;
    }
  },
  {
    immediate: true,
    deep: true,
  }
);

// 关闭浮窗
const mousedownHandle = () => {
  props.window.close();
};
</script>

<style lang="scss" scoped>
.info-window {
  position: relative;
  @include wh(368px, 250px);
  @include bis("@/assets/images/map_info_window.png");
  &-container {
    position: absolute;
    top: 20px;
    padding: 0 15px;
    width: 100%;
    .top {
      display: flex;
      @include wh(100%, 28px);
    }
    .center {
      display: flex;
      margin-top: 10px;
      &-round {
        overflow: hidden;
        position: relative;
        @include ct-f(x);
        @include wh(104px, 94px);
        .image {
          position: absolute;
          top: 5px;
        }
        .percent {
          span:nth-child(1) {
            @include ct-p(x) {
              top: 35px;
            }
            @include sc(18px, #242859) {
              font-weight: bolder;
            }
          }
          span:nth-child(2) {
            @include ct-p(x) {
              top: 57px;
            }
            @include sc(12px, #242859);
          }
        }
      }
      &-battery,
      &-water,
      &-garbage {
        flex: 1;
        .percent {
          text-align: center;
          @include sc(14px, #242859) {
            font-weight: bolder;
          }
        }
        .image {
          @include ct-f(x);
          margin-top: 5px;
        }
      }
    }
    .bottom {
      @include fj;
      height: 32px;
      line-height: 32px;
      background: #f6f6f6;
      border-radius: 4px;
      font-size: 12px;
      padding: 0 10px;
      &-left {
        color: #555;
      }
      &-right {
        color: #383838;
        span:nth-child(2) {
          padding-left: 10px;
        }
      }
    }
    .footer {
      @include ct-f(y);
      height: 38px;
      border-radius: 4px;
      .content {
        @include sc(14px, #383838);
        @include ell;
        max-width: 310px;
      }
    }
  }
  &-close {
    position: absolute;
    top: 5px;
    right: 18px;
    display: block;
    @include ct-f;
    @include wh(16px) {
      font-size: 16px;
    }
    cursor: pointer;
  }
}
</style>
