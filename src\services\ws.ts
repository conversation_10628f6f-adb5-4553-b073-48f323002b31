import protobuf from "protobufjs";
import { useMainStore } from "@/stores/main";
import { sendToken, onBaseStatus, addCar } from "@/services/wsapi";

let reconnectTimer: any = null; // 重新连接定时器
let lockReconnect = false; // 避免重复连接
let needReconnect = true; // 当token失效时不允许重连
let AwesomeMessage: any;
const { socket } = useMainStore();

/** ws协议的前端变量 */
const msgEnum = [
  "allStatus",
  "baseStatus",
  "statsStatus",
  "systemWarnStatus",
  "alarmMessageStatus",
  "taskContinueConfirm",
  "chargingStationStatus",
  "screenWorkView",
  "screenAlarm",
  "screenWorkers",
  "vehicleList",
  "fahrtroutes",
  "siteInformation",
];

// websocket连接流程 (carLive页面)
// main - beforeCreate - connect() - new WebSocket
// 情况1=> open - sendToken                        => carLive - mounted - onBaseStatus+addCar => 连接成功
// 情况2=> carLive - mounted - onBaseStatus+addCar => 失败(未open)，递归直至成功open
// 情况3=> close                                   => connect() 直至成功open

export const connect = (openCb?: Function) => {
  let wsUrl = import.meta.env.VITE_WS_URL;
  if (import.meta.env.MODE === "testing" && window.location.protocol === "https:") {
    // 测试环境 https 需要使用 wss
    wsUrl = "wss://test-yun.chengshizhiguang.com/wss-test/";
  }
  socket.instance = new WebSocket(wsUrl);
  socket.instance.eventMap = new Map();
  socket.instance.binaryType = "arraybuffer";
  socket.instance.addEventListener("open", async function (event) {
    // console.log("open: ", event);
    sendToken();
    openCb && openCb();
    if (location.pathname.includes("carLive")) {
      onBaseStatus();
      addCar(socket.activeCarId ? [...new Set([...socket.enableCarIds, socket.activeCarId])] : socket.enableCarIds);
    }
  });
  socket.instance.addEventListener("error", function (event) {
    // console.log("error: ", event);
    // reconnect(openCb); // 貌似error之后也会触发close，只在close处理重连即可
  });
  socket.instance.addEventListener("close", function (event) {
    // console.log("close: ", needReconnect, socket.instance, event);
    // 退出登录后 socket.instance = null
    // token失效后 needReconnect = false  ws响应中会模拟退出登录
    needReconnect && socket.instance && reconnect(openCb);
    needReconnect = true;
  });
  socket.instance?.addEventListener("message", function (event) {
    const message = AwesomeMessage.decode(new Uint8Array(event.data));
    const data = JSON.parse(message.json);
    if (data.result) {
      // 10000=token失效 后端会主动断开触发close
      if (data.result === 10000) needReconnect = false;
      const cb = socket.instance?.eventMap?.get(data.uuid);
      cb && cb(data);
    } else {
      console.warn(`%ctype=${data.type}[${msgEnum[data.type]}]`, "color:darkgreen;font-weight:bold;", data);
      [
        () => {
          // type = 0
          socket.allStatus = data.message;
        },
        () => {
          // type = 1 车辆开关机 变更状态setMachineStatus : pending / fulfilled
          socket.baseStatus = data.message.map((v: any) => {
            const current = socket.baseStatus?.find((_v) => _v.deviceId === v.deviceId);
            return {
              ...{
                setMachineStatus: current?.setMachineStatus === undefined ? "" : current?.setMachineStatus,
              },
              ...v,
            };
          });
        },
        () => {
          // type = 2
          socket.statsStatus = data.message;
        },
        () => {
          // type = 3 全局告警
          socket.systemWarnStatus = data.message;
        },
        () => {
          // type = 4 窗口消息
          socket.alarmMessageStatus = data.message;
        },
        () => {
          // type = 5 服务端主动通知
          const { deviceId, emergencyOut } = data.message.content as _TaskContinueConfirmType["content"];
          // 显示最新车需要的弹框，其它车弹框关闭代表是旧的则不处理
          if (socket.taskContinueConfirm !== deviceId && !emergencyOut) return;
          socket.taskContinueConfirm = false;
          if (emergencyOut) {
            setTimeout(() => {
              socket.taskContinueConfirm = deviceId || false;
            }, 0);
          }
        },
        () => {
          // type = 6 充电桩状态消息
          socket.chargingStationStatus = data.message;
        },
        () => {
          // type = 7 大屏 - 作业概览汇总
          socket.screenWorkView = data.message;
        },
        () => {
          // type = 8 大屏 - 告警消息
          socket.screenAlarm = data.message;
        },
        () => {
          // type = 9 大屏 - 人员列表
          socket.screenWorkers = data.message;
        },
        () => {
          // type = 10 实时车辆列表
          socket.vehicleList = data.message;
        },
        () => {
          // type = 11 车辆行车路线
          socket.fahrtroutes = data.message;
        },
        () => {
          // type = 12 站点信息
          socket.siteInformation = data.message;
        },
      ][data.type]();
    }
  });
};
const reconnect = (openCb?: Function) => {
  if (lockReconnect) return;
  lockReconnect = true;
  reconnectTimer && clearTimeout(reconnectTimer);
  reconnectTimer = setTimeout(() => {
    connect(openCb);
    lockReconnect = false;
  }, Math.random() * 1000 + 1000);
};

protobuf.load(`/WebsocketInfo.proto`, function (err, root) {
  AwesomeMessage = root?.lookupType("WebsocketInfo.WebsocketInfo");
});
