<template>
  <x-drawer
    :title="$t('controlSetting')"
    :visible="props.show"
    bodyPadding="0 0 32px 0"
    @update:visible="updateVisible"
    width="544px"
  >
    <div class="custom-setting">
      <div class="custom-setting-img">
        <div class="custom-setting-img-title">{{ id }}</div>
        <div class="custom-setting-img-content">
          <x-icon
            class="blow-motor"
            name="light_blow_motor"
            width="172px"
            height="198px"
            :style="{ opacity: customType[1].buttonStatus ? 1 : 0 }"
          />
          <img src="@/assets/images/light_status_car.png" />
          <x-icon
            class="head-lamp top"
            name="light_head_lamp"
            width="67px"
            height="57px"
            :style="{ opacity: customType[0].buttonStatus ? 1 : 0 }"
          />
          <x-icon
            class="head-lamp bottom"
            name="light_head_lamp"
            width="67px"
            height="57px"
            :style="{ opacity: customType[0].buttonStatus ? 1 : 0 }"
          />
          <x-icon
            class="water-pump top"
            name="light_water_pump"
            width="122px"
            height="82px"
            :style="{ opacity: customType[2].buttonStatus ? 1 : 0 }"
          />
          <x-icon
            class="water-pump bottom"
            name="light_water_pump"
            width="122px"
            height="82px"
            :style="{ opacity: customType[2].buttonStatus ? 1 : 0 }"
          />
        </div>
      </div>
      <div class="custom-setting-switch">
        <div class="custom-setting-switch-title">
          <span>{{ $t("switchSetting") }}</span>
          <span @click="authorityShow = !authorityShow" class="loadMore">
            <x-icon
              :class="authorityShow ? 'arrowBottom' : 'arrowTop'"
              name="arrow"
              width="13px"
              height="13px"
            />
          </span>
        </div>
        <div class="custom-setting-switch-content">
          <div class="btn-list-top">
            <template v-for="(item, index) in customType" :key="index">
              <div
                :class="['list-item', { disabled: item.isLoading }]"
                @click="!item.isLoading && toggleButtonStatus(item, index)"
                v-if="showBaseBtn(item)"
              >
                <div :class="['item-icon', { enable: item.buttonStatus }]">
                  <x-icon
                    v-if="!item.isLoading"
                    :name="
                      item.buttonStatus ? `${item.icon}_enable` : item.icon
                    "
                    width="22px"
                    height="22px"
                  />
                  <div v-else class="loading" />
                </div>
                <div class="item-title">
                  {{ item.label }}({{
                    item.buttonStatus ? $t("on") : $t("off")
                  }})
                </div>
              </div>
            </template>
          </div>
          <div class="btn-list-bottom">
            <div class="btn-list-bottom-left">
              <div class="btn-title">{{ $t("sweepBrush") }}</div>
              <div style="display: flex">
                <!-- 扫刷升降 -->
                <div class="btn-bar left">
                  <template v-for="(item, index) in brushType" :key="index">
                    <div
                      v-if="!item.isLoading"
                      class="btn"
                      @click="!item.isLoading && changeBrushStatus(item, index)"
                    >
                      {{ item.label }}
                    </div>
                    <div v-else class="btn loading">
                      <x-icon name="loading" />
                    </div>
                  </template>
                </div>
                <!-- 扫刷宽度 -->
                <div
                  class="btn-bar left"
                  style="margin-left: 5px"
                  v-if="props.isThreeEcar"
                >
                  <template
                    v-for="(item, index) in brushWidthType"
                    :key="index"
                  >
                    <div
                      v-if="!item.isLoading"
                      class="btn"
                      @click="!item.isLoading && changeBrushWidth(item, index)"
                    >
                      {{ item.label }}
                    </div>
                    <div v-else class="btn loading">
                      <x-icon name="loading" />
                    </div>
                  </template>
                </div>
              </div>
            </div>
            <!-- 风机档位 -->
            <div class="btn-list-bottom-right" v-if="!props.isThreeEcar">
              <div class="btn-title">{{ $t("blower") }}</div>
              <div class="btn-bar right">
                <template v-for="(item, index) in blowType" :key="index">
                  <div
                    v-if="!item.isLoading"
                    class="btn"
                    :class="{
                      active: blowInfo.activeIndex === index,
                      disabled: blowDisabled,
                    }"
                    @click="
                      !blowDisabled &&
                        !item.isLoading &&
                        changeBlowStatus(item, index)
                    "
                  >
                    {{ item.label }}
                  </div>
                  <div v-else class="btn loading">
                    <x-icon name="loading" />
                  </div>
                </template>
              </div>
            </div>
          </div>
        </div>
        <div class="custom-setting-authority" v-if="authorityShow">
          <div class="custom-setting-authority-title">
            {{ $t("permissionRelease") }}
          </div>
          <div class="custom-setting-authority-content">
            <div class="content-top">
              <div v-for="(item, index) in customType" :key="index">
                <x-checkbox
                  v-if="showAuthorityBtn(item)"
                  class="checkbox"
                  :text="item.label"
                  :checked="item.checkStatus"
                  @update:checked="customType[index].checkStatus = $event"
                />
              </div>
            </div>
            <div class="content-bottom">
              <x-checkbox
                :class="['checkbox', { active: all() }]"
                :text="$t('selectAll')"
                :checked="all()"
                :indeterminate="!all() && half()"
                :stopPropagation="false"
                @update:checked="toggleAllChecked"
              />
              <x-button
                type="blue"
                :text="$t('setting')"
                size="small"
                :disabled="!half()"
                @click="setting"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="custom-setting-speed">
        <div class="custom-setting-speed-title">{{ $t("speedSetting") }}</div>
        <div class="custom-setting-speed-content">
          <template v-for="(item, index) in speedOptions.options" :key="index">
            <div class="speed-btn" @click="changeSpeed(item.value)">
              <div class="speed-btn-left">
                <x-icon
                  :name="item.icon"
                  width="28px"
                  height="18px"
                  style="margin-right: 15px"
                />
              </div>
              <div class="speed-btn-right">
                <span class="speed-label">{{ item.label }}</span>
                <span class="speed-value">{{ item.value }}km/h</span>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </x-drawer>
</template>
<script lang="tsx" setup>
import { reactive, watch, onMounted, ref, computed } from "vue";
import { updateLampBlowWater } from "@/services/api";
import { useMainStore } from "@/stores/main";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { speedType } from "@/assets/ts/config";
import { settingSpeed } from "@/services/wsapi";
import { parseSpeed } from "@/services/wsconfig";
import xModal from "@/components/x-modal";
import xDrawer from "@/components/x-drawer.vue";
import xIcon from "@/components/x-icon.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import xButton from "@/components/x-button.vue";
import Message from "@/components/x-message";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  isThreeEcar: {
    type: Boolean,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 基础开关
 */
// 基础数据
const customType = reactive([
  {
    label: $t("normalLight"),
    value: "headLamp",
    icon: "head_lamp",
    isLoading: false, // 按钮加载
    buttonStatus: false, // 开关状态
    checkStatus: false, // 权限状态
    timer: null, // 定时器
  },
  {
    label: $t("blower"),
    value: "blowMotor",
    icon: "blow_motor",
    isLoading: false,
    buttonStatus: false,
    checkStatus: false,
    timer: null,
  },
  {
    label: $t("sprinkler"),
    value: "waterPump",
    icon: "water_pump",
    isLoading: false,
    buttonStatus: false,
    checkStatus: false,
    timer: null,
  },
  {
    label: $t("suctionNozzleDustReduction"), // 吸盘喷水
    value: "suctionWaterPump",
    icon: "suction_water_pump",
    isLoading: false,
    buttonStatus: false,
    checkStatus: false,
    timer: null,
  },
  {
    label: $t("boxDustReduction"), // 风机降尘
    value: "blowWaterPump",
    icon: "blow_water_pump",
    isLoading: false,
    buttonStatus: false,
    checkStatus: false,
    timer: null,
  },
]);

/**
 * 风机
 */
// 基础数据
const blowType = reactive([
  {
    label: $t("close"),
    value: 0, // 入参
    isLoading: false, // 按钮加载
    desc: $t("close"), // 提示信息
  },
  {
    label: $t("firstGear"),
    value: 3,
    isLoading: false,
    desc: $t("openActive") + $t("firstGear"),
  },
  {
    label: $t("secondGear"),
    value: 4,
    isLoading: false,
    desc: $t("openActive") + $t("secondGear"),
  },
  {
    label: $t("thirdGear"),
    value: 5,
    isLoading: false,
    desc: $t("openActive") + $t("thirdGear"),
  },
]);

// 风机状态
const blowInfo = reactive({
  activeIndex: 0, // 当前风机状态
  timer: null, // 定时器
});

// 禁用风机
const blowDisabled = computed(() => blowType.some((item) => item.isLoading));

// 获取当前风机状态
const getBlowStatus = (speed: number) => {
  let status;
  if (speed > 0) {
    if (speed <= 2800) {
      // 低速（一档）
      status = 1;
    } else if (speed > 2800 && speed < 3199) {
      // 中速（二档）
      status = 2;
    } else {
      // 高速（二档）
      status = 3;
    }
  } else {
    // 关闭
    status = 0;
  }
  return status;
};

/**
 * 扫刷
 */
// 扫刷升降
const brushType = reactive([
  {
    label: $t("descending"),
    value: 0,
    isLoading: false,
  },
  {
    label: $t("lift"),
    value: 1,
    isLoading: false,
  },
]);
// 扫刷宽度
const brushWidthType = reactive([
  {
    label: $t("narrowGear"),
    value: 5,
    isLoading: false,
  },
  {
    label: $t("middleGear"),
    value: 4,
    isLoading: false,
  },
  {
    label: $t("wideGear"),
    value: 3,
    isLoading: false,
  },
]);

const { socket } = useMainStore();

const currentSpeed = ref(0.0);
const authorityShow = ref(false);

// 三轮车：照明灯 洒水 ——————  ——————   扫刷升降 扫刷宽度  ——————
// 四轮车：照明灯 洒水 吸口降尘 箱体降尘 扫刷升降  ——————  风机档位

// 基础开关按钮显示条件
const showBaseBtn = (item: any) => {
  if (props.isThreeEcar) {
    return (
      item.value !== "blowMotor" &&
      item.value !== "suctionWaterPump" &&
      item.value !== "blowWaterPump"
    );
  } else {
    return item.value !== "blowMotor";
  }
};

// 权限按钮显示条件
const showAuthorityBtn = (item: any) => {
  if (props.isThreeEcar) {
    return (
      item.value !== "blowMotor" &&
      item.value !== "suctionWaterPump" &&
      item.value !== "blowWaterPump"
    );
  } else {
    return true;
  }
};

onMounted(() => {
  watch(
    () => socket.allStatus,
    (newV) => {
      if (newV) {
        const target = newV.find((v) => v.deviceId === props.id);
        if (target) {
          const {
            headLamp,
            fanMotorSpeed,
            brushWaterSparyStatus,
            fanDustFallStatus,
            suctionDustFallStatus,
            speed,
          } = target;
          // 风机状态处理
          const newBlowStatus = getBlowStatus(fanMotorSpeed);
          if (blowInfo.activeIndex !== newBlowStatus) {
            clearInterval(blowInfo.timer);
            blowType.forEach((item) => (item.isLoading = false));
          }
          blowInfo.activeIndex = newBlowStatus;

          const waterPump = brushWaterSparyStatus === "open";
          const suctionWaterPump = suctionDustFallStatus === "open";
          const blowWaterPump = fanDustFallStatus === "open";

          const updateCustomType = (index, value) => {
            if (value !== customType[index].buttonStatus) {
              clearInterval(customType[index].timer);
              customType[index].isLoading = false;
            }
            customType[index].buttonStatus = value;
          };

          updateCustomType(0, headLamp);
          updateCustomType(2, waterPump);
          updateCustomType(3, suctionWaterPump);
          updateCustomType(4, blowWaterPump);

          currentSpeed.value = parseSpeed(speed);
        }
      }
    }
  );
});

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      customType.forEach((item) => {
        item.checkStatus = false;
      });
    }
  }
);

/**
 * 开关设置
 */
// 基础开关
const toggleButtonStatus = debounce((item: any, index: number) => {
  const params = {
    deviceId: props.id,
  };
  params[item.value] = item.buttonStatus ? 0 : 1;
  customType[index].isLoading = true;
  updateLampBlowWater(params)
    .then(() => {
      Message(
        "success",
        `${item.label}${item.buttonStatus ? $t("close") : $t("open")}${$t(
          "commandSendSuccess"
        )}`
      );
      customType[index].timer = setTimeout(() => {
        Message(
          "error",
          `${item.label}${item.buttonStatus ? $t("close") : $t("open")}${$t(
            "failed"
          )}`
        );
        customType[index].isLoading = false;
      }, 30000);
    })
    .catch(() => {
      customType[index].isLoading = false;
    });
});
// 风机开关档位
const changeBlowStatus = debounce((item: any, index: number) => {
  const params = {
    deviceId: props.id,
    blowMotor: item.value,
  };
  blowType[index].isLoading = true;
  blowInfo.activeIndex = index;
  updateLampBlowWater(params)
    .then(() => {
      Message(
        "success",
        `${$t("blower")}${item.desc}${$t("commandSendSuccess")}`
      );
      blowInfo.timer = setTimeout(() => {
        Message("error", `${$t("blower")}${item.desc}${$t("failed")}`);
        blowType[index].isLoading = false;
      }, 30000);
    })
    .catch(() => {
      blowType[index].isLoading = false;
    });
});
// 扫刷升降处理
const changeBrushStatus = debounce((item: any, index: number) => {
  const params = {
    deviceId: props.id,
    sweepBrush: item.value,
  };
  brushType[index].isLoading = true;
  updateLampBlowWater(params)
    .then(() => {
      Message(
        "success",
        `${$t("sweepBrush")}${item.label}${$t("commandSendSuccess")}`
      );
    })
    .finally(() => {
      brushType[index].isLoading = false;
    });
});
// 扫刷宽度处理
const changeBrushWidth = debounce((item: any, index: number) => {
  const params = {
    deviceId: props.id,
    sweepBrush: item.value,
  };
  brushWidthType[index].isLoading = true;
  updateLampBlowWater(params)
    .then(() => {
      Message(
        "success",
        `${$t("setting")}${item.label}${$t("commandSendSuccess")}`
      );
    })
    .finally(() => {
      brushWidthType[index].isLoading = false;
    });
});

/**
 * 速度设置
 */
const speedOptions = reactive({
  options: speedType,
});
const changeSpeed = (value) => {
  const speedOption = speedOptions.options.find(
    (speed) => speed.value === value
  );
  const speedText = speedOption?.label || "";
  xModal.confirm({
    title: `${$t("makeSureVehicleSpeedTo")}${speedText}${$t("isItOk")}`,
    closeBeforeConfirm: true,
    content: (
      <div style="font-size:14px;color:#383838;">
        车辆当前速度{currentSpeed.value}km/h
      </div>
    ),
    confirm() {
      return settingSpeed(props.id, speedOption?.prop).then(() => {
        Message(
          "success",
          `${props.id}${$t("vehicle")}${speedText}${$t("commandSetSuccess")}`
        );
      });
    },
  });
};

/**
 * 权限释放
 */
// 半选
const half = () => {
  return customType.some((item) => item.checkStatus);
};
// 全选
const all = () => {
  return customType.every((item) => item.checkStatus);
};
// 切换全选
const toggleAllChecked = (checked) => {
  customType.forEach((item) => {
    item.checkStatus = checked;
  });
};
// 提交设置
const setting = debounce(async () => {
  const params = {
    deviceId: props.id,
  };
  const selectedItemsBase = customType.filter((item) => item.checkStatus);
  const selectedItems = props.isThreeEcar
    ? selectedItemsBase.filter(
        (item) => item.value === "headLamp" || item.value === "waterPump"
      )
    : selectedItemsBase;
  selectedItems.forEach((item) => {
    params[item.value] = 2;
  });
  await updateLampBlowWater(params);
  const selectedLabels = selectedItems.map((item) => item.label).join("、");
  Message("success", `${selectedLabels}${$t("controlAuthReleasedSuccess")}`);
});
</script>

<style lang="scss" scoped>
.custom-setting {
  height: 100%;
  &-switch,
  &-speed,
  &-authority {
    padding: 15px;
    margin: 0 25px 20px 25px;
    background: #fff;
    border-radius: 10px;
    &-title {
      display: flex;
      justify-content: space-between;
      @include sc(14px, #9f9fa4) {
        font-weight: bold;
      }
    }
  }
  &-img {
    margin: 20px 25px 20px 20px;
    background: #f4f7fe;
    border-radius: 10px;
    &-title {
      @include sc(16px, #242859) {
        font-weight: bold;
      }
    }
    &-content {
      position: relative;
      padding: 15px 0;
      @include wh(544px, 180px);
      img {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(calc(-50% - 60px), calc(-50% + 20px));
        @include wh(362px, 144px);
      }
      .head-lamp,
      .blow-motor,
      .water-pump {
        position: absolute;
        top: 50%;
        left: 50%;
        transition: opacity 1s;
      }
      .head-lamp {
        &.top {
          transform: translate(calc(95px - 50%), -50%);
        }
        &.bottom {
          transform: translate(calc(95px - 50%), calc(35px - 50%));
        }
      }
      .blow-motor {
        transform: translate(calc(115px - 50%), calc(30px - 50%));
      }
      .water-pump {
        &.top {
          transform: translate(calc(155px - 50%), calc(-17px - 50%));
        }
        &.bottom {
          transform: translate(calc(155px - 50%), calc(54px - 50%));
        }
      }
    }
  }
  &-switch {
    &-title {
      .loadMore {
        cursor: pointer;
        .arrowBottom {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(180deg);
        }
        .arrowTop {
          transition: 0.2s;
          transform-origin: center;
          transform: rotateZ(0deg);
        }
      }
    }
    &-content {
      padding: 15px 0;
      .btn-list-top {
        display: flex;
        .list-item {
          width: 80px;
          margin-right: 20px;
          @include fj {
            flex-direction: column;
            align-items: center;
          }
          .item-icon {
            @include ct-f;
            @include wh(34.8px, 34.8px) {
              border-radius: 50%;
              background-color: rgba(196, 196, 196, 0.3);
            }
            transition: background-color 0.6s;
            &.enable {
              background-color: #5964fb;
              .loading,
              .loading:before,
              .loading:after {
                animation: dotLoadingEnable 1.8s infinite ease-in-out;
              }
            }
            .loading,
            .loading:before,
            .loading:after {
              @include wh(4px, 4px);
              border-radius: 50%;
              animation-fill-mode: both;
              animation: dotLoading 1.8s infinite ease-in-out;
            }
            .loading {
              position: relative;
              margin-top: -5px;
              animation-delay: -0.16s;
            }
            .loading:before,
            .loading:after {
              content: "";
              position: absolute;
              top: 0;
            }
            .loading:before {
              left: -7px;
              animation-delay: -0.32s;
            }
            .loading:after {
              left: 7px;
            }
          }
          .item-title {
            margin-top: 5px;
            @include sc(12px, #5e5e5e);
          }
          cursor: pointer;
          &.disabled {
            cursor: not-allowed;
          }
        }
      }
      .btn-list-bottom {
        display: flex;
        margin-top: 15px;
        &-left,
        &-right {
          display: flex;
          flex-direction: column;
          background: rgba(244, 247, 254, 0.7);
          border-radius: 8px;
          padding: 10px;
          .btn-title {
            @include sc(12px, rgb(51, 51, 51));
          }
          .btn-bar {
            @include ct-f;
            padding: 2px;
            margin-top: 10px;
            border-radius: 4px;
            background: #fff;
            &.right {
              .btn {
                @include ct-f;
                @include wh(42px, 32px) {
                  line-height: 32px;
                }
                @include sc(14px, #9f9fa4);
                cursor: pointer;
                &.active {
                  @include sc(14px, #5964fb);
                  background: #ecefff;
                  border-radius: 4px;
                }
                &.disabled {
                  cursor: not-allowed;
                }
              }
            }
            &.left {
              .btn {
                @include ct-f;
                @include wh(42px, 36px) {
                  line-height: 36px;
                }
                @include sc(14px, #9f9fa4);
                cursor: pointer;
                &:active {
                  @include sc(14px, #333);
                  background: #ecefff;
                  border-radius: 4px;
                }
              }
            }
            .loading {
              svg {
                animation: rotateCircle 2s linear infinite;
              }
            }
          }
        }
        &-left {
          margin-right: 10px;
        }
      }
    }
  }
  &-speed {
    &-content {
      display: flex;
      padding-top: 15px;
      .speed-btn {
        @include ct-f {
          margin-right: 15px;
        }
        @include wh(122px, 48px);
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgb(229, 229, 229);
        border-radius: 8px;
        cursor: pointer;
        &:active {
          background: #ecefff;
        }
        &-right {
          display: flex;
          flex-direction: column;
          .speed-label {
            @include sc(14px, #555);
          }
          .speed-value {
            @include sc(12px, #9f9fa4);
          }
        }
      }
    }
  }
  &-authority {
    padding: 0;
    margin: 15px 0 0 0;
    &-content {
      padding-top: 15px;
      .content-top {
        display: flex;
        padding: 10px 0 20px 15px;
        border-bottom: 1px solid #e5e5e5;
        .checkbox {
          margin-right: 20px;
          :deep(.x-checkbox-text) {
            @include sc(14px, #555);
          }
        }
      }
      .content-bottom {
        @include ct-f(y) {
          justify-content: space-between;
        }
        padding-top: 20px;
        .checkbox {
          :deep(.x-checkbox-text) {
            font-size: 14px;
          }
          &.active {
            :deep(.x-checkbox-text) {
              @include sc(14px, #555);
            }
          }
        }
      }
    }
  }
}
@keyframes dotLoading {
  0%,
  80%,
  100% {
    box-shadow: 0 4px 0 -1.2px #555;
  }
  40% {
    box-shadow: 0 4px 0 0 #555;
  }
}
@keyframes dotLoadingEnable {
  0%,
  80%,
  100% {
    box-shadow: 0 4px 0 -1.2px #fff;
  }
  40% {
    box-shadow: 0 4px 0 0 #fff;
  }
}
</style>
