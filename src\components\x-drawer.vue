<template>
  <Teleport
    :disabled="!props.container"
    :to="props.container"
  >
    <Transition name="x-drawer">
      <div
        class="x-drawer"
        v-show="props.visible"
      >
        <Transition name="x-drawer-mask">
          <div
            v-if="props.visible && props.modal"
            class="x-drawer-mask"
            @click="handleMask"
          ></div>
        </Transition>
        <Transition name="x-drawer-content">
          <div
            v-if="props.visible"
            class="x-drawer-content"
            :style="contentStyle"
          >
            <div class="content-header">
              <div class="content-header-title">
                <slot name="title">{{ props.title }}</slot>
              </div>
              <span
                class="content-header-close"
                v-html="closeText"
                @click="handleCancel"
              ></span>
            </div>
            <!-- 跟随内容footer -->
            <template v-if="props.footerType == 'following'">
              <div
                class="content-body"
                :style="bodyStyle"
              >
                <div class="content-body-slot">
                  <slot></slot>
                  <div
                    v-if="props.btnOption"
                    class="content-body-button"
                    :style="buttonStyle"
                  >
                    <x-button
                      type="white"
                      :text="props.btnOption.cancel || $t('cancel')"
                      @click="handleCancel"
                      style="margin-right: 9px"
                    />
                    <x-button
                      type="blue"
                      :text="props.btnOption.confirm || $t('recognize')"
                      :loading="props.btnOption.confirmLoading"
                      :disabled="props.btnOption.confirmDisabled"
                      @click="handleConfirm"
                    />
                  </div>
                </div>
              </div>
            </template>
            <!-- 底部固定footer -->
            <template v-else>
              <div
                class="content-body"
                :style="bodyStyle"
              >
                <div class="content-body-slot">
                  <slot></slot>
                </div>
              </div>
              <div
                v-if="props.btnOption"
                class="content-body-button fixed"
                :style="buttonStyle"
              >
                <x-button
                  type="white"
                  :text="props.btnOption.cancel || $t('cancel')"
                  @click="handleCancel"
                  style="margin-right: 9px"
                />
                <x-button
                  type="blue"
                  :text="props.btnOption.confirm || $t('recognize')"
                  :loading="props.btnOption.confirmLoading"
                  :disabled="props.btnOption.confirmDisabled"
                  @click="handleConfirm"
                />
              </div>
            </template>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import type { PropType } from "vue";
import { throttle } from "@/assets/ts/utils";
import xButton from "@/components/x-button.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = defineProps({
  container: {
    type: [HTMLElement, String],
    default: () => "body",
  },
  visible: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  width: {
    type: String,
    default: "800px",
  },
  bodyPadding: {
    // 根据UI设计新增
    type: String,
  },
  maskClosable: {
    type: Boolean,
    default: true,
  },
  modal: {
    type: Boolean,
    default: true,
  },
  btnOption: {
    type: Object as PropType<{
      cancel?: string;
      confirm?: string;
      confirmDisabled?: boolean;
      confirmLoading?: boolean;
      position: "start" | "center" | "end";
    }>,
  },
  footerType: {
    type: String as PropType<"fixed" | "following">,
    default: "fixed",
  },
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);
const contentStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width}`;
  return style;
});
const bodyStyle = computed(() => {
  let style = "";
  if (props.bodyPadding) style += `padding: ${props.bodyPadding}`;
  return style;
});
const buttonStyle = computed(() => {
  let style = "";
  style += `justify-content: ${props.btnOption?.position};`;
  return style;
});
const closeText = ref("&#10005");
// 动画是1.3s
const handleCancel = throttle(
  () => {
    emits("update:visible", false);
    emits("cancel");
  },
  1300,
  true
);
const handleMask = () => {
  if (props.maskClosable) {
    handleCancel();
  }
};
const handleConfirm = () => {
  emits("confirm");
};
</script>

<style lang="scss" scoped>
.x-drawer {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  pointer-events: none;
  &-enter {
    &-active {
      transition: all 0s ease 1.3s;
    }
  }
  &-leave {
    &-active {
      transition: all 0s ease 1.3s;
    }
  }
  &-mask {
    @include wh(100%);
    pointer-events: auto;
    background-color: rgba(0, 0, 0, 0.5);
    &-enter {
      &-from {
        opacity: 0;
      }
      &-active {
        transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        opacity: 0;
      }
    }
  }
  &-content {
    pointer-events: auto;
    box-shadow: 0px 4px 50px 0px rgba(0, 0, 0, 0.25);
    &-enter {
      &-from {
        transform: translateX(100%);
      }
      &-active {
        transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 1.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: translateX(100%);
      }
    }
    position: absolute;
    top: 0;
    right: 0;
    height: 100%;
    .content-header {
      z-index: var(--z-index-text);
      position: relative;
      @include fj {
        align-items: center;
      }
      @include wh(100%, 56px) {
        padding: 0 14px;
        background-color: #fff;
        box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
      }
      @include sc(16px, #242859);
      &-close {
        cursor: pointer;
      }
    }
    .content-body {
      display: flex;
      flex-direction: column;
      width: 100%;
      height: calc(100% - 56px);
      padding: 20px 20px 16px 20px;
      background-color: #f4f7fe;
      &-slot {
        overflow-y: auto;
        overflow-x: hidden;
        @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
        width: 100%;
        flex: 1;
      }
      &-button {
        display: flex;
        @include wh(100%, 32px) {
          margin-top: 18px;
        }
        &.fixed {
          position: absolute;
          bottom: 0;
          padding: 0 46px;
          height: 56px;
          align-items: center;
          background: rgba(15, 16, 18, 0.1);
          backdrop-filter: blur(2px);
        }
      }
    }
  }
}
</style>
