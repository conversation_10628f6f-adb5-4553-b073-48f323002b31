<template>
  <x-modal
    title="项目区域"
    :visible="props.show"
    @update:visible="updateVisible"
    width="1045px"
    height="55vh"
    :bodyStyle="{ padding: '0 30px' }"
  >
    <div class="content">
      <x-table :cols="table.cols" :dataSource="table.dataSource"> </x-table>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import xModal from "./x-modal.vue";
import xTable from "./x-table.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  info: {
    type: Array,
    required: true,
  },
});

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: "序号",
      width: "100",
    },
    {
      key: "area",
      title: "所在区域",
      width: "100",
    },
  ],
  dataSource: [] as any[],
});

const emits = defineEmits(["update:show"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      table.dataSource =
        props.info?.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            area: item,
          };
        }) || [];
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 0 10px;
  height: 40vh;
  overflow-y: auto;
}
</style>
