<template>
  <section class="x-cascader">
    <x-popover
      trigger="click"
      :container="props.popupContainer"
      v-model:visible="config.popShow"
      placement="leftBottom"
      :triangle="false"
      :gap="6"
      style="width: 100%"
    >
      <div
        ref="_bindRef"
        :class="['x-cascader-selector', { 'pop-show': config.popShow }]"
      >
        <ul
          class="selector-value"
          v-if="config.labelArr.length"
        >
          <li
            class="selector-value-item"
            v-for="(item, index) in config.labelArr"
            :key="index"
          >
            <span class="selector-value-item-label">{{ item.label }}</span>
            <span
              v-if="allowClear"
              class="selector-value-item-close"
              v-html="closeText"
              @mousedown.stop="deleteValue(index)"
            ></span>
          </li>
        </ul>
        <span v-else>{{ $t("PSelect") }}</span>
        <span :class="['selector-arrow', { 'pop-show': config.popShow }]"></span>
      </div>
      <template #content>
        <div
          class="x-cascader-content"
          :style="{ maxWidth: `${_width}px` }"
        >
          <ul
            class="menu"
            v-if="props.options.length > 0"
            :style="{ width: `${_width / 3}px` }"
          >
            <li
              :class="['menu-item', { hover: item.hover, enable: item.enable }]"
              v-for="(item, index) in props.options"
              :key="index"
              @mouseenter="props.options.forEach((v, i) => (v.hover = i === index))"
              @mouseleave="mouseLeaveHandle($event, item)"
              @click="menuClickHandle"
            >
              <span class="menu-item-label">{{ item.label }}</span>
              <span
                class="menu-item-arrow"
                v-if="item.children && item.children.length > 0"
              ></span>
            </li>
          </ul>
          <ul
            v-if="firstChilds.length > 0"
            class="menu"
            :style="{ width: `${_width / 3}px` }"
          >
            <li
              :class="['menu-item', { hover: item.hover, enable: item.enable }]"
              v-for="(item, index) in firstChilds"
              :key="index"
              @mouseenter="item.hover = true"
              @mouseleave="mouseLeaveHandle($event, item)"
              @click="menuClickHandle"
            >
              <span class="menu-item-label">{{ item.label }}</span>
              <span
                class="menu-item-arrow"
                v-if="item.children && item.children.length > 0"
              ></span>
            </li>
          </ul>
          <ul
            v-if="secondChilds.length > 0"
            class="menu"
            :style="{ width: `${_width / 3}px` }"
          >
            <li
              :class="['menu-item', { hover: item.hover, enable: item.enable }]"
              v-for="(item, index) in secondChilds"
              :key="index"
              @mouseenter="item.hover = true"
              @mouseleave="mouseLeaveHandle($event, item)"
              @click="menuClickHandle"
            >
              <span class="menu-item-label">{{ item.label }}</span>
              <span
                class="menu-item-arrow"
                v-if="item.children && item.children.length > 0"
              ></span>
            </li>
          </ul>
          <div
            v-if="!(props.options.length || firstChilds.length || secondChilds.length)"
            class="menu"
            :style="{ width: `${_width / 1.5}px` }"
          >
            <div class="menu-empty">{{ $t("noData") }}</div>
          </div>
        </div>
      </template>
    </x-popover>
  </section>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref, nextTick, computed, watch } from "vue";
import type { PropType } from "vue";
import xPopover from "@/components/x-popover.vue";
import { treeBfsParse } from "@/assets/ts/utils";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

export type OptionType = {
  label: string;
  value: string | number;
  hover?: boolean;
  enable?: boolean;
  children?: OptionType[];
};

const props = defineProps({
  value: {
    type: Array as PropType<(string | number)[]>,
    default: () => [],
  },
  options: {
    type: Array as PropType<OptionType[]>,
    required: true,
  },
  popupContainer: {
    type: HTMLElement,
  },
  allowClear: {
    type: Boolean,
    default: true,
  },
  // 自动选中下级的第一项
  autoSelectFirstChild: {
    type: Boolean,
    default: false,
  },
  // 面板宽度
  menuWidth: {
    type: String,
  },
});

const emits = defineEmits(["update:value"]);

const closeText = ref("&#10005");
const config = reactive({
  popShow: false,
  labelArr: [] as { value: string | number; label: string; index: number }[],
});
const firstChilds = computed(() => props.options?.find((v) => v.hover)?.children || []);
const secondChilds = computed(
  () => props.options?.find((v) => v.hover)?.children?.find((v) => v.hover)?.children || []
);

const mouseLeaveHandle = (e: MouseEvent, item: OptionType) => {
  if (e.x < (e.target as HTMLElement).getBoundingClientRect().right || !item.children?.length) {
    item.hover = false;
  }
};

const deleteValue = (deepNum: number) => {
  let menus = props.options;
  for (let i = 0; i < deepNum; i++) {
    if (i === deepNum - 1) menus[config.labelArr[i].index].hover = false;
    menus = menus[config.labelArr[i].index].children!;
  }
  for (let i = deepNum; i < config.labelArr.length; i++) {
    menus[config.labelArr[i].index].enable = false;
    menus[config.labelArr[i].index].hover = false;
    menus = menus[config.labelArr[i].index].children!;
  }
  config.labelArr = config.labelArr.slice(0, deepNum);
  emits(
    "update:value",
    config.labelArr.map((v) => v.value)
  );
};
const menuClickHandle = () => {
  let menus = props.options;
  // 清除之前的enable
  while (menus.length) {
    const target = menus.find((v) => v.enable);
    if (!target) break;
    target.enable = false;
    menus = target.children || [];
  }
  const _labelArr = [];
  menus = props.options;
  // 添加新的enable
  while (menus.length) {
    const tarIndex = menus.findIndex((v) => v.hover);
    const target = menus[tarIndex];
    if (!target) break;
    target.enable = true;
    _labelArr.push({
      value: target.value,
      label: target.label,
      index: tarIndex,
    });
    menus = target.children || [];
  }
  config.labelArr = _labelArr;
  if (props.autoSelectFirstChild && config.labelArr.length === 1 && firstChilds.value.length > 0) {
    firstChilds.value[0].enable = true;
    config.labelArr.push({
      value: firstChilds.value[0].value,
      label: firstChilds.value[0].label,
      index: 0,
    });
    emits(
      "update:value",
      config.labelArr.map((v) => v.value)
    );
  } else {
    emits(
      "update:value",
      config.labelArr.map((v) => v.value)
    );
  }
  config.popShow = false;
};

const _bindRef = ref<any>();
const _width = ref(0);
onMounted(() => {
  nextTick(() => {
    _width.value = props.menuWidth || _bindRef.value.getBoundingClientRect().width;
  });
});

watch(
  () => props.value,
  (newv) => {
    config.labelArr = [];
    if (newv && newv.length > 0) {
      treeBfsParse(props.options, "children", (sub: any) => {
        if (newv.includes(sub.value) && !config.labelArr.includes(sub)) {
          config.labelArr.push(sub);
        }
      });
    }
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<style lang="scss" scoped>
.x-cascader {
  width: 100%;
  &-selector {
    cursor: pointer;
    @include fj {
      align-items: center;
    }
    color: #999;
    @include wh(100%, 32px) {
      padding: 0 9px;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid rgb(220, 220, 220);
    }
    &.pop-show {
      border-color: #5964fb;
    }
    .selector-arrow {
      @include triangle(4px, 6px, rgb(220, 220, 220), bottom);
      transition: all 0.2s ease;
      &.pop-show {
        border-bottom: 6px solid #5964fb;
        border-top: none;
      }
    }
  }
  .selector-value {
    @include ct-f(y);
    width: calc(100% - 13px);
    height: 100%;
    &-item {
      @include fj {
        align-items: center;
      }
      height: 24px;
      padding: 0 6px;
      margin-right: 4px;
      background: rgba(217, 224, 251, 0.5);
      border-radius: 4px;
      min-width: 0;
      &-label {
        color: #383838;
        margin-right: 4px;
        @include ell;
      }
      &-close {
        cursor: pointer;
        user-select: none;
        color: #5e5e5e;
      }
    }
  }
  &-content {
    display: flex;
    box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2), 0px 4px 30px rgba(0, 0, 0, 0.1);
    max-height: 188px;
    .menu {
      max-height: 188px;
      padding: 0 5px;
      background-color: #fff;
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
      &-item {
        &:last-child {
          margin-bottom: 3px;
        }
        cursor: pointer;
        @include fj;
        position: relative;
        @include wh(100%, 34px) {
          padding-left: 8px;
          margin-top: 3px;
          border-radius: 4px;
        }
        @include sc(13px, #5e5e5e) {
          line-height: 34px;
        }
        &.enable {
          background: rgb(217, 224, 251);
          color: #5964fb;
        }
        &.hover {
          color: #5964fb;
        }
        &-label {
          @include ell;
          margin-right: 10px;
        }
        &-arrow {
          position: absolute;
          top: 13px;
          right: 8px;
          @include v-arrow(right, 1px, 8px);
        }
      }
      &-empty {
        @include wh(100%, 40px) {
          padding-left: 7px;
          border-radius: 4px;
        }
        color: #9f9fa4;
        line-height: 40px;
        white-space: nowrap;
      }
    }
  }
}
</style>
