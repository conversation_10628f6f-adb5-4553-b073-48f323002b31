<template>
  <div
    ref="windowButtonRef"
    :class="[
      'info-window-btn',
      { default: !props.disabled },
      { disabled: props.disabled },
      { active: isActive && !props.disabled },
      { hovered: isHovered && !props.disabled },
    ]"
    @mousedown="handleClick"
    @mouseenter="handleMouseOver"
    @mouseleave="handleMouseOut"
    @click="!props.disabled && emits('onClick')"
  >
    <x-icon :name="props.name" width="18px" height="18px" />
    <span class="info-window-btn-text">{{ props.text }}</span>
  </div>
</template>
<script lang="ts" setup>
import { ref } from "vue";
import xIcon from "@/components/x-icon.vue";

/**
 * 问题：状态快速切换时，引用的svg文件频繁替换，偶现mouseLeave失效
 * 解决：修改svg文件使svg颜色自动继承父元素
 * --将svg标签第一个fill改为fill="currentColor"
 * --将path里的stroke属性改为stroke="currentColor"
 * --将path里的fill属性删除
 */
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  text: {
    type: String,
    required: true,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const windowButtonRef = ref<any>();

const emits = defineEmits(["onClick"]);

const isActive = ref(false);
const isHovered = ref(false);

const handleClick = () => {
  isActive.value = true;
};
const handleMouseOver = () => {
  isHovered.value = true;
};
const handleMouseOut = () => {
  isHovered.value = false;
  isActive.value = false;
};
</script>

<style lang="scss" scoped>
.info-window-btn {
  margin: 0 5px;
  @include ct-f(y) {
    flex-direction: column;
    justify-content: space-between;
    padding: 3px;
  }
  height: 45px;
  font-size: 12px;
  &.default {
    color: #383838;
    &:hover {
      color: #fff;
      background: #5964fb;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  &.disabled {
    color: #c0bebe;
    &:hover {
      cursor: not-allowed;
    }
  }
  &.hovered {
    color: #383838;
    &:hover {
      color: #fff;
      background: #5964fb;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  &.active {
    color: #383838;
    &:hover {
      color: #fff;
      background: #404be6;
      border-radius: 4px;
      cursor: pointer;
    }
  }
}
</style>
