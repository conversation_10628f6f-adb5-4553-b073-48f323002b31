<template>
  <div class="route-item-container" ref="taskContentRef">
    <div
      class="route-item"
      v-for="(item, index) in form.routeList"
      :key="index"
      @mouseover="toggleHover(index, true)"
      @mouseleave="toggleHover(index, false)"
    >
      <div class="route-item-content">
        <div class="route-item-content-left">
          {{ (index + 1).toString().padStart(2, "0") }}
        </div>
        <div class="route-item-content-center">
          <x-select
            v-model:value="item.vehRouteNo"
            :options="props.routeList"
            :popupContainer="props.container"
            @update:value="changeRouteNo($event, index)"
            :placeholder="$t('PSelectRoute')"
            style="width: 180px; margin-right: 10px"
          />
          <x-cascader
            v-model:value="item.sweepingTypeList"
            :options="formOptions.cascader[index]"
            :popupContainer="props.container"
            :allowClear="false"
            :autoSelectFirstChild="true"
            menuWidth="300"
            @update:value="changeCleaningType($event, index)"
            style="width: 200px"
          />
        </div>
        <div class="route-item-content-right" v-if="item.hovered">
          <x-icon
            v-if="form.routeList.length > 1"
            class="minus-icon"
            name="minus_circle"
            width="20"
            height="20"
            @click="delRoute(index)"
          />
          <x-icon
            v-if="form.routeList.length < maxRouteLength"
            class="plus-icon"
            name="plus_circle"
            width="20"
            height="20"
            @click="addRoute(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref } from "vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import xCascader from "@/components/x-cascader.vue";
import { baseCleaningType, fullCleaningType } from "@/assets/ts/config";
import { treeBfsParse, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

interface RouteOption {
  routeId: string;
  vehRouteNo: string;
  taskType: string;
}

const props = defineProps({
  dataSource: {
    type: Array<RouteOption>,
    required: true,
  },
  routeList: {
    type: Array,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  // 最大路线条数
  maxRouteLength: {
    type: Number,
    default: 10,
  },
});

const emits = defineEmits(["update:value", "delRoute", "changeRoute"]);

const taskContentRef = ref<any>();

const form = reactive({
  routeList: [
    {
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
      sweepingTypeList: [] as string[],
      hovered: false,
    },
  ],
});

// 添加路线
const addRoute = (index: number) => {
  form.routeList.splice(index + 1, 0, {
    routeId: "0",
    vehRouteNo: "",
    taskType: "",
    sweepingType: "",
    sweepingTypeList: [] as string[],
    hovered: false,
  });
  emits("update:value", form.routeList);
};

// 删除路线
const delRoute = (index: number) => {
  form.routeList.splice(index, 1);
  emits("update:value", form.routeList);
  emits("delRoute", index);
};

// 更改路线
const changeRouteNo = (value: string, index: number) => {
  getTaskTypeList(value, index);
  emits("changeRoute");
};

const formOptions = reactive({
  cascader: [] as any[][],
});

treeBfsParse(fullCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

treeBfsParse(baseCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

// 根据路线获取任务类型
const getTaskTypeList = (value: string, index: number) => {
  if (value) {
    const selectedRoute =
      props.routeList.find((item) => item.value === value) || {};
    formOptions.cascader[index] = selectedRoute.cleanTypeArrays?.includes(
      "edgewise"
    )
      ? fullCleaningType
      : baseCleaningType;
  } else {
    formOptions.cascader[index] = [];
  }
};

// 更改清扫类型
const changeCleaningType = (value: string, index: number) => {
  form.routeList[index].taskType = value[0];
  form.routeList[index].sweepingType = value[1] || "";
  emits("update:value", form.routeList);
};

// 将数据更新分批处理
const updateFormRouteList = (data: RouteOption[]) => {
  const batchSize = 10;
  let index = 0;
  const updateBatch = () => {
    const slice = data.slice(index, index + batchSize);
    form.routeList.splice(index, batchSize, ...slice);
    index += batchSize;
    if (index < data.length) {
      requestAnimationFrame(updateBatch);
    }
  };
  updateBatch();
};

watch(
  () => props.dataSource,
  (newV) => {
    newV.forEach((item, index) => {
      getTaskTypeList(item.vehRouteNo, index);
    });
    updateFormRouteList(newV);
  },
  {
    immediate: true,
  }
);

const toggleHover = (index: number, isHovered: boolean) => {
  form.routeList[index].hovered = isHovered;
};
</script>
<style lang="scss" scoped>
.route-item-container {
  .route-item {
    padding: 10px;
    &-content {
      @include ct-f(y);
      &-left {
        @include wh(20px, 20px) {
          line-height: 20px;
          text-align: center;
        }
        @include sc(14px, #9f9fa4) {
          border: 1px solid #9f9fa4;
          border-radius: 50px;
        }
      }
      &-center {
        display: flex;
        margin-left: 10px;
      }
      &-right {
        display: flex;
        margin-left: 10px;
        .minus-icon,
        .plus-icon {
          cursor: pointer;
        }
        .minus-icon {
          margin-right: 5px;
        }
      }
    }
    &:hover {
      box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease, border 0.3s ease;
    }
  }
}
</style>
