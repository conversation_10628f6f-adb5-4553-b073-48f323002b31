<template>
  <x-drawer
    :title="$t('readMore')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="672px"
  >
    <div class="content">
      <div class="content-body">
        <div class="content-body-title">{{ detail.info.deviceNo }}</div>
        <div class="content-body-subtitle">
          <div class="content-body-subtitle-label">{{ $t("readTime") }}</div>
          <div class="content-body-subtitle-value">
            {{ detail.info.createdTime }}
          </div>
        </div>
        <template v-for="(item, index) in detail.info.contents" :key="index">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("type") }}：</div>
            <div class="content-body-item-value">
              {{ item.captureType }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("camera") }}：</div>
            <div class="content-body-item-value">
              {{ item.cameraStand?.join("/") || "" }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">
              {{ $t("captureInterval") }}：
            </div>
            <div class="content-body-item-value">
              <span v-if="item.captureDuration"
                >{{ item.captureDuration }}S</span
              >
            </div>
          </div>
          <hr
            v-if="index !== detail.info.contents.length - 1"
            class="split-line"
          />
        </template>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="tsx" setup>
import { reactive, watch } from "vue";
import { getEventCaptureInfo } from "@/services/api";
import type { GetEventCaptureInfoResponse } from "@/services/type";
import { fourCameraType, threeCameraType } from "@/assets/ts/config";
import XDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("eventCapture");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  deviceNo: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const detail = reactive({
  info: {} as GetEventCaptureInfoResponse,
});

const formOptions = reactive({
  cameraStand: fourCameraType.concat(threeCameraType),
});

// 回显数据
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const res =
        (await getEventCaptureInfo({ deviceNo: props.deviceNo })) || {};
      res.contents.forEach((item) => {
        item.cameraStand = item.cameraStand.map((value) => {
          const camera = formOptions.cameraStand.find(
            (cam) => cam.value === value
          );
          return camera ? camera.label : value;
        });
      });
      detail.info = res || {};
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 30px 30px 30px;
  border-radius: 8px;
  background: #fff;
  &-body {
    &-title {
      @include sc(18px, rgb(16, 22, 55)) {
        font-weight: bold;
      }
    }
    &-subtitle {
      margin-bottom: 30px;
      @include fj(flex-start) {
        margin-top: 10px;
      }
      &-label {
        @include sc(14px, #9f9fa4);
        margin-right: 15px;
      }
      &-value {
        @include sc(14px, #383838);
      }
    }
    &-item {
      display: flex;
      margin-top: 20px;
      font-size: 14px;
      &-label {
        width: 90px;
        color: #999;
      }
      &-value {
        flex: 1;
        display: flex;
        flex-direction: column;
        color: #383838;
      }
    }
    .split-line {
      margin: 20px 0;
      height: 1px;
      border: none;
      background-color: #e5e5e5;
    }
  }
}
</style>
