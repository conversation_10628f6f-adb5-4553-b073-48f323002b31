<template>
  <div class="multi-task" ref="taskContentRef">
    <div
      class="route-item"
      v-for="(item, index) in form.routeList"
      :key="index"
    >
      <div class="route-item-content">
        <div class="route-item-content-left">
          {{ (index + 1).toString().padStart(2, "0") }}
        </div>
        <div class="route-item-content-center">
          <x-select
            v-model:value="item.vehRouteNo"
            :options="props.routeList"
            :popupContainer="props.container"
            :placeholder="$t('PSelectRoute')"
            style="width: 240px"
          />
          <x-select
            v-model:value="item.taskType"
            :options="getTaskTypeList(item.vehRouteNo)"
            :popupContainer="props.container"
            :placeholder="$t('PSelectTaskType')"
            style="width: 140px; margin-left: 5px"
          />
        </div>
        <div
          class="route-item-content-right"
          v-if="form.routeList.length > 1"
          @click="delRoute(index)"
        >
          ✕
        </div>
      </div>
    </div>
    <div
      class="add-btn"
      v-if="form.routeList.length < maxRouteLength"
      @click="addRoute"
    >
      <x-icon name="button_add_circle" width="20" height="20" />
      <span>{{ $t("addRoute") }}</span>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref } from "vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

interface RouteOption {
  vehRouteNo: string;
  taskType: string;
}

const props = defineProps({
  dataSource: {
    type: Array<RouteOption>,
    required: true,
  },
  routeList: {
    type: Array,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  // 最大路线条数
  maxRouteLength: {
    type: Number,
    default: 10,
  },
});

const emits = defineEmits(["update:value"]);

const taskContentRef = ref<any>();

const form = reactive({
  routeList: [
    {
      vehRouteNo: "",
      taskType: "",
    },
  ],
});

// 根据路线获取任务类型
const getTaskTypeList = (value: string) => {
  const selectedRoute = props.routeList.find((item) => item.value === value);
  return selectedRoute
    ? selectedRoute.cleanTypeList.map(({ label, value }) => ({
        label,
        value,
      }))
    : [];
};

// 添加路线
const addRoute = () => {
  form.routeList.push({
    vehRouteNo: "",
    taskType: "",
  });
  emits("update:value", form.routeList);
};

// 删除路线
const delRoute = (index: number) => {
  form.routeList.splice(index, 1);
  emits("update:value", form.routeList);
};

watch(
  () => form.routeList,
  (newV) => {
    emits("update:value", newV);
  }
);

watch(
  () => props.dataSource,
  (newV) => {
    form.routeList = newV;
  },
  {
    immediate: true,
  }
);
</script>
<style lang="scss" scoped>
.multi-task {
  .route-item {
    &-content {
      @include ct-f(y);
      &-left {
        @include wh(20px, 20px) {
          line-height: 20px;
          text-align: center;
        }
        @include sc(14px, #9f9fa4) {
          border: 1px solid #9f9fa4;
          border-radius: 50px;
        }
      }
      &-center {
        display: flex;
        margin-left: 10px;
      }
      &-right {
        margin-left: 10px;
        @include wh(14px, 14px) {
          line-height: 14px;
          text-align: center;
        }
        @include sc(10px, #9f9fa4);
        cursor: pointer;
        &:hover {
          color: #f41515;
        }
        &:active {
          color: #f41515;
          background: #f5f8ff;
        }
      }
    }
    &::after {
      content: "";
      display: block;
      clear: both;
      width: 3px;
      height: 33px;
      background-color: rgba(85, 85, 85, 0.3);
      margin: 0 9px;
    }
  }
  .add-btn {
    @include ct-f(y);
    margin-top: 6px;
    cursor: pointer;
    span {
      margin-left: 10px;
      @include sc(14px, #242859);
    }
  }
}
</style>
