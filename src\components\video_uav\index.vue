<template>
  <section
    class="video-uav"
    :style="rtvsDivAttr.style"
  >
    <VideoComp class="video-uav-video" />
    <x-icon
      v-if="!isPlaying"
      name="map_continue_hover"
      width="40"
      height="40"
      class="video-uav-close"
      color="#0b83dc"
      @click="playVideo"
      v-loading="isLoading"
      element-loading-background="rgba(255, 255, 255, 0.5)"
    />
    <div
      class="opera"
      v-show="VIDEO_VISIBLE"
    >
      <div
        class="opera-close"
        @click="close"
      >
        ✕
      </div>
      <div
        class="opera-toggle"
        @click="switchScreen"
      >
        <x-icon :name="`map_video_${playerCount === 1 ? 'four' : 'one'}`" />
        <span>{{ playerCount === 1 ? "小屏" : "大屏" }}</span>
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { watch, ref, reactive, toRefs, onMounted, computed } from "vue";
import { getUavCabinInfo, getUavToken, getUavAirportInfo } from "@/services/api";
import VideoComp from "./components/Video.vue";
import WS from "./components/WS.js";
import { Connection } from "./components/Connection.js";
import Config from "./components/Config.js";
import { useMainStore } from "@/stores/main";
import { ElMessage } from "element-plus";
// import { Loading } from "@element-plus/icons-vue";
const { socket, updateUavVideoVisible } = useMainStore();
const config = reactive({
  ...Config,
  token: "",
  nodeId: "",
  cameraId: "",
  userId: "",
  pullStreamType: null,
});
const layoutOpts = {
  min: {
    width: 500,
    height: 280,
  },
  max: {
    width: 1000,
    height: 560,
  },
};

const VIDEO_VISIBLE = ref(socket.uavVideoVisible);
const UAVID = ref("");
const cameraList = ref([]);
const playerWindow = ref<any>("");

const rtvsDivAttr = computed(() => {
  const currentLayout = layoutOpts[playerWindow.value];
  const width = VIDEO_VISIBLE.value ? currentLayout?.width ?? 0 : 0;
  const height = VIDEO_VISIBLE.value ? currentLayout?.height ?? 0 : 0;
  console.log(width, "width");
  console.log(height, "height");
  return {
    style: {
      width: `${width}px`,
      height: `${height}px`,
    },
    width,
    height,
  };
});

let connected = ref(0); // 0未建立连接 1已连接
let streamType = ref(0); // 0为主码流 1为辅码流
let ws = null;
let conn = null;
const isPlaying = ref(false);
const isLoading = ref(false);

const switchScreen = () => {
  if (playerWindow.value === "min") {
    playerWindow.value = "max";
  } else {
    playerWindow.value = "min";
  }
};

const close = () => {
  VIDEO_VISIBLE.value = false;
  isPlaying.value = false;
  isLoading.value = false;
  disconnect();
  updateUavVideoVisible(false);
};
// 连接函数
const connect = () => {
  try {
    console.log("Config before WS creation:", {
      wsUrl: config.wsUrl,
      token: config.token,
      userId: config.userId,
      cameraId: config.cameraId,
      pullStreamType: config.pullStreamType,
      videoAgg: config.videoAgg,
      is5g: config.is5g,
      nodeId: config.nodeId,
    });
    // 创建WebSocket连接
    ws = new WS(config.wsUrl, config.token, config.userId);

    // 添加错误处理
    ws.onError = (error) => {
      console.error("WebSocket error:", error);
      isPlaying.value = false;
      isLoading.value = false;
      ElMessage.error("视频连接失败，请稍后重试");
      disconnect();
    };

    ws.initWebSocket();

    // 根据不同配置创建视频连接
    let cameraId = config.cameraId;

    // 处理不同的视频来源和推流方式
    if (config.from == 1) {
      // 机场
      if (config.pullStreamType == 1) {
        config.sfu = true;
        cameraId = config.cameraId + "_dock";
      } else {
        config.sfu = false;
      }
    } else {
      // 无人机视频
      if (config.videoAgg == 1) {
        config.sfu = true;
      } else if (config.is5g) {
        config.sfu = true;
      } else if (config.pullStreamType == 1) {
        config.sfu = true;
        cameraId = config.cameraId + "_dock";
      } else {
        config.sfu = false;
      }
    }

    // 创建连接
    conn = new Connection(ws, cameraId, config.nodeId, config.sfu, config.streamType, false, false);

    // 关联视频元素
    let rtcEle = document.getElementById("rtc");
    conn.updateElement(rtcEle);

    // 启动连接
    if (ws) {
      conn.start();
    }

    connected.value = 1;
  } catch (error) {
    console.error("Connection error:", error);
    isPlaying.value = false;
    isLoading.value = false;
    ElMessage.error("视频连接失败，请稍后重试");
    disconnect();
  }
};

// 断开连接
const disconnect = () => {
  if (conn && conn.main) {
    conn.stop();
    conn = null;
  }
  if (ws && ws.socket) {
    ws.socket.close();
  }
  connected.value = 0;
  isPlaying.value = false; // 确保断开连接时重置播放状态
};

// 切换码流
const changeStreamType = () => {
  if (streamType.value == 0) {
    // 表示主切辅
    streamType.value = 1;
  } else {
    streamType.value = 0;
  }
  if (conn) {
    conn.changeStream(streamType.value);
  }
};

const connectText = computed(() => {
  return connected.value ? "断开连接" : "建立连接";
});

const streamTypeText = computed(() => {
  return streamType.value == 0 ? "主码流" : "辅码流";
});

const init = async (id: string) => {
  try {
    const [tokenData, cabinData, airportData] = await Promise.all([
      getUavToken(),
      getUavCabinInfo(),
      getUavAirportInfo(),
    ]);
    config.token = tokenData.accessToken;
    config.accessToken = tokenData.accessToken;
    config.userId = tokenData.userId;
    console.log(UAVID.value, "UAVID.value");
    const matchedDrone = airportData.drones?.find((item: any) => item.droneId === UAVID.value);
    config.nodeId = matchedDrone?.nodeId ?? "";
    config.pullStreamType = matchedDrone?.pullStreamType ?? 0;
    cameraList.value = cabinData;
    config.cameraId = cameraList.value[0]?.gimbalId;
    console.log(config, "config1111111111");

    return true;
  } catch (error) {
    console.error("Init error:", error);
    ElMessage.error("初始化失败，请稍后重试");
    return false;
  }
};

watch(
  () => socket.activeUavId,
  async (newV) => {
    if (newV) {
      UAVID.value = newV;
      await init(newV);
    }
  }
);

watch(
  () => socket.uavVideoVisible,
  async (newV) => {
    if (newV) {
      VIDEO_VISIBLE.value = newV;
      isPlaying.value = false;
      isLoading.value = false;
      playerWindow.value = "min";
      // 如果已经有 activeUavId，则直接播放视频
      if (UAVID.value) {
        await playVideo();
      }
    } else {
      // 关闭视频时清理状态
      VIDEO_VISIBLE.value = false;
      isPlaying.value = false;
      isLoading.value = false;
      disconnect();
    }
  }
);

const playVideo = async () => {
  if (isPlaying.value) {
    return;
  }

  try {
    // 检查必要的配置是否存在
    if (!config.token || !config.userId) {
      throw new Error("缺少必要的配置信息");
    }

    isLoading.value = true;
    isPlaying.value = true;

    // 开始播放视频
    connect();

    // 监听连接状态
    const checkConnection = setInterval(() => {
      if (connected.value === 1) {
        clearInterval(checkConnection);
        isLoading.value = false;
      }
    }, 1000);

    // 5秒后如果还没连接成功，认为失败
    setTimeout(() => {
      if (connected.value !== 1) {
        clearInterval(checkConnection);
        isPlaying.value = false;
        isLoading.value = false;
        ElMessage.error("视频连接超时，请稍后重试");
        disconnect();
      }
    }, 5000);
  } catch (error) {
    // 播放失败，重置状态
    isPlaying.value = false;
    isLoading.value = false;
    disconnect();
    ElMessage.error(error.message || "视频播放失败，请稍后重试");
  }
};
</script>

<style scoped lang="scss">
.video-uav {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
  background: #ffffff80;
  backdrop-filter: blur(4px);
  .video-uav-video {
    position: relative;
  }
  .video-uav-close {
    position: absolute;
    top: 50%;
    right: 50%;
    transform: translate(50%, -50%);
    cursor: pointer;
    transition: color 0.3s ease;
    &:hover {
      opacity: 0.8;
    }
  }
  .opera {
    position: absolute;
    right: -4px;
    top: 5px;
    transform: translateX(100%);
    @include wh(54px, 100%);
    user-select: none;
    &-close {
      cursor: pointer;
      @include wh(24px) {
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(12px, rgba(255, 255, 255, 0.8)) {
        text-align: center;
      }
      line-height: 24px;
    }
    &-toggle {
      overflow: hidden;
      cursor: pointer;
      margin-top: 10px;
      @include wh(24px, 24px) {
        padding: 0 6px;
        border-radius: 12px;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(12px, rgba(255, 255, 255, 0.8)) {
        line-height: 24px;
      }

      transition: width 0.4s ease;
      span {
        padding-left: 4px;
      }
      &:hover {
        width: 54px;
      }
    }
  }
}
</style>
