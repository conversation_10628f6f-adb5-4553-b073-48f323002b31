import { describe, it, expect, afterEach, vi } from "vitest";
import { mount, shallowMount } from '@vue/test-utils';
import { asyncExpect } from './utils'
import XCascader from '@/components/x-cascader.vue';
import XPopover from '@/components/x-popover.vue';
import { nextTick } from 'vue'

const baseProps = {
  popupContainer: document.body,
  value: [],
  options: [  {
    children: [
      {
        children: [
          { label: "大观区", value: "340803" },
          { label: "怀宁县", value: "340822" },
        ],
        label: "安庆市",
        value: "340800",
      },
      {
        children: [
          { label: "蚌山区", value: "340303" },
          { label: "固镇县", value: "340323" },
        ],
        label: "蚌埠市",
        value: "340300",
      },
    ],
    label: "安徽省",
    value: "340000",
  },
  {
    children: [
      { label: "大堂区", value: "820004" },
      { label: "风顺堂区", value: "820005" },
    ],
    label: "澳门特别行政区",
    value: "820000",
  }]
}

describe('Snapshot', () => {
  it('正确渲染 外部快照', () => {
    const wrapper = mount(XCascader,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 内部快照', () => {
    const wrapper = mount(XCascader,{
      props: { ...baseProps }
    });
    expect(document.body.querySelector('.x-cascader-content')).toMatchSnapshot();
  });
});

describe('Props Render + Events', () => {

  it('mouseenter后验证选项dom的变化', () => {
    const wrapper = mount(XCascader,{
      props: { ...baseProps },
      sync: false
    });
    const beforeTextList = [...document.querySelectorAll('.x-cascader-content .menu-item-label')].map(v=>v.innerHTML)
    expect(beforeTextList).toMatchInlineSnapshot(`
      [
        "安徽省",
        "澳门特别行政区",
        "安徽省",
        "澳门特别行政区",
        "安徽省",
        "澳门特别行政区",
      ]
    `);
    const event = new MouseEvent('mouseenter', {
      'bubbles': true,
      'cancelable': true
    })
    document.querySelector('.x-cascader-content .menu-item')?.dispatchEvent(event);
    nextTick(()=>{
      const afterTextList = [...document.querySelectorAll('.x-cascader-content .menu-item-label')].map(v=>v.innerHTML)
      expect(afterTextList).toMatchInlineSnapshot(`
        [
          "安徽省",
          "澳门特别行政区",
          "安庆市",
          "蚌埠市",
          "安徽省",
          "澳门特别行政区",
          "安庆市",
          "蚌埠市",
          "安徽省",
          "澳门特别行政区",
          "安庆市",
          "蚌埠市",
        ]
      `);
    })
  });

  // ----------------click后没有触发emit，暂无头绪
  // it('click后验证value的变化', async () => {
  //   const wrapper = mount(XCascader,{
  //     props: { ...baseProps },
  //     sync: false
  //   });

  //   // const popover = wrapper.findComponent(XPopover)
  //   // popover.trigger('mousedown')
  //   // expect(popover.emitted()['update:visible'][0][0]).toBe(true)
  //   // expect(popover.emitted()['visibleChange'][0][0]).toBe(true)

  //   await asyncExpect(()=>{
  //     document.querySelector('.x-cascader-content .menu-item').click();
  //   })
  //   await asyncExpect(()=>{
  //     expect(wrapper.emitted()["update:value"]).toBeUndefined()
  //   })
  // });
});