<template>
  <section class="x-table">
    <div v-if="props.loading" class="x-table-loading">
      <img src="@/assets/images/loading.gif" alt="" />
    </div>
    <div v-else-if="props.dataSource.length === 0" class="x-table-nodata">
      <img src="@/assets/images/screen_table_nodata.png" alt="" />
      <span>{{ $t("noData") }}</span>
    </div>
    <template v-else>
      <table>
        <tr class="head">
          <template
            v-for="(col, index) in props.cols"
            :key="`table-thead-${index}`"
          >
            <td :style="{ flex: col.width ? col.width : 1 }">
              <slot
                v-if="$slots.headerCell"
                name="headerCell"
                :title="col.title"
                :column="col"
              >
              </slot>
              <span v-else>{{ col.title }}</span>
            </td>
          </template>
        </tr>
      </table>
      <table
        :style="{
          height: `calc(100% - 1.6vh ${
            props.pagination ? '- 4vh - 5.4vh' : ''
          })`,
        }"
      >
        <tr
          class="body"
          v-for="(item, index) in props.dataSource"
          :key="`table-tbody-tr-${index}`"
          v-bind="customRow(item, index)"
        >
          <td
            v-for="(col, _index) in props.cols"
            :key="`table-tbody-td-${index}-${_index}`"
            :style="{ flex: col.width || 1 }"
          >
            <template v-if="col.slots">
              <div :class="{ 'wrap-cell': col.slotsWrap }">
                <slot
                  :name="col.slots"
                  :colIndex="_index"
                  :col="col"
                  :recordIndex="index"
                  :record="item"
                ></slot>
              </div>
            </template>
            <template v-else>
              {{
                item[col.key] === "" ||
                item[col.key] === null ||
                item[col.key] === undefined
                  ? "--"
                  : item[col.key]
              }}
            </template>
          </td>
        </tr>
      </table>
    </template>
    <x-pagination
      v-if="props.pagination && props.dataSource.length > 0 && !props.loading"
      style="margin-top: 32px; align-self: flex-end"
      :total="props.pagination.total"
      :current="props.pagination.current"
      @update:current="currentChange"
      :pageSize="props.pagination.pageSize"
      @update:pageSize="pageSizeChange"
    />
  </section>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";
import type { PageSizeType } from "./types";
import xPagination from "@/components/x-pagination.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = defineProps({
  cols: {
    type: Array as PropType<
      {
        key: string;
        title: string;
        slots?: string;
        slotsWrap?: boolean; // 插槽中的内容超出换行 默认false
        width?: string;
      }[]
    >,
    default: () => [],
  },
  dataSource: {
    type: Array as PropType<{ [key: string]: any }[]>,
    default: () => [],
  },
  pagination: {
    type: Object as PropType<{
      total: number;
      current: number;
      pageSize: PageSizeType;
    }>,
  },
  loading: {
    type: Boolean,
    default: () => false,
  },
  customRow: {
    type: Function,
    default: () => {},
  },
});

props.cols.forEach((col) => {
  if (typeof col.slotsWrap === "undefined") {
    col.slotsWrap = false;
  }
});

const emits = defineEmits(["change"]);

const currentChange = (current: number) => {
  emits("change", "current", current);
};
const pageSizeChange = (pageSize: number) => {
  emits("change", "pageSize", pageSize);
};
</script>

<style lang="scss" scoped>
.x-table {
  position: relative;
  width: 100%;
  height: 100%;
  border-radius: 10px;
  display: flex;
  flex-direction: column;
  &-loading {
    @include ct-p;
    img {
      @include wh(50px);
    }
  }
  &-nodata {
    img,
    span {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    img {
      transform: translate(-50%, -70%);
      @include wh(227px, 103px);
    }
    span {
      transform: translate(-50%, 5vh);
      @include sc(1.6vh, rgb(221, 229, 250));
    }
  }
  table:nth-child(1) {
    @include wh(100%, 3.4vh);
    .head {
      @include fj {
        align-items: center;
      }
      @include wh(100%);
      padding-left: 8px;
      td {
        @include ell;
        padding-left: 8px;
      }
      background-color: rgba(63, 72, 113, 0.4);
      @include sc(1.3vh, rgb(176, 189, 248));
    }
  }
  table:nth-child(2) {
    overflow-y: overlay;
    scrollbar-width: none;
    display: block;
    width: 100%;
    .body {
      @include fj {
        align-items: center;
      }
      width: 100%;
      padding-left: 8px;
      td {
        @include ell;
        padding-left: 8px;
      }
    }
    .body {
      min-height: 5.4vh;
      border-bottom: 1px solid rgba(108, 128, 151, 0.5);
      @include sc(1.4vh, rgb(221, 229, 250));
      &:hover {
        font-weight: bold;
        border-radius: 4px;
        background: linear-gradient(
          180deg,
          rgba(159, 175, 209, 0.27),
          rgba(211, 219, 236, 0.12) 31.298%,
          rgba(155, 174, 216, 0.25) 100%
        );
      }
      td {
        @include ell;
      }
      .wrap-cell {
        white-space: normal;
      }
    }
  }
}
</style>
