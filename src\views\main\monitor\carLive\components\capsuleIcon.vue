<template>
  <section
    class="capsule-icon"
    :style="{
      height: props.height,
    }"
  >
    <div
      class="capsule-icon-icon"
      :style="{
        backgroundColor: props.config.color,
        backgroundImage: 'url(' + props.config.imgUrl + ')',
        backgroundSize: 'cover',
        width: props.height,
        height: props.height,
      }"
    >
      <x-icon
        v-if="props.config.icon"
        :name="props.config.icon"
      />
    </div>
    <span
      class="capsule-icon-text"
      :style="{
        color: props.config.color,
      }"
    >
      {{ props.config.text }}
    </span>
  </section>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  config: {
    type: Object as PropType<{
      icon?: string;
      text: string;
      color: string;
      imgUrl?: string;
    }>,
    required: true,
  },
  height: {
    type: String,
    default: () => "28px",
  },
});
</script>

<style lang="scss" scoped>
.capsule-icon {
  @include ct-f(y);
  padding-right: 8px;
  border-radius: 60px;
  background-color: rgb(255, 255, 255);
  box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
  &-icon {
    @include ct-f;

    border-radius: 50%;

    transition: 0.6s background-color;
  }
  &-text {
    margin-left: 8px;
    margin-right: 3px;
    font-size: 12px;
    transition: 0.6s color;
  }
}
</style>
