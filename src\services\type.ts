export type GetPublicKeyResponse = {
  publicKey: string;
};
export type LoginRequest = {
  userAccount: string;
  password: string;
  uuid: string;
  captcha: string;
};

export type LoginResponse = {
  /** token，请求时放入请求头 */
  token: string;
  /** 用户信息 */
  userInfo: {
    /** 创建人 */
    createdBy: number;
    /** 创建时间 */
    createdTime: number;
    /** 邮箱 */
    email: string;
    enterpriseTree: {
      entId: number;
      entId2NameMap: Record<string, string>;
      entIdList: number[];
      entName: string;
      proList: null;
      subEntList: EnterpriseTreeSubEntList[];
      velList: string[];
    };
    /** 企业id */
    entId: number;
    /** 头像路径 */
    imgUrl: string;
    /** 上一次登陆的ip */
    lastIp: string;
    /** 上一次登陆时间 */
    lastLoginTime: number;
    /** 手机号 */
    mobile: string;
    /** 盐 */
    password: null;
    permitList: string[];
    /** 项目id */
    projectId: number;
    /** 判断密码是否需要改变 */
    pwdChange: boolean;
    /** 所属角色名 */
    roleName: string;
    salt: null;
    /** 岗位类型 */
    stationText: string;
    status: number;
    statusText: string;
    updatedBy: number;
    updatedTime: number;
    urlList: string[];
    /** 账号 */
    userAccount: string;
    /** 用户id */
    userId: string;
    /** 用户名 */
    userName: string;
    /** 用户类型 */
    userType: number;
    /** 账号状态 */
    userTypeText: string;
    [property: string]: any;
  };
};
export type EnterpriseTreeSubEntList = {
  entId: number;
  entId2NameMap: null;
  entIdList: number[];
  entName: string;
  proList: null;
  subEntList: EnterpriseTreeSubEntList[];
  velList: string[];
};
export type LiveCarTreeResponse = {
  proList: {
    id: string;
    /** 项目名称 */
    proName: string;
    velList: {
      /** 是否选中 */
      checked: boolean;
      id: string;
      /** 车牌号码 */
      vehicleNo: string;
    }[];
  }[];
};

export type UserListRequest = {
  /** 企业id列表 */
  entIdList?: string[];
  /** 企业名称 */
  entName?: string;
  limit: number;
  /** 手机号 */
  mobile?: string;
  page: number;
  /** 账号 */
  userAccount?: string;
  /** 用户id */
  userId?: string;
  /** 名字 */
  userName?: string;
  /** 关键词(全部) */
  keyword?: string;
  station: number;
};

export type UserListResponse = {
  /** 当前页 */
  currPage: number;
  list: {
    createdBy: number;
    createdTime: number;
    /** 企业id */
    entId: string;
    /** 企业名称 */
    entName: string;
    /** 手机号 */
    mobile: string;
    /** 是否已经分配项目，true：分配了 */
    hasPro?: boolean;
    /** 岗位 */
    station: string; // 1：管理员，2：操作员，3其他
    /** 岗位文字 */
    stationText: string;
    /** 状态 */
    status: string; // 0：禁用   1：正常
    /** 状态文字 */
    statusText: string;
    updatedBy: number;
    updatedTime: number;
    /** 用户账号 */
    userAccount: string;
    /** 用户id */
    userId: number;
    /** 姓名 */
    userName: number;
    /** 用户类型 */
    userType: string;
    /** 用户类型文字 */
    userTypeText: string;
  }[];
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
};

export type AddUserRequest = {
  /** 企业id */
  entId: number;
  /** 手机号 */
  mobile: string;
  /** 密码 */
  password: string;
  /** 角色id列表 */
  roleIdList: number[];
  /** 岗位 */
  station: number;
  /** 用户账号 */
  userAccount: string;
  /** 用户姓名 */
  userName: string;
};

export type CompListResponse = {
  /** 企业id */
  entId: number;
  /** 企业名称 */
  entName: string;
}[];

export type UserSearchRoleRequest = {
  /** 企业id */
  entId: number;
  /** 用户id，可为空，为空时查询新增时的结果 */
  userId?: string;
};

export type UserSearchRoleResponse = {
  roleList: {
    /** 是否选中，true表示选中 */
    choose: string;
    /** remark */
    remark: string;
    /** 角色id */
    roleId: string;
    /** 角色名称 */
    roleName: string;
    /** 角色类型 */
    roleType: string;
  }[];
};

export type GetUserDetailResponse = {
  /** 企业车辆树 */
  enterpriseTree: any;
  /** 企业id */
  entId: number;
  /** 企业名称 */
  entName: string;
  /** 电话 */
  mobile: string;
  /** 所属角色 */
  roleList: {
    roleId: number;
    roleName: string;
  }[];
  /** 岗位 */
  stationText: string;
  station: number;
  /** 状态 */
  statusText: string;
  status: number;
  /** 账号 */
  userAccount: string;
  /** 用户id */
  userId: number;
  /** 名字 */
  userName: string;
  /** 用户类型 */
  userTypeText: string;
};

export type EditUserRequest = {
  entId: number;
  mobile: string;
  roleIdList: number[];
  station: number;
  /** 用户id */
  userId: string;
  /** 用户名 */
  userName: string;
};

export type CarAuthRequest = {
  /** 用户id */
  userId: number;
  /** 车辆id列表 */
  vehicleIdList: number[];
};

export type UserStatusRequest = {
  // 用户id
  userId: string;
  // 状态，状态  0：禁用   1：正常
  status: number;
};

export type CompanyListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 企业名称 */
  entName?: string;
};
export type CompanyListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    createdBy: string;
    createdTime: string;
    updatedBy: string;
    updatedTime: string;
    /** 企业地址 */
    entAddr: string;
    /** 企业id */
    entId: number;
    /** 企业名称 */
    entName: string;
    /** 联系电话 */
    mobile: string;
    /** 上级企业 */
    parentEnt: string;
    /** 上级企业id */
    parentEntId: number;
    /** 权限 */
    permitTree: string;
    /** 状态 */
    status: number;
    /** 主账号 */
    userAccount: string;
    /** 主账号id */
    userId: number;
    /** 主账号姓名 */
    userName: string;
  }[];
};
export type AddCompanyRequest = {
  /** 企业信息 */
  cszgSysEnterprise: {
    /** 企业地址 */
    entAddr: string;
    /** 企业名称 */
    entName: string;
    /** 上级企业id */
    parentEntId: number;
    /** 省，行政区划代码 */
    province: number;
    /** 市，行政区划代码 */
    city: number;
    /** 区，行政区划代码 */
    district: number;
  };
  /** 权限id列表 */
  permitIdList: number[];
  /** 主账号信息 */
  sysUser: {
    /** 联系人电话 */
    mobile: string;
    /** 密码 */
    password: string;
    /** 主账号 */
    userAccount: string;
    /** 联系人 */
    userName: string;
  };
};
export type GetCompanyDetailResponse = {
  /** 企业地址 */
  entAddr: string;
  /** 企业id */
  entId: number;
  /** 企业名称 */
  entName: string;
  /** 手机号 */
  mobile: string;
  /** 上级企业 */
  parentEnt: string;
  /** 上级企业id */
  parentEntId: number;
  /** 省，行政区划代码 */
  province: number;
  /** 市，行政区划代码 */
  city: number;
  /** 区，行政区划代码 */
  district: number;
  /** 主账号权限列表 */
  permitTree: {
    /** 菜单id */
    menuId: string;
    /** 名称 */
    name: string;
    /** 上级菜单id */
    parentId: string;
    /** 按钮权限 */
    perms: string;
    /** 下级权限列表，permitTree的列表 */
    subPermList: { [key: string]: any }[];
    /** 路径 */
    url: string;
  };
  /** 主账号 */
  userAccount: string;
  /** 主账号id */
  userId: string;
  /** 主账号联系人 */
  userName: string;
  /** 状态 */
  status: number;
  /** 状态文本 */
  statusText: string;
};
export type EditCompanyRequest = {
  /** 企业信息 */
  cszgSysEnterprise: {
    /** 地址 */
    entAddr: string;
    /** 企业id */
    entId: number;
    /** 企业名称 */
    entName: string;
    /** 上级企业id */
    parentEntId: number;
    /** 省，行政区划代码 */
    province: number;
    /** 市，行政区划代码 */
    city: number;
    /** 区，行政区划代码 */
    district: number;
  };
  /** 菜单id列表 */
  permitIdList: number[];
  /** 用户信息 */
  sysUser: {
    /** 手机号 */
    mobile: string;
    /** 主账号 */
    userAccount: string;
    /** 主账号id */
    userId: string;
    /** 用户名 */
    userName: string;
  };
};

export type ProListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 企业名称 */
  entName?: string;
  /** 项目名称 */
  proName?: string;
  /** 负责人姓名 */
  userName?: string;
  /** 运营结束时间 */
  openDateEnd?: string;
  /** 运营开始时间 */
  openDateStart?: string;
};
export type ProListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    /** 企业id */
    entId?: string;
    /** 企业名称 */
    entName?: string;
    /** 流水id */
    id?: number;
    /** 运营时间 */
    openDate?: string;
    /** 运营时间文字 */
    openDateText?: string;
    /** 项目名称 */
    proName?: string;
    /** 状态文字 */
    statusText?: string;
    /** 用户id */
    userId?: string;
    /** 负责人 */
    userName?: string;
  }[];
};
export type AddProRequest = {
  /** 企业id */
  entId: string;
  /** 运营时间 */
  openDate?: string;
  /** 项目名称 */
  proName: string;
  /** 负责人 */
  userId?: string;
};
export type GetProDetailResponse = {
  /** 企业id */
  entId: string;
  /** 企业名称 */
  entName: string;
  /** 流水id */
  id: number;
  /** 运营时间 */
  openDate: string;
  /** 运营时间文字 */
  openDateText: string;
  /** 项目名称 */
  proName: string;
  /** 项目编号 */
  proSeq: string;
  /** 项目人员列表 */
  relUserList: {
    /** 手机 */
    mobile: string;
    /** 账号 */
    userAccount: string;
    /** id */
    userId: string;
    /** 用户名 */
    userName: string;
  }[];
  /** 项目车辆列表 */
  relVehList: {
    /** 流水id */
    id: string;
    /** 车牌号码 */
    vehicleNo: string;
  }[];
  /** 状态文字 */
  statusText: string;
  /** 用户id */
  userId: string;
  /** 负责人 */
  userName: string;
};
export type EditProRequest = {
  /** 流水id */
  id: string;
  /** 运营时间 */
  openDate?: string;
  /** 项目名称 */
  proName: string;
  /** 用户id */
  userId?: string;
};
export type ProRelUserRequest = {
  /** 项目流水id */
  id: number;
  /** 人员id列表 */
  userIdList: number[];
};
export type ProRelVehRequest = {
  /** 项目流水id */
  id: number;
  /** 车辆id列表 */
  vehIdList: number[];
};
export type UserListInEntResponse = {
  /** 用户列表 */
  userList: {
    /** 用户账号 */
    userAccount: string;
    /** 用户id */
    userId: string;
    /** 名字 */
    userName: string;
    hasPro: boolean;
  }[];
};
export type VehListInEntResponse = {
  /** 车辆列表 */
  velList: {
    /** 车辆id */
    id: string;
    /** 用户编号 */
    vehicleNo: string;
    /** 是否选择 */
    checked: boolean;
  }[];
};
export type OperConfigRequest = {
  /** 配置关键字
   * 车辆类型：vehicle_type
   * 告警配置：warn_config */
  configList: string[];
};
export type OperConfigResponse = {
  /** 配置列表 */
  operConfigList: {
    /** 键 */
    k: number;
    /** 值 */
    val: string;
  }[];
};
export type VehicleTypeRequest = {
  /** 配置关键字 */
  configList: string[];
};
export type VehicleTypeResponse = {
  /** 配置列表 */
  operConfigList: {
    /** 键 */
    k: number;
    /** 值 */
    val: string;
  }[];
};

export type VehicleModelListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 车型号 */
  vehicleModel?: string;
  /** 车辆类型 */
  vehicleType?: number;
};
export type VehicleModelListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    id?: number;
    /** 备注 */
    remark?: string;
    /** 车型号 */
    vehicleModel?: string;
    /** 车类型id */
    vehicleType?: string;
    vehicleTypeText?: string;
  }[];
};
export type AddVehicleModelRequest = {
  /** 备注 */
  remark: string;
  /** 车辆型号 */
  vehicleModel: string;
  /** 车辆类型id */
  vehicleType: number;
};
export type GetVehicleModelDetailResponse = {
  /** 流水id */
  id: string;
  /** 备注 */
  remark: string;
  /** 车型号 */
  vehicleModel: string;
  /** 车类型id */
  vehicleType: number;
  /** 车型号文字 */
  vehicleTypeText: string;
};
export type EditVehicleModelRequest = {
  /** 流水id */
  id: string;
  /** 车辆型号 */
  remark: string;
  /** 备注 */
  vehicleModel: string;
  /** 车辆类型id */
  vehicleType: number;
};

export type SimListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 企业id列表 */
  entIdList?: number[];
  /** 企业名称 */
  entName?: string;
  /** 登记时间结束，2012-10-21 00:00:00 */
  registerDateEnd?: string;
  /** 登记时间开始，2012-10-21 00:00:00 */
  registerDateStart?: string;
  /** sim卡号 */
  simNo?: string;
  /** 状态 */
  status?: number;
};
export type SimListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    /** 企业名称 */
    entName: string;
    /** id */
    id: number;
    /** 运营商，网络运营商 0：移动：1 联通；2 电信；3 其他 */
    netOperator: string;
    /** 运营商文字 */
    netOperatorText: string;
    /** 登记日期 */
    registerDate: string;
    /** 登记日期文字 */
    registerDateText: string;
    /** sim卡号 */
    simId: number;
    /** 状态文字 */
    statusText: string;
    /** 车辆id */
    vehicleId: string;
    /** 车牌号 */
    vehicleNo: string;
  }[];
};
export type AddSimRequest = {
  /** 企业id */
  entId: string;
  /** 运营商， 网络运营商 0：移动：1 联通；2 电信；3 其他 */
  netOperator: number;
  /** 登记日期 */
  registerDate: string;
  /** sim卡号 */
  simId: string;
};
export type GetSimDetailResponse = {
  /** 企业名称 */
  entName: string;
  /** 企业id */
  entId: string;
  /** 流水id */
  id: string;
  /** 运营商 */
  netOperator: number;
  /** 运营商文字 */
  netOperatorText: string;
  /** 登记日期 */
  registerDate: string;
  /** 登记日期文字 */
  registerDateText: string;
  /** sim卡号 */
  simId: string;
  /** 状态文字 */
  statusText: string;
  status: number;
  /** 车辆id */
  vehicleId: string;
  /** 车牌号 */
  vehicleNo: string;
};
export type EditSimRequest = {
  /** 企业id */
  entId: string;
  /** 流水id */
  id: string;
  /** 运营商，网络运营商 0：移动：1 联通；2 电信；3 其他 */
  netOperator: number;
  /** 登记日期 */
  registerDate: string;
  /** sim卡号 */
  simId: string;
};
export type EntSimRequest = {
  /** 企业id */
  entId?: string;
  /** 车辆id */
  vehicleId?: string;
};
export type EntSimResponse = {
  simList: any[];
};

export type VehicleListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 企业id列表 */
  entIdList?: number[];
  /** 企业名称 */
  entName: string;
  /** 投放结束时间 */
  opDateEnd?: string;
  /** 投放开始时间 */
  opDateStart?: string;
  /** sim卡号 */
  simId?: string;
  /** vin */
  vin?: string;
  /** 状态 */
  status?: string;
  /** 车辆型号id */
  vehicleModelId?: string;
  /** 车牌号 */
  vehicleNo?: string;
  /** 车辆类型 */
  vehicleType?: number;
};
export type VehicleListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    /** 企业id */
    entId: string;
    /** 企业名称 */
    entName: string;
    /** 流水id */
    id: number;
    /** 资料url */
    instrUrl: string;
    /** 启用日期 */
    opDate: string;
    /** 启用日期文字 */
    opDateText: string;
    /** 项目id */
    proId: string;
    /** sim卡号 */
    simId: number;
    /** 状态文字 */
    statusText: string;
    /** 车辆型号 */
    vehicleModelId: string;
    /** 车型号文字 */
    vehicleModeText: string;
    /** 车牌号 */
    vehicleNo: string;
    /** 车辆类型 */
    vehicleType: string;
    /** 车类型文字 */
    vehicleTypeText: string;
  }[];
};
export type AddVehicleRequest = {
  entId: number;
  opDate?: string;
  simId: string;
  vin: string;
  sn: string;
  remoteSim: string;
  vehicleModelId: string;
  vehicleNo: string;
  vehicleType: string;
  instrUrl: string[];
  dandelionAccount?: string;
  dandelionPassword?: string;
  rtkAccount: string;
  dvrId?: string;
};
export type GetVehicleDetailResponse = {
  /** 企业id */
  entId: string;
  /** 企业名称 */
  entName: string;
  /** 流水id */
  id: string;
  /** 资料地址 */
  instrUrl: { fileName: string; instrUrl: string }[];
  /** 启用日期 */
  opDate: string;
  /** 启用日期文字 */
  opDateText: string;
  /** 项目id */
  proId: string;
  /** SIM卡号 */
  simId: string;
  /** 状态文字 */
  statusText: string;
  /** vin */
  vin: string;
  /** sn */
  sn: string;
  /** 远程sim */
  remoteSim: string;
  /** DVR id */
  dvrId: string;
  /** 车型号id */
  vehicleModelId: string;
  /** 车型号文字 */
  vehicleModeText: string;
  /** 车牌号 */
  vehicleNo: string;
  /** 车类型 */
  vehicleType: string;
  /** 车类型文字 */
  vehicleTypeText: string;
  /** 蒲公英 */
  dandelionAccount: string;
  dandelionPassword: string;
  /** RTK账号 */
  rtkAccount: string;
  vehOperateStatus?: number;
  remoteNo?: string;
};
export type EditVehicleRequest = {
  /** 流水id */
  id: string;
  /** 企业id */
  entId?: string;
  /** 启用日期 */
  opDate?: string;
  /** sim卡号 */
  simId: string;
  /** vin */
  vin: string;
  /** 车辆型号 */
  vehicleModelId?: string;
  /** 车牌号 */
  vehicleNo: string;
  /** 车辆类型 */
  vehicleType?: string;
  /** 资料地址 */
  instrUrl: string[];
  /** 蒲公英 */
  dandelionAccount?: string;
  dandelionPassword?: string;
  /** RTK账号 */
  rtkAccount: string;
};
export type VehicleTreeInEntResponse = {
  enterpriseTree: {
    /** 企业id */
    entId: string;
    /** 企业名称 */
    entName: string;
    /** 项目列表 */
    proList: {
      /** 项目id */
      id: string;
      /** 项目名称 */
      proName: string;
      /** 车辆列表 */
      velList: {
        /** 车辆id */
        id: string;
        /** 车牌号 */
        vehicleNo: string;
      }[];
    }[];
    /** 子企业树列表，enterpriseTree */
    subEntList: { [key: string]: any }[];
  };
};

export type RoleListRequest = {
  /** 每页条数 */
  limit: number;
  /** 页数 */
  page: number;
  /** 角色名称 */
  roleName?: string;
  entName?: string;
};
export type RoleListResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    /** 企业名称 */
    entName: string;
    /** 备注 */
    remark: string;
    /** 角色id */
    roleId: number;
    /** 角色名称 */
    roleName: string;
    /** 角色类型，角色类型0-系统角色，1普通角色 */
    roleType: number;
  }[];
};
export type AddRoleRequest = {
  /** 企业id */
  entId: number;
  /** 菜单按钮id列表 */
  permitIdList: number[];
  /** 备注 */
  remark?: string;
  /** 角色名称 */
  roleName: string;
};
export type GetRoleDetailResponse = {
  /** 权限树 */
  permitTree: {
    /** 菜单id */
    menuId: string;
    /** 名称 */
    name: string;
    /** 上级菜单id */
    parentId: string;
    /** 权限 */
    perms: string;
    /** 下级权限 */
    subPermList: { [key: string]: any }[];
    /** 路径 */
    url: string;
  };
  /** 角色id */
  roleId: string;
  /** 用户账号列表 */
  userAccountList: string[];
  entId: number;
  entName: string;
  roleName: string;
};
export type EditRoleRequest = {
  /** 企业id */
  entId: number;
  /** 菜单按钮id */
  permitIdList: number[];
  /** 备注 */
  remark?: string;
  /** 角色id */
  roleId: string;
  /** 角色名称 */
  roleName: string;
};
export type RoleUserAccountRequest = {
  limit: number;
  page: number;
  roleId: string;
};
export type RoleUserAccountResponse = {
  /** 当前页 */
  currPage: number;
  /** 条数 */
  pageSize: number;
  /** 总数 */
  totalCount: number;
  /** 列表 */
  list: {
    account?: string;
    mobile?: string;
    userName?: string;
  }[];
};

export type PermTreeResponse = {
  /** 菜单id */
  menuId: string;
  /** 名称 */
  name: string;
  /** 上级id */
  parentId: string;
  /** 权限 */
  perms: string;
  /** 下级菜单权限列表 */
  subPermList: {
    /** 菜单id */
    menuId?: string;
    /** 名称 */
    name?: string;
    /** 上级id */
    parentId?: string;
    /** 权限 */
    perms?: string;
    /** 下级菜单权限列表 */
    subPermList?: { [key: string]: any }[];
    /** 路径 */
    url?: string;
  }[];
  /** 路径 */
  url: string;
};

export type updateHeadPicResponse = {
  /** 新头像路径 */
  uploadUrl: string;
};
export type checkPwdRequest = {
  /** 原密码 */
  password: string;
  /** 用户id */
  userId: string;
};
export type checkPwdResponse = {
  /** 校验原密码是否相同 */
  check: boolean;
};
export type changePwdRequest = {
  password: string;
  userId: string;
};

export type WarnListRequest = {
  /** 车牌号 */
  deviceId: string;
  /** 条数 */
  limit: number;
  /** value : 1 统计所有的警告，2 已解决的警告 3 未解决的警告 4 实时警告 */
  mode: string;
  /** 页码 */
  page: number;
  warnDateEnd: string;
  warnDateStart: string;
  warnLevel: string;
  /** 故障码 */
  faultCode: string;
};
export type WarnListResponse = {
  currPage: number;
  list: {
    carType: string;
    createdTime: string;
    deviceId: string;
    faultAddress: string;
    faultContent: string;
    faultType: number;
    faultCode: string;
    id: string;
    /** 是否结束0未结束，1已结束 */
    isEnd: number;
    isSolved: number;
    solveTime: string;
    warnLevel: number;
  }[];
  pageSize: number;
  totalCount: number;
};
export type StatisticsNumberRequest = {
  /** 车牌号码 */
  deviceId?: string;
  warnDateEnd?: string;
  warnDateStart?: string;
};
export type StatisticsNumberResponse = {
  /** 实时警告 */
  currentCount: number;
  /** 已解决的警告 */
  solvedCount: number;
  /** 总计警告数 */
  total: number;
  /** 未解决的警告 */
  unsolvedCount: number;
};
export type WarningDetailRequest = {
  id: string;
};
export type WarnDetailResponse = {
  /** 车辆类型 */
  carType: string;
  /** 公司 */
  company: string;
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 问题描述 */
  description: string;
  /** 车牌号 */
  deviceId: string;
  /** 结束时间 */
  endTime: string;
  /** 故障地址 */
  faultAddress: string;
  faultContent?: string;
  faultType?: number;
  /** 主键 */
  id: string;
  /** 是否结束0未结束，1已结束 */
  isEnd: number;
  /** 是否已读 */
  isNoticed?: number;
  /** 是否解决0未解决,1已解决 */
  isSolved: number;
  /** 制造商 */
  manufacturer?: string;
  /** 所属项目 */
  project: string;
  /** 警告记录 */
  records: {
    description?: string;
    isSolved?: number;
    updateTime?: string;
    userName?: string;
  }[];
  /** 解决时间 */
  solveTime: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  /** 故障等级 */
  warnLevel: number;
  /** 故障等级描述 */
  warnLevelStr: string;
};
export type WarningStatusRequest = {
  /** 理由/问题描述 */
  description: string;
  /** 对应表的主键 */
  id: string;
  /** 必填  0表示为解决，1已解决 */
  isSolved: number;
};
export type VehicleVinRepeatRequest = {
  /** 车id */
  id?: string;
  /** vin码 */
  vin: string;
};
export type VehicleNoRepeatRequest = {
  /** 车辆id编号 */
  id?: string;
  /** 车牌号码 */
  vehicleNo: string;
};
export type DandelionAccountRepeatRequest = {
  /** 车id */
  id?: string;
  /** 蒲公英账号 */
  dandelionAccount: string;
};
export type WarnConfigListRequest = {
  /** 是否接收 0：未接收，1：已接收 */
  isReceived?: number;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 车辆类型 1：PB ,2:PC */
  vehicleType?: number;
  /** 警告等级 1：一级 */
  warnLevel?: number;
  /** 警告模块名称 */
  warnModule?: string;
  /** 警告名 */
  warnName?: string;
  /** 警告版本 */
  version?: string;
};
export type WarnConfigListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  /** 列表数据 */
  list: {
    /** 创建者id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 警情描述 */
    description?: string;
    id?: string;
    /** 接口对应描述 */
    interfaceDescription: string;
    /** 平台是否接收，0：未接收，1：已接收 */
    isReceived: number;
    /** 平台全局弹窗，0：不弹窗，1：弹窗 */
    popup: number;
    /** 更新者id */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 警告模块，1：PB ,2:PC */
    vehicleType: number;
    /** 警告等级 1：一级 */
    warnLevel: number;
    /** 警告模块 */
    warnModule: string;
    /** 警告名称 */
    warnName: string;
  }[];
};
export type ChangeReceiveRequest = {
  id: string;
  /** 平台是否接收，0：未接收，1：已接收 */
  isReceived: number;
};
export type AddWarnConfigRequest = {
  /** 警情描述 */
  description: string;
  /** 接口对应描述 */
  interfaceDescription: string;
  /** 平台是否接收，0：未接收，1：已接收 */
  isReceived: number;
  /** 平台全局弹窗，0：不弹窗，1：弹窗 */
  popup: number;
  /** 车辆类型，1：PB ,2:PC */
  vehicleType: number;
  /** 警告等级 1：一级 */
  warnLevel: number;
  /** 警告模块 */
  warnModule: string;
  /** 警告名称 */
  warnName: string;
};
export type EditWarnConfigRequest = {
  /** 警情描述 */
  description?: string;
  id: string;
  /** 接口对应描述 */
  interfaceDescription: string;
  /** 平台是否接收，0：未接收，1：已接收 */
  isReceived: number;
  /** 平台全局弹窗，0：不弹窗，1：弹窗 */
  popup: number;
  /** 警告模块，1：PB ,2:PC */
  vehicleType: number;
  /** 警告等级 1：一级 */
  warnLevel: number;
  /** 警告模块 */
  warnModule: string;
  /** 警告名称 */
  warnName: string;
};
export type WarnConfigDetailRequest = {
  /** 主键 */
  id?: string;
};
export type WarnConfigDetailResponse = {
  /** 创造者id */
  createdBy: number;
  /** 创造时间 */
  createdTime: string;
  /** 描述 */
  description: string;
  /** 主键 */
  id: string;
  /** 接口描述 */
  interfaceDescription: string;
  /** 是否接收 0：未接收，1：已接收 */
  isReceived: number;
  /** 是否弹窗 0：未接收，1：已接收 */
  popup: number;
  /** 更新ID */
  updatedBy: number;
  /** 更新时间 */
  updatedTime: string;
  /** 车辆类型 1：PB ,2:PC */
  vehicleType: number;
  /** 车辆类型描述 */
  vehicleTypeStr: string;
  /** 警告等级 1：一级 */
  warnLevel: number;
  /** 警告等级描述 */
  warnLevelStr: string;
  /** 警告模块名称 */
  warnModule: string;
  /** 警告名 */
  warnName: string;
};
export type DeleteWarnConfigRequest = {
  /** 主键 */
  id?: string;
};
// 模块不空，名称空，查询模块列表
// 模块不空，名称不空，查询模块列表下名称
// 模块空，名称不空，查询模块列表下名称
// 模块空，名称空，参数非法
export type WarnModuleAndNameRequest = {
  /** 警告模块 */
  warnModule: string;
  /** 警告名称 */
  warnName: string;
  /** 警告版本 1: 1.0 2: 2.0 */
  remark1: string;
};
export type WarnModuleAndNameResponse = string[];
export type WarnSettingListResponse = {
  code: number;
  message: string;
  key: {
    createdBy: number;
    createdTime: string;
    description: string;
    id: string;
    interfaceDescription: string;
    isReceived: number;
    popup: number;
    updatedBy: number;
    updatedTime: string;
    vehicleType: number;
    warnLevel: number;
    warnModule: string;
    warnName: string;
  }[];
  success: boolean;
  timestamp: number;
};
export type BatchSetWarnConfigRequest = {
  /** 主键 */
  id?: number;
  /** 是否接受 */
  isReceived?: number;
  /** 是否弹窗 */
  popup?: number;
  /** 警告等级 */
  warnLevel?: number;
  /** 警告名称 */
  warnName?: string;
}[];

export type RTKListRequest = {
  /** 企业id链表 */
  entIdList?: number[];
  /** 企业名 */
  entName?: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 登记结束时间 */
  registerEndDate?: string;
  /** 登记开始时间 */
  registerStatDate?: string;
  /** RTK账号 */
  rtkAccount?: string;
  /** 状态 */
  status?: number;
};
export type RTKListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 创建者ID */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 企业id */
    entId: number;
    entName?: string;
    /** id */
    id?: number;
    /** 网络运营商 0：移动：1 联通；2 电信；3 其他 */
    netOperator: number;
    netOperatorText?: string;
    /** 登记时间 */
    registerDate?: string;
    registerDateText?: string;
    /** RTK账号 */
    rtkAccount: string;
    /** RTK密码 */
    rtkPassword: string;
    /** 状态  0：禁用   1：正常 */
    status?: number;
    statusText?: string;
    /** 更新者ID */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 车id */
    vehicleId?: number;
    vehicleNo?: string;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
};
export type AddRTKRequest = {
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业id */
  entId: number;
  entName?: string;
  /** id */
  id?: number;
  /** 网络运营商 0：移动：1 联通；2 电信；3 其他 */
  netOperator: number;
  netOperatorText?: string;
  /** 登记时间 */
  registerDate?: string;
  registerDateText?: string;
  /** RTK账号 */
  rtkAccount: string;
  /** RTK密码 */
  rtkPassword: string;
  /** 状态  0：禁用   1：正常 */
  status?: number;
  statusText?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  /** 车id */
  vehicleId?: number;
  vehicleNo?: string;
};
export type GetRTKDetailResponse = {
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业id */
  entId: number;
  entName?: string;
  /** id */
  id: string;
  /** 网络运营商 0：移动：1 联通；2 电信；3 其他 */
  netOperator: number;
  netOperatorText?: string;
  /** 登记时间 */
  registerDate?: string;
  registerDateText?: string;
  /** RTK账号 */
  rtkAccount: string;
  /** RTK密码 */
  rtkPassword: string;
  /** 状态  0：禁用   1：正常 */
  status?: number;
  statusText?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  /** 车id */
  vehicleId?: number;
  vehicleNo?: string;
};
export type EditRTKRequest = {
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业id */
  entId: number;
  entName?: string;
  /** id */
  id: string;
  /** 网络运营商 0：移动：1 联通；2 电信；3 其他 */
  netOperator: number;
  netOperatorText?: string;
  /** 登记时间 */
  registerDate?: string;
  registerDateText?: string;
  /** RTK账号 */
  rtkAccount: string;
  /** RTK密码 */
  rtkPassword: string;
  /** 状态  0：禁用   1：正常 */
  status?: number;
  statusText?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  /** 车id */
  vehicleId?: number;
  vehicleNo?: string;
};
export type AvailableRTKRequest = {
  /** 企业id */
  entId: string;
};
export type AvailableRTKResponse = {
  /** rtk实体类列表 */
  sysRTKEntities: {
    /** 创建者ID */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 企业id */
    entId: number;
    entName?: string;
    /** id */
    id?: number;
    /** 网络运营商 0：移动：1 联通；2 电信；3 其他 */
    netOperator: number;
    netOperatorText?: string;
    /** 登记时间 */
    registerDate?: string;
    registerDateText?: string;
    /** RTK账号 */
    rtkAccount: string;
    /** RTK密码 */
    rtkPassword: string;
    /** 状态  0：禁用   1：正常 */
    status?: number;
    statusText?: string;
    /** 更新者ID */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 车id */
    vehicleId?: number;
    vehicleNo?: string;
  }[];
};
export type CheckRTKRequest = {
  /** id */
  id?: string;
  /** rtk账号 */
  rtkAccount: string;
  /** rtk密码 */
  rtkPassword: string;
};

export type GetProAreaListRequest = {
  /** 区域名称 */
  areaName?: string;
  /** 区域类型 */
  areaType?: number;
  /** 企业id */
  entId?: number;
  /** 企业名称 */
  entName?: string;
  /** 页面条数 */
  limit: number;
  /** 当前页面 */
  page: number;
  /** 项目id */
  projectId?: number;
  /** 项目名称 */
  projectName?: string;
};
export type GetProAreaListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据[ 项目区域站点信息管理实体类 ] */
  list?: {
    /** 区域名称 */
    areaName: string;
    /** 区域类型，0：园区，1：街道 */
    areaType: number;
    areaTypeName?: string;
    /** 创建者ID */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 企业Id */
    entId?: number;
    entName?: string;
    /** id */
    id?: number;
    /** 项目id */
    proId?: number;
    proName?: string;
    /** 更新者ID */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount: number;
};
export type GetProListResponse = {
  /** id */
  id: string;
  /** 项目名称 */
  proName: string;
}[];
export type ProjectListResponse = {
  proNames?: string[];
};
export type GetProUserListRequest = {
  /** 通用查询 */
  generalQueryName?: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 项目名 */
  proName?: string;
};
export type GetProUserListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** id */
    id?: string;
    /** 手机号 */
    mobile?: string;
    /** 项目id */
    proId?: string;
    /** 项目名称 */
    proName?: string;
    /** 岗位1：管理员，2：操作员，3其他 */
    station?: number;
    /** 管理员，操作员，其他 */
    stationText?: string;
    /** 状态  0：禁用   1：正常
     * @ListValue(val={0,1}) */
    status?: number;
    /** 禁用 正常 */
    statusText?: string;
    /** 用户账号 */
    userAccount?: string;
    /** 用户id */
    userId?: number;
    /** 用户名 */
    userName?: string;
    /** 车辆 */
    vehicles?: {
      /** 车辆id */
      vehicle_id?: number;
      /** 车牌号码 */
      vehicleNo?: string;
    }[];
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
};
export type GetProUserDetailResponse = {
  /** id */
  id?: number;
  /** 手机号 */
  mobile?: string;
  /** 分级已授权的车辆列表 */
  proAreaVelParams?: {
    /** 项目区域集合 */
    areaList?: {
      /** 区域名称 */
      areaName?: string;
      /** 项目区域id */
      id?: number;
      /** 车辆列表 */
      vehNoList?: {
        checked?: boolean;
        /** 车辆id */
        id?: number;
        /** 车牌号 */
        vehicleNo?: string;
      }[];
    }[];
    /** 项目id */
    id?: number;
    /** 项目名称 */
    proName?: string;
  }[];
  /** 项目id */
  proId?: number;
  /** 项目名称 */
  proName?: string;
  /** 岗位1：管理员，2：操作员，3其他 */
  station?: number;
  /** 管理员，操作员，其他 */
  stationText?: string;
  /** 状态  0：禁用   1：正常
   * @ListValue(val={0,1}) */
  status?: number;
  /** 禁用 正常 */
  statusText?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 用户id */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 车辆列表 */
  vehicles?: {
    /** 车辆id */
    vehicleId?: number;
    /** 车牌号码 */
    vehicleNo?: string;
  }[];
};
export type GetVehAuthListResponse = {
  proAreaVelList?: {
    /** 项目区域集合 */
    areaList?: {
      /** 区域名称 */
      areaName?: string;
      /** 项目区域id */
      id?: number;
      /** 车辆列表 */
      vehNoList?: {
        checked?: boolean;
        /** 车辆id */
        id?: number;
        /** 车牌号 */
        vehicleNo?: string;
      }[];
    }[];
    /** 项目id */
    id?: number;
    /** 项目名称 */
    proName?: string;
    /** 车辆列表 */
    vehNoList?: any[];
  }[];
};
export type SaveVehAuthRequest = {
  /** 需要授权车辆的用户id */
  userId: number;
  /** 车辆id */
  velIds?: number[];
};

export type AddProAreaRequest = {
  areaBlockDtoList?: {
    /** 区块点位 */
    areaBlockGpsList?: {
      /** 纬度 */
      latitude?: number;
      /** 经度 */
      longitude?: number;
      /** 点位顺序 */
      orders?: number;
      /** y */
      x?: number;
      /** x */
      y?: number;
      [property: string]: any;
    }[];
    /** 区块名称 */
    blockName?: string;
    /** 区块编号 */
    blockNo?: string;
    id?: string;
    /** 项目区域编号 */
    proAreaId?: string;
    [property: string]: any;
  }[];
  /** 围栏 */
  areaFenceDto?: {
    /** gps数据 */
    gps?: {
      /** 纬度 */
      latitude?: number;
      /** 经度 */
      longitude?: number;
      /** 点位顺序 */
      orders?: number;
      [property: string]: any;
    }[];
    /** 项目区域编号 */
    proAreaId?: number;
    [property: string]: any;
  };
  /** 区域名称 */
  areaName?: string;
  /** 路线 */
  areaRouteDto?: {
    /** 正常清扫：transfer_sweeping
     * 转场：transfer_no_sweeping
     * 沿边清扫：edgewise
     * 泊车：parking */
    cleanType?: string;
    /** 清扫类型数组 */
    cleanTypeArrays?: string[];
    cleanTypeList?: {
      key?: string;
      value?: string;
      [property: string]: any;
    }[];
    /** gps数据 */
    gps?: {
      /** 纬度 */
      latitude?: number;
      /** 经度 */
      longitude?: number;
      /** 点位顺序 */
      orders?: number;
      [property: string]: any;
    }[];
    id?: string;
    order?: number;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 路线名称 */
    routeName?: string;
    vehNoList?: string[];
    /** 车辆路线编号 */
    vehRouteNo?: string;
    [property: string]: any;
  }[];
  /** 区域类型，0：园区，1：街道 */
  areaType?: number;
  /** 企业id */
  entId?: string;
  /** 主键 */
  id?: string;
  /** 项目id */
  projectId?: string;
  /** 站点 */
  sysAreaStationEntityList?: {
    /** 站点x点 */
    areaX?: string;
    /** 站点y点 */
    areaY?: string;
    /** 站点z点 */
    areaZ?: string;
    /** 转场：transfer_no_sweeping
     * 泊车：parking */
    cleanType?: string;
    cleanTypeArrays?: string[];
    cleanTypeList?: {
      key?: string;
      value?: string;
      [property: string]: any;
    }[];
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 编号 */
    id?: string;
    /** 纬度 */
    latitude?: number;
    /** 经度 */
    longitude?: number;
    /** 项目区域编号 */
    proAreaId?: string;
    /** 站点名称 */
    stationName?: string;
    /** 站点图片 */
    stationPic?: string;
    stationParkingRelEntityList?: {
      /** 项目站点编号 */
      areaStationId?: number;
      chargingId?: number;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 编号 */
      id?: string;
      /** 停车位名称 */
      parkName?: string;
      /** 停车位编号 */
      parkNo?: string;
      [property: string]: any;
    }[];
    /** 站点绑定的车辆列表 */
    stationVehRelEntityList?: {
      /** 项目区域id */
      proAreaId?: string;
      /** 车辆id */
      vehicleId: string;
    }[];
    /** 站点类型，0：垃圾点，1：充电点，2：加水点3：停车点 */
    stationType?: number;
    stationTypeText?: string;
    /** 路线id */
    wayId?: string;
    /** yaw角 */
    yaw?: string;
    [property: string]: any;
  }[];
  /** 自录路线 */
  recordingRouteEntities: {
    /** 自录路线id */
    id?: string;
    /** 车端路径 */
    routePath?: string;
    /** 路线类型，false业务类true辅助类 */
    routeType?: boolean;
    /** 开关，false关true开 */
    routeStatus?: boolean;
  }[];
  [property: string]: any;

  TrafficLightEntityList: {
    /**
     * id
     */
    id: string;
    /**
     * 配置时间s秒
     */
    passTime: number;
    /**
     * 红绿灯名称
     */
    trafficName: string;
    [property: string]: any;
  };
};

export type GetProAreaCarAuthRequest = {
  // id（区域id主键）
  id: number;
  // 项目id
  proId: number;
};

export type ProAreaCarAuthRequest = {
  // 车辆id
  vehicleId: number[];
  // 区域id
  proAreaId: number;
};
export type GetProVehListRequest = {
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 区域id */
  proAreaId?: string;
  /** 项目id */
  proId?: string;
  /** 项目名称 */
  proName?: string;
  /** 是否已排班，0：关，1开 */
  taskSwitch?: number | string;
  /** 车辆负责人 */
  userName?: string;
  /** 车牌号 */
  vehicleNo?: string;
};
export type GetProVehListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 区域名称 */
    areaName?: string;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 主键 */
    id?: string;
    /** 站点类型，1：垃圾点，2：充电点，3：加水点4：停车点 */
    parkPosition?: number;
    /** 区域项目id */
    proAreaId?: string;
    /** 项目Id */
    proId: string;
    /** 项目名称 */
    proName?: string;
    /** 0:否，1:是 */
    shutDown?: number;
    /** 排版计划，0：关，1开 */
    taskSwitch?: number;
    /** 任务类型，0:无，1:每天，2:星期 */
    taskType?: number;
    /** 任务类型，0:无，1:每天，2:星期 */
    taskTypeText: string;
    /** 更新人 */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 用户id */
    userId?: string;
    /** 车牌负责人 */
    userName?: string;
    /** 车辆id */
    vehicleId?: string;
    /** 车牌号码 */
    vehicleNo?: string;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
};
export type UpdateProVehManagerRequest = {
  /** id */
  id?: string;
  /** 名称 */
  name?: string;
};
export type GetProVehManagerListRequest = {
  /** 项目id */
  proId: string;
};
export type GetProVehManagerListResponse = {
  /** 用户名 */
  userAccount?: string;
  /** 用户id */
  userId?: number;
  /** 用户名 */
  userName?: string;
  /** 状态  0：主账号   1：副账号 */
  userType?: number;
  /** 状态  0：主账号   1：副账号 */
  userTypeText?: string;
}[];
export type GetProjectAndAreaRequest = number[];
export type GetProjectAndAreaResponse = {
  /** id */
  id?: number;
  /** 名称 */
  name?: string;
}[];
export type UpdateTaskStatusRequest = {
  /** 主键 */
  id: string;
  /** 是否已排班，0：关，1开 */
  taskSwitch: number;
};
export type UpdateVehTaskRequest = {
  /** id 主键 */
  id: string;
  /** 任务类型，0：无，1:每天，2:星期 */
  taskType: number;
  /** 班次 */
  vehArrangement?: {
    /** 任务类型，0:每天，1-7 对应着星期一到星期天， */
    executeDays?: string[];
    /** 结束时间 */
    executeTimeEnd: string;
    /** 开始时间，格式”hh:mm“ */
    executeTimeStart: string;
    /** 站点类型，1：垃圾点，2：充电点，3：加水点4：停车点 */
    parkPosition: number;
    /** 清扫类型 */
    cleanType: string;
    /** 停车场id */
    parkId?: string;
    /** 是否关机 0:否，1:是 */
    shutDown: number;
    /** 任务列表 */
    taskList?: {
      /** 参数等待与车端约定 */
      taskType: string;
      /** 车辆路线编号 */
      vehRouteNo: string;
      /** 清扫类型 */
      sweepingType?: string;
    }[];
  }[];
  /** 车辆id */
  vehicleId: string;
};
export type GetVehTaskInfoRequest = {
  /** 车辆Id */
  vehicleId?: string;
};
export type GetVehTaskInfoResponse = {
  /** 任务类型，0:每天，1-7 对应着星期一到星期天， */
  executeDay?: number;
  /** 执行结束时间 */
  executeTimeEnd?: string;
  /** 执行开始时间 */
  executeTimeStart?: string;
  /** 站点类型，1：垃圾点，2：充电点，3：加水点4：停车点 */
  parkPosition: number;
  /** 清扫类型 */
  cleanType: string;
  /** 停车场id */
  parkId?: string;
  /** 是否关机 0:否，1:是 */
  shutDown: number;
  /** 任务列表 */
  templateId: number;
  taskList?: {
    /** 任务类型
     * edgewise(沿边清扫)
     * transfer_no_sweeping(只转场)
     * transfer_sweeping(转场清扫)
     * parking(泊车) */
    taskType?: string;
    /** 路线名 */
    vehRouteName?: string;
    /** 清扫类型 */
    sweepingType?: string;
  }[];
}[];
export type GetRouteListRequest = {
  /** 区域id */
  proAreaId: string;
};
export type GetVehStationListResponse = {
  /** 站点x点 */
  areaX?: string;
  /** 站点y点 */
  areaY?: string;
  /** 站点z点 */
  areaZ?: string;
  /** 转场：transfer_no_sweeping
   * 泊车：parking */
  cleanType?: string;
  cleanTypeArrays?: string[];
  cleanTypeList?: {
    key?: string;
    value?: string;
    [property: string]: any;
  }[];
  /** 创建人id */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 编号 */
  id?: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 停车id */
  parkId?: string;
  /** 项目区域编号 */
  proAreaId?: number;
  /** 站点名称 */
  stationName?: string;
  stationParkingRelEntityList?: {
    /** 项目站点编号 */
    areaStationId?: number;
    chargingId?: number;
    chargingStationNo?: string;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 编号 */
    id?: number;
    /** 停车位名称 */
    parkName?: string;
    /** 停车位编号 */
    parkNo?: string;
    location: string;
    serialNumber?: string;
    [property: string]: any;
  }[];
  /** 站点类型，1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
  stationType?: number;
  stationTypeText?: string;
  /** 路线id */
  wayId?: string;
  /** yaw角 */
  yaw?: string;
  [property: string]: any;
}[];
export type GetVehRouteListResponse = {
  /** 清扫类型 */
  cleanTypeList?: {
    key: string;
    value: string;
  }[];
  /** 编号 */
  id?: string;
  /** 路线名称 */
  routeName?: string;
  /** 车辆路线编号 */
  vehRouteNo?: string;
  cleanTypeArrays: string[];
  gps: { longitude: number; latitude: number }[];
}[];
export type GetVehBlockListResponse = {
  /** 区块id */
  id: string;
  /** 区块名称 */
  blockName: string;
  /** 经纬度 */
  areaBlockGpsList: {
    longitude: number;
    latitude: number;
    x?: number;
    y?: number;
  }[];
}[];
export type GetAreaRangeResponse = {
  id: string;
  /** 区域id */
  proAreaId: string;
  /** 分块id: 相同表示是同一个物体或者区块的多边形 */
  xmlId: string;
  /** 点位类型 0：任务范围 1：障碍物范围 */
  pointType: number;
  longitude: number;
  latitude: number;
}[];

export type UpdateLampBlowWaterRequest = {
  /** 警示灯开关控制 0关，1开 */
  alarmLamp?: string;
  /** 0关，1开, 2释放, 3 low:低速，4 medium:中速，5 high:高速 */
  blowMotor?: number;
  /** 风机喷水开关控制 0关，1开 */
  blowWaterPump?: string;
  deviceId: string;
  /** 2.0释放权限 */
  enable?: boolean;
  /** 0,关，1开,2释放 */
  headLamp?: number;
  /** 0:否 1:是  是否停止充电（此字段只针对易咖三轮车） */
  stopCharge?: number;
  /** 垃圾箱喷水开关控制0,关，1开 */
  suctionWaterPump?: string;
  /** 扫刷控制，0 down:降落, 1 up:抬升，,2退出控制,3 outer_most:边刷放下，伸最大,4 outer_middle:边刷放下,剧中，5 outer_none:边刷放下，没有伸缩 */
  sweepBrush?: number;
  /** 0,关，1开,2释放 */
  waterPump?: number;
  [x: string]: any;
};

export type GetVehLocusDatesRequest = {
  /** 车牌号 */
  deviceId: string;
  /** 月份 */
  month: number;
  /** 年份 */
  year: number;
};
export type GetVehLocusDatesResponse = string[];
export type GetVehLocusPositionRequest = {
  /** 车牌 */
  deviceId: string;
  /** 结束时间 */
  lastTime: string;
  /** 开始时间 */
  startTime: string;
  /** 任务编号 */
  workNo?: string;
};
export type GetVehLocusPositionResponse = {
  createdTime: string;
  /** 车牌号 */
  deviceId: string;
  /** id */
  id: number;
  /** 纬度 */
  latitude: string;
  /** 经度 */
  longitude: string;
  addr: string;
  gpsTime: string;
  speed: number;
  tank: number;
  direction: number;
  driveMode: string;
}[];
export type GetVehLocusVehiclesResponse = {
  /** 可以忽略 */
  checked: boolean;
  /** id */
  id: string;
  /** 车牌号 */
  vehicleNo: string;
};
export type GetOperationRecordListRequest = {
  /** 车牌号 */
  deviceId?: string;
  /** 结束时间 */
  endTime?: string;
  /** 企业id链表 */
  entIdList?: number[];
  /** 企业名 */
  entName?: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 开始时间 */
  startTime?: string;
  /** 任务,操作事项 */
  taskItem?: string;
  /** 任务结果 */
  taskResult?: string;
  userName?: string;
};
export type GetOperationRecordListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list: {
    /** 任务内容 */
    content?: string;
    /** 操作时间 */
    createdTime?: string;
    /** 车辆唯一标识 */
    deviceId?: number;
    /** 企业id */
    entId?: number;
    /** 用户名 */
    entName?: string;
    /** 编号 */
    id?: number;
    /** 操作来源，1：app，2：pc,3:远程驾驶,默认0：-- */
    source?: number;
    sourceTxt?: string;
    /** 速度 */
    speed?: number;
    /** 操作事项,车辆开机/车辆关机/任务下发/任务暂停/任务恢复,从1开始，0：-- */
    taskItem?: number;
    taskItemTxt?: string;
    /** 操作结果，1表示成功，0表示失败 */
    taskResult?: number;
    taskResultTxt?: string;
    /** 操作类型，1：人为操作，2：系统操作 */
    taskType?: number;
    taskTypeTxt?: string;
    /** 用户id */
    userId?: number;
    /** 用户名 */
    userName?: string;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
};
export type GetOperationRecordDeatilResponse = {
  /** 任务内容 */
  content?: string;
  /** 操作时间 */
  createdTime?: string;
  /** 车辆唯一标识 */
  deviceId?: number;
  /** 企业id */
  entId?: number;
  /** 用户名 */
  entName?: string;
  /** 编号 */
  id?: number;
  /** 项目区域名称 */
  proAreaName?: string;
  /** 项目名称 */
  proName?: string;
  /** 操作来源，1：app，2：pc,3:远程驾驶,默认0：-- */
  source?: number;
  sourceTxt?: string;
  /** 速度 */
  speed?: number;
  /** 操作事项,车辆开机/车辆关机/任务下发/任务暂停/任务恢复,从1开始，0：-- */
  taskItem?: number;
  taskItemTxt?: string;
  /** 循环次数 */
  executionNumber?: number;
  /** 任务名称和任务类型对应列表 */
  taskNameTypeList?: {
    /** 任务名称 */
    taskName?: string;
    /** 任务类型 */
    taskType?: string;
    [property: string]: any;
  }[];
  /** 操作结果，1表示成功，0表示失败 */
  taskResult?: number;
  taskResultTxt?: string;
  /** 操作类型，1：人为操作，2：系统操作 */
  taskType?: number;
  taskTypeTxt?: string;
  /** 用户账号 */
  userAccount?: string;
  /** 用户id */
  userId?: number;
  /** 用户名 */
  userName?: string;
  [property: string]: any;
};
export type ChangeDriveModeRequest = {
  /** 控制类型，start_emergency_brake:开始急停，stop_emergency_brake:停止急停，start_automatic_mode:进入自动驾驶模式 */
  driveMode: number;
  /** 车牌号 */
  vehNo: string;
  /** 车辆vin */
  vin?: string;
};
export type GPSVo = {
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 点位顺序 */
  orders?: number;
  /** y */
  x?: number;
  /** x */
  y?: number;
};
export type SaveAreaRouteTemplateRequest = {
  /** 项目区域编号 */
  proAreaId?: string;
  /** 模板列表 */
  sysAreaTemplateEntities: {
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 编号 */
    id?: string;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 模板名称 */
    templateName?: string;
    /** 模版路线列表 */
    routeTemplateRelEntities?: {
      /** 清洁类型 */
      cleanType?: string;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 编号 */
      id?: string;
      /** 模板路线排序 */
      orders?: number;
      /** 项目区域编号 */
      routeId?: string;
      /** 路线名称 */
      routeName?: string;
      /** 清扫类型 */
      sweepingType?: string;
      templateId?: string;
      [property: string]: any;
    }[];
    /** 模版区块列表 */
    sysAreaTemplateBlockRelEntities?: {
      /** 项目区域模块编号 */
      blockId?: string;
      /** 区块名称 */
      blockName?: string;
      /** 区块编号 */
      blockNo?: string;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 区块GPS */
      gpsList?: GPSVo[];
      /** 编号 */
      id?: number;
      /** 排序 */
      orders?: number;
      /** 模版id */
      templateId?: string;
      [property: string]: any;
    }[];
    /** 模版自录路线列表 */
    sysAreaTemplateRecordingRouteEntities?: {
      /** 是否清扫 */
      clean?: boolean;
      /** orders */
      orders?: number;
      /** 录制路线id */
      recordingId?: string;
      /** 模版id */
      templateId?: string;
    }[];
    [property: string]: any;
  }[];
  [property: string]: any;
};
export type GetAreaRouteTemplateResponse = {
  /** 创建人id */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 编号 */
  id?: string;
  /** 项目区域编号 */
  proAreaId?: number;
  /** 路线 */
  routeTemplateRelEntities?: {
    cleanType?: string;
    createdBy?: number;
    createdTime?: string;
    gps: {
      /**
       * 纬度
       */
      latitude: string;
      /**
       * 经度
       */
      longitude: string;
      [property: string]: any;
    }[];
    id?: number;
    /**
     * 排序
     */
    orders?: number;
    routeId?: number;
    /**
     * 路线名称
     */
    routeName?: string;
    sweepingType?: string;
    templateId?: number;
    /**
     * 路线id
     */
    vehRouteNo?: string;
    [property: string]: any;
  }[];
  /** 区块 */
  sysAreaTemplateBlockRelEntities?: {
    id: string;
    templateId: string;
    blockId: string;
    createdTime: string;
    createdBy: string;
    orders: number;
    clean: boolean;
    blockName: string;
    blockNo: string;
    gpsList: {
      orders: number;
      longitude: number;
      latitude: number;
      x: number;
      y: number;
    }[];
  }[];
  /** 自录路线 */
  sysAreaTemplateRecordingRouteEntities?: {
    /** 编号 */
    id: string;
    /** 模版id */
    templateId: string;
    /** 项目区域模块编号 */
    recordingId: string;
    /** 创建时间 */
    createdTime: string;
    /** 创建人id */
    createdBy: number;
    /** 排序 */
    orders: number;
    /** 是否清扫 */
    clean: boolean;
    /** 区块名称 */
    routeName: string;
    /** 区块编号 */
    routePath: string;
    recordingRouteEntity: {
      id: string;
      /** 项目区域编号 */
      proAreaId: string;
      /** 区块名称 */
      routeName: string;
      /** 区块编号 */
      routePath: string;
      routeFileUrl: string;
      /** 创建时间 */
      createdTime: string;
      /** 创建人id */
      createdBy: number;
      /** 更新时间 */
      updatedTime: string;
      /** 更新人id */
      updatedBy: number;
      recordingGpsList: {
        /** 编号 */
        id: string;
        /** 区块的编号 */
        recordingId: string;
        /** 经度 */
        longitude: number;
        /** 纬度 */
        latitude: number;
        /** y */
        x: number;
        /** x */
        y: number;
        /** 创建时间 */
        createdTime: string;
        /** 创建人id */
        createdBy: number;
        [x: string]: any;
      }[];
      [x: string]: any;
    };
  }[];
  templateName?: string;
  /** 版本 1|2 */
  version: number;
  [property: string]: any;
  sysRouteTemplatePointsEntities: {
    createdBy?: number;
    createdTime?: string;
    id?: number;
    /**
     * 纬度
     */
    latitude?: number;
    /**
     * 经度
     */
    longitude?: number;
    orders?: number;
    tempId?: number;
    x?: number;
    y?: number;
    [property: string]: any;
  }[];
}[];
export type GetRouteTemplateRequest = {
  vehNo: string;
  [property: string]: any;
};
export type GetRouteTemplateResponse = {
  /** 创建人id */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 编号 */
  id?: string;
  /** 项目区域编号 */
  proAreaId?: number;
  /** 模版名称 */
  templateName?: string;
  /** 模版版本 1:1.0路线; 2:2.0区块; 3:2.0自录路线 4:3.0模版  5结构化路线模版，6结构化点位模版 */
  version?: number;
  routeTemplateRelEntities?: {
    /** 清洁类型 */
    cleanType?: string;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 编号 */
    id?: number;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 路线名称 */
    routeName?: string;
    /** 模板编号 */
    templateId?: number;
    /** 车辆路线编号 */
    vehRouteNo?: number;
    routeId?: string;
  }[];
  sysAreaTemplateBlockRelEntities?: {
    /** 编号 */
    id?: number;
    /** 区块名称 */
    blockName?: string;
    /** 模板编号 */
    templateId?: number;
    blockId: number;
    gpsList: { longitude: number; latitude: number }[];
  }[];
  sysAreaTemplateRecordingRouteEntities?: {
    id?: string;
    templateId?: string;
    recordingId?: string;
    createdTime?: string;
    createdBy?: number;
    orders?: number;
    routeName?: string;
    routePath?: string;
    recordingRouteEntity?: {
      id?: string;
      proAreaId?: string;
      routeName?: string;
      routePath?: string;
      routeFileUrl?: string;
      createdTime?: string;
      createdBy?: number;
      updatedTime?: string;
      updatedBy?: number;
      recordingGpsList?: { longitude: number; latitude: number }[];
    };
  }[];

  sysRouteTemplatePointsEntities: {
    createdBy?: number;
    createdTime?: string;
    id?: number;
    /**
     * 纬度
     */
    latitude?: number;
    /**
     * 经度
     */
    longitude?: number;
    orders?: number;
    tempId?: number;
    x?: number;
    y?: number;
    [property: string]: any;
  };
}[];

export type carCleanReportRequest = {
  // 车辆唯一标识
  deviceId: string;
  // 开始时间
  workStart: string;
  // 结束时间
  workEnd: string;
  // 当前页
  page: number;
  // 页大小
  limit: number;
};

export type GetVehOperationTypeResponse = {
  taskItems?: {
    "0"?: string;
  };
};

export type GetChargingStationListRequest = {
  /** 充电桩编号 */
  chargingStationNo?: string;
  /** 企业名 */
  entName?: string;
  /** 是否绑定 */
  isBinding?: boolean | string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  [property: string]: any;
};

export type GetChargingStationListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 站点编号 */
    areaStationId?: number;
    /** 充电桩编号 */
    chargingStationNo?: string;
    /** 充电桩型号 */
    chargingStationType?: string;
    /** 创建者ID */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 企业编号 */
    entId?: number;
    entName?: string;
    /** 编号 */
    id?: number;
    isBinding?: boolean;
    /** 备注 */
    notes?: string;
    proAreaName?: string;
    proName?: string;
    /** 品牌方 */
    supplier?: string;
    /** 更新者ID */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 额定功率 */
    ratedPower: string;
    /** 序列号 */
    serialNumber: string;
    /** 类型 */
    type: string[];
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};

export type SaveChargingStationInfoRequest = {
  /** 站点编号 */
  areaStationId?: number;
  /** 充电桩编号 */
  chargingStationNo?: string;
  /** 充电桩型号 */
  chargingStationType?: string;
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业编号 */
  entId?: number;
  entName?: string;
  /** 编号 */
  id?: number;
  isBinding?: boolean;
  /** 备注 */
  notes?: string;
  proAreaName?: string;
  proName?: string;
  /** 品牌方 */
  supplier?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  [property: string]: any;
};

export type UpdateChargingStationInfoRequest = {
  /** 站点编号 */
  areaStationId?: number;
  /** 充电桩编号 */
  chargingStationNo?: string;
  /** 充电桩型号 */
  chargingStationType?: string;
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业编号 */
  entId?: number;
  entName?: string;
  /** 编号 */
  id?: number;
  isBinding?: boolean;
  /** 备注 */
  notes?: string;
  proAreaName?: string;
  proName?: string;
  /** 品牌方 */
  supplier?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  [property: string]: any;
};

export type GetChargingStationInfoResponse = {
  /** 站点编号 */
  areaStationId?: number;
  /** 充电桩编号 */
  chargingStationNo?: string;
  /** 充电桩型号 */
  chargingStationType?: string;
  /** 创建者ID */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 企业编号 */
  entId?: number;
  entName?: string;
  /** 编号 */
  id?: number;
  isBinding?: boolean;
  /** 备注 */
  notes?: string;
  proAreaName?: string;
  proName?: string;
  /** 品牌方 */
  supplier?: string;
  /** 更新者ID */
  updatedBy?: number;
  /** 更新时间 */
  updatedTime?: string;
  /** 额定功率 */
  ratedPower: string;
  /** 序列号 */
  serialNumber: string;
  /** 类型 */
  type: string[];
  [property: string]: any;
};
export type GetStatisticsInfoRequest = {
  /** 车辆编号 */
  deviceId: string;
  /** 结束时间 */
  endDate: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 开始时间 */
  startDate: string;
  /** 统计类型，1按照日期，2按照月份，0按照任务 */
  statisticsType: number;
  [property: string]: any;
};
export type GetStatisticsInfoResponse = {
  /** 区域id */
  areaId?: number;
  /** 创建人id */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 车辆唯一标识 */
  deviceId?: string;
  /** 当前页 */
  end?: number;
  /** 清扫面积 */
  finishedWorkArea?: number;
  /** 编号 */
  id?: number;
  /** 页大小 */
  limit?: number;
  /** 垃圾量 */
  litter?: number;
  /** 里程 */
  mileage?: number;
  /** 当前页 */
  page?: number;
  /** 统计类型 */
  statisticsType?: number;
  /** 任务日期 */
  taskDate?: string;
  /** 任务持续时间 */
  taskDuration?: number;
  /** 任务次数 */
  taskTimes?: number;
  /** 用电量 */
  usedElectricityAmount?: number;
  /** 用水量 */
  usedWaterAmount?: number;
  /** 结束时间 */
  workEnd?: string;
  /** 编号 */
  workNo?: number;
  /** 开始时间 */
  workStart?: string;
  areaName?: string;
  projectName?: string;
  [property: string]: any;
};
export type GetStatisticsListRequest = {
  /** 车辆编号 */
  deviceId: string;
  /** 结束时间 */
  endDate: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 开始时间 */
  startDate: string;
  /** 统计类型，1按照日期，2按照月份，0按照任务 */
  statisticsType: number;
  [property: string]: any;
};
export type GetStatisticsListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 区域id */
    areaId?: number;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 车辆唯一标识 */
    deviceId?: string;
    /** 当前页 */
    end?: number;
    /** 清扫面积 */
    finishedWorkArea?: number;
    /** 编号 */
    id?: number;
    /** 页大小 */
    limit?: number;
    /** 垃圾量 */
    litter?: number;
    /** 里程 */
    mileage?: number;
    /** 当前页 */
    page?: number;
    /** 统计类型 */
    statisticsType?: number;
    /** 任务日期 */
    taskDate?: string;
    taskDateText?: string;
    /** 任务持续时间 */
    taskDuration?: number;
    /** 任务次数 */
    taskTimes?: number;
    /** 用电量 */
    usedElectricityAmount?: number;
    /** 用水量 */
    usedWaterAmount?: number;
    /** 结束时间 */
    workEnd?: string;
    /** 编号 */
    workNo?: number;
    /** 开始时间 */
    workStart?: string;
    areaName?: string;
    projectName?: string;
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};
export type GetVehEventListRequest = {
  /** 车牌号 */
  deviceId?: string;
  /** 结束时间 */
  endTime?: string;
  /** 事件类型 */
  eventType?: number;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 开始时间 */
  startTime?: string;
  [property: string]: any;
};
export type GetVehEventListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 区域id */
    areaId?: number;
    /** 摄像头
     * 1./front_center/image_raw：前置摄像头，2./left_center/image_raw:左侧摄像头，3./right_center/image_raw:右侧摄像头，4./back_center/image_raw:后置摄像头 */
    camera?: number;
    cameraTxt?: string;
    /** 创建时间 */
    createdTime?: string;
    /** 事件id */
    eventId?: number;
    /**
     *
     * 事件类型，1，垃圾Garbage,2.坑洞PotHole,3.井盖缺失HoleCoverMiss，4.新垃圾new_garbages，5.已清理掉的垃圾cleaned_garbages，6未清理掉的垃圾missed_garbages
     * ，7.obstacle_avoidance:避障，8.obstacle_stop:停障 */
    eventType?: number;
    eventTypeTxt?: string;
    /** id */
    id?: number;
    /** 时间地址 */
    location?: string;
    /** 车辆信息url */
    picUrl?: string;
    /** 车牌号 */
    vehNo?: string;
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};
export type GetVehEventTypeResponse = {
  eventTypeMap?: {
    "0"?: string;
    [property: string]: any;
  };
  [property: string]: any;
};

export type GetSecurityEventListRequest = {
  dateEnd?: string;
  dateStart?: string;
  deviceNo?: string;
  incidentType?: number;
  /** 页大小 */
  limit: number;
  /** 当前页 */
  page: number;
  [property: string]: any;
};
export type GetSecurityEventListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 区域ID */
    areaId?: number;
    areaName?: string;
    /** 摄像头 */
    camera?: string;
    /** 创建者 */
    createBy?: number;
    /** 创建时间 */
    createTime?: string;
    /** 车牌号 */
    deviceNo?: string;
    id?: number;
    /** 位置 */
    incidentLocation?: string;
    /** 开启状态 */
    incidentStatus?: number;
    /** 类别 */
    incidentType?: number;
    /** 类别描述 */
    incidentTypeText?: string;
    /** 图片地址 */
    picAddress?: string;
    projectName?: string;
    /** 更新者 */
    updateBy?: number;
    /** 更新时间 */
    updateTime?: string;
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};
export type GetEventCaptureListRequest = {
  deviceNo?: string;
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  [property: string]: any;
};
export type GetEventCaptureListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 相机; front: 前置 */
    cameraStand?: string;
    /** 抓拍时间间隔，单位s */
    captureDuration?: number;
    /** 1/0 开启/关闭 */
    captureOperation?: number;
    /** 1:垃圾检测、2:凹坑检测、3:裸图片 */
    captureType?: number;
    contents?: {
      cameraStand: string[];
      /** 抓拍时间间隔，单位s */
      captureDuration?: number;
      /** 1:垃圾检测、2:凹坑检测、3:裸图片 */
      captureType?: number;
      captureTypeText?: string;
      [property: string]: any;
    }[];
    createdBy?: string;
    createdTime?: string;
    /** 车牌号 */
    deviceNo?: string;
    id?: number;
    updateBy?: string;
    updatedTime?: string;
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};
export type SaveEventCaptureConfigRequest = {
  contents?: {
    cameraStand: string[];
    /** 抓拍时间间隔，单位s */
    captureDuration?: number;
    /** 1:垃圾检测、2:凹坑检测、3:裸图片 */
    captureType?: number;
    [property: string]: any;
  }[];
  /** 车牌号 */
  deviceNo?: string;
  [property: string]: any;
};
export type GetEventCaptureInfoRequest = {
  deviceNo?: string;
  [property: string]: any;
};
export type GetEventCaptureInfoResponse = {
  /** 相机; front: 前置 */
  cameraStand?: string;
  /** 抓拍时间间隔，单位s */
  captureDuration?: number;
  /** 1/0 开启/关闭 */
  captureOperation?: number;
  /** 1:垃圾检测、2:凹坑检测、3:裸图片 */
  captureType?: number;
  /** 读取时拿该数据 */
  contents?: {
    cameraStand: string[];
    /** 抓拍时间间隔，单位s */
    captureDuration?: number;
    /** 1:垃圾检测、2:凹坑检测、3:裸图片 */
    captureType?: number;
    captureTypeText?: string;
    [property: string]: any;
  }[];
  createdBy?: string;
  createdTime?: string;
  /** 车牌号 */
  deviceNo?: string;
  id?: number;
  updateBy?: string;
  updatedTime?: string;
  [property: string]: any;
};
export type SetEventCaptureStatusRequest = {
  /** 车牌号 */
  deviceNo?: string;
  /** 1/0 开启/关闭 */
  operation?: number;
  [property: string]: any;
};
export type GetVehTypeByIdRequest = {
  /** 车牌号 */
  deviceNo: string;
  [property: string]: any;
};
export type GetVehTypeByIdResponse = {
  /** 1：三轮车，2：四轮车 */
  vehType: string;
  [property: string]: any;
};
export type ExportStatisticsRequest = {
  /** 车辆编号 */
  deviceId: string;
  /** 结束时间 */
  endDate: string;
  /** 开始时间 */
  startDate: string;
  /** 统计类型，1按照日期，2按照月份，0按照任务 */
  statisticsType: number;
  [property: string]: any;
};
export type ExportCalculateListRequest = {
  // 车辆唯一标识
  deviceId: string;
  // 开始时间
  workStart: string;
  // 结束时间
  workEnd: string;
};

export interface ListWarnDownloadRequest {
  /** 车牌号 */
  deviceId: string;
  /** 条数 */
  limit: number;
  /** value : 1 统计所有的警告，2 已解决的警告 3 未解决的警告 4 实时警告 */
  mode: string;
  /** 页码 */
  page: number;
  warnDateEnd: string;
  warnDateStart: string;
  /** 告警等级 */
  warnLevel: string;
  [property: string]: any;
}
export type GetStaionDetailRequest = {
  /** 区域ID */
  areaId: string;
  /** 站点ID */
  stationId: string;
  [property: string]: any;
};
export type GetStaionDetailResponse = {
  /** 地址 */
  address?: string;
  /** 区域名称 */
  areaName?: string;
  /** 项目名称 */
  projectName?: string;
  /** 车牌号列表 */
  vehNoList?: string[];
  [property: string]: any;
};

export type UpdateStationPicResponse = {
  /** 图片 */
  uploadUrl: string;
  [property: string]: any;
};

export type GetVehicleSetupRequest = {
  /** 自动充电 */
  automaticRecharge?: boolean;
  /** 自动充电电量 */
  automaticRechargeElectricity?: number;
  /** 强制关机 */
  forcedShutdown?: boolean;
  /** 强制关机电量 */
  forcedShutdownElectricity?: number;
  /** 电量提醒 */
  powerReminder?: boolean;
  /** 设置电量提醒的电量阈值 */
  powerReminderElectricity?: number;
  /** 车牌号 */
  vehNo: string;
  [property: string]: any;
};

export type GetVehicleSetupResponse = {
  /** 自动充电 */
  automaticRecharge?: boolean;
  /** 自动充电电量 */
  automaticRechargeElectricity?: number;
  /** 强制关机 */
  forcedShutdown?: boolean;
  /** 强制关机电量 */
  forcedShutdownElectricity?: number;
  /** 电量提醒 */
  powerReminder?: boolean;
  /** 设置电量提醒的电量阈值 */
  powerReminderElectricity?: number;
  /** 车牌号 */
  vehNo: string;
  [property: string]: any;
};

export type SetVehicleSetupRequest = {
  /** 自动充电 */
  automaticRecharge?: boolean;
  /** 自动充电电量 */
  automaticRechargeElectricity?: number;
  /** 强制关机 */
  forcedShutdown?: boolean;
  /** 强制关机电量 */
  forcedShutdownElectricity?: number;
  /** 电量提醒 */
  powerReminder?: boolean;
  /** 设置电量提醒的电量阈值 */
  powerReminderElectricity?: number;
  /** 车牌号 */
  vehNo: string;
  [property: string]: any;
};

export type GetAreaScheduleListRequest = {
  /** 页大小 */
  limit?: number;
  /** 当前页 */
  page?: number;
  /** 项目区域id */
  proAreaId?: string;
  /** 区域名称 */
  proAreaName?: string;
  /** 项目名称 */
  proName?: string;
  /** 项目id */
  proId?: string;
  /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
  scheduleTypes?: number[];
  /** 站点类型，1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
  stationType?: number;
  [property: string]: any;
};
export type GetAreaScheduleListResponse = {
  /** 当前页数 */
  currPage?: number;
  /** 列表数据 */
  list?: {
    /** 项目区域编号 */
    proAreaId?: number;
    /** 区域名称 */
    proAreaName?: string;
    /** 项目名称 */
    proName?: string;
    /** 定时任务列表 */
    scheduleTemplateList?: any[];
    /** 模板数量 */
    tempNumber?: number;
    [property: string]: any;
  }[];
  /** 每页记录数 */
  pageSize?: number;
  /** 总记录数 */
  totalCount?: number;
  [property: string]: any;
};
export type GetTempListByAreaRequest = {
  /** 项目区域id */
  proAreaId?: string;
  /** 站点类型，1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
  stationType?: number;
  [property: string]: any;
};
export type GetTempListByAreaResponse = {
  relId: string;
  relName: string;
  [property: string]: any;
}[];
export type GetStationListByAreaRequest = {
  /** 项目区域id */
  proAreaId?: string;
  /** 站点类型，1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
  stationType?: number;
  [property: string]: any;
};
export type GetStationListByAreaResponse = {
  relId: string;
  relName: string;
  [property: string]: any;
}[];
export type GetScheduleTempDetailRequest = {
  /** 项目区域id */
  proAreaId?: string;
  /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
  scheduleTypes?: number[];
  [property: string]: any;
};
export type GetScheduleTempDetailResponse = {
  /** 项目区域编号 */
  proAreaId?: number;
  /** 区域名称 */
  proAreaName?: string;
  /** 项目名称 */
  proName?: string;
  /** 定时任务列表 */
  scheduleTemplateList: {
    /** 模版id */
    proAreaId?: string;
    /** 任务列表 */
    scheduleList: {
      /** 洒水0关1开 */
      blowWater?: number;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 任务类型，0:每天，1:星期1.。7:周天， */
      executeDays?: string[];
      /** 任务类型，0:每天，1:星期1.。7:周天， */
      executeDaysTxt?: string[];
      /** 执行结束时间 */
      executeTimeEnd?: string;
      /** 执行开始时间 */
      executeTimeStart?: string;
      /** 风机档位，1,2,3档 */
      fan?: number;
      /** 主键 */
      id?: string;
      /** 关联id */
      relId?: string;
      /** 站点类型，1：垃圾点，2：充电点，3：加水点，4：停车点，5：补给点 */
      stationType?: number;
      /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
      scheduleType?: number;
      /** 当前车速 */
      speed?: number;
      /** 任务执行cron表达式 */
      taskCron?: string;
      /** 0覆盖清扫，1识别清扫 */
      taskType?: number;
      /** 更新人 */
      updatedBy?: number;
      /** 更新时间 */
      updatedTime?: string;
      [property: string]: any;
    }[];
    /** 模版id */
    tempId?: number;
    /** 模版名称 */
    tempName: string;
    [property: string]: any;
  }[];
  /** 模板数量 */
  tempNumber?: number;
  [property: string]: any;
};
export type UpdateScheduleTemplateRequest = {
  /** 项目区域编号 */
  proAreaId?: string;
  /** 区域名称 */
  proAreaName?: string;
  /** 项目名称 */
  proName?: string;
  /** 定时任务列表 */
  scheduleTemplateList?: {
    /** 模版id */
    proAreaId?: string;
    /** 任务列表 */
    scheduleList?: {
      /** 洒水0关1开 */
      blowWater?: number;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 任务类型，0:每天，1:星期1.。7:周天， */
      executeDays?: string[];
      /** 任务类型，0:每天，1:星期1.。7:周天， */
      executeDaysTxt?: string[];
      /** 执行结束时间 */
      executeTimeEnd?: string;
      /** 执行开始时间 */
      executeTimeStart?: string;
      /** 风机档位，1,2,3档 */
      fan?: number;
      /** 主键 */
      id?: number;
      /** 关联id */
      relId?: number;
      /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
      scheduleType?: number;
      /** 当前车速 */
      speed?: number;
      /** 任务执行cron表达式 */
      taskCron?: string;
      /** 0覆盖清扫，1识别清扫 */
      taskType?: number;
      /** 更新人 */
      updatedBy?: number;
      /** 更新时间 */
      updatedTime?: string;
      [property: string]: any;
    }[];
    /** 模版id */
    tempId?: number;
    /** 模版名称 */
    tempName?: string;
    [property: string]: any;
  }[];
  /** 模板数量 */
  tempNumber?: number;
  [property: string]: any;
};
export type AddScheduleTemplateRequest = {
  /** 模版id */
  proAreaId?: string;
  /** 任务列表 */
  scheduleList?: {
    /** 洒水0关1开 */
    blowWater?: number;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 任务类型，0:每天，1:星期1.。7:周天， */
    executeDays?: string[];
    /** 任务类型，0:每天，1:星期1.。7:周天， */
    executeDaysTxt?: string[];
    /** 执行结束时间 */
    executeTimeEnd?: string;
    /** 执行开始时间 */
    executeTimeStart?: string;
    /** 风机档位，1,2,3档 */
    fan?: number;
    /** 主键 */
    id?: number;
    /** 关联id */
    relId?: string;
    /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
    scheduleType?: number;
    /** 当前车速 */
    speed?: number;
    /** 任务执行cron表达式 */
    taskCron?: string;
    /** 0覆盖清扫，1识别清扫 */
    taskType?: number;
    /** 更新人 */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    [property: string]: any;
  }[];
  /** 模版id */
  tempId?: number;
  /** 模版名称 */
  tempName?: string;
  [property: string]: any;
};
export type SaveCarScheduleRequest = {
  /** 排版数据 */
  arrangement?: {
    /** 洒水0关1开 */
    blowWater?: number;
    /** 任务类型，0:每天，1:星期1.。7:周天， */
    executeDays?: string[];
    /** 执行结束时间 格式为 00:00 */
    executeTimeEnd?: string;
    /** 执行开始时间 格式为 00:00 */
    executeTimeStart?: string;
    /** 0,关，1开,3 low:低速，4 medium:中速，5 high:高速,2退出控制 */
    fan?: number;
    /** 项目区域编号 */
    proAreaId?: string;
    /** 关联id */
    relId?: number;
    /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
    scheduleType?: number;
    /** 当前车速 */
    speed?: number;
    /** 洒水0未同步1已同步 */
    status?: number;
    /** 排班开关0关1开 */
    switchOn?: number;
    /** 任务执行cron表达式 */
    taskCron?: string;
    /** 0覆盖清扫，1识别清扫 */
    taskType?: number;
    /** 车牌号码 */
    vehicleNo?: string;
    [property: string]: any;
  }[];
  /** 车牌号 */
  vehicleId?: string;
  [property: string]: any;
};
export type GetCurrentScheduleRequest = {
  /** 项目车辆ID */
  id: string;
  [property: string]: any;
};
export type GetCurrentScheduleResponse = {
  scheduleList: {
    /** 洒水0关1开 */
    blowWater?: number;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 任务类型，0:每天，1:星期1.。7:周天， */
    executeDays?: string[];
    /** 执行结束时间 格式为 00:00 */
    executeTimeEnd?: string;
    /** 执行开始时间 格式为 00:00 */
    executeTimeStart?: string;
    /** 0,关，1开,3 low:低速，4 medium:中速，5 high:高速,2退出控制 */
    fan?: number;
    /** 主键 */
    id?: number;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 关联id */
    relId?: number;
    /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
    scheduleType?: number;
    /** 当前车速 */
    speed?: number;
    /** 洒水0未同步1已同步 */
    status?: number;
    /** 排班开关0关1开 */
    switchOn?: number;
    /** 任务执行cron表达式 */
    taskCron?: string;
    /** 0覆盖清扫，1识别清扫 */
    taskType?: number;
    /** 更新人 */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 车牌号码 */
    vehicleNo?: string;
    [property: string]: any;
  }[];
  [property: string]: any;
};
export type GetScheduleDetailRequest = {
  /** 项目车辆ID */
  id: string;
  /** 车辆id */
  vehicleId: string;
  [property: string]: any;
};
export type GetScheduleDetailResponse = {
  /** 排版操作记录 */
  arrangement?: {
    /** 洒水0关1开 */
    blowWater?: number;
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 任务类型，0:每天，1:星期1.。7:周天， */
    executeDays?: string[];
    /** 执行结束时间 格式为 00:00 */
    executeTimeEnd?: string;
    /** 执行开始时间 格式为 00:00 */
    executeTimeStart?: string;
    /** 0,关，1开,3 low:低速，4 medium:中速，5 high:高速,2退出控制 */
    fan?: number;
    /** 主键 */
    id?: number;
    /** scheduleType为2,3时，关联的停车位id */
    parkNoList?: string[];
    /** scheduleType为2,3时，关联的停车路线id */
    parkWayId?: string;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 关联id */
    relId?: number;
    /** scheduleType为1时，关联的模板信息 */
    routeInfo?: {
      /** 清洁类型 */
      cleanType?: string;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 编号 */
      id?: number;
      /** 模板路线排序 */
      orders?: number;
      /** 项目区域编号 */
      routeId?: number;
      /** 路线名称 */
      routeName?: string;
      /** 清扫类型 */
      sweepingType?: string;
      /** 模板编号 */
      templateId?: number;
      vehRouteNo?: string;
      [property: string]: any;
    }[];
    /** 1：定时任务，2：定时倒垃圾，3：定时充电，4,：定时开关机 */
    scheduleType?: number;
    /** 当前车速 */
    speed?: number;
    /** 站点名称 */
    stationName?: string;
    /** 洒水0未同步1已同步 */
    status?: number;
    /** 排班开关0关1开 */
    switchOn?: number;
    /** 任务执行cron表达式 */
    taskCron?: string;
    /** 0覆盖清扫，1识别清扫 */
    taskType?: number;
    /** scheduleType为1时，关联的模板信息 */
    tempName?: string;
    /** 更新人 */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    /** 车牌号码 */
    vehicleNo?: string;
    [property: string]: any;
  }[];
  /** 云端同步时间 */
  cloudSynchroniseTime?: string;
  /** 是否同步 */
  synchroniseStatus?: boolean;
  /** 车辆信息 */
  vehicle?: {
    /** 区域名称 */
    areaName?: string;
    /** 车辆型号 */
    carModel?: string;
    /** 车辆类型 */
    carType?: string;
    /** 车辆VIN码 */
    carVinNo?: string;
    id?: number;
    /** 车辆信息url */
    instrUrl?: {
      /** 创建者ID */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 文件名称 */
      fileName?: string;
      /** id */
      id?: number;
      /** 车辆信息url */
      instrUrl?: string;
      /** 状态  0：正常   1：删除 */
      isDelete?: number;
      /** 车牌号码 */
      vehicleId?: number;
      [property: string]: any;
    };

    /** 投放时间 */
    launchTime?: string;
    /** 项目名称 */
    proName?: string;
    /** 车辆sim卡号 */
    simNo?: string;
    /** 车牌负责人 */
    userName?: string;
    /** 负责人号码 */
    userPhoneNo?: string;
    /** 车牌号码 */
    vehicleNo?: string;
    [property: string]: any;
  };
  /** 车端同步时间 */
  vehicleSynchroniseTime?: string;
  [property: string]: any;
};
export type CheckTempNameRequest = {
  /** 模版id */
  proAreaId?: string;
  /** 模版id */
  tempId?: number;
  /** 模版名称 */
  tempName?: string;
  [property: string]: any;
};
export type GetProCalculateListRequest = {
  /** 区域,紧急需求新增 */
  areaId: number;
  /** 日期类型1日，2周，3月 */
  dateType?: number;
  /** 车牌号 */
  deviceId?: string;
  /** 企业,紧急需求新增 */
  entId: number;
  /** 页大小 */
  limit: number;
  /** 当前页 */
  page: number;
  /** 项目,紧急需求新增 */
  proId: number;
  /** true项目，false区域 */
  proOrArea: boolean;
  /** vin,紧急需求新增 */
  vin?: string;
  /** 周数 */
  weekNo?: number;
  /** 结束时间 */
  workEnd?: string;
  /** 开始时间 */
  workStart?: string;
  [property: string]: any;
};
export type ExportProCalculateListRequest = {
  /** 区域,紧急需求新增 */
  areaId: number;
  /** 日期类型1日，2周，3月 */
  dateType?: number;
  /** 车牌号 */
  deviceId?: string;
  /** 企业,紧急需求新增 */
  entId: number;
  /** 页大小 */
  limit: number;
  /** 当前页 */
  page: number;
  /** 项目,紧急需求新增 */
  proId: number;
  /** true项目，false区域 */
  proOrArea: boolean;
  /** vin,紧急需求新增 */
  vin?: string;
  /** 周数 */
  weekNo?: number;
  /** 结束时间 */
  workEnd?: string;
  /** 开始时间 */
  workStart?: string;
  [property: string]: any;
};

export type ScreenCarWorkRequest = {
  proId: string;
  workStart: string;
  workEnd: string;
};
export type QueryAreaRecordingRouteResponse = {
  /** 创建人id */
  createdBy: number;
  /** 创建时间 */
  createdTime: string;
  id: string;
  /** 项目区域编号 */
  proAreaId: number;
  routeFileUrl: string;
  /** 区块名称 */
  routeName: string;
  /** 区块编号 */
  routePath: string;
  /** 更新人id */
  updatedBy: number;
  /** 更新时间 */
  updatedTime: string;
}[];
export type QueryVehRecordingRouteData = {
  createdBy?: number;
  createdTime?: string;
  id?: number;
  proAreaId?: number;
  recordingGpsList?: {
    createdBy?: number;
    createdTime?: string;
    id?: number;
    latitude?: number;
    longitude?: number;
    recordingId?: number;
    x?: number;
    y?: number;
    [property: string]: any;
  }[];
  routeFileUrl?: string;
  routeName?: string;
  routePath?: string;
  updatedBy?: number;
  updatedTime?: string;
}[];
export type GetProAreaResponse = {
  /** 区域区块 */
  areaBlockDtoList: {
    /** 区块点位 */
    areaBlockGpsList?: {
      /** 纬度 */
      latitude: number;
      /** 经度 */
      longitude: number;
      /** 点位顺序 */
      orders: number;
      /** y */
      x: number;
      /** x */
      y: number;
      [property: string]: any;
    }[];
    /** 区块名称 */
    blockName: string;
    /** 区块编号 */
    blockNo?: string;
    drawMode?: string;
    id: string;
    /** 项目区域编号 */
    proAreaId?: number;
    [property: string]: any;
  }[];
  /** 区域围栏 */
  areaFence: {
    /** gps数据 */
    gps: {
      /** 纬度 */
      latitude: number;
      /** 经度 */
      longitude: number;
      /** 点位顺序 */
      orders: number;
      [property: string]: any;
    }[];
    /** 项目区域编号 */
    proAreaId: number;
    [property: string]: any;
  };
  /** 区域路线 */
  areaRoute: {
    cleanType?: string;
    /** 清扫类型数组 */
    cleanTypeArrays?: string[];
    cleanTypeList?: {
      key?: string;
      value?: string;
      [property: string]: any;
    }[];
    /** gps数据 */
    gps?: {
      /** 纬度 */
      latitude: number;
      /** 经度 */
      longitude: number;
      /** 点位顺序 */
      orders: number;
      [property: string]: any;
    }[];
    Gps?: {
      latitude: number;
      longitude: number;
      orders: number;
      [property: string]: any;
    }[];
    id: string;
    order?: number;
    /** 项目区域编号 */
    proAreaId?: number;
    /** 路线名称 */
    routeName: string;
    vehNoList?: string[];
    /** 车辆路线编号 */
    vehRouteNo?: string;
    [property: string]: any;
  }[];
  /** 区域站点 */
  areaStation: {
    /** 站点x点 */
    areaX?: string;
    /** 站点y点 */
    areaY?: string;
    /** 站点z点 */
    areaZ?: string;
    cleanType?: string;
    cleanTypeArrays?: string[];
    cleanTypeList?: {
      key?: string;
      value?: string;
      [property: string]: any;
    }[];
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    /** 编号 */
    id: string;
    /** 纬度 */
    latitude: number;
    /** 经度 */
    longitude: number;
    parkId: string;
    /** 项目区域编号 */
    proAreaId: string;
    /** 站点名称 */
    stationName: string;
    /** 是否为避雨车位 */
    rainShelter?: boolean;
    /** 站点绑定的车辆列表 */
    stationVehRelEntityList: {
      /** 站点id */
      areaStationId: string;
      /** 车辆id */
      vehicleId: string;
    }[];
    /** 车位列表 */
    stationParkingRelEntityList?: {
      /** 项目站点编号 */
      areaStationId: number;
      chargingId?: number;
      chargingStationNo?: string;
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 编号 */
      id: string;
      location?: string;
      /** 停车位名称 */
      parkName?: string;
      /** 停车位编号 */
      parkNo?: string;
      serialNumber?: string;
      [property: string]: any;
    }[];
    /** 站点图片 */
    stationPic?: string;
    /** 站点类型，0：垃圾点，1：充电点，2：加水点3：停车点 */
    stationType: number;
    stationTypeText: string;
    /** 路线id */
    wayId: string;
    /** yaw角 */
    yaw?: string;
    [property: string]: any;
  }[];
  /** 区域信息 */
  proAreaEntity: {
    /** 区域名称 */
    areaName: string;
    /** 区域编号 */
    areaSeq: number;
    /** 区域类型，0：园区，1：街道 */
    areaType: number;
    areaTypeName: string;
    /** 创建者ID */
    createdBy: number;
    /** 创建时间 */
    createdTime: string;
    /** 企业Id */
    entId: string;
    entName: string;
    /** id */
    id: string;
    /** 纬度 */
    latOrigin: number;
    /** 经度 */
    lonOrigin: number;
    /** 项目id */
    proId: string;
    /** 项目名称 */
    proName: string;
    /** 项目编号 */
    proSeq: number;
    /** 更新者ID */
    updatedBy: number;
    /** 更新时间 */
    updatedTime: string;
    [property: string]: any;
  };
  /** 自录路线 */
  recordingRouteEntities: {
    /** 创建人id */
    createdBy?: number;
    /** 创建时间 */
    createdTime?: string;
    id: string;
    /** md5 */
    md5: string;
    /** 路线类型，false业务类true辅助类 */
    routeType: boolean;
    /** 开关，false关true开 */
    routeStatus: boolean;
    /** 项目区域编号 */
    proAreaId?: number;
    recordingGpsList?: {
      /** 创建人id */
      createdBy?: number;
      /** 创建时间 */
      createdTime?: string;
      /** 编号 */
      id: string;

      /** 纬度 */
      latitude: number;
      /** 经度 */
      longitude: number;
      /** 路线的编号 */
      recordingId?: number;
      /** y */
      x: number;
      /** x */
      y: number;
    }[];
    routeFileUrl?: string;
    /** 路线名称 */
    routeName: string;
    /** 车端路径 */
    routePath: string;
    /** 更新人id */
    updatedBy?: number;
    /** 更新时间 */
    updatedTime?: string;
    [property: string]: any;
  }[];
  [property: string]: any;
};
export type LiveCarVideoInfoResponse = {
  /** 年审日期 */
  annualReviewData: number;
  /** 品牌 */
  brand: string;
  /** 购买地址 */
  buyAddress: string;
  /** 购买日期 */
  buyDate: number;
  /** 限载人数 */
  capacity: number;
  /** 车辆编码 */
  carId: number;
  /** 车辆牌号 */
  carNo: string;
  /** 车辆类型编码 */
  carTypeId: number;
  /** 车辆类型名称 */
  carTypeName: string;
  /** 证件照 */
  certificatePic: string;
  /** 颜色 */
  colour: string;
  /** 创建时间 */
  createTime: number;
  /** 保管人 */
  custodian: string;
  /** 设备号 */
  deviceNo: string;
  /** 排量（升） */
  displacement: number;
  /** 司机编码 */
  driverId: number;
  /** 司机姓名 */
  driverName: string;
  /** 司机电话 */
  driverTel: string;
  /** 发动机号 */
  engineNo: string;
  /** 档案编号 */
  fileNo: string;
  /** 组编码 */
  groupId: number;
  /** 组名称 */
  groupName: string;
  /** 车辆识别码 */
  identityNo: string;
  /** 里程系数 */
  mileageRatio: number;
  /** 车主 */
  owner: string;
  /** 配件（1-摄像头，2-油针，3-温感，4-门磁正转，5-门磁反转） */
  parts: number[];
  /** 图片 */
  pic: string;
  /** 登记日期 */
  registerDate: number;
  /** 备注 */
  remarks: string;
  /** SIM卡号 */
  simNo: string;
  /** 状态（0-有效，1-无效） */
  status: string;
  /** 更新时间 */
  updateTime: number;
  /** 使用状态（1-正常，2-维修，3-报废 */
  useStatus: string;
  [property: string]: any;
};
export type QueryVehRealTimeInfoByVehNoResponse = {
  /** 区域名称 */
  areaId: number;
  /** 区域名称 */
  areaName: string;
  /** dvrId */
  dvrId: string;
  /** 企业名称 */
  entId: number;
  /** 企业名称 */
  entName: string;
  /** 是否在线 */
  online: boolean;
  /** 项目名称 */
  proId: number;
  /** 项目名称 */
  proName: string;
  /** 车牌号 */
  vehNo: string;
  /** vin */
  vin: string;
};
export type CameraSnapAndUploadTempResponse = {
  /** 桶名 */
  bucket?: string;
  /** 摄像头 */
  camera?: string;
  /** 创建时间 */
  createdTime?: string;
  /** 车辆唯一标识 */
  deviceId?: string;
  /** 图片地址 */
  fileName?: string;
  /** 组 */
  groupId?: string;
  /** id */
  id?: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 图片地址 */
  picUrl?: string;
  /** 状态 */
  snapStatus?: number;
  /** 创建人id */
  updatedTime?: string;
};
export type UserAreaStationInfo = {
  /** 站点x点 */
  areaX?: string;
  /** 站点y点 */
  areaY?: string;
  /** 站点z点 */
  areaZ?: string;
  /** 创建人id */
  createdBy?: number;
  /** 创建时间 */
  createdTime?: string;
  /** 编号 */
  id: string;
  /** 纬度 */
  latitude?: number;
  /** 经度 */
  longitude?: number;
  /** 停车id */
  parkId?: string;
  /** 项目区域编号 */
  proAreaId?: string;
  /** 站点名称 */
  stationName?: string;
  /** 站点类型，0：垃圾点，1：充电点，2：加水点3：停车点 */
  stationType: number;
  stationTypeText?: string;
  /** 站点图片 */
  stationPic?: string;
  /** 站点的车位列表 */
  stationParkingRelEntityList?: {
    id: string;
    areaStationId?: string;
    /** 车位编号 */
    parkNo?: string;
    /** 车位名称 */
    parkName?: string;
    createdTime?: string;
    chargingId?: string;
    chargingStationNo?: string;
    serialNumber?: string;
  }[];
  /** yaw角 */
  yaw?: string;
};

export type GetVehicleAlarmListRequest = {
  /**
   * 车牌号
   */
  deviceId: string;
  /**
   * 故障吗
   */
  faultCode: string;
  /**
   * 是否结束0未结束,1已结束
   */
  isEnd: boolean;
  /**
   * 是否处置0未解决,1已解决
   */
  isSolved: number;
  /**
   * 条数
   */
  limit: number;
  /**
   * value : 1 统计所有的警告，2 已解决的警告 3 未解决的警告 4 实时警告
   */
  mode: string;
  /**
   * 页码
   */
  page: number;
  warnDateEnd: string;
  warnDateStart: string;
  /**
   * 告警等级
   */
  warnLevel: string;
  /**
   * 告警等级
   */
  warnLevelList: number[];
  /**
   * 告警等级多选
   */
  warnLevels: string;
  [property: string]: any;
};

export type GetVehicleAlarmListResponse = {
  carType: string;
  createdTime: string;
  deviceId: string;
  faultAddress: null | string;
  /**
   * 故障码
   */
  faultCode: string;
  faultContent: string;
  faultType: number;
  id: string;
  /**
   * 是否结束0未结束，1已结束
   */
  isEnd: number;
  isSolved: number;
  /**
   * 图片
   */
  picUrl: string;
  solveTime: null | string;
  /**
   * vin
   */
  vin: string;
  warnLevel: number | null;
  [property: string]: any;
};

// getVehicleAlarmDetail

export type GetVehicleAlarmDetailResponse = {
  /**
   * 区域名称
   */
  areaName: string;
  /**
   * 车辆类型
   */
  carType: string;
  /**
   * 公司
   */
  company: string;
  /**
   * 创建者ID
   */
  createdBy?: number;
  /**
   * 创建时间
   */
  createdTime?: string;
  /**
   * 问题描述
   */
  description: string;
  /**
   * 车牌号
   */
  deviceId: string;
  /**
   * 结束时间
   */
  endTime: string;
  /**
   * 故障地址
   */
  faultAddress: string;
  /**
   * 故障码
   */
  faultCode: string;
  faultContent?: string;
  faultType?: number;
  /**
   * 主键
   */
  id: string;
  /**
   * 是否结束0未结束，1已结束
   */
  isEnd: number;
  /**
   * 是否已读
   */
  isNoticed?: number;
  /**
   * 是否解决0未解决,1已解决
   */
  isSolved: number;
  /**
   * 制造商
   */
  manufacturer?: string;
  /**
   * 型号名称
   */
  moduleName: string;
  /**
   * 图片
   */
  picUrl: string;
  /**
   * 所属项目
   */
  project: string;
  /**
   * 警告记录
   */
  records: Record<string, any>[];
  /**
   * 解决时间
   */
  solveTime: string;
  /**
   * 更新者ID
   */
  updatedBy?: number;
  /**
   * 更新时间
   */
  updatedTime?: string;
  /**
   * vin
   */
  vin: string;
  /**
   * 故障等级
   */
  warnLevel: number;
  [property: string]: any;
  /**
   * 抓拍结果
   */
  vehSnapRecordList: [
    {
      /**
       * 摄像头
       */
      camera?: string;
      /**
       * 创建时间
       */
      createdTime?: string;
      /**
       * 车辆唯一标识
       */
      deviceId?: string;
      /**
       * 文件名
       */
      fileName?: string;
      /**
       * 组id
       */
      groupId?: number;
      /**
       * id
       */
      id?: number;
      /**
       * 纬度
       */
      latitude?: number;
      /**
       * 经度
       */
      longitude?: number;
      /**
       * 原图图片地址
       */
      originalPicUrl?: string;
      /**
       * 图片类型，0，抓拍垃圾，1红绿灯
       */
      picType?: number;
      /**
       * 图片地址
       */
      picUrl?: string;
      /**
       * 复核结果 0：不通过,1：通过
       */
      recognizeResult?: number;
      /**
       * 状态 0抓取中,1已上传
       */
      snapStatus?: number;
      /**
       * 更新人
       */
      updatedBy?: number;
      /**
       * 更新人
       */
      updatedByName?: string;
      /**
       * 创建人id
       */
      updatedTime?: string;
      [property: string]: any;
    }
  ];
  /**
   * 工单复核
   */
  vehicleWorkOrderList: [
    {
      /**
       * 区域id
       */
      areaId?: number;
      areaName?: string;
      camera?: number;
      cameraTxt?: string;
      /**
       * 创建时间
       */
      createdTime?: string;
      /**
       * 事件id
       */
      eventId?: number;
      eventType?: number;
      eventTypeTxt?: string;
      /**
       * 文件名
       */
      fileName?: string;
      /**
       * 是否需要复核 0：否,1：是
       */
      garbageReview?: number;
      /**
       * id
       */
      id?: number;
      /**
       * 扫净结果 0：否,1：是
       */
      isClean?: number;
      /**
       * 工单是否已经提交 0：否,1：是
       */
      isSubmit?: number;
      /**
       * 纬度
       */
      latitude?: number;
      /**
       * 时间地址
       */
      location?: string;
      /**
       * 经度
       */
      longitude?: number;
      /**
       * 原图图片地址
       */
      originalPicUrl?: string;
      /**
       * 车辆信息url
       */
      picUrl?: string;
      projectName?: string;
      /**
       * 识别结果 0：否,1：是
       */
      recognizeResult?: number;
      /**
       * 更新人
       */
      updatedBy?: number;
      /**
       * 更新人
       */
      updatedByName?: string;
      /**
       * 更新时间
       */
      updatedTime?: string;
      /**
       * 车牌号
       */
      vehNo?: string;
      [property: string]: any;
    }
  ];
};

export type GetVehicleTaskDetailResponse = {
  /**
   * 区域id
   */
  areaId: number;
  /**
   * 创建人id
   */
  createdBy: number;
  /**
   * 创建时间
   */
  createdTime: string;
  /**
   * 车辆唯一标识
   */
  deviceId: string;
  /**
   * 清扫面积
   */
  finishedWorkArea: number;
  /**
   * 编号
   */
  id: number;
  /**
   * 垃圾量
   */
  litter: number;
  /**
   * 里程
   */
  mileage: number;
  /**
   * 统计类型
   */
  statisticsType: number;
  sweepType: number;
  /**
   * 完成度
   */
  taskCompletionPercentage: number;
  /**
   * 任务日期
   */
  taskDate: string;
  taskDateText: string;
  /**
   * 任务持续时间
   */
  taskDuration: number;
  /**
   * 任务次数
   */
  taskTimes: number;
  /**
   * 任务类型：    TASK(0, "任务"),     GARBAGE(1, "垃圾点"),     CHARGING(2, "充电点"),     WATER(3,
   * "加水点"),     PARKING(4, "停车点"),     SUPPLYING(5, "补给点");
   */
  taskWorkType: number;
  /**
   * 任务名称
   */
  tempName: string;
  /**
   * 用电量
   */
  usedElectricityAmount: number;
  /**
   * 用水量
   */
  usedWaterAmount: number;
  /**
   * 操作人员
   */
  userName: string;
  vin: string;
  /**
   * 结束时间
   */
  workEnd: string;
  /**
   * 编号
   */
  workNo: number;
  /**
   * 开始时间
   */
  workStart: string;
  /**
   * 任务状态0进行中，1已完成
   */
  workStatus: number;
  [property: string]: any;
};

export type GetSmartOrderListResponse = {
  /**
   * 区域id
   */
  areaId?: number;
  /**
   * 区域名称
   */
  areaName?: null;
  /**
   * 摄像头
   * 1./front_center/image_raw：前置摄像头，2./left_center/image_raw:左侧摄像头，3./right_center/image_raw:右侧摄像头，4./back_center/image_raw:后置摄像头
   */
  camera?: number;
  /**
   * 摄像头类型反显
   */
  cameraTxt?: null;
  /**
   * 创建时间
   */
  createdTime?: string;
  /**
   * 事件id
   */
  eventId?: number;
  /**
   *
   * 事件类型，1，垃圾Garbage,2.坑洞PotHole,3.井盖缺失HoleCoverMiss，4.新垃圾new_garbages，5.已清理掉的垃圾cleaned_garbages，6未清理掉的垃圾missed_garbages
   * ，7.obstacle_avoidance:避障，8.obstacle_stop:停障
   */
  eventType?: number;
  /**
   * 事件类型反显
   */
  eventTypeTxt?: null;
  /**
   * id
   */
  id?: string;
  /**
   * 扫净结果 0：否,1：是
   */
  isClean?: number;
  /**
   * 时间地址
   */
  location?: string;
  /**
   * 车辆信息url，图片地址
   */
  picUrl?: string;
  /**
   * 项目名称
   */
  projectName?: null;
  /**
   * 识别结果 0：否,1：是
   */
  recognizeResult?: number;
  /**
   * 车牌号
   */
  vehNo?: string;
  [property: string]: any;
};

export type GetRedLightListResponse = {
  /**
   * 桶名
   */
  bucket: null;
  /**
   * 摄像头
   */
  camera: string;
  captureDetailInfoList: null;
  /**
   * 创建时间
   */
  createdTime: string;
  /**
   * 车辆唯一标识
   */
  deviceId: string;
  /**
   * 文件名
   */
  fileName: string;
  /**
   * 组id
   */
  groupId: string;
  /**
   * id
   */
  id: string;
  /**
   * 纬度
   */
  latitude: number;
  /**
   * 经度
   */
  longitude: number;
  /**
   * 图片类型，0，抓拍垃圾，1红绿灯
   */
  picType: number;
  /**
   * 图片地址
   */
  picUrl: string;
  /**
   * 状态
   */
  snapStatus: number;
  /**
   * 更新时间
   */
  updatedTime: string;
  /**
   * 警告类型
   */
  warningType: null;
  [property: string]: any;
  /**
   * 识别结果
   */
  recognizeResult: number;
  /**
   * 发送id
   */
  sendId: string;
};

export type QueryCustomAreaResponse = {
  /**
   * 区域编号
   */
  areaId?: string;
  createTime?: string;
  /**
   * 组id
   */
  groupId?: number;
  /**
   * id
   */
  id?: number;
  latitude?: number;
  longitude?: number;
  /**
   * 名称
   */
  name?: string;
  /**
   * 点类型
   */
  pointType?: number;
  x?: number;
  y?: number;
  z?: number;
  [property: string]: any;
};

export type TrafficLightExamineParams = {
  /** 数据上传时间 */
  dataTime: string;
  /** 0：禁止通过 1：允许通过 ,2:未校验  3，强制通行*/
  navigationAdvice: number;
  /** 图片地址 */
  picUrl: string;
  /** 车牌号 */
  vehNo: string;
  /** 发送id */
  snapId: string;
  /** 红绿灯id */
  trafficLightId: string;
};

export type GetVehicleAdvancedConfigResponse = {
  /**
   * 充电中自动续扫-结束时间
   */
  automaticContinuingEndTime: string;
  /**
   * 充电中自动续扫 0：关闭 1：开启
   */
  automaticContinuingScan: number;
  /**
   * 充电中自动续扫-开始时间
   */
  automaticContinuingStartTime: string;
  /**
   * 自动倒垃圾阈值
   */
  automaticDumping: number;
  /**
   * 自动倾倒开关 0：关闭 1：开启
   */
  automaticDumpingSwitch: number;
  /**
   * 自动充电开关 0：关闭 1：开启
   */
  automaticRechargeSwitch: number;
  /**
   * 自动加水阈值
   */
  automaticWater: number;
  /**
   * 自动加水开关 0：关闭 1：开启
   */
  automaticWaterSwitch: number;
  /**
   * 工单单车事件上报 0：关闭 1：开启
   */
  bikeReport: number;
  /**
   * 工单单车事件复核 0：关闭 1：开启
   */
  bikeReview: number;
  /**
   * 蜂鸣器响声开关 0：关闭 1：开启
   */
  buzzer: number;
  /**
   * 全覆盖路线重叠间距 范围 0.15~0.2
   */
  ccoverPathOverlapInterval: number;
  /**
   * 管控开关 0：关闭 1：开启
   */
  charge: number;
  /**
   * 管控-结束时间
   */
  chargeEndTime: string;
  /**
   * 管控-开始时间
   */
  chargeStartTime: string;
  /**
   * 碰撞开关 0：关闭 1：开启
   */
  collision: number;
  /**
   * 车辆行驶习惯 1：靠右 2：靠左
   */
  drivingHabit?: number;
  /**
   * 工单垃圾上报 0：关闭 1：开启
   */
  garbageReport: number;
  /**
   * 工单垃圾复核 0：关闭 1：开启
   */
  garbageReview: number;
  /**
   * id
   */
  id: string;
  /**
   * 空闲调度 0:返航充电位 1:就近停车
   */
  idleScheduling: number;
  /**
   * 空闲调度开关 0：关闭 1：开启
   */
  idleSchedulingSwitch: number;
  /**
   * 巡拣清扫机构 0：关闭 1：开启
   */
  institution: number;
  /**
   * 左边刷碰撞传感器 0：关闭 1：开启
   */
  leftCollisionSensor: number;
  /**
   * 低电强制回充
   */
  lowPowerForcedRecharge: number;
  /**
   * 低电关机保护
   */
  lowPowerOffProtection: number;
  /**
   * 管线开关 0：关闭 1：开启
   */
  pipeline: number;
  /**
   * 管线识别 0：关闭 1：开启
   */
  pipelineIdentification: number;
  /**
   * 市政抓拍点位坐标数组
   */
  points: string;
  /**
   * 远控弹框设置 0：关闭 1：开启
   */
  remoteControlFrame: number;
  /**
   * 右边刷碰撞传感器 0：关闭 1：开启
   */
  rightCollisionSensor: number;
  /**
   * 急停开关 0：关闭 1：开启
   */
  scram: number;
  /**
   * 智驾保护开关 0：关闭 1：开启
   */
  smartDrivingProtect: number;
  /**
   * 是否开启市政抓拍 0：关闭 1：开启
   */
  snapEnable: number;
  /**
   * 市政抓拍间隔（单位：秒）
   */
  snapInterval: number;
  /**
   * 充电执行任务电量值
   */
  taskChargePower: number;
  /**
   * 红绿灯弹框设置 0：关闭 1：开启
   */
  trafficLight: number;
  /**
   * 红绿灯提示 默认50
   */
  trafficLightIndication: number;
  /**
   * 工单车辆调度 0：关闭 1：开启
   */
  vehicleScheduling: number;
  /**
   * 车牌号
   */
  vehNo: string;
  [property: string]: any;
};

export type SetVehicleAdvancedConfigParams = {
  /**
   * 工单单车事件上报 0：关闭 1：开启
   */
  bikeReport: number;
  /**
   * 工单单车事件复核 0：关闭 1：开启
   */
  bikeReview: number;
  /**
   * 蜂鸣器响声开关 0：关闭 1：开启
   */
  buzzer: number;
  /**
   * 车辆行驶习惯 1：靠右 2：靠左
   */
  drivingHabit: number;
  /**
   * 工单垃圾上报 0：关闭 1：开启
   */
  garbageReport: number;
  /**
   * 工单垃圾复核 0：关闭 1：开启
   */
  garbageReview: number;
  /**
   * id
   */
  id: number;
  /**
   * 巡拣清扫机构 0：关闭 1：开启
   */
  institution: number;
  /**
   * 左边刷碰撞传感器 0：关闭 1：开启
   */
  leftCollisionSensor: number;
  /**
   * 低电强制回充
   */
  lowPowerForcedRecharge: number;
  /**
   * 低电关机保护
   */
  lowPowerOffProtection: number;
  /**
   * 管线识别 0：关闭 1：开启
   */
  pipelineIdentification: number;
  /**
   * 远控弹框设置 0：关闭 1：开启
   */
  remoteControlFrame: number;
  /**
   * 右边刷碰撞传感器 0：关闭 1：开启
   */
  rightCollisionSensor: number;
  /**
   * 充电执行任务电量值
   */
  taskChargePower: number;
  /**
   * 红绿灯弹框设置 0：关闭 1：开启
   */
  trafficLight: number;
  /**
   * 工单车辆调度 0：关闭 1：开启
   */
  vehicleScheduling: number;
  [property: string]: any;
  /**
   * 红绿灯提示 默认50

   */
  trafficLightIndication: number;
  /**
   * 智能驾驶保护 0：关闭 1：开启
   */
  smartDrivingProtect: number;
  /**
   * 管控-开始时间
   */
  chargeStartTime: string;
  /**
   * 管控-结束时间
   */
  chargeEndTime: string;
};

export type GetVehPreviewInformationResponse = {
  /**
   * 车辆唯一标识
   */
  deviceId: string;
  /**
   * 方向 1=直行 2=左转 3=右转
   */
  direction: number;
  /**
   * 距离
   */
  distance: number;
  /**
   * 预计时间
   */
  estimatedTime: string;
  /**
   * 主键
   */
  id: string;
  /**
   * 剩余时间
   */
  remainingTime: string;
  /**
   * 序号
   */
  serialNumber: string;
  /**
   * 是否为即将到达的节点 0：否 1：是，是否为即将到达的节点 0：否 1：是
   */
  step: number;
  /**
   * 交通灯id
   */
  trafficId: null;
  /**
   * 红绿灯类型 1=人行道 2=机动车道
   */
  trafficLightType: number;
  [property: string]: any;
};

export type GetTrafficLightListResponse = {
  /**
   * 创建人id
   */
  createdBy?: number;
  /**
   * 创建时间
   */
  createdTime?: string;
  /**
   * 编号
   */
  id?: number;
  /**
   * 经度
   */
  lightId?: string;
  location?: string;
  locationOrigin?: string;
  /**
   * 项目区域编号
   */
  proAreaId?: number;
  /**
   * 纬度
   */
  trafficName?: string;
  passTime: string;
  [property: string]: any;
};

export type uavRouteListResponse = {
  /**
   * 任务ID
   * @type {string}
   */
  missionId: string;

  /**
   * 航线ID
   * @type {string}
   */
  missionLineType: string;

  /**
   * 航线类型；0：航点航线；1：跳点航线；2：建图航线
   * @type {0 | 1 | 2}
   */
  platformId: 0 | 1 | 2;

  /**
   * 关联机场ID
   * @type {string}
   */
  platformName: string;

  /**
   * 关联机场名
   * @type {string}
   */
  plateformHight: string;

  /**
   * 机场模型海拔
   * @type {number}
   */
  nodeId: number;

  /**
   * UAVM服务ID
   * @type {string}
   */
  plateformLongitudeDeg: string;

  /**
   * 机场经度
   * @type {number}
   */
  plateformLatitudeDeg: number;

  /**
   * 机场纬度
   * @type {number}
   */
  droneId: number;

  /**
   * 最近一次执飞该航线的无人机ID
   * @type {string}
   */
  droneName: string;

  /**
   * 最近一次执飞该航线的无人机
   * @type {string}
   */
  systemId: string;

  /**
   * 系统ID
   * @type {string}
   */
  custId: string;

  /**
   * 租户ID
   * @type {string}
   */
  custName: string;

  /**
   * 客户名
   * @type {string}
   */
  missionType: string;

  /**
   * 航线类型
   * @type {string}
   */
  missionName: string;

  /**
   * 航线名
   * @type {string}
   */
  flyArea: string;

  /**
   * 预计巡飞面积
   * @type {number}
   */
  flyAreaShape: number;

  /**
   * 预计采集区域形状
   * @type {string}
   */
  lineShape: string;

  /**
   * 采集线条形状
   * @type {string}
   */
  collectPhotos: string;

  /**
   * 预计建图照片数
   * @type {number}
   */
  collectGimbalModel: number;

  /**
   * 挂载型号 (e.g., DC-03)
   * @type {string}
   */
  chooseShot: string;

  /**
   * 选择镜头 (0: 广角, 1: 红外, 2: 可见光等镜头)
   * @type {0 | 1 | 2}
   */
  collectType: 0 | 1 | 2;

  /**
   * 采集方式
   * @type {string}
   */
  Gsd: string;

  /**
   * GSD (Ground Sample Distance)
   * @type {number}
   */
  collectTakeOffHight: number;

  /**
   * 起飞高度
   * @type {number}
   */
  collectPhotoHight: number;

  /**
   * 建图高度
   * @type {number}
   */
  collectSpeed: number;

  /**
   * 采集速度
   * @type {number}
   */
  collectSideOverlapRate: number;

  /**
   * 航向重叠率
   * @type {number}
   */
  collectFlightOverlapRate: number;

  /**
   * 航向重叠率
   * @type {number}
   */
  collectMargins: number;

  /**
   * 边距
   * @type {number}
   */
  collectFlightAngle: number;

  /**
   * 航线角度
   * @type {number}
   */
  photoMode: number;

  /**
   * 拍照模式 (0: 等距间隔拍照; 1: 等时间隔拍照)
   * @type {0 | 1}
   */
  signalLostDeal: 0 | 1;

  /**
   * 信号丢失 (0: 继续执行; 1: 断联系返航)
   * @type {0 | 1}
   */
  flyKilometer: number;

  /**
   * 航线规划飞行距离
   * @type {number}
   */
  executeTime: number;

  /**
   * 预计飞行时长
   * @type {number}
   */
  hightType: number;

  /**
   * 0：相对起飞点；1：相对海拔
   * @type {0 | 1}
   */
  operateMode: 0 | 1 | 3;

  /**
   * 0: 手动, 1: 自动 (监控点自动触发), 3: 定时自动巡航
   * @type {0 | 1 | 3}
   */
  aiSwitch: boolean;

  /**
   * AI识别开关
   * @type {boolean}
   */
  aiTypeId: string;

  /**
   * AI模型能力
   * @type {string}
   */
  remark: string;

  /**
   * 备注
   * @type {string}
   */
};
export type MunicipalFacilitiesListResponse = {
  /**
   * 区域id
   */
  areaId?: number;
  /**
   * id
   */
  id?: string;
  /**
   * 点位名称
   */
  pointName?: string;
  /**
   * 车牌号
   */
  reportDeviceId?: string;
  /**
   * x
   */
  x?: number;
  /**
   * y
   */
  y?: number;
  /**
   * 偏转角
   */
  yaw?: number;
  /**
   * z
   */
  z?: number;
  [property: string]: any;
};
