import { formatRoundNum, thousandSeparator, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("common");

export const zoomConfig = [
  {
    type: "省级地名",
    zoom: 9,
  },
  {
    type: "地市级地名",
    zoom: 11,
  },
  {
    type: "区县级地名",
    zoom: 13,
  },
];

export const autoView = [60, 200, 60, 290];
export const mapZoom = 22;

export const offlineText = $t("turnedOff");

export const offlineSvg = "map_car_offline";

export const trackKeyPointStyle = {
  fillStyle: "#5964FB",
  strokeStyle: "#fff",
};
export const trackStartPointStyle = {
  radius: 0,
};
export const trackEndPointStyle = {
  radius: 0,
};
export const trackPathLineStyle = {
  strokeStyle: "rgba(16, 22, 55, 0.3)",
};
export const trackPathLineHoverStyle = {
  strokeStyle: "#5964FB",
};
export const trackPathLinePassedStyle = {
  lineWidth: 4,
  strokeStyle: "#5964FB",
};
export const topZIndex = 200;

export const markerStyle = {
  zIndex: 20,
  anchor: "bottom-center",
  bubble: true,
};

export const siteMarkerHoverMarkerStyle = {
  zIndex: 40,
  anchor: "center",
  offset: [4, -54],
  bubble: true,
};

export const polylineHoverMarkerStyle = {
  zIndex: 40,
  anchor: "bottom-center",
  bubble: true,
};
export const polylineMarkerStyle = {
  zIndex: 30,
  anchor: "bottom-center",
  bubble: true,
};
export const polylineStyle = {
  zIndex: 30,
  strokeOpacity: 1,
  strokeColor: "#242859",
  strokeWeight: 4,
  showDir: true,
};
export const polylineCarLiveStyle = {
  zIndex: topZIndex,
  borderWeight: 0,
  fillOpacity: 1,
  strokeOpacity: 1,
  strokeStyle: "solid" as any,
  strokeWeight: 8,
  strokeColor: "#5964FB",
  showDir: true,
};
export const polylineTemplateStyle = {
  zIndex: topZIndex,
  borderWeight: 0,
  fillOpacity: 1,
  strokeOpacity: 1,
  strokeStyle: "solid" as any,
  strokeWeight: 4,
  strokeColor: "#5557d5",
  showDir: false,
};
export const polylineEditorStyle = {
  createOptions: {
    // 非编辑状态线样式
    ...polylineStyle,
  },
  editOptions: {
    // 编辑状态线样式
    ...polylineCarLiveStyle,
  },
  midControlPoint: {
    // 小点位样式
    zIndex: topZIndex,
    fillOpacity: 1,
    fillColor: "#fff",
    strokeWeight: 1,
    strokeColor: "#5964FB",
    bubble: true,
    cursor: "pointer",
  },
  controlPoint: {
    // 大点位样式
    zIndex: topZIndex,
    fillColor: "#5964FB",
    fillOpacity: 1,
    strokeWeight: 1,
    strokeColor: "#fff",
    bubble: true,
    cursor: "pointer",
  },
};

export const polygonStyle = {
  strokeWeight: 0,
  fillOpacity: 0.2,
  fillColor: "#5964FB",
  zIndex: 10,
  bubble: true,
};

export const polygonEditorStyle = {
  createOptions: {
    // 非编辑状态线样式
    ...polygonStyle,
  },
  editOptions: {
    // 编辑状态线样式
    zIndex: 10,
    borderWeight: 0,
    fillOpacity: 0.2,
    fillColor: "#5964FB",
    // strokeOpacity: 0.2,
    strokeStyle: "dashed" as any,
    strokeWeight: 3,
    strokeColor: "#5964FB",
  },
  midControlPoint: {
    // 小点位样式
    zIndex: 10,
    fillOpacity: 1,
    fillColor: "#fff",
    strokeWeight: 2,
    strokeColor: "#5964FB",
    bubble: true,
    cursor: "pointer",
  } as AMap.CircleMarkerOptions,
  controlPoint: {
    // 大点位样式
    zIndex: 10,
    fillColor: "#5964FB",
    fillOpacity: 1,
    strokeWeight: 2,
    strokeColor: "#fff",
    bubble: true,
    cursor: "pointer",
    radius: 7,
  } as AMap.CircleMarkerOptions,
};

export const blockPolygonEditorStyle = {
  ...polygonEditorStyle,
  createOptions: {
    strokeWeight: 0,
    fillOpacity: 0.7,
    fillColor: "#9F9FA4",
    zIndex: 10,
    bubble: true,
  },
  editOptions: {
    // 编辑状态线样式
    zIndex: 11,
    borderWeight: 1,
    fillOpacity: 0.4,
    fillColor: "#5964FB",
    // strokeOpacity: 0.2,
    strokeStyle: "dashed" as any,
    strokeWeight: 3,
    strokeColor: "#5964FB",
  },
};

export const workStatusMap = [
  {
    text: $t("nowork"),
    icon: "map_nowork",
    color: "#5964FB",
  },
  {
    text: $t("working"),
    icon: "map_work",
    color: "#5964FB",
  },
  {
    text: $t("unknown"),
    icon: "map_question",
    color: "#999",
  },
  {
    text: $t("charging"),
    icon: "map_charge",
    color: "#28E09F",
  },
];

export const driveModeMap = {
  standby: {
    text: $t("standby"),
    icon: "map_drive_standby",
    color: "#5964FB",
  },
  interrupted: {
    text: $t("break"),
    icon: "map_drive_manual",
    color: "#5964FB",
  },
  cloud: {
    text: $t("remoteDriving"),
    icon: "map_drive_manual",
    color: "#5964FB",
  },
  cloud_control: {
    text: $t("remoteDriving"),
    icon: "map_drive_manual",
    color: "#5964FB",
  },
  manual: {
    text: $t("manualDriving"),
    icon: "map_drive_manual",
    color: "#5964FB",
  },
  automatic: {
    text: $t("autoDriving"),
    icon: "map_drive_auto",
    color: "#5964FB",
  },
  auto_control: {
    text: $t("autoDriving"),
    icon: "map_drive_auto",
    color: "#5964FB",
  },
  remote: {
    text: $t("controlDriving"),
    icon: "map_drive_remote",
    color: "#5964FB",
  },
  remote_control: {
    text: $t("controlDriving"),
    icon: "map_drive_remote",
    color: "#5964FB",
  },
  emergency_braking: {
    text: $t("emergencyStop"),
    icon: "map_drive_break",
    color: "#E24562",
  },
  soft_emergency: {
    text: $t("emergencyStop"),
    icon: "map_drive_break",
    color: "#E24562",
  },
  hard_emergency: {
    text: $t("emergencyStop"),
    icon: "map_drive_break",
    color: "#E24562",
  },
  null: {
    text: $t("unknown"),
    icon: "map_question",
    color: "#999",
  },
  undefined: {
    text: $t("unknown"),
    icon: "map_question",
    color: "#999",
  },
  other: {
    text: $t("unknown"),
    icon: "map_question",
    color: "#999",
  },
};

export const alarmTypeMap = [
  {
    text: $t("full"),
    tip: $t("full"),
    value: "0",
    icon: "alarm_all",
  },
  {
    text: $t("waterNumber"),
    tip: $t("waterShortage"),
    value: "1",
    icon: "alarm_water",
  },
  {
    text: $t("electricNumber"),
    tip: $t("electricShortage"),
    value: "2",
    icon: "alarm_battery",
  },
  {
    text: $t("garbageCan"),
    tip: $t("dumpGarbage"),
    value: "3",
    icon: "alarm_garbage",
  },
  {
    text: $t("task"),
    tip: $t("task"),
    value: "4",
    icon: "alarm_task",
  },
  {
    text: $t("event"),
    tip: $t("event"),
    value: "5",
    icon: "alarm_event",
  },
];

export const taskTypeMap = [
  { text: "待机", tip: "", value: 0 },
  { text: "正常泊车", tip: "去停车任务", value: 1 },
  { text: "充电类型", tip: "去充电任务", value: 2 },
  { text: "垃圾触发类型", tip: "去倒垃圾任务", value: 3 },
  { text: "水箱触发类型", tip: "去加水任务", value: 4 },
  { text: "任务中", tip: "清扫任务", value: 5 },
  { text: "任务规划中", tip: "任务规划中", value: 6 },
  { text: "任务中", tip: "车辆正在进行泊车中", value: 7 },
  { text: "任务中", tip: "车辆正在进行充电对接中", value: 8 },
  { text: "任务中", tip: "车辆正在进行加水中", value: 9 },
  { text: "任务中", tip: "车辆正在进行垃圾倾倒中", value: 10 },
];

export const carCaptureTypeOpts = [
  { label: "垃圾满溢", value: 1 },
  { label: "明显垃圾", value: 2 },
  { label: "共享单车不规范停放", value: 3 },
  { label: "乱堆放", value: 4 },
  { label: "道路脏污、污染", value: 5 },
  { label: "城市家具不整洁", value: 6 },
  { label: "违规占道占绿经营", value: 7 },
  { label: "黄土裸露", value: 8 },
  { label: "乱张贴乱涂写乱刻画", value: 9 },
  { label: "城市家具不完好", value: 10 },
  { label: "行道树缺株", value: 11 },
  { label: "沿街外立面围护设施不完好", value: 12 },
  { label: "户外广告招牌设置未维护", value: 13 },
  { label: "乱挂晒", value: 14 },
  { label: "不文明养犬", value: 15 },
  { label: "道路病害", value: 16 },
  { label: "违规停车", value: 17 },
  { label: "垃圾桶未盖", value: 18 },
  { label: "占道经营", value: 19 },
];

// 速度speed Number formatRoundNum
// 电量electric Number formatRoundNum
// 水箱水量tank Number formatRoundNum
// 垃圾箱量litter Number formatRoundNum
// 电池温度temperatureMax Number formatRoundNum
// 电池电流current Number formatRoundNum
// 电池电压voltage Number formatRoundNum
// 当前里程currentMileage Number formatRoundNum
// 总里程totalMileage Number formatRoundNum
// 后端没传的时候就显示'--'
const resetValues = [undefined, null];
export const undefinedOrNullToZero = (value: any) => (resetValues.includes(value) ? 0 : value);
export const showBatteryWarn = (number: any) => number <= 20;
export const showWaterWarn = (number: any) => number <= 40;
export const showGarbageWarn = (number: any) => number >= 80;
export const parseSpeed = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 1));
export const parseBattery = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 0));
export const parseWater = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 0));
export const parseGarbage = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 0));
export const parseTemperature = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 1));
export const parseCurrent = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 1));
export const parseVoltage = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 1));
export const parseTotalMileage = (data: any) =>
  resetValues.includes(data) ? "--" : thousandSeparator(Number(formatRoundNum(data, 1)));
export const parseCurrentMileage = (data: any) => (resetValues.includes(data) ? "--" : formatRoundNum(data, 1));
export const parseCleaningArea = (data: any) => formatRoundNum(data, 0);
export const parseTirePressure = (data: any) => formatRoundNum(data, 1);
export const parseTireTemperature = (data: any) => formatRoundNum(data, 1);
