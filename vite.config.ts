/// <reference types="vitest" />

import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "vite";
import vue from "@vitejs/plugin-vue";
import vueJsx from "@vitejs/plugin-vue-jsx";
// @ts-ignore
import { svgLoader } from "./src/plugins/SvgPlugin.ts";
// @ts-ignore
import { sourceMapDelete } from './src/plugins/SourceMapPlugin.js'
import { sentryVitePlugin } from "@sentry/vite-plugin";
import { visualizer } from 'rollup-plugin-visualizer';
import viteCompression from 'vite-plugin-compression';
// import basicSsl from '@vitejs/plugin-basic-ssl'
import AutoImport from 'unplugin-auto-import/vite';
import Components from 'unplugin-vue-components/vite';
import { ElementPlusResolver } from 'unplugin-vue-components/resolvers';

// https://vitejs.dev/config/
export default defineConfig(async ({ command, mode, ssrBuild })=>{
  console.log(`命令${command} 模式${mode} SSR构建${ssrBuild}`);
  const env = loadEnv(mode, process.cwd(), '')

  return {
    test: {
      root: 'src/',
      environment: 'jsdom',
      environmentOptions: {},
      snapshotFormat: {
        escapeString: false,
      },
      coverage: {
        provider: 'c8', // c8  istanbul
      },
    },
    root: './', // 项目根目录
    base: '/', // 公共基础路径
    publicDir: 'public', // 静态资源服务的文件夹
    cacheDir: 'node_modules/.vite', // 存储缓存文件的目录
    plugins: [
      // basicSsl(),
      ...[
        vue(),
        vueJsx(),
        svgLoader("./src/assets/icons/"),
        viteCompression({
          ext: '.gz',
          threshold: 1025,
          deleteOriginFile: false, // 由于小于1025b的文件不会生成.gz，所以源文件需要保留
        }),
        AutoImport({
          resolvers: [ElementPlusResolver()],
        }),
        Components({
          resolvers: [ElementPlusResolver()],
        }),
      ],
      ...(mode === 'development' ?
        [
          visualizer({
            gzipSize: true,
            brotliSize: true,
            emitFile: false,
            filename: 'visualizer.html',
          })
        ]
        : []
      ),
      ...(
        mode === env.VITE_SENTRY_SOURCE_MAP ?
        [
          sentryVitePlugin({
            release: {
              name: env.VITE_SENTRY_RELEASE
            },
            org: "sentry",
            project: "cloud-platform-frontend",
            authToken: env.VITE_SENTRY_AUTH_TOKEN,
            // telemetry: false,
            url: env.VITE_SENTRY_URL,
          }),
          sourceMapDelete()
        ]
        : []
      )
    ],
    resolve: {
      alias: { // @rollup/plugin-alias的entries选项
        "@": fileURLToPath(new URL("./src", import.meta.url)),
        // axios: 'https://cdn.jsdelivr.net/npm/axios@1.2.1/+esm', // https://ainyi.com/119
      },
      extensions: ['.mjs', '.js', '.ts', '.jsx', '.tsx', '.json'] // 导入想省略的扩展名列表
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: '@import "@/assets/css/variables.scss";',
          api: 'modern-compiler'
        },
      },
      devSourcemap: true,
    },
    server: {
      open: false,
      host: true,
      port: 5174,
      // https: true,
      strictPort: false, // 设为true时，若port端口被占用则直接退出
      proxy: {
        '/dev': {
          target: env.VITE_APP_PROXY_URL, // ***************
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/dev/, ''),
        },
        '/test': {
          target: 'http://***********:9020', // ************
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/test/, ''),
        },
        '/prod': {
          target: 'http://**********:9020', // ************
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/prod/, ''),
        },
        '/mini': {
          target: 'http://*************:9020',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/mini/, ''),
        },
        '/nodejs': {
          target: 'http://127.0.0.1:3000',
          changeOrigin: true,
          rewrite: (path) => path.replace(/^\/nodejs/, ''),
        },
      },
      headers: {},
      fs: {
        strict: true, // 限制root路径以外的文件访问
        deny: ['.env', '.env.*', '*.{pem,crt}'], // 限制 Vite 开发服务器提供敏感文件的黑名单
      },
    },
    build: {
      target: 'modules', // 最终构建的浏览器兼容目标
      modulePreload: true, // 默认情况下，一个模块预加载polyfill会被自动注入。
      outDir: 'dist', // 输入路径
      copyPublicDir: true, // 将publicDir目录中的所有文件复制到outDir目录
      assetsDir: 'assets', // 生成静态资源的存放路径
      assetsInlineLimit: 4096, // 小于此大小使用内联为base64编码，减少http请求
      chunkSizeWarningLimit: 500, // 触发警告的chunk大小(kbs)
      cssCodeSplit: true, // CSS代码拆分
      reportCompressedSize: true, // gzip 压缩大小报告
      sourcemap: mode === env.VITE_SENTRY_SOURCE_MAP, // 是否生成source map文件
      minify: 'esbuild', // 最小化混淆器
      // 自定义底层的Rollup打包配置
      rollupOptions: {
        output: {
          chunkFileNames: 'js/[name]-[hash].js', // 非入口文件
          entryFileNames: 'js/[name]-[hash].js', // 入口文件
          assetFileNames: '[ext]/[name]-[hash].[ext]', // 非js文件
          manualChunks(id){
            if (id.includes('node_modules')) {
              const moduleName = id.toString().split('node_modules/')[1].split('/')[1].toString()
              const firstModuleName = moduleName.split('@')[0] || moduleName.split('@')[1]
              const needFusion = ['protobufjs','sentry'] // 模块在.pnpm里目录太多的进行合并
              const needFusionTarget = needFusion.find(value=>firstModuleName.includes(value))
              return 'module-'+(needFusionTarget||firstModuleName);
            }
          }
        }
      },
    },
    esbuild: {
      drop: mode === 'production' ? ['console', 'debugger'] : [],
    },
    preview: {
      open: true,
      host: 'localhost',
      port: 4173,
    },
    optimizeDeps: {}, // 依赖优化选项
    ssr: {
      target: 'node', // SSR构建目标
      format: 'esm', // SSR的构建语法格式
    },
    worker: {
      format: 'iife', // 打包worker时的输出类型
      plugins: [], // 用于打包worker的Vite插件
      rollupOptions: {}, // 用于打包worker的Rollup配置项
    }
  }
});


