<template>
  <section
    class="site-marker"
    ref="siteMarkerInfoWindowRef"
  >
    <span
      class="site-marker-close"
      v-html="closeText"
      @mousedown="mousedownHandle"
    ></span>
    <div class="site-marker-top">
      <transition
        name="site-marker"
        mode="out-in"
      >
        <!-- 主页 -->
        <template v-if="!activePanelIndex">
          <div class="panel-info">
            <div class="panel-info-image">
              <img
                :src="image.url"
                alt=""
                @click="props.stationPic && (image.show = true)"
                class="image"
              />
            </div>
            <div class="panel-info-content">
              <div class="panel-info-content-top">
                <div class="panel-info-content-top-left">
                  {{ props.name || $t("unknown") }}
                </div>
                <div class="panel-info-content-top-right">
                  <div v-if="props.type === 'map_battery'">
                    <div class="detail-btn">
                      <div class="detail-btn-left">
                        <span style="color: #5964fb">{{ idleCount }}</span
                        >/{{ props.stationList?.length || 0 }}
                      </div>
                      <div
                        class="detail-btn-right"
                        @click="activePanelIndex = 1"
                      >
                        {{ $t("detail") }}
                      </div>
                    </div>
                  </div>
                  <div v-else>{{ props.stationList?.length || 0 }}个{{ siteConfig.stationText || $t("location") }}</div>
                </div>
              </div>
              <div class="panel-info-content-center">{{ info.projectName }}-{{ info.areaName }}</div>
              <div class="panel-info-content-bottom">
                <div class="panel-info-content-bottom-icon">
                  <x-icon name="locate_gray" />
                </div>
                <span class="text">{{ info.address }}</span>
              </div>
            </div>
          </div>
        </template>
        <!-- 详情 -->
        <template v-else>
          <div class="panel-detail">
            <div class="panel-detail-title">
              {{ $t("selectChargingPosition") }}
            </div>
            <div class="panel-detail-content">
              <div
                v-for="(item, index) in detailInfo.chargingList"
                :key="index"
                class="panel-detail-content-item"
                :class="{ disabled: item.status !== 'idle' || item.warn }"
                @click="item.status === 'idle' && !item.warn && (detailInfo.activeIndex = index)"
              >
                <x-icon
                  :name="getStationIcon(index)"
                  class="panel-detail-content-item-icon"
                />
                <div class="panel-detail-content-item-inner">
                  <div v-if="item.status !== 'idle'">
                    <img
                      src="@/assets/images/map_site_charging_car.png"
                      alt=""
                      class="inner-image"
                    />
                  </div>
                  <div v-else>
                    <div
                      class="inner-text"
                      :class="{ active: detailInfo.activeIndex === index }"
                    >
                      {{ item.parkName || "--" }}
                    </div>
                  </div>
                  <div v-if="(item.warn && item.status === 'idle') || item.warn">
                    <div class="inner-warning-tip">{{ $t("fault") }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </transition>
    </div>
    <div class="site-marker-bottom">
      <x-select
        v-model:value="deviceId"
        :options="formOptions.deviceOptions"
        :popupContainer="props.popupContainer"
        style="width: 160px; margin-right: 5px"
      />
      <x-button
        :disabled="!deviceId"
        :text="siteConfig.operaText || $t('opera')"
        :icon="deviceId ? `${props.type}_btn` : `${props.type}_btn_disabled`"
        @click="toStation"
      />
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from "vue";
import type { PropType } from "vue";
import { sitesType } from "@/assets/ts/config";
import xIcon from "@/components/x-icon.vue";
import xImage from "@/components/x-image.vue";
import xButton from "@/components/x-button.vue";
import XSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import { useMainStore } from "@/stores/main";
import { task } from "@/services/wsapi";
import { getStaionDetail } from "@/services/api";
import { closeChargingStatus } from "@/services/wsapi";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  type: {
    // 垃圾点/充电点/加水点/停车点
    type: String as PropType<"map_garbage" | "map_battery" | "map_water" | "map_park">,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  areaId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  stationPic: {
    type: String,
    required: true,
  },
  stationType: {
    type: Number,
    required: true,
  },
  stationList: {
    type: Array,
    required: true,
  },
});

const siteMarkerInfoWindowRef = ref<any>();

const closeText = ref("&#10005");

const { socket } = useMainStore();

const siteConfig = sitesType.find((site) => site.value === props.stationType);

// 当前选中面板
const activePanelIndex = ref(0); // 0:主页 1:详情

// 关闭站点信息窗体
const mousedownHandle = () => {
  props.window.close();
  closeChargingStatus();
};

/**
 * 场所图片
 */
const image = reactive({
  show: false,
  url: props.stationPic || new URL("@/assets/images/map_station_bg.png", import.meta.url).href,
});

/**
 * 基础信息
 */
const info = reactive({
  projectName: $t("unknown"),
  areaName: $t("unknown"),
  address: $t("unknown"),
});

// 空闲充电桩数量
const idleCount = ref(0);

onMounted(() => {
  activePanelIndex.value = 0;
  detailInfo.activeIndex = 0;
  // 获取附加信息
  getStationBaseInfo();
});

const getStationBaseInfo = async () => {
  const { projectName, areaName, address, vehNoList } = await getStaionDetail({
    areaId: props.areaId,
    stationId: props.id,
  });
  info.projectName = projectName;
  info.areaName = areaName;
  info.address = address;
  formOptions.deviceOptions = vehNoList.map((item) => ({
    label: item,
    value: item,
  }));
};

watch(
  () => socket.chargingStationStatus,
  (newV) => {
    if (newV) {
      idleCount.value = newV.filter((item) => item.status === "idle").length - 1 || 0;

      detailInfo.chargingList.forEach((item) => {
        const matchedMessage = newV.find((msg) => msg.deviceId === item.chargingStationNo);
        if (matchedMessage) {
          item.status = matchedMessage.status;
          item.warn = matchedMessage.warn;
        }
      });
    }
  }
);

/**
 * 车位详情
 */
const detailInfo = reactive({
  chargingList: [
    {
      id: "0",
      parkName: $t("auto"),
      status: "idle",
      warn: false,
    },
    ...props.stationList,
  ],
  // 当前选中充电桩
  activeIndex: 0,
});

// 车位围栏样式
const getStationIcon = (index: number) => {
  const selectedStation = detailInfo.chargingList[index] as any;
  if (selectedStation.warn) {
    return "charging_station_warning";
  } else if (selectedStation.status !== "idle") {
    return "charging_station_disabled";
  } else if (index === detailInfo.activeIndex) {
    return "charging_station_checked";
  } else {
    return "charging_station";
  }
};

/**
 * 设置任务
 */
const deviceId = ref("");
const defaultSpeed = 0.6;
const defaultExecuteTimes = 1;

const formOptions = reactive({
  deviceOptions: [] as any[],
});

const toStation = async () => {
  // 站点编号#任务类型(#泊车位id)
  let param = `${props.id}#${siteConfig.paramText}`;
  if (detailInfo.activeIndex) {
    const parkNo = detailInfo.chargingList[detailInfo.activeIndex].parkNo;
    param += `#${parkNo}`;
  }
  new Promise(() => {
    task("ecar", deviceId.value, defaultSpeed, defaultExecuteTimes, param).then(() => {
      Message("success", $t("taskSet"));
    });
  });
};
</script>

<style lang="scss" scoped>
.site-marker {
  overflow: hidden;
  padding: 6px 7px 7px 6px;
  @include wh(278px, 275px);
  @include bis("@/assets/images/map_site_marker_info_window.png");
  &-enter {
    &-from {
      opacity: 0.3;
      transform: translateX(3%);
    }
    &-active {
      transition: all 0.3s ease-out;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease-out;
    }
    &-to {
      opacity: 0.3;
      transform: translateX(-3%);
    }
  }
  &-close {
    z-index: 6;
    display: block;
    @include ct-f;
    @include wh(16px);
    @include sc(16px, rgb(85, 85, 85));
    position: absolute;
    top: 18px;
    right: 18px;
    cursor: pointer;
  }
  &-top {
    @include wh(100%, 205px);
    .panel-info {
      &-image {
        display: flex;
        @include wh(100%, 114px);
        .image {
          width: 100%;
          border-radius: 4px 4px 0 0;
          object-fit: cover;
        }
      }
      &-content {
        padding: 5px 10px;
        &-top {
          @include ct-f(y) {
            justify-content: space-between;
          }
          font-weight: 700;
          &-left {
            @include sc(14px, #383838);
          }
          &-right {
            @include sc(12px, blue);
            .detail-btn {
              display: flex;
              border: 1px solid #5964fb;
              font-weight: 400;
              border-radius: 2px;
              &-left {
                @include wh(43px, 23px);
                @include sc(12px, #9f9fa4);
                @include ct-f;
              }
              &-right {
                @include wh(33px, 23px);
                @include sc(12px, #fff);
                background: #5964fb;
                @include ct-f;
                cursor: pointer;
                &:hover {
                  background-color: rgb(125, 134, 253);
                }
                &:active {
                  background-color: rgb(58, 67, 203);
                }
              }
            }
          }
        }
        &-center {
          margin-bottom: 10px;
          @include sc(12px, #9f9fa4);
        }
        &-bottom {
          display: flex;
          margin-top: 4px;
          width: 100%;
          max-height: 33px;
          overflow: hidden;
          &-icon {
            padding-right: 5px;
          }
          .text {
            @include sc(12px, #383838);
            @include ells(2);
          }
        }
      }
    }
    .panel-detail {
      padding: 5px 10px;
      &-title {
        margin: 10px 5px;
        @include sc(14px, #333333);
      }
      &-content {
        flex-wrap: wrap;
        max-height: 153px;
        overflow-y: auto;
        @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
        @include ct-f(y);
        &-item {
          position: relative;
          @include wh(30px, 46px);
          margin: 0 5px 5px 5px;
          cursor: pointer;
          &.disabled {
            cursor: not-allowed;
          }
          &-icon {
            @include wh(30px, 46px);
          }
          &-inner {
            .inner-text {
              @include ct-p;
              width: 30px;
              text-align: center;
              @include sc(12px, #555555);
              &.active {
                color: #5964fb;
              }
            }
            .inner-image {
              @include ct-p;
              width: 18px;
              text-align: center;
            }
            .inner-warning-tip {
              position: absolute;
              top: 5px;
              width: 30px;
              text-align: center;
              @include sc(11px, #f9852ecc);
            }
          }
        }
      }
    }
  }
  &-bottom {
    padding: 5px;
    @include ct-f(y) {
      justify-content: space-between;
    }
  }
}
</style>
