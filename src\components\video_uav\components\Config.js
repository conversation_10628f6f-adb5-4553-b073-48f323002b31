let Config = {
  wsUrl: "wss://drone.godouav.com/rtc1/ws", //视频连接ws 测试地址ws://dev.godouav.com/rtc1/ws 公网地址：wss://drone.godouav.com/rtc1/ws
  accessToken: "",
  nodeId: "", 
  cameraId: "", 
  userId: "", 
  sfu: false,
  streamType: 0,
  from: 0,//0无人机视频 1 机场视频
  pullStreamType: 0,//机场启用 自组网推流0  服务器推流1
  videoAgg: 0,//无人机是否启用视频聚合
  is5g: false,//无人机视频是否开启5g
  rtc: {
    iceServers: [
      {
        urls: ["turn:qstun.godouav.com"], //"turn:stun.godouav.com" //turn:qstun.godouav.com
        username: "kurento",
        credential: "kurento",
      },
    ],
  },
};

export default Config;
