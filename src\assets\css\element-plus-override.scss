// .el-radio__input.is-checked .el-radio__inner {
//     background: #5964FB !important;
//     border-color: #5964FB !important;
// }

// .el-radio__inner:hover {
//     border-color: #5964FB !important;
// }
// .el-radio__input.is-checked+.el-radio__label {
//     color: #5964FB !important;
// }
// .el-input {
//     --el-input-focus-border:#5964FB !important;
//     --el-input-hover-border-color: #5964FB !important;
//     --el-input-focus-border-color: #5964FB !important;
// }

:root {
    --el-color-primary: #5964FB !important;
    --el-color-primary-light-3: #8590fc !important;
    --el-color-primary-light-5: #a7affd !important;
    --el-color-primary-light-7: #c9cdfe !important;
    --el-color-primary-light-8: #dadbfe !important;
    --el-color-primary-light-9: #eceefe !important;
    --el-color-primary-dark-2: #4750c9 !important;
  }

  .el-pager >.number {
    background-color: #fff !important;
    margin: 0 4px;
    border: 1px solid #dcdcdc !important;
}

.el-pager >.number:hover {
  background-color: rgba(89, 100, 251, 0.1) !important;
    border: none !important;
    color: #000 !important;
}
.el-pager >.number.is-active {
    background-color: #5964FB !important;
    color: #fff !important;
    border-color: #5964FB !important;
}

.el-table__header th {
  background-color: #F8F9FE !important;  
  color: #7A7EA8 !important;                
  font-weight: bold;
}

.el-image-viewer__img {
      width: 70% !important;
      height: auto !important;
    }