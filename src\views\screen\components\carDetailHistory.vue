<template>
  <section class="car-detail-history" ref="historyRef">
    <CarDetailHistoryAmap
      class="history-amap"
      :trackList="option.trackList"
      :trackIndex="option.trackIndex"
    >
      <div class="history-option">
        <div class="history-option-top">
          <div class="top-tabs">
            <div
              :class="[
                'top-tabs-item',
                { enable: item.value === option.activeTab },
              ]"
              v-for="(item, index) in option.tabs"
              :key="index"
              @click="option.activeTab = item.value"
            >
              {{ item.label }}
            </div>
          </div>

          <DatePicker
            style="width: 140px"
            v-model:value="option.date"
            :popupContainer="historyRef"
            :spotList="option.spotList"
            allowClear
            @changeYearMonth="changeYearMonth"
          />
        </div>
        <div class="history-option-bottom">
          <x-select
            v-show="option.activeTab === 'task'"
            v-model:value="option.taskId"
            :options="option.taskList"
            :popupContainer="historyRef"
          />
          <TimeRangePicker
            v-show="option.activeTab === 'time'"
            v-model:value="option.timeRange"
            :popupContainer="historyRef"
          />
          <div :class="['bottom-btn', { disable: noSearch }]" @click="search">
            查询
          </div>
        </div>
      </div>
    </CarDetailHistoryAmap>
    <div class="history-control">
      <div class="time-line">
        <div class="time-line-left">
          <x-icon
            class="time-line-left-play"
            :name="`screen_history_${option.playStatus ? 'stop' : 'start'}`"
            @click="playClick"
          />
          <div class="time-line-left-time">
            {{ option.currTrack?.createdTime.split(" ")[1] || "00:00:00" }}
          </div>
          <div class="time-line-left-select" @mousedown="mousedownSpeed">
            {{ "X" + option.speed }}
          </div>
          <div v-show="option.showSpeedOptions" class="select-options">
            <div
              :class="[
                'select-options-item',
                { enable: option.speed === item },
              ]"
              v-for="(item, index) in option.speedOptions"
              :key="index"
              @mousedown="mousedownSpeedItem(item)"
            >
              {{ "X" + item }}
            </div>
          </div>
        </div>
        <div class="time-line-right" ref="xSliderRef">
          <x-slider
            class="time-line-progress"
            v-model:value="option.progress"
          />
          <div class="time-line-view">
            <div
              class="time-line-item"
              :style="{ left: item + 'px' }"
              v-for="(item, index) in option.trackLeftList"
              :key="index"
            >
              <span>{{ option.trackTimeList[index] }}</span>
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="track-line"></div>
      <div class="video-line"></div> -->
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, watch, computed, onMounted } from "vue";
import { formatDateTime } from "@/assets/ts/dateTime";
import { animateTime } from "@/assets/ts/animate";
import {
  historyTaskList,
  getTrackPositions,
  getTrackDates,
} from "@/services/api";
import { throttle } from "@/assets/ts/utils";
import { DatePicker } from "@/components/x-date-picker";
import xSlider from "@/components/x-slider.vue";
import { TimeRangePicker } from "@/components/x-time-picker";
import xSelect from "./x-select.vue";
import CarDetailHistoryAmap from "./carDetailHistoryAmap.vue";
import Message from "./x-message";

const props = defineProps({
  id: {
    type: String,
    required: true,
  },
});

onMounted(() => {
  const { width } = xSliderRef.value!.getBoundingClientRect();
  const gapNum = width / (showLineNum + 1);
  for (let i = 0; i < showLineNum; i++) {
    option.trackLeftList.push(gapNum * (1 + i));
  }
  changeYearMonth({
    year: new Date().getFullYear(),
    month: new Date().getMonth(),
  });
});

const historyRef = ref<HTMLElement>();
const xSliderRef = ref<HTMLElement>();
const maxProgress = 100;
const showLineNum = 20;
const option = reactive({
  tabs: [
    {
      label: "时间",
      value: "time",
    },
    {
      label: "任务",
      value: "task",
    },
  ],
  activeTab: "time",
  date: formatDateTime(Date.now(), "YYYY-MM-DD"),
  spotList: [],
  timeRange: ["00:00:00", "23:59:59"],
  taskId: "",
  taskList: [] as { value: string; label: string }[],
  progress: 0,
  trackList: [] as any[],
  // 时间轴和progress都是完全按照路线的index来做的，也就是点与点之间的时间间隔必须是一致的
  trackIndex: computed(() =>
    Math.round(
      Math.max(option.trackList.length - 1, 0) * (option.progress / maxProgress)
    )
  ),
  trackLeftList: [],
  trackTimeList: computed(() => {
    const len = option.trackList.length;
    if (len > 2) {
      const startMs = Date.parse(option.trackList[0].createdTime);
      const endMs = Date.parse(option.trackList[len - 1].createdTime);
      const gapMs = (endMs - startMs) / (showLineNum + 1);
      const result = [];
      for (let i = 0; i < showLineNum; i++) {
        result.push(formatDateTime(startMs + gapMs * (1 + i), "HH:mm:ss"));
      }
      return result;
    } else {
      return new Array(showLineNum).fill("00:00:00");
    }
  }),
  currTrack: computed(() => {
    return option.trackList[option.trackIndex];
  }),
  playStatus: 0 as 0 | 1, // 0暂停 1播放
  playTimer: null, // 用于播放后停止
  speed: 1,
  speedOptions: [1, 2, 4, 6],
  showSpeedOptions: false,
});
watch(
  () => option.date,
  async (newV) => {
    option.taskList = (
      (await historyTaskList({
        deviceId: props.id,
        startTime: `${newV} 00:00:00`,
        lastTime: `${newV} 23:59:59`,
      })) as string[]
    ).map((v) => ({
      value: v,
      label: v,
    }));
    option.taskId = option.taskList[0].value || "";
  }
);

const noSearch = computed(
  () =>
    (option.activeTab === "time" && !option.timeRange[0]) ||
    (option.activeTab === "task" && !option.taskId)
);

const changeYearMonth = async ({ year, month }) => {
  const dateList = await getTrackDates({
    deviceId: props.id,
    year: year,
    month: month + 1,
  });
  option.spotList = dateList.map(
    (dateString: string) => dateString.split(" ")[0]
  );
};

const search = async () => {
  if (!noSearch.value) {
    option.trackList = await getTrackPositions({
      deviceId: props.id,
      ...(option.activeTab === "time"
        ? {
            startTime: `${option.date} ${option.timeRange[0]}`,
            lastTime: `${option.date} ${option.timeRange[1]}`,
          }
        : {
            startTime: `${option.date} 00:00:00`,
            lastTime: `${option.date} 23:59:59`,
            workNo: option.taskId,
          }),
    });
    option.progress = 0;
    pauseHandle();
  }
};

const playHandle = () => {
  option.playStatus = 1;
  if (option.progress === maxProgress) {
    option.progress = 0;
  }
  option.playTimer = animateTime(() => {
    const newValue = option.progress + maxProgress / option.trackList.length;
    if (newValue >= maxProgress) {
      option.progress = maxProgress;
      pauseHandle();
    } else {
      option.progress = newValue;
    }
  }, 240 / option.speed);
};

const pauseHandle = () => {
  option.playStatus = 0;
  if (option.playTimer) {
    option.playTimer();
    option.playTimer = null;
  }
};

const mousedownSpeed = (e: Event) => {
  e && e.stopPropagation();
  if (option.showSpeedOptions) {
    document.removeEventListener("mousedown", docClickHandler);
  } else {
    document.addEventListener("mousedown", docClickHandler);
  }
  option.showSpeedOptions = !option.showSpeedOptions;
};

const docClickHandler = () => {
  option.showSpeedOptions = false;
};

// play按钮
const playClick = throttle(
  () => {
    if (!option.trackList.length) {
      Message("error", "请先查询");
    } else {
      option.playStatus ? pauseHandle() : playHandle();
    }
  },
  500,
  true
);

// 切换速度
const mousedownSpeedItem = (item: number) => {
  option.speed = item;
  if (option.playStatus === 1) {
    pauseHandle();
    playHandle();
  }
};
</script>

<style lang="scss" scoped>
.car-detail-history {
  position: relative;
  @include wh(100%);
  display: flex;
  flex-direction: column;
  .history-amap {
    position: relative;
    width: 100%;
    flex: 1;
    .history-option {
      z-index: var(--z-index-text);
      position: absolute;
      left: 0;
      top: 0;
      @include wh(338px, 19vh) {
        padding: 1.7vh 24px;
        box-shadow: 0, 0, 10, 0 rgba(124, 146, 251, 0.4);
        border-radius: 20px;
        background: linear-gradient(
          to bottom,
          rgba(96, 102, 150, 0.57),
          rgba(14, 18, 50, 0.52)
        );
      }
      &-top {
        @include fj;
        @include wh(100%, 3.2vh);
        .top-tabs {
          display: flex;
          height: 100%;
          border-radius: 4px;
          background-color: rgba(204, 219, 248, 0.2);
          &-item {
            cursor: pointer;
            @include wh(58px, 100%) {
              border-radius: 4px;
              @include sc(1.6vh, rgb(248, 250, 255)) {
                line-height: 3.2vh;
                text-align: center;
              }
            }
            &.enable {
              background-color: #5a76fc;
              color: rgb(248, 250, 255);
            }
          }
        }
      }
      &-bottom {
        margin-top: 1.8vh;
        .bottom-btn {
          cursor: pointer;
          margin: 2.8vh auto 0 auto;
          @include wh(180px, 3.4vh) {
            border-radius: 0.8vh;
            background: linear-gradient(rgb(158, 174, 250), rgb(90, 118, 252));
          }
          @include sc(1.4vh, rgb(255, 255, 255)) {
            line-height: 3.4vh;
            text-align: center;
          }
          &.disable {
            background: #444;
            cursor: not-allowed;
          }
        }
      }
    }
  }
  .history-control {
    width: 100%;
    .time-line {
      display: flex;
      @include wh(100%, 4vh);
      &-left {
        position: relative;
        @include ct-f(y);
        @include wh(185px, 100%) {
          padding: 0 8px;
          background: linear-gradient(
            to bottom,
            rgba(255, 255, 255, 0.2),
            rgba(181, 181, 181, 0)
          );
        }
        &-play {
          cursor: pointer;
          @include wh(22px, 2.2vh);
        }
        &-time {
          margin-left: 8px;
          @include sc(2vh, #fff) {
            font-weight: 700;
          }
        }
        &-select {
          cursor: pointer;
          @include wh(30px, 2.4vh) {
            margin-left: 8px;
            border-radius: 4px;
            background-color: rgb(21, 22, 30);
            @include sc(1.3vh, rgba(221, 229, 250, 0.8)) {
              line-height: 2.4vh;
              text-align: center;
            }
          }
        }
        .select-options {
          position: absolute;
          right: 0.8vh;
          top: -14.5vh;

          @include wh(46px, 14.5vh) {
            padding: 0.9vh 5px;
            border-radius: 8px;
            background: linear-gradient(
              to bottom,
              rgba(11, 16, 54, 0.8),
              rgba(19, 21, 42, 0.67),
              rgba(21, 35, 64, 0.66)
            );
          }
          &-item {
            cursor: pointer;
            @include wh(100%, 21%) {
              border-radius: 4px;
              @include sc(1.4vh, rgba(255, 255, 255, 0.7)) {
                line-height: 3vh;
                text-align: center;
              }
            }
            &:nth-child(n + 2) {
              margin-top: 0.6vh;
            }
            &.enable,
            &:hover {
              font-weight: 700;
              color: #fff;
              background: linear-gradient(
                to bottom,
                rgb(158, 174, 250),
                rgb(90, 118, 252)
              );
            }
          }
        }
      }
      &-right {
        flex: 1;
        height: 100%;
        display: flex;
        flex-direction: column;
        .time-line-progress {
          z-index: var(--z-index-text);
          height: 0.9vh;
        }
        .time-line-view {
          position: relative;
          flex: 1;
          width: 100%;
          background-color: #191b2d;
          .time-line-item {
            position: absolute;
            bottom: 2px;
            @include wh(1px, 1vh) {
              background-color: rgba(221, 229, 250, 0.6);
            }
            span {
              @include ct-p(x) {
                top: -1.4vh;
              }
              @include sc(1vh, #dde5fa);
            }
          }
        }
      }
    }
    .track-line {
      @include wh(100%, 2.8vh);
      &-left {
      }
      &-right {
      }
    }
    .video-line {
      @include wh(100%, 2.8vh);
      &-left {
      }
      &-right {
      }
    }
  }
}
</style>
