<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="一键处置"
    append-to-body
    @close="emit('update:modelValue', false)"
  >
    <div class="batch-dispose-dialog">
      <div class="batch-dispose-dialog-form">
        <div class="item">
          <div class="label">告警信息</div>
          <div class="value">
            <el-scrollbar height="200">
              <div
                class="value-item"
                v-for="item in itemList"
                :key="item.deviceId"
              >
                <span class="value-item-label">{{ item.deviceId }}</span>
                <span class="value-item-value">{{ item.faultContent }}</span>
              </div>
            </el-scrollbar>
          </div>
        </div>
        <div class="item">
          <div class="label"><span class="required">*</span>解决方案</div>
          <div class="value">
            <el-input
              style="width: 100%"
              v-model="formData.description"
              type="textarea"
              rows="4"
              placeholder="请输入解决方案"
            />
          </div>
        </div>
      </div>

      <div class="batch-dispose-dialog-footer">
        <el-button
          type="primary"
          @click="handleConfirm"
          >提交</el-button
        >
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { updateVehicleAlarmStatus } from "@/services/api";
import { ElMessage } from "element-plus";
import { ref, reactive, toRefs, onMounted } from "vue";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemList: {
    type: Array,
    default: () => [],
  },
});
const emit = defineEmits(["update:modelValue", "refresh"]);
const formData = reactive({
  description: "",
});
const handleConfirm = async () => {
  if (!formData.description) {
    ElMessage.warning("请输入解决方案");
    return;
  }
  try {
    await updateVehicleAlarmStatus({
      idList: props.itemList.map((item) => item.id),
      description: formData.description,
    });
    ElMessage.success("处置成功");
    emit("update:modelValue", false);
    emit("refresh");
  } catch (err) {
    console.log(err);
  }
};
</script>
<style scoped lang="scss">
.batch-dispose-dialog {
  padding: 20px 25px;
  &-form {
    .item {
      display: flex;
      gap: 35px;
      margin-bottom: 30px;
      .label {
        width: 100px;
        text-align: left;
        margin-right: 10px;
        color: #9f9fa4;
      }
      .required {
        color: #f56c6c;
      }
      .value {
        flex: 1;
        .value-item {
          display: flex;
          gap: 10px;
          color: #383838;
        }
      }
    }
  }
  &-footer {
    display: flex;
    justify-content: center;
  }
}
</style>
