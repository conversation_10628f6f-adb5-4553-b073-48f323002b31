<template>
  <section class="car-list">
    <div class="car-list-top">
      <div class="top-right">
        <div
          class="zoom-in"
          @click="table.status = 'min'"
        ></div>
        <div
          class="zoom-out"
          @click="table.status = table.status === 'normal' ? 'max' : 'normal'"
        >
          <div class="zoom-out-rect">
            <div
              v-show="table.status === 'max'"
              class="zoom-out-rect-in"
            ></div>
          </div>
        </div>
        <div
          class="close"
          v-html="closeText"
          @click="clearAllCars"
        ></div>
      </div>
    </div>
    <div
      class="car-list-bottom"
      :style="{
        height: `${tableHeight}px`,
      }"
    >
      <x-table
        v-if="table.dataSource.length > 0"
        :cols="table.cols"
        :dataSource="table.dataSource"
        :loading="table.loading"
        :customRow="customRowHandler"
      >
        <template #headerCell="{ title, column }">
          <div
            v-if="column.key === 'time'"
            @click="sortTime"
            class="cell-time"
          >
            <span>{{ title }}</span>
            <x-icon
              :name="sortAsc ? 'sort_asc' : 'sort_desc'"
              width="15"
              height="15"
            />
          </div>
          <span v-else>{{ title }}</span>
        </template>
        <template #carId="{ record }">
          <div class="car-id">
            <div :class="['car-id-round', { disable: !record.online }]"></div>
            <div :class="['car-id-text', { disable: !record.online }]">
              {{ record.carId }}
            </div>
          </div>
        </template>
        <template #status="{ record }">
          <div
            class="status"
            :style="{
              backgroundColor: record.statusStyle.backgroundColor,
              color: record.statusStyle.color,
            }"
          >
            <x-icon :name="record.online ? record.statusStyle.icon : offlineSvg" />
            <span>{{ record.online ? record.status : offlineText }}</span>
          </div>
        </template>
        <template #water="{ record }">
          <span :style="{ color: showWaterWarn(record.water) ? 'red' : '#555' }"> {{ record.water }}% </span>
        </template>
        <template #garbage="{ record }">
          <span :style="{ color: showGarbageWarn(record.garbage) ? 'red' : '#555' }"> {{ record.garbage }}% </span>
        </template>
        <template #battery="{ record }">
          <span :style="{ color: showBatteryWarn(record.battery) ? 'red' : '#555' }"> {{ record.battery }}% </span>
        </template>

        <template #warn="{ record }">
          <div class="warn">
            <div class="warn-number">
              <x-icon
                v-show="record.warn > 0"
                name="map_car_stats_warn"
              />
              <span :class="{ red: record.warn > 0 }">{{ record.warn }}</span>
            </div>
            <div
              class="warn-close"
              v-html="closeText"
              @click="deleteCar(record.carId)"
            ></div>
          </div>
        </template>
      </x-table>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed, watch, onMounted } from "vue";
import type { PropType } from "vue";
import {
  workStatusMap,
  showBatteryWarn,
  showWaterWarn,
  showGarbageWarn,
  parseSpeed,
  parseBattery,
  parseWater,
  parseGarbage,
  offlineSvg,
  offlineText,
} from "@/services/wsconfig";
import { delCar } from "@/services/wsapi";
import { useMainStore } from "@/stores/main";
import { treeBfsParse, fixXTreeChecked, i18nSimpleKey } from "@/assets/ts/utils";
import xTable from "@/components/x-table.vue";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { mapZoom } from "@/services/wsconfig";
import xIcon from "@/components/x-icon.vue";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
});

const { socket, updateEnableCarIds } = useMainStore();

const closeText = ref("&#10005");
const deleteCar = (id: string) => {
  delCar([id]);
  // 由于x-tree的props.treeData未使用深度监听(性能优化)，需要手动更改
  socket.selectedCarItem = socket.selectedCarItem.filter((v) => v.deviceId !== id);
  treeBfsParse(socket.enableCarTree, "children", (item: TreeItemType) => {
    if (item.title === id) {
      item.checked = false;
    }
  });
  fixXTreeChecked(socket.enableCarTree);
  updateEnableCarIds(socket.enableCarIds.filter((_id) => _id !== id));
};
const clearAllCars = () => {
  delCar(socket.enableCarIds);
  // 由于x-tree的props.treeData未使用深度监听(性能优化)，需要手动更改
  socket.selectedCarItem = [];
  treeBfsParse(socket.enableCarTree, "children", (item: TreeItemType) => {
    item.checked = false;
    item.indeterminate = false;
  });
  updateEnableCarIds([]);
};
const tableHeight = computed(() =>
  Math.min({ min: 0, normal: 40 + 56 * 3, max: 40 + 56 * 6 }[table.status], socket.enableCarIds.length * 56 + 40)
);
const table = reactive({
  status: "normal" as "min" | "normal" | "max",
  cols: [
    {
      key: "carId",
      title: $t("carId"),
      width: "142",
      slots: "carId",
    },
    {
      key: "status",
      title: $t("workStatus"),
      width: "142",
      slots: "status",
    },
    {
      key: "water",
      title: $t("waterTank"),
      width: "100",
      slots: "water",
    },
    {
      key: "garbage",
      title: $t("garbageCan"),
      width: "100",
      slots: "garbage",
    },
    {
      key: "battery",
      title: $t("electricNumber"),
      width: "100",
      slots: "battery",
    },
    {
      key: "speed",
      title: `${$t("speed")}(km/h)`,
      width: "100",
    },
    {
      key: "time",
      title: $t("uploadTime"),
      width: "200",
    },
    {
      key: "warn",
      title: $t("currentFault"),
      width: "142",
      slots: "warn",
    },
  ],
  dataSource: [] as any[],
  loading: true,
});

const filterDataSource = ref<any[]>([]);
const mapItem = (item: any) => {
  const index = socket.enableCarIds.indexOf(item.deviceId) + 1;
  return {
    index: index,
    online: item.online,
    carId: item.deviceId,
    status: workStatusMap[item.workStatus].text,
    statusStyle: [
      {
        icon: "map_list_not_work",
        backgroundColor: "rgba(249, 133, 46, 0.06)",
        color: "#F9852E",
      },
      {
        icon: "map_list_work",
        backgroundColor: "rgba(89, 100, 251, 0.06)",
        color: "#5964FB",
      },
      {
        icon: "map_list_offline",
        backgroundColor: "rgb(240, 240, 240)",
        color: "#5E5E5E",
      },
      {
        icon: "map_list_charge",
        backgroundColor: "rgba(39, 212, 161, 0.1)",
        color: "#28E09F",
      },
    ][item.online ? item.workStatus : 2],
    water: parseWater(item.tank),
    garbage: parseGarbage(item.litter),
    battery: parseBattery(item.electric),
    speed: parseSpeed(item.speed),
    time: `${item.updateDate} ${item.updateTime}`,
    warn: item.warnNum,
  };
};
const update = (enableCarIds: typeof socket.enableCarIds, allStatus: typeof socket.allStatus) => {
  if (allStatus) {
    filterDataSource.value = allStatus.filter((item) => enableCarIds?.includes(item.deviceId));
    table.dataSource = filterDataSource.value.map((item) => mapItem(item)).sort((a, b) => b.index - a.index);
    table.loading = false;
  }
};

onMounted(() => {
  if (socket.enableCarIds.length === 0) {
    setTimeout(() => {
      table.loading = false;
    }, 2400);
  }
  // watch监听数组时不支持ts类型检查，所以分开写
  watch(
    () => socket.enableCarIds,
    (newV) => update(newV, socket.allStatus)
  );
  watch(
    () => socket.allStatus,
    (newV) => update(socket.enableCarIds, newV)
  );
});

const customRowHandler = (record: any) => {
  return {
    onClick: () => {
      const carBaseStatus = socket.baseStatus?.filter((v) => record.carId?.includes(v.deviceId));
      props.map.setZoomAndCenter(mapZoom, [carBaseStatus![0].longitude, carBaseStatus![0].latitude]);
    },
  };
};
const sortAsc = ref(true);
const sortTime = () => {
  sortAsc.value = !sortAsc.value;
  const sortRule = sortAsc.value ? "asc" : "desc";
  table.dataSource = sortData(filterDataSource.value, sortRule).map((item) => mapItem(item));
};

const sortData = (data: Array<object>, sortOrder: "asc" | "desc" = "asc") => {
  const sortedData = data.sort((a: any, b: any) => {
    const dateA = new Date(`${a.updateDate} ${a.updateTime}`);
    const dateB = new Date(`${b.updateDate} ${b.updateTime}`);
    return sortOrder === "asc" ? dateA.getTime() - dateB.getTime() : dateB.getTime() - dateA.getTime();
  });
  return sortedData;
};
</script>

<style lang="scss" scoped>
.car-list {
  z-index: var(--z-index-popover);
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;

  &-top {
    display: flex;
    flex-direction: row-reverse;
    position: absolute;
    top: -33px;
    right: 0;
    @include wh(max-content, 33px);

    .top-right {
      position: relative;
      display: flex;
      @include wh(106px, 100%) {
        background-color: #fff;
      }

      &::before {
        content: "";
        position: absolute;
        top: 0;
        left: -20px;

        @include wh(0) {
          border-top: 16.5px solid transparent;
          border-bottom: 17px solid #fff;
          border-left: 10px solid transparent;
          border-right: 10px solid #fff;
        }
      }
      .zoom-in,
      .zoom-out,
      .close {
        cursor: pointer;
        height: 100%;
        flex: 1;
        &:hover {
          background-color: rgb(217, 224, 251);
        }
      }
      .zoom-in {
        position: relative;
        &::after {
          content: "";
          @include ct-p;
          @include wh(10px, 1.3px) {
            background-color: #383838;
            border-radius: 10px;
          }
        }
      }
      .zoom-out {
        @include ct-f;
        &-rect {
          position: relative;
          @include wh(10px) {
            border-radius: 2px;
            border: 1.3px solid #383838;
          }
          &-in {
            position: absolute;
            bottom: -1.3px;
            right: -1.3px;
            @include wh(6px) {
              border-radius: 2px;
              border: 1.3px solid #383838;
            }
          }
        }
      }
      .close {
        line-height: 33px;
        text-align: center;
      }
    }
  }

  &-bottom {
    width: 100%;
    background-color: #fff;
    transition: height 0.5s ease;

    .cell-time {
      cursor: pointer;
      span {
        margin-right: 5px;
      }
    }

    .car-id {
      @include ct-f(y);

      &-round {
        position: relative;

        @include wh(8px) {
          border-radius: 50%;
          background-color: #5964fb;
          margin-right: 2px;
        }

        &::after {
          content: "";
          @include ct-p;

          @include wh(4px) {
            border-radius: 50%;
            background-color: #fff;
          }
        }

        &.disable {
          background-color: #dce3fb;
        }
      }

      &-text {
        @include sc(13px, #383838) {
          font-weight: bolder;
        }

        &.disable {
          color: #9f9fa4;
        }
      }
    }
    .status {
      @include ct-f(y);
      @include wh(86px, 28px) {
        padding-left: 10px;
        border-radius: 50px;
      }
      span {
        padding-left: 6px;
      }
    }

    .warn {
      @include fj {
        align-items: center;
      }

      padding-right: 20px;

      &-number {
        @include ct-f(y);

        span {
          margin-left: 4px;

          &.red {
            color: #e24562;
          }
        }
      }

      &-close {
        cursor: pointer;
      }
    }
  }
}
</style>
