<template>
  <section
    class="alarm-statistics"
    ref="alarmStatisticsRef"
  >
    <div class="alarm-statistics-top">
      <div class="top-title">{{ $t("alarmStatistics") }}</div>
    </div>
    <div class="alarm-statistics-middle">
      <div class="alarm-statistics-middle-top">
        <div class="top-left">
          <x-input
            v-model:value="searchForm.deviceId"
            :placeholder="$t('PEnterDeviceId')"
            suffix="input_search"
            style="width: 200px; margin-right: 16px"
          />
          <x-select
            v-model:value="searchForm.vin"
            :options="searchForm.vinOptions"
            :popupContainer="alarmStatisticsRef"
            placeholder="请选择VIN"
            showPopSearch
            allowClear
            style="width: 200px; margin-right: 16px"
          />
          <x-select
            v-model:value="searchForm.warnLevel"
            :options="warnLevelDefType"
            :popupContainer="alarmStatisticsRef"
            placeholder="请选择告警等级"
            allowClear
            style="width: 200px; margin-right: 16px"
          />
          <x-input
            v-model:value="searchForm.faultCode"
            :placeholder="$t('PEnterFaultCode')"
            suffix="input_search"
            style="width: 200px; margin-right: 16px"
          />
          <x-radio-button
            v-model:value="rangeOptions.activeTab"
            :options="rangeOptions.tabs"
            @change="changeRange"
            style="margin-right: 16px"
          />
          <DateRangePicker
            v-model:value="searchForm.opDate"
            :popupContainer="alarmStatisticsRef"
            :placeholder="[$t('startTime'), $t('endTime')]"
            @update:value="updateDateRange"
            format="YYYY-MM-DD HH:mm:ss"
            allowClear
            showTime
            showNow
            style="width: 370px"
          />
        </div>
        <div class="top-right">
          <x-button
            @click="exportExcel"
            icon="export_blue"
            type="paleBlue"
            text="导出"
            style="margin-right: 12px"
          />
          <x-button
            v-if="permitList.includes('sys:vehicleWarn:list')"
            @click="searchAlarmList"
            type="blue"
            :text="$t('search')"
            style="margin-right: 12px"
          />
          <x-button
            @click="resetSearchForm"
            type="green"
            :text="$t('reset')"
          />
        </div>
      </div>
      <div class="alarm-statistics-middle-bottom">
        <x-radio-button
          type="tab"
          v-model:value="statusOptions.activeTab"
          :options="statusOptions.tabs"
          @change="statusOptions.activeTab = $event"
        >
          <template #content="{ option }">
            <span>{{ option.label }}({{ option.num }})</span>
          </template>
        </x-radio-button>
      </div>
    </div>
    <div class="alarm-statistics-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #headerCell="{ title, column }">
          <div v-if="column.key === 'warnLevel'">
            <span style="vertical-align: middle">{{ title }}</span>
            <warnLevelDefinition />
          </div>
          <span v-else>{{ title }}</span>
        </template>
        <template #status="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.isSolved === 0 ? 'status_red' : 'status_green'"
              width="12"
              height="12"
            />
            <span>{{ record.isSolved === 0 ? $t("unsolved") : $t("solved") }}</span>
          </div>
        </template>
        <template #warnLevel="{ record }">
          <div
            class="table-level-slot"
            v-for="(item, index) in levelTips.info"
            :key="index"
          >
            <x-icon
              v-if="item.value === record.warnLevel"
              :name="item.icon"
              width="18"
              height="18"
            />
            <span
              v-if="item.value === record.warnLevel"
              style="margin-left: 10px"
              >{{ item.label }}</span
            >
          </div>
        </template>
        <template #createdTime="{ record }">
          <span style="color: #9f9fa4">
            {{ `${record.createdTime}—${record.endTime || ""}` }}
          </span>
        </template>
        <template #faultAddress="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-address-popover"
            :container="alarmStatisticsRef"
          >
            {{ record.faultAddress }}
            <template #content>
              <div class="table-address-popover-hover">
                {{ record.faultAddress }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="record.isSolved === 0 && permitList.includes('sys:vehicleWarn:status')"
                @click="openHandle(record)"
              >
                {{ $t("process") }}
              </span>
              <span
                v-if="permitList.includes('sys:vehicleWarn:detail')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <handleWarning
        v-model:show="handleModal.show"
        :id="handleModal.id"
        :isEnd="handleModal.isEnd"
        @confirm="handleModal.confirm"
      />
      <warningDetail
        v-model:show="detailModal.show"
        :id="detailModal.id"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, reactive, watch, onMounted } from "vue";
import type { PageSizeType } from "@/components/types";
import { warnLevelDefType } from "@/assets/ts/config";
import { getTimeRange } from "@/assets/ts/dateTime";
import { getVehAndVinList, getWarnList, statisticsNumber, exportVehicleAlarmStatistics } from "@/services/api";
import type { WarnListRequest } from "@/services/type";
import xRadioButton from "@/components/x-radio-button.vue";
import xPopover from "@/components/x-popover.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xIcon from "@/components/x-icon.vue";
import handleWarning from "./components/handleWarning.vue";
import warningDetail from "./components/warningDetail.vue";
import { DateRangePicker } from "@/components/x-date-picker";
import { useRoute } from "vue-router";
import { useMainStore } from "@/stores/main";
import warnLevelDefinition from "../../components/warnLevelDefinition.vue";
import { download, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmStatistics");
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
const {
  userInfo: { permitList },
} = useMainStore();

const route = useRoute();

const alarmStatisticsRef = ref<any>();

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderId"),
      width: "80",
    },
    {
      key: "faultContent",
      title: $t("faultName"),
      width: "150",
    },
    {
      key: "faultCode",
      title: $t("faultCode"),
      width: "80",
    },
    {
      key: "status",
      title: $t("status"),
      width: "80",
      slots: "status",
    },
    {
      key: "warnLevel",
      title: $t("level"),
      width: "80",
      slots: "warnLevel",
    },
    {
      key: "deviceId",
      title: $t("deviceId"),
      width: "100",
    },
    {
      key: "vin",
      title: "VIN",
      width: "100",
    },
    {
      key: "createdTime",
      title: $t("time"),
      width: "200",
      slots: "createdTime",
    },
    // {
    //   key: "faultAddress",
    //   title: "地址",
    //   width: "200",
    //   slots: "faultAddress",
    // },
    {
      key: "opera",
      title: $t("opera"),
      width: "200",
      slots: "opera",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const levelTips = reactive({
  info: warnLevelDefType,
  show: false,
});

const statusOptions = reactive({
  tabs: [
    {
      value: "1",
      label: $t("total"),
      name: "total",
      num: 0,
    },
    {
      value: "4",
      label: $t("realTime"),
      name: "currentCount",
      num: 0,
    },
    {
      value: "3",
      label: $t("unsolved"),
      name: "unsolvedCount",
      num: 0,
    },
    {
      value: "2",
      label: $t("solved"),
      name: "solvedCount",
      num: 0,
    },
  ],
  activeTab: route.query.mode || "1",
});

const rangeOptions = reactive({
  tabs: [
    {
      value: 0,
      label: $t("today"),
      range: getTimeRange(0, 0, "dateTime"),
    },
    {
      value: 1,
      label: $t("yesterday"),
      range: getTimeRange(1, 1, "dateTime"),
    },
    {
      value: 2,
      label: $t("lastSevenDays"),
      range: getTimeRange(0, 6, "dateTime"),
    },
  ],
  activeTab: null,
});

const searchForm = reactive({
  deviceId: (route.query.deviceId as string) || "",
  opDate: ["", ""],
  mode: route.query.mode || "1",
  vin: "",
  warnLevel: "",
  faultCode: "",
  vinOptions: [] as { label: string; value: string }[],
});
// 校验导出日期范围
const checkDateRange = () => {
  console.log(searchForm.opDate, "www");
  const [startDate, endDate] = searchForm.opDate;
  if (startDate && endDate) {
    const diffMilliseconds = new Date(endDate).getTime() - new Date(startDate).getTime();
    const diffDays = diffMilliseconds / (1000 * 60 * 60 * 24);
    if (diffDays > 7) {
      Message("error", "导出日期范围不能超过7天");
      searchForm.opDate = ["", ""];
      return false;
    }
  } else {
    Message("error", "请选择正确的时间范围");
    searchForm.opDate = ["", ""];
    return false;
  }
  return true;
};

// 导出
const exportExcel = async () => {
  if (rangeOptions.activeTab !== null) {
    [searchForm.opDate[0], searchForm.opDate[1]] = rangeOptions.tabs[rangeOptions.activeTab].range;
  }
  if (!checkDateRange()) {
    return;
  }
  const param = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceId: (searchForm.deviceId && searchForm.deviceId) || searchForm.vin,
    mode: searchForm.mode,
    warnLevel: searchForm.warnLevel || "",
  } as WarnListRequest;
  const [warnDateStart, warnDateEnd] = searchForm.opDate;
  param.warnDateStart = warnDateStart || "";
  param.warnDateEnd = warnDateEnd || "";
  try {
    const response = await exportVehicleAlarmStatistics(param);
    download(response);
    Message("success", "导出成功");
  } catch (error) {
    console.log(error);
    Message("error", "导出失败");
  }
};

// 查询
const searchAlarmList = async () => {
  table.loading = true;
  const param = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceId: (searchForm.deviceId && searchForm.deviceId) || searchForm.vin,
    mode: searchForm.mode,
    warnLevel: searchForm.warnLevel,
    faultCode: searchForm.faultCode,
  } as WarnListRequest;
  const [warnDateStart, warnDateEnd] = searchForm.opDate;
  param.warnDateStart = warnDateStart || "";
  param.warnDateEnd = warnDateEnd || "";

  if (rangeOptions.activeTab !== null) {
    [param.warnDateStart, param.warnDateEnd] = rangeOptions.tabs[rangeOptions.activeTab].range;
  }

  // 统计数字
  const statistics = await statisticsNumber(param);
  statusOptions.tabs.forEach((item) => {
    // @ts-ignore
    item.num = statistics[item.name];
  });

  // 告警列表
  const { totalCount, list } = await getWarnList(param);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];

  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

// 分页
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchAlarmList();
};

// 重置
const resetSearchForm = () => {
  statusOptions.activeTab = "1";
  rangeOptions.activeTab = null;
  searchForm.deviceId = "";
  searchForm.opDate = ["", ""];
  searchForm.vin = "";
  searchForm.warnLevel = "";
  searchForm.faultCode = "";
  searchAlarmList();
};

watch(
  () => statusOptions.activeTab,
  (newV) => {
    if (newV !== null) {
      searchForm.mode = newV;
      statusOptions.activeTab = newV;
      searchAlarmList();
    }
  }
);
const changeRange = (value: any) => {
  if (value !== null) {
    rangeOptions.activeTab = value;
    searchAlarmList();
  }
};

watch(
  () => rangeOptions.activeTab,
  (newV) => {
    if (newV !== null) {
      searchForm.opDate = ["", ""];
    }
  }
);
const updateDateRange = (value: string) => {
  if (value) {
    rangeOptions.activeTab = null;
  }
};

(async () => {
  searchAlarmList();
})();

// 处理
const handleModal = reactive({
  show: false,
  id: "",
  isEnd: 0,
  confirm: () => searchAlarmList(),
});

const openHandle = (record: any) => {
  handleModal.id = record.id;
  handleModal.isEnd = record.isEnd;
  handleModal.show = true;
};

// 详情
const detailModal = reactive({
  show: false,
  id: "",
});

const openDetail = (id: string) => {
  detailModal.id = id;
  detailModal.show = true;
};
onMounted(() => {
  getVehAndVinList().then((res) => {
    searchForm.vinOptions = res.map((item) => {
      return {
        label: item.vin,
        value: item.vehicleNo,
      };
    });
  });
});
</script>

<style lang="scss" scoped>
.alarm-statistics {
  @include wh(100%) {
    display: flex;
    flex-direction: column;
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, #242859) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    &-top {
      @include fj;
      @include wh(100%, 32px) {
        margin-top: 20px;
      }
      .top-left,
      .top-right {
        display: flex;
      }
    }
    &-bottom {
      margin: 20px 0 16px 0;
    }
  }
  &-bottom {
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-address-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
    }
  }
}
.table-address-popover-hover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
</style>
