<template>
  <div class="x-switch">
    <div
      :class="[`x-switch-handle ${props.size}`, { checked: isChecked, disabled: disabled }]"
      :style="switchStyle"
      @click="!props.disabled && toggle()"
    >
      <div :class="['x-switch-inner', isChecked ? 'inner-checked' : 'inner-unchecked']">
        <template v-if="isChecked">
          {{ props.checkedChildren }}
          <slot
            v-if="$slots.checkedChildren && !props.checkedChildren"
            name="checkedChildren"
          ></slot>
        </template>
        <template v-else
          >{{ props.unCheckedChildren }}
          <slot
            v-if="$slots.unCheckedChildren && !props.unCheckedChildren"
            name="unCheckedChildren"
          ></slot>
        </template>
      </div>
      <div :class="['x-node', { 'x-node-checked': isChecked }]"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { ref, computed, watch, onMounted } from "vue";
import type { PropType } from "vue";

const props = defineProps({
  // （v-model）指定当前是否选中
  checked: {
    type: [Boolean, String, Number],
    default: false,
  },
  // 选中时的内容
  checkedChildren: {
    type: [Number, String],
  },
  // 选中时的值
  checkedValue: {
    type: [Boolean, String, Number],
    default: true,
  },
  // 是否禁用
  disabled: {
    type: Boolean,
    default: false,
  },
  // 开关大小
  size: {
    type: String as PropType<"default" | "small">,
    default: "default",
  },
  // 非选中时的内容
  unCheckedChildren: {
    type: [Number, String],
  },
  // 非选中时的值
  unCheckedValue: {
    type: [Boolean, String, Number],
    default: false,
  },
});
const switchStyle = computed(() => {
  let style = "";
  const width = {
    default: 42,
    small: 28,
  }[props.size];
  const height = {
    default: 22,
    small: 16,
  }[props.size];
  return (style += `height: ${height}px; line-height: ${height}px; min-width: ${width}px;`);
});
const isChecked = ref<boolean | number | string>(false);
const emits = defineEmits(["update:checked", "change"]);
const toggle = () => {
  isChecked.value = !isChecked.value;
  const emitValue = isChecked.value ? props.checkedValue : props.unCheckedValue;
  emits("update:checked", emitValue);
  emits("change", emitValue);
};
const initSwitcher = () => {
  isChecked.value = props.checked;
};
onMounted(initSwitcher);
watch(
  () => props.checked,
  () => {
    initSwitcher();
  }
);

defineExpose({
  toggle,
});
</script>
<style lang="scss" scoped>
$defaultSize: 16px;
$smallSize: 10px;
.x-switch {
  display: inline-block;
  margin: auto;
  &-handle {
    position: relative;
    background: #e5e6e8;
    border-radius: 100px;
    cursor: pointer;
    transition: background-color 0.36s;
    .x-switch-inner {
      display: inline-block;
      padding: 0 8px;
      @include sc(14px, #fff);
      transition: all 0.36s;
    }
    .inner-checked {
      margin-right: $defaultSize;
    }
    .inner-unchecked {
      margin-left: $defaultSize;
    }
    .x-node {
      position: absolute;
      top: 3px;
      left: 3px;
      @include wh($defaultSize, $defaultSize);
      background: #fff;
      border-radius: 30px;
      transition: all 0.36s;
    }
    .x-node-checked {
      left: 100%;
      margin-left: -4px;
      transform: translateX(-100%);
    }
  }
  .checked {
    background: #5964fb;
  }
  .disabled {
    cursor: not-allowed;
    opacity: 0.4;
  }
  .small {
    .x-switch-inner {
      padding: 0 5px;
      font-size: 12px;
    }
    .inner-checked {
      margin-right: $smallSize;
    }
    .inner-unchecked {
      margin-left: $smallSize;
    }
    .x-node {
      @include wh($smallSize, $smallSize);
    }
  }
}
</style>
