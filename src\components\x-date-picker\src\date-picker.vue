<script lang="ts" setup>
import xPopover from "@/components/x-popover.vue";
import xSelect from "@/components/x-select.vue";
import DatePanel from "./date-panel.vue";
import { getYearMonth, parseDateFormat, parseDate } from "./utils";
import { formatDateTime, safeGetTime } from "@/assets/ts/dateTime";
import { reactive, ref, watch, nextTick, computed, onMounted } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");
/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    allowClear?: boolean;
    disabledDate?: Function;
    disabledTime?: Function;
    format?: string;
    popupContainer?: HTMLElement;
    placeholder?: string;
    showNow?: boolean;
    showTime?: boolean;
    showToday?: boolean;
    value?: string;
    spotList?: string[];
    showSelect?: boolean;
    selectType?: "day" | "week" | "month";
  }>(),
  {
    allowClear: true,
    format: "YYYY-MM-DD",
    placeholder: "请选择日期",
    showNow: false,
    showTime: false,
    showToday: false,
    value: "",
    showSelect: false,
    selectType: "day",
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value", "changeYearMonth", "changeSelect"]);

/**
 * 引用
 */
const triggerRef = ref();
const inputRef = ref();

onMounted(() => {
  data.allMonthListRefList = document.querySelectorAll(".month-list")!;
});

/**
 * 动态值
 */
const data = reactive({
  allMonthListRefList: [],
  popoverVisible: false,
  tempValue: formatDateTime(parseDate(props.value), props.format, ""), // 显示值
  previewValue: "", // 预览值
  year: 0,
  month: 0,
  nextYear: 0, // 没卵用
  nextMonth: 0, // 没卵用
  today: formatDateTime(new Date().getTime(), "YYYY-MM-DD"),
  todaystamp: safeGetTime(formatDateTime(new Date().getTime(), "YYYY-MM-DD")),
  selectType: props.selectType,
  selectFormat: computed(() =>
    data.selectType === "month" ? "YYYY-MM" : props.format
  ),
});

const selectOpts = [
  {
    label: "日",
    value: "day",
  },
  {
    label: "周",
    value: "week",
  },
  {
    label: "月",
    value: "month",
  },
];
const selectChange = (value: string) => {
  emits("changeSelect", value);
};

// 得到年月
getYearMonth({
  data,
  value: formatDateTime(parseDate(data.tempValue), "YYYY-MM-DD HH:mm:ss", ""),
});

/**
 * 气泡层隐藏
 */
const popoverVisibleChange = (visible: boolean) => {
  // 恢复临时值
  if (visible === false) {
    if (data.selectType === "week") {
      data.tempValue = props.value.replace(",", " ~ ");
    } else {
      data.tempValue = formatDateTime(
        parseDate(props.value),
        data.selectFormat,
        ""
      );
    }
  }
};

/**
 * 触发器被点击
 */
const onClickTrigger = () => {
  // 显示气泡层
  if (data.popoverVisible === false) {
    data.popoverVisible = true;
    const [year, month] = (
      props.value || formatDateTime(data.todaystamp, "YYYY-MM-DD")
    ).split("-");
    data.year = Number(year);
    data.month = Number(month) - 1;
  }

  // 自动聚焦
  if (document.activeElement !== inputRef.value) {
    inputRef.value.focus();
  }
};
/**
 * 保持层被点击   // 感觉没啥乱用，后面再删
 */
const onClickKeep = () => {
  // 如果气泡层显示，执行触发器被点击
  data.popoverVisible && triggerRef.value.click();
};
/**
 * 更新临时的值
 */
const updateTempValue = ({ date, time }: any) => {
  // 保持层被点击
  onClickKeep();
  // 更新临时值
  if (date && time) {
    data.tempValue = formatDateTime(
      parseDate(`${date} ${time}`),
      data.selectFormat,
      ""
    );
    return;
  }
  // 值拆分
  const [tempDate, tempTime] = formatDateTime(
    parseDate(data.tempValue),
    data.selectFormat,
    ""
  )
    .split(" ")
    .filter((v: string) => v);

  // 仅日期
  if (date) {
    if (data.selectType === "week") {
      data.tempValue = date.replace(",", " ~ ");
    } else {
      const formatParse = parseDateFormat(data.selectFormat);
      const defaultTime = formatParse.hour_minute_second
        ? " 00:00:00"
        : formatParse.hour_minute
        ? " 00:00"
        : formatParse.hour
        ? " 00"
        : "";
      data.tempValue = formatDateTime(
        parseDate(tempTime ? `${date} ${tempTime}` : `${date}${defaultTime}`),
        data.selectFormat,
        ""
      );
    }
    return;
  }
  // 仅时间
  if (time) {
    data.tempValue = formatDateTime(
      parseDate(tempDate ? `${tempDate} ${time}` : `${data.today} ${time}`),
      data.selectFormat,
      ""
    );
    return;
  }
};
/**
 * 更新预览值
 */
const updatePreviewValue = ({ date }: any = {}) => {
  // 值拆分
  const tempTime = formatDateTime(
    parseDate(data.tempValue),
    data.selectFormat,
    ""
  )?.split(" ")[1];

  // 预览
  if (date) {
    if (data.selectType === "week") {
      data.previewValue = date.replace(",", " ~ ");
    } else {
      data.previewValue = formatDateTime(
        parseDate(tempTime ? `${date} ${tempTime}` : date),
        data.selectFormat,
        ""
      );
    }

    return;
  }

  // 退出预览
  data.previewValue = "";
};
/**
 * 确定
 */
const onClickConfirm = () => {
  if (data.tempValue) {
    // 更新表单值
    emits(
      "update:value",
      data.selectType === "week"
        ? data.tempValue.replace(" ~ ", ",")
        : formatDateTime(parseDate(data.tempValue), data.selectFormat, "")
    );

    // 隐藏气泡层
    if (data.popoverVisible === true) {
      data.popoverVisible = false;
    }
  }
};
/**
 * 清除
 */
const onClickClear = () => {
  // 更新表单值
  emits("update:value", "");
  // 重置临时值
  data.tempValue = "";
  data.previewValue = "";
};
/**
 * 此刻
 */
const onClickNow = () => {
  data.tempValue = formatDateTime(
    parseDate(formatDateTime(new Date().getTime(), "YYYY-MM-DD HH:mm:ss")),
    data.selectFormat,
    ""
  );
  changeYearMonth({
    year: Number(data.today.split("-")[0]),
    month: Number(data.today.split("-")[1]) - 1,
  });
};
/**
 * 今天
 */
const onClickToday = () => {
  data.tempValue = formatDateTime(parseDate(data.today), data.selectFormat, "");
  changeYearMonth({
    year: Number(data.today.split("-")[0]),
    month: Number(data.today.split("-")[1]) - 1,
  });
};
/**
 * 更新年月
 */
const changeYearMonth = (change: any) => {
  getYearMonth({ data, ...change });

  emits("changeYearMonth", change);
};
/**
 * 值变化
 */
watch(
  () => props.value,
  (newV) => {
    if (data.selectType === "week") {
      data.tempValue = newV.replace(",", " ~ ");
    } else {
      data.tempValue = formatDateTime(parseDate(newV), data.selectFormat, "");
    }
  }
);
const validDate = reactive({
  spotList: [],
});
watch(
  () => props.spotList,
  (newV) => {
    nextTick(() => {
      validDate.spotList = newV || [];
    });
  },
  {
    immediate: true,
    deep: true,
  }
);
</script>

<template>
  <div class="date-picker">
    <div v-if="props.showSelect" class="date-picker-select">
      <x-select
        v-model:value="data.selectType"
        :popupContainer="props.popupContainer"
        :options="selectOpts"
        @change="selectChange"
      />
    </div>
    <div class="date-picker-popover">
      <x-popover
        trigger="focus"
        placement="leftBottom"
        contentType="none"
        v-model:visible="data.popoverVisible"
        :container="props.popupContainer"
        :triangle="false"
        :allowKeepShowElements="[...data.allMonthListRefList]"
        @visibleChange="popoverVisibleChange"
      >
        <div
          class="x-date-picker-trigger"
          :class="[
            {
              'x-date-picker-trigger_state-actived': data.popoverVisible,
              'show-select': props.showSelect,
            },
          ]"
          ref="triggerRef"
          @click="onClickTrigger"
        >
          <div class="x-date-picker-trigger__item">
            <!-- <input
              class="x-date-picker-trigger__input"
              :class="[
                {
                  'x-date-picker-trigger__input_state-preview':
                    data.previewValue,
                },
              ]"
              type="text"
              ref="inputRef"
              :placeholder="props.placeholder"
              :value="data.previewValue || data.tempValue"
            /> -->
            <div
              ref="inputRef"
              :class="[
                'x-date-picker-trigger__input',
                {
                  'x-date-picker-trigger__input_state-preview':
                    data.previewValue || !data.tempValue,
                },
              ]"
            >
              {{ data.previewValue || data.tempValue || props.placeholder }}
            </div>
          </div>
          <div class="x-date-picker-trigger__suffix">
            <x-icon name="calendar" width="12" height="12" />
          </div>
          <div
            class="x-date-picker-trigger__clear"
            v-if="props.allowClear"
            @click="onClickClear"
          >
            <x-icon name="input_clear" width="12" height="12" />
          </div>
        </div>
        <template #content>
          <div class="popover" @click="onClickKeep">
            <div class="popover__body">
              <DatePanel
                :value="data.tempValue"
                :visible="data.popoverVisible"
                :year="data.year"
                :month="data.month"
                :todaystamp="data.todaystamp"
                :format="props.format"
                :popupContainer="props.popupContainer"
                :showTime="props.showTime"
                :disabledTime="props.disabledTime"
                :disabledDate="props.disabledDate"
                :spotList="validDate.spotList"
                :dateType="data.selectType"
                @updateTempValue="updateTempValue"
                @updatePreviewValue="updatePreviewValue"
                @changeYearMonth="changeYearMonth"
              >
              </DatePanel>
            </div>
            <slot name="renderExtraFooter"></slot>
            <div class="popover__foot">
              <template v-if="props.showTime">
                <template v-if="props.showNow">
                  <div class="button-side" @click="onClickNow">
                    {{ $t("now") }}
                  </div>
                </template>
              </template>
              <template v-else-if="props.showToday">
                <div class="button-side" @click="onClickToday">
                  {{ $t("today") }}
                </div>
              </template>
              <div
                :class="['button-confirm', { disabled: !data.tempValue }]"
                @click="onClickConfirm"
              >
                {{ $t("confirm") }}
              </div>
            </div>
          </div>
        </template>
      </x-popover>
    </div>
  </div>
</template>

<style scoped src="./date-picker.scss"></style>
