<template>
  <Teleport to="body">
    <Transition name="x-modal">
      <div v-show="props.visible" class="x-modal">
        <Transition name="x-modal-mask">
          <div
            v-show="props.visible"
            class="x-modal-mask"
            @click="handleMask"
          ></div>
        </Transition>
        <Transition name="x-modal-content">
          <div
            v-show="props.visible"
            class="x-modal-content"
            :style="contentStyle"
          >
            <div class="content-header" v-if="props.title">
              <div class="content-header-title">{{ props.title }}</div>
              <span
                class="content-header-close"
                v-html="closeText"
                @click="handleCancel"
              ></span>
            </div>
            <div class="content-body" :style="bodyStyle">
              <div class="content-body-slot">
                <span
                  v-if="!props.title"
                  class="content-body-slot-close"
                  v-html="closeText"
                  @click="handleCancel"
                ></span>
                <slot></slot>
              </div>
              <div
                v-if="
                  props.btnOption && Object.keys(props.btnOption).length > 0
                "
                class="content-body-button"
                :style="buttonStyle"
              >
                <x-button
                  type="white"
                  :text="props.btnOption.cancel || $t('cancel')"
                  @click="handleCancel"
                  style="margin-right: 9px"
                />
                <x-button
                  type="blue"
                  :text="props.btnOption.confirm || $t('recognize')"
                  :loading="props.btnOption.confirmLoading"
                  :disabled="props.btnOption.confirmDisabled"
                  @click="handleConfirm"
                />
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import type { PropType } from "vue";
import xButton from "@/components/x-button.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");
const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
  title: {
    type: String,
    required: false,
  },
  width: {
    type: String,
  },
  height: {
    type: String,
  },
  maskClosable: {
    type: Boolean,
    default: true,
  },
  btnOption: {
    type: Object as PropType<{
      cancel?: string;
      confirm?: string;
      confirmDisabled?: boolean;
      confirmLoading?: boolean;
      position: string;
    }>,
  },
  bodyStyle: {
    type: Object,
  },
  radius: {
    type: String,
  },
});
const emits = defineEmits(["update:visible", "cancel", "confirm"]);
const contentStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width};`;
  if (props.height) style += `height: ${props.height};`;
  if (props.radius) style += `border-radius: ${props.radius};`;
  return style;
});
const bodyStyle = computed(() => {
  let style = "";
  if (props.bodyStyle) {
    for (const key of Object.keys(props.bodyStyle)) {
      style += `${key}:${props.bodyStyle[key]};`;
    }
  }
  if (!props.title) {
    style += "border-radius: 10px;";
  }
  return style;
});
const buttonStyle = computed(() => {
  let style = "";
  style += `justify-content: ${props.btnOption?.position};`;
  return style;
});
const closeText = ref("&#10005");
const handleCancel = () => {
  emits("update:visible", false);
  emits("cancel");
};
const handleMask = () => {
  if (props.maskClosable) {
    handleCancel();
  }
};
const handleConfirm = () => {
  emits("confirm");
};
</script>

<style lang="scss" scoped>
.x-modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  &-enter {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-mask {
    @include wh(100%) {
      background-color: rgba(4, 6, 22, 0.5);
    }
    &-enter {
      &-from {
        opacity: 0;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        opacity: 0;
      }
    }
  }
  &-content {
    &-enter {
      &-from {
        transform: scale(0.2);
        transform-origin: left top;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: scale(0.2);
        transform-origin: left top;
      }
    }
    display: flex;
    flex-direction: column;
    @include ct-p;
    background-color: #fff;
    border-radius: 10px;
    .content-header {
      @include fj {
        align-items: center;
      }
      @include wh(100%, 54px) {
        padding: 0 14px;
      }
      @include sc(16px, #242859);
      &-close {
        cursor: pointer;
      }
    }
    .content-body {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex: 1;
      padding: 20px;
      &-slot {
        width: 100%;
        flex: 1;
        &-close {
          @include sc(16px, #242859) {
            position: absolute;
            top: 0;
            right: 0;
            padding: 14px;
            cursor: pointer;
          }
        }
      }
      &-button {
        display: flex;
        @include wh(100%, 32px) {
          margin-top: 18px;
        }
      }
    }
  }
}
</style>
