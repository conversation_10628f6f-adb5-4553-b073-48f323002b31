<template>
  <x-drawer
    :title="$t('roleDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="20px 0 20px 20px"
    width="680px"
  >
    <div class="content">
      <div class="info">
        <div class="info__head">
          <div class="title info__title">
            {{ detail.info.roleName || $t("noData") }}
            <span class="subtitle"
              >（{{ detail.info.roleType || $t("noData") }}）</span
            >
          </div>
          <x-button
            v-if="permitList.includes('sys:role:userAccount')"
            @click="openStaffDrawer(detail.info.roleId)"
            type="blue"
            text="人员"
          />
        </div>
        <div class="subtitle">
          {{ detail.info.entName || $t("noData") }}
        </div>
      </div>
      <div class="content-fun">
        <div class="content-fun-title">{{ $t("functionSetting") }}</div>
        <div class="content-fun-thead">
          <div class="content-fun-thead-th">{{ $t("interface") }}</div>
          <div class="content-fun-thead-td">{{ $t("function") }}</div>
        </div>
        <div
          v-for="item in detail.info?.permitTree?.subPermList || []"
          :key="item.menuId"
          class="content-fun-tbody"
        >
          <div class="content-fun-tbody-th">{{ item.name }}</div>
          <div class="content-fun-tbody-td">
            <div
              v-for="sub in item.subPermList"
              :key="sub.menuId"
              class="content-fun-tbody-td-item"
            >
              {{ sub.name }}
            </div>
          </div>
        </div>
      </div>
      <Staff v-model:show="staffReactive.show" :id="staffReactive.id" />
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { GetRoleDetailResponse } from "@/services/type";
import { reactive, watch } from "vue";
import { getRoleDetail } from "@/services/api";
import { useMainStore } from "@/stores/main";
import xButton from "@/components/x-button.vue";
import xDrawer from "@/components/x-drawer.vue";
import Staff from "./staff.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("roleManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 详情数据
 */
const detail = reactive({
  info: {} as GetRoleDetailResponse,
});

/**
 * 相关操作
 */
// 人员
const staffReactive = reactive({
  show: false,
  id: "",
});
const openStaffDrawer = (id: string) => {
  staffReactive.id = id;
  staffReactive.show = true;
};

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = await getRoleDetail(props.id);
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  margin-right: 20px;
  &-fun {
    padding: 20px;
    margin-top: 15px;
    border-radius: 8px;
    background: #fff;
    &-title {
      @include sc(16px, #242859);
    }
    &-thead {
      margin-top: 16px;
      line-height: 36px;
      @include fj(flex-start) {
        background: linear-gradient(
          180deg,
          rgba(89, 100, 251, 0.12),
          rgba(89, 100, 251, 0.03) 100%
        );
      }
      &-th {
        @include sc(14px, #383838) {
          width: 140px;
          padding: 0 24px;
        }
      }
      &-td {
        @include sc(14px, #383838);
        flex: 1;
        overflow: hidden;
      }
    }
    &-tbody {
      @include fj(flex-start) {
        align-items: center;
        background-color: #fafafa;
        padding: 16px 0;
        position: relative;
      }
      &::after {
        content: "";
        position: absolute;
        left: 139px;
        top: 16px;
        bottom: 16px;
        width: 1px;
        background-color: #dce3fb;
      }
      &-th {
        @include sc(14px, #383838) {
          width: 140px;
          padding: 0 24px;
        }
      }
      &-td {
        @include sc(14px, #383838);
        flex: 1;
        overflow: hidden;
        &-item {
          display: inline-block;
          vertical-align: top;
          margin-left: 24px;
          padding: 10px;
        }
      }
    }
  }
}
.info {
  padding: 20px;
  border-radius: 8px;
  background: #fff;
  &__head {
    display: flex;
    justify-content: space-between;
  }
  &__title {
    flex: 1;
  }
}
.title {
  color: rgb(16, 22, 55);
  font-size: 18px;
  font-weight: 700;
  line-height: 32px;
}
.subtitle {
  color: rgb(94, 94, 94);
  font-size: 14px;
}
</style>
