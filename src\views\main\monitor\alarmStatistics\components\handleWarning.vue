<template>
  <x-modal
    :title="$t('editProcess')"
    width="480px"
    height="456px"
    :visible="props.show"
    :btnOption="btnOption"
    @update:visible="updateVisible"
    @confirm="modalConfirm"
  >
    <div class="handle-warning-modal">
      <x-form ref="formRef" :model="form" :rules="formRules">
        <x-form-item name="description">
          <div class="form-label-top">
            <span class="required"
              >{{ $t("reason") }}/{{ $t("problemDesc") }}</span
            >
          </div>
          <x-textarea
            v-model:value="form.description"
            :maxlength="150"
            :showCount="false"
            :auto-size="{ minRows: 2, maxRows: 5 }"
            :placeholder="$t('PEnter')"
          />
        </x-form-item>
        <x-form-item name="isSolved" style="margin-top: -10px">
          <div class="form-label-bottom">
            <span class="required">{{ $t("status") }}</span>
          </div>
          <x-radio
            v-model:value="form.isSolved"
            :options="warnDealType"
            :gap="16"
            @change="changeStatus"
          />
          <div v-show="showTip" class="form-warn-tip">
            <Transition name="form-warn-tip">
              <span v-show="showTip">{{ $t("vehicleStillAlarm") }}</span>
            </Transition>
          </div>
        </x-form-item>
      </x-form>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { updateWarnStatus } from "@/services/api";
import { warnDealType } from "@/assets/ts/config";
import xModal from "@/components/x-modal";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xTextarea from "@/components/x-textarea.vue";
import xRadio from "@/components/x-radio.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmStatistics");

const formRef = ref<any>();

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  isEnd: {
    type: Number,
    required: true,
  },
});

const btnOption = reactive({
  position: "center" as "center",
  confirmDisabled: false,
});

const formRules = reactive({
  description: [
    ["required", `${$t("PEnter")}${$t("reason")}/${$t("problemDesc")}`],
  ],
});

const form = reactive({
  description: "",
  isSolved: 0,
});

const showTip = ref(false);

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      formRef.value.resetFields();
      form.description = "";
      form.isSolved = 0;
      showTip.value = false;
      btnOption.confirmDisabled = false;
    }
  }
);

const changeStatus = (value: any) => {
  const tipBoolean = value === 1 && props.isEnd === 0 ? true : false;
  showTip.value = tipBoolean;
  btnOption.confirmDisabled = tipBoolean;
};

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const modalConfirm = async () => {
  if (formRef.value.validate()) {
    await updateWarnStatus({
      id: props.id,
      description: form.description,
      isSolved: form.isSolved,
    });
    emits("update:show", false);
    Message("success", $t("dataSaveSuccess"));
    emits("confirm");
  }
};
</script>

<style lang="scss" scoped>
.handle-warning-modal {
  height: 100%;
  padding: 0 10px;
  .form-label-top {
    @include sc(14px, #5e5e5e) {
      margin-bottom: 10px;
    }
  }
  .form-label-bottom {
    @include sc(14px, #383838) {
      display: inline-block;
      margin-right: 20px;
    }
  }
  .required::before {
    content: "*";
    color: #f41515;
  }
  .form-warn-tip {
    @include wh(100%, 24px);
    @include sc(14px, #e24562) {
      line-height: 24px;
    }
    span {
      display: block;
    }
    &-enter-active,
    &-leave-active {
      transition: all 0.4s ease-out;
    }
    &-enter-from,
    &-leave-to {
      opacity: 0;
      transform: translateX(-10px);
    }
  }
  :deep(.x-textarea-content) {
    height: 150px;
    border: 1px solid rgb(220, 220, 220);
  }
  :deep(.x-form-item-control-explain) {
    font-size: 14px;
  }
}
</style>
