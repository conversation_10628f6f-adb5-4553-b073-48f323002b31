<template>
  <div class="tool-box">
    <div class="tool-box-left">
      <div class="tool-box-left-item">
        <div
          class="tool-box-left-item-icon"
          v-if="carInfo.workStatus === 1"
          :class="{ 'is-active': activeButtons.router }"
          @click="handleClickIcon('router')"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <template #content>
              <div style="padding: 8px">显示路线</div>
            </template>
            <x-icon
              width="20px"
              height="20px"
              :name="`map_route`"
            />
          </x-popover>
        </div>

        <div
          class="tool-box-left-item-icon"
          :class="{ 'is-active': activeButtons.block }"
          @click="handleClickIcon('block')"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <template #content>
              <div style="padding: 8px">显示区块</div>
            </template>
            <x-icon
              width="20px"
              height="20px"
              :name="`map_fences`"
            />
          </x-popover>
        </div>
      </div>
    </div>

    <div class="tool-box-right">
      <div class="tool-box-right-item">
        <div
          class="tool-box-right-item-icon"
          v-for="item in rightList"
          :key="item.icon"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <template #content>
              <div style="padding: 8px">{{ item.name }}</div>
            </template>
            <x-icon
              v-if="item.icon !== 'traffic_light'"
              width="20px"
              height="20px"
              :name="item.icon"
              @click="handleClickIcon(item.icon)"
            />
            <template v-if="item.icon === 'traffic_light'">
              <el-image
                v-if="carItem.warnTraffic"
                style="width: 20px; height: 20px"
                @click="handleClickIcon(item.icon)"
                :src="trafficLightSrc"
              />
              <x-icon
                v-else
                width="20px"
                height="20px"
                :name="item.icon"
                @click="handleClickIcon(item.icon)"
              />
            </template>
          </x-popover>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, inject, Ref } from "vue";
import xPopover from "@/components/x-popover.vue";
import { _BaseStatusType } from "@/stores/typings";
import { ElMessage } from "element-plus";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  carItem: {
    type: Object,
    default: () => ({}),
  },
});

console.log(props.carItem, "carItem");

const carInfo = inject("carInfo") as Ref<CarInfo>;
const trafficLightSrc = new URL("@/assets/images/traffic_light.gif", import.meta.url).href;
const emit = defineEmits(["handleClickIcon"]);

const rightList = [
  {
    icon: "navigation",
    name: "导航",
  },
  {
    icon: "video",
    name: "实时视频",
  },
  {
    icon: "remote_control",
    name: "远程控制",
  },
  {
    icon: "traffic_light",
    name: "红绿灯",
  },
];

/** 按钮选中状态 */
const activeButtons = ref({
  router: false,
  block: false,
});

const handleClickIcon = (type: string) => {
  if (type === "navigation") {
    ElMessage.warning("需求开发中");
    return;
  }
  // 切换选中状态
  if (type === "router" || type === "block") {
    activeButtons.value[type] = !activeButtons.value[type];
  }

  // if (type === "traffic_light" && !props.carItem.warnTraffic) {
  //   ElMessage.warning("当前没有红绿灯告警");
  //   return;
  // }

  emit("handleClickIcon", type);
};
</script>
<style scoped lang="scss">
.tool-box {
  position: absolute;
  top: 7px;
  left: 0px;
  width: calc(100% - 574px);
  transform: translateX(287px);
  height: 40px;
  // z-index: 888;
  // background-color: #fff;
  padding: 0px 10px;
  display: flex;
  justify-content: space-between;
  &-left,
  &-right {
    display: flex;
    align-items: center;
    &-item {
      cursor: pointer;
      // width: 80px;
      height: 42px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 5px;
      border-radius: 4px;
      box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
      background: rgb(255, 255, 255);
      &-icon {
        padding: 6px;
        border-radius: 4px;
        &-active {
          background: rgba(229, 230, 232, 0.4);
        }
        &:hover {
          background: rgba(229, 230, 232, 0.4);
        }
        &.is-active {
          background-color: rgba(72, 111, 245, 0.1);
        }
      }
    }
  }
}
</style>
