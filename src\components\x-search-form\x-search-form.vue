<!-- 基于element封装的搜索表单组件 -->
<template>
  <div class="search-form">
    <el-form
      ref="formRef"
      :model="formData"
      class="search-form__content"
      @submit.prevent
    >
      <!-- 左侧表单区域 -->
      <div class="search-form__left">
        <el-row :gutter="gutter">
          <template
            v-for="(item, index) in formItems"
            :key="index"
          >
            <!-- 控制显示隐藏 -->
            <el-col
              v-show="!item.hidden && (!isCollapse || index < showItemCount)"
              :span="item.span || defaultSpan"
            >
              <el-form-item
                :label="showLabel && item.label ? item.label : ''"
                :label-width="labelWidth"
                :prop="item.prop"
              >
                <!-- 默认输入框 -->
                <template v-if="item.type === 'input'">
                  <el-input
                    v-model="formData[item.prop]"
                    :placeholder="item.placeholder || `请输入${item.label}`"
                    :style="{ width: item.width }"
                    clearable
                  >
                    <template
                      v-if="item.suffix"
                      #suffix
                    >
                      <slot :name="`${item.prop}-suffix`">
                        <el-icon><component :is="item.suffix" /></el-icon>
                      </slot>
                    </template>
                  </el-input>
                </template>

                <!-- 选择器 -->
                <template v-else-if="item.type === 'select'">
                  <el-select
                    v-model="formData[item.prop]"
                    :placeholder="item.placeholder || `请选择${item.label}`"
                    :style="{ width: item.width }"
                    clearable
                    :filterable="item.filterable ?? true"
                  >
                    <el-option
                      v-for="option in item.options"
                      :key="option.value"
                      :label="option.label"
                      :value="option.value"
                    />
                  </el-select>
                </template>

                <!-- 日期选择器 -->
                <template v-else-if="item.type === 'date'">
                  <el-date-picker
                    v-model="formData[item.prop]"
                    :type="item.dateType || 'date'"
                    :placeholder="item.placeholder || `请输入${item.label}`"
                    :style="{ width: item.width }"
                    :value-format="item.valueFormat || 'YYYY-MM-DD'"
                    clearable
                  />
                </template>

                <!-- 日期范围选择器 -->
                <template v-else-if="item.type === 'daterange'">
                  <el-date-picker
                    v-model="formData[item.prop]"
                    type="daterange"
                    :start-placeholder="item.startPlaceholder || '开始日期'"
                    :end-placeholder="item.endPlaceholder || '结束日期'"
                    :style="{ width: item.width }"
                    :value-format="item.valueFormat || 'YYYY-MM-DD'"
                    clearable
                  />
                </template>

                <!-- 日期时间范围选择器 -->
                <template v-else-if="item.type === 'datetimerange'">
                  <el-date-picker
                    v-model="formData[item.prop]"
                    type="datetimerange"
                    :start-placeholder="item.startPlaceholder || '开始日期'"
                    :end-placeholder="item.endPlaceholder || '结束日期'"
                    :style="{ width: item.width }"
                    :value-format="item.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                    clearable
                  />
                </template>
                <!-- 自定义插槽 -->
                <template v-else>
                  <slot
                    :name="item.prop"
                    :form-data="formData"
                    :onUpdate="(value) => (formData[item.prop] = value)"
                  ></slot>
                </template>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </div>

      <!-- 右侧按钮区域 -->
      <div class="search-form__right">
        <div class="search-form__btns">
          <slot name="buttons">
            <el-button
              :loading="searchLoading"
              :type="searchBtnType"
              :color="searchBtnColor"
              class="search-form__btn"
              @click="handleSearch"
            >
              {{ searchText }}
            </el-button>
            <el-button
              :loading="resetLoading"
              :type="resetBtnType"
              :color="resetBtnColor"
              class="search-form__btn"
              @click="handleReset"
              style="color: #fff"
            >
              {{ resetText }}
            </el-button>
          </slot>
        </div>

        <!-- 展开/收起按钮 -->
        <el-button
          v-if="showCollapse"
          link
          type="primary"
          class="search-form__collapse-btn"
          @click="toggleCollapse"
        >
          {{ isCollapse ? "展开" : "收起" }}
          <el-icon>
            <arrow-down v-if="isCollapse" />
            <arrow-up v-else />
          </el-icon>
        </el-button>
      </div>
    </el-form>
  </div>
</template>

<script lang="ts" setup>
import { ArrowDown, ArrowUp } from "@element-plus/icons-vue";
import type { FormInstance } from "element-plus";
import { PropType, ref, toRaw, watch } from "vue";

// Props 定义
const props = defineProps({
  /** 表单项配置 */
  formItems: {
    type: Array as PropType<SearchFormItem[]>,
    required: true,
  },
  /** 表单数据 */
  modelValue: {
    type: Object,
    required: true,
  },
  /** 栅格间隔 */
  gutter: {
    type: Number,
    default: 5,
  },
  /** 默认占据的列数 */
  defaultSpan: {
    type: Number,
    default: 4,
  },
  /** 是否显示展开收起 */
  showCollapse: {
    type: Boolean,
    default: false,
  },
  /** 默认显示几项 */
  showItemCount: {
    type: Number,
    default: 6,
  },
  /** 查询按钮文字 */
  searchText: {
    type: String,
    default: "查询",
  },
  /** 重置按钮文字 */
  resetText: {
    type: String,
    default: "重置",
  },
  /** 查询按钮类型 */
  searchBtnType: {
    type: String as PropType<"" | "primary" | "success" | "warning" | "danger" | "info" | "text">,
    default: "primary",
  },
  /** 重置按钮类型 */
  resetBtnType: {
    type: String as PropType<"" | "primary" | "success" | "warning" | "danger" | "info" | "text">,
    default: "",
  },
  /** 查询按钮颜色 */
  searchBtnColor: {
    type: String,
    default: "#5964FB",
  },
  /** 重置按钮颜色 */
  resetBtnColor: {
    type: String,
    default: "#28E09F",
  },
  /** 查询按钮loading */
  searchLoading: {
    type: Boolean,
    default: false,
  },
  /** 重置按钮loading */
  resetLoading: {
    type: Boolean,
    default: false,
  },
  /** 是否显示表单项标签 */
  showLabel: {
    type: Boolean,
    default: false,
  },
  /** label 宽度 */
  labelWidth: {
    type: String,
    default: "auto",
  },
});

const emit = defineEmits(["update:modelValue", "search", "reset"]);

// 表单实例
const formRef = ref<FormInstance>();
// 表单数据
const formData = computed({
  get: () => props.modelValue,
  set: (val) => emit("update:modelValue", val),
});

// 是否折叠
const isCollapse = ref(true);

// 监听外部数据变化
watch(
  () => props.modelValue,
  (val) => {
    formData.value = { ...val };
  },
  { deep: true }
);

// 监听内部数据变化
watch(
  formData,
  (val) => {
    emit("update:modelValue", val);
  },
  { deep: true }
);

// 查询
const handleSearch = () => {
  console.log("搜索参数：", toRaw(formData.value));
  emit("search", toRaw(formData.value));
};

// 重置
const handleReset = async () => {
  if (!formRef.value) return;
  await formRef.value.resetFields();
  emit("reset");
};

// 切换折叠状态
const toggleCollapse = () => {
  isCollapse.value = !isCollapse.value;
};
</script>

<style lang="scss" scoped>
.search-form {
  &__content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    gap: 20px;
  }

  &__left {
    flex: 1;
  }

  &__right {
    display: flex;
    flex-direction: column;
    align-items: flex-end; // 修改这里，让子元素右对齐
    gap: 10px;
  }

  &__btns {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;
  }

  &__btn {
    width: 60px !important;
    height: 32px !important;
    padding: 0 !important;
  }

  &__collapse-btn {
    display: flex !important;
    height: 32px !important;
    align-items: center !important;
    gap: 4px !important;
    color: #5964fb;

    .el-icon {
      margin-left: 4px;
      font-size: 12px;
    }
  }
}
</style>
