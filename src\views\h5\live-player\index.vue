<template>
  <section
    class="live-player"
    id="live-player"
  >
    <div class="camera-front">
      <VideoFlv
        name="实时视频"
        :url="getCameraUrl('1')"
        position="前摄像头"
      />
      <p class="camera-title">前摄像头</p>
    </div>
    <div class="camera-side">
      <div class="camera-side-left">
        <VideoFlv
          name="实时视频"
          :url="getCameraUrl('2')"
          position="左摄像头"
        />
        <p class="camera-title">左摄像头</p>
      </div>
      <div class="camera-side-right">
        <VideoFlv
          name="实时视频"
          :url="getCameraUrl('3')"
          position="右摄像头"
        />
        <p class="camera-title">右摄像头</p>
      </div>
    </div>
    <div class="camera-back">
      <VideoFlv
        name="实时视频"
        :url="getCameraUrl('4')"
        position="后摄像头"
      />
      <p class="camera-title">后摄像头</p>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted, ref, unref } from "vue";
import VideoFlv from "./components/videoFlv.vue";
import { useRoute } from "vue-router";
import { getVideoStreamAddress } from "@/services/api";

const route = useRoute();
const vehNo = ref<string>(route.query.id as string);

const cameraList = ref<{ cameraId: string; flvPath: string; rtmpPath: string }[]>([]);
const getCameraList = async () => {
  if (!vehNo.value) return;
  const res = await getVideoStreamAddress({ vehNo: vehNo.value });
  cameraList.value = res;
};
getCameraList();

const getCameraUrl = (cameraId: string) => {
  return cameraList.value.find((v) => v.cameraId === cameraId)?.flvPath ?? "";
};

let title: string;
onMounted(() => {
  title = document.title;
  document.title = `实时视频【${unref(vehNo)}】`;
});

onUnmounted(() => {
  document.title = title;
});
</script>
<style lang="scss" scoped>
.live-player {
  .camera-side {
    display: flex;
    column-gap: 10px;
    .camera-side-left,
    .camera-side-right {
      flex: 1;
      display: flex;
      flex-direction: column;
    }
  }

  .camera-title {
    padding: 8px 0 8px 4px;
  }
}
</style>
