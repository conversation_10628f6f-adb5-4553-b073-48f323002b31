<template>
  <div class="x-tab-pane" v-if="props.name === currentTab">
    <slot></slot>
  </div>
</template>

<script setup lang="ts">
import { inject } from "vue";

const props = defineProps({
  // 对应 activeKey
  name: {
    type: [String, Number],
  },
  // 选项卡标题
  tab: {
    type: String,
  },
  // 禁用选项
  disabled: {
    type: Boolean,
    default: false,
  },
});

const currentTab = inject("currentTab") as string;
</script>
