<template>
  <div class="inspection-page">
    <template v-if="!loading">
      <div
        class="inspection-page-item"
        v-for="item in inspectionData"
        :key="item.id"
      >
        <div class="image_wrapper">
          <div
            class="image-container"
            @click="handelZoom(item)"
          >
            <el-image
              class="image_item"
              :src="item.picUrl"
              fit="cover"
            />
            <div class="image-label left">清扫前</div>
            <div class="image-time">{{ item.createdTime }}</div>
            <div
              class="magnifier-icon"
              v-if="item.picUrl"
            >
              <x-icon
                width="20px"
                height="20px"
                :name="`zoom_in`"
                color="red"
              />
            </div>
          </div>
          <div
            class="image-container"
            @click="handelZoom(item)"
          >
            <el-image
              class="image_item"
              :src="item.afterCleanPic"
              fit="cover"
            />
            <div class="image-label right">清扫后</div>
            <div class="image-time">-</div>
            <div
              class="magnifier-icon"
              v-if="item.afterCleanPic"
            >
              <x-icon
                width="20px"
                height="20px"
                :name="`zoom_in`"
                color="red"
              />
            </div>
          </div>
        </div>
      </div>
    </template>
    <div v-else>
      <div
        v-if="loading"
        class="loading-more"
      >
        加载中...
      </div>
      <div
        v-if="!hasMore && inspectionData.length > 0"
        class="no-more"
      >
        没有更多数据了
      </div>
    </div>
    <XEmpty
      v-if="inspectionData.length === 0"
      description="暂无数据"
    ></XEmpty>
  </div>

  <el-dialog
    v-model="dialogVisible"
    title="巡检前后对比"
    width="80%"
    append-to-body
  >
    <div class="preview_wrapper">
      <div class="preview-container">
        <el-image
          class="image_item"
          :src="currentItem.picUrl"
          fit="cover"
        />
        <div class="image-label left">清扫前</div>
        <div class="image-time">{{ currentItem.createdTime }}</div>
      </div>
      <div class="preview-container">
        <el-image
          class="image_item"
          :src="currentItem.afterCleanPic"
          fit="cover"
        />
        <div class="image-label right">清扫后</div>
        <div class="image-time">-</div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import type { CarInfo } from "../../../type";
import XEmpty from "@/components/x-empty.vue";
import { getSmartOrderList } from "@/services/api";
import { ref, reactive, toRefs, onMounted, inject, Ref, nextTick, onUnmounted } from "vue";
import { debounce } from "@/assets/ts/utils";
const carInfo = inject("carInfo") as Ref<CarInfo>;
const loading = ref<boolean>(false);
const dialogVisible = ref(false);
const currentItem = ref({});
const inspectionData = ref<any[]>([]);
const currentPage = ref(1);
const hasMore = ref(true);
const ITEM_HEIGHT = 210; // 每个item的总高度（200px + 10px margin-bottom）
const PAGE_SIZE = 10; // 每页显示的数量
const handelZoom = (item: any) => {
  currentItem.value = item;
  dialogVisible.value = true;
};

const scrollLock = ref(false);
const loadMore = async () => {
  if (loading.value || !hasMore.value) return;

  try {
    loading.value = true;
    scrollLock.value = true;

    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    if (!scrollbar) return;

    const { scrollTop, scrollHeight } = scrollbar;

    const res = await getSmartOrderList({
      deviceId: carInfo.value.deviceId,
      page: currentPage.value + 1,
      limit: PAGE_SIZE,
      eventType: [0, 5],
      cleanMode: "1",
    });

    const newList = res.list || [];
    if (newList.length > 0) {
      inspectionData.value = [...inspectionData.value, ...newList];
      currentPage.value++;
      hasMore.value = newList.length === PAGE_SIZE;

      // 等待DOM更新后恢复滚动位置
      await nextTick();
      if (scrollbar) {
        scrollbar.scrollTop = scrollTop;
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error("加载更多数据失败:", error);
  } finally {
    loading.value = false;
    scrollLock.value = false;
  }
};

const load = async () => {
  try {
    loading.value = true;
    currentPage.value = 1;
    hasMore.value = true;
    inspectionData.value = [];

    const res = await getSmartOrderList({
      deviceId: carInfo.value.deviceId,
      page: 1,
      limit: PAGE_SIZE,
      eventType: [0, 5],
      cleanMode: "1",
    });

    inspectionData.value = res.list || [];
    hasMore.value = (res.list || []).length === PAGE_SIZE;
  } catch (error) {
    console.error("加载数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleScroll = debounce(() => {
  if (loading.value || scrollLock.value) return;

  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  if (!scrollbar) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollbar;
  const distanceToBottom = scrollHeight - scrollTop - clientHeight;

  if (distanceToBottom < ITEM_HEIGHT && hasMore.value) {
    loadMore();
  }
}, 200);
onMounted(() => {
  load();
  nextTick(() => {
    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    scrollbar?.addEventListener("scroll", handleScroll);
  });
});

onUnmounted(() => {
  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  scrollbar?.removeEventListener("scroll", handleScroll);
});
defineExpose({
  load,
});
</script>

<script lang="ts">
export default {
  name: "InspectionPage",
};
</script>
<style scoped lang="scss">
.inspection-page {
  width: 100%;
  height: 100%;
  padding: 10px;
  padding-bottom: 100px;
  &-item {
    width: 100%;
    height: 201px;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    .image_wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: row;
      gap: 10px;
      margin-bottom: 10px;
      .image-container {
        position: relative;
        width: 100%;
        height: 100%;
        cursor: pointer;

        &:hover .magnifier-icon {
          opacity: 0.8;
        }

        .image_item {
          width: 100%;
          height: 100%;
          border-radius: 5px;
        }
        .image-time {
          position: absolute;
          bottom: 5px;
          left: 5px;
          font-size: 10px;
          color: #ffffff;
        }
        .image-label {
          position: absolute;
          top: 0;
          left: 0;
          padding: 2px 4px;
          border-radius: 4px;
          color: white;
          font-size: 12px;
          &.left {
            background-color: #8b5cf6;
          }
          &.right {
            background-color: #10b981;
          }
        }

        .magnifier-icon {
          position: absolute;
          top: 50%;
          left: 50%;
          transform: translate(-50%, -50%);
          background: rgba(0, 0, 0, 0.5);
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          opacity: 0;
          transition: opacity 0.3s ease;
        }
      }
    }
  }
}
.preview_wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 10px;

  .preview-container {
    position: relative;
    width: 100%;
    height: 100%;
    cursor: pointer;

    &:hover .magnifier-icon {
      opacity: 0.8;
    }

    .image_item {
      width: 100%;
      height: 100%;
      border-radius: 5px;
    }
    .image-time {
      font-size: 14px;
      color: #555555;
    }
    .image-label {
      position: absolute;
      top: 0;
      left: 0;
      padding: 2px 4px;
      border-radius: 4px;
      color: white;
      font-size: 14px;
      &.left {
        background-color: #8b5cf6;
      }
      &.right {
        background-color: #10b981;
      }
    }
  }
}
</style>
