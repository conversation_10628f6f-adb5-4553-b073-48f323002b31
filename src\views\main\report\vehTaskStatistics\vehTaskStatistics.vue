<template>
  <section class="veh-task-statistics" ref="vehTaskStatisticsRef">
    <div class="veh-task-statistics-top">
      <div class="top-title">{{ $t("vehTaskStatistics") }}</div>
    </div>
    <div class="veh-task-statistics-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.deviceId"
          :options="formOptions.deviceOptions"
          :popupContainer="vehTaskStatisticsRef"
          :placeholder="$t('vehNumber')"
          showSearch
          style="width: 240px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.statisticsType"
          :options="formOptions.statisticsType"
          :popupContainer="vehTaskStatisticsRef"
          class="date-type-selector"
        />
        <DateRangePicker
          v-model:value="searchForm.opDate"
          :rangeType="searchForm.statisticsType"
          :popupContainer="vehTaskStatisticsRef"
          :placeholder="[$t('startTime'), $t('endTime')]"
          @update:value="updateDateRange"
          class="date-range-selector"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:vehCalculate:statisticsInfo')"
          @click="searchList"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="veh-task-statistics-bottom">
      <div class="info-container">
        <template v-if="loading">
          <div class="loading">
            <img src="@/assets/images/loading.gif" alt="" />
          </div>
        </template>
        <template v-else>
          <div class="info-title">
            <div class="info-title-left">
              <div class="info-title-left-top">
                {{ baseData.deviceId || $t("PEntVehNumber") }}
              </div>
              <div class="info-title-left-bottom">
                <span v-if="baseData.workStart && baseData.workEnd"
                  >{{ baseData.workStart }}
                  ————
                  {{ baseData.workEnd }}
                </span>
                <span v-else>{{ $t("PEntTime") }}</span>
              </div>
            </div>
            <div
              class="info-title-right"
              v-if="
                dataLoaded &&
                permitList.includes('sys:vehCalculate:statisticsList')
              "
            >
              <x-button
                type="highlightWhite"
                :text="$t('detail')"
                @click="openDetail"
              />
            </div>
          </div>
          <div class="info-content">
            <div class="info-content-top">
              <template v-for="(item, index) in detailedData" :key="index">
                <div class="info-item">
                  <div class="info-item-left">
                    <div class="info-item-left-top">
                      <div class="ch">{{ item.title }}</div>
                      <div class="en">{{ item.titleEN }}</div>
                    </div>
                    <div class="info-item-left-bottom">
                      {{ handleValue(item.key, item.value, "--") }}
                    </div>
                  </div>
                  <div class="info-item-right">
                    <x-icon :name="item.icon" width="37" height="37" />
                  </div>
                </div>
              </template>
            </div>
            <div class="info-content-bottom">
              <div class="info-item">
                <div class="info-item-title">{{ $t("proInfo") }}</div>
                <div class="info-item-content">
                  <div class="label">{{ $t("proName") }}</div>
                  <div class="value">
                    {{ handleValue("projectName", baseData.projectName, "--") }}
                  </div>
                </div>
                <div class="info-item-content">
                  <div class="label">{{ $t("proArea") }}</div>
                  <div class="value">
                    {{ handleValue("areaName", baseData.areaName, "--") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </div>
    </div>
    <Detail
      v-model:show="recordDetail.show"
      :deviceId="recordDetail.deviceId"
      :startDate="recordDetail.startDate"
      :endDate="recordDetail.endDate"
      :statisticsType="recordDetail.statisticsType"
    />
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive } from "vue";
import { DateRangePicker } from "@/components/x-date-picker";
import { getStatisticsInfo, getVehList } from "@/services/api";
import { useMainStore } from "@/stores/main";
import { i18nSimpleKey } from "@/assets/ts/utils";
import xButton from "@/components/x-button.vue";
import Detail from "./components/detail.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";

const $t = i18nSimpleKey("vehTaskStatistics");
const {
  userInfo: { permitList },
} = useMainStore();

const vehTaskStatisticsRef = ref<any>();

// 面板基础信息
const baseData = reactive({
  deviceId: "",
  workStart: "",
  workEnd: "",
  projectName: "",
  areaName: "",
  statisticsType: 1,
});

// 面板数据信息
const detailedData = reactive([
  {
    key: "taskTimes",
    value: "",
    title: `${$t("taskTimes")}(${$t("times")})`,
    titleEN: "number of times",
    icon: "task_work_times",
  },
  {
    key: "taskDuration",
    value: "",
    title: `${$t("taskDuration")}(h)`,
    titleEN: "duration",
    icon: "task_work_duration",
  },
  {
    key: "mileage",
    value: "",
    title: `${$t("traveMiles")}(km)`,
    titleEN: "mileage",
    icon: "task_mileage",
  },
  {
    key: "finishedWorkArea",
    value: "",
    title: `${$t("cleanArea")}(m²)`,
    titleEN: "area",
    icon: "task_work_area",
  },
  {
    key: "usedWaterAmount",
    value: "",
    title: `${$t("useWater")}(L)`,
    titleEN: "water",
    icon: "task_used_water",
  },
  {
    key: "usedElectricityAmount",
    value: "",
    title: `${$t("useElectric")}(kW·h)`,
    titleEN: "electricity",
    icon: "task_used_electricity",
  },
  {
    key: "litter",
    value: "",
    title: `${$t("garbageNumber")}(L)`,
    titleEN: "waste",
    icon: "task_litter",
  },
]);

// 数据支持
const formOptions = reactive({
  statisticsType: [
    {
      value: "date",
      label: $t("date"),
    },
    {
      value: "month",
      label: $t("month"),
    },
  ],
  deviceOptions: [] as { label: string; value: string }[],
});

// 已加载可用数据 用于详情按钮展示
const dataLoaded = ref(false);
const loading = ref(false);

// 更新时间
const updateDateRange = (value: string) => {
  const [startDate, endDate] = value;
  if (startDate && endDate) {
    const diffMilliseconds =
      new Date(endDate).getTime() - new Date(startDate).getTime();
    const diffYears = diffMilliseconds / (1000 * 60 * 60 * 24 * 365);
    if (diffYears > 1) {
      Message("error", $t("cannotExce365Day"));
      searchForm.opDate = ["", ""];
      return;
    }
  }
};

// 查询
const searchForm = reactive({
  deviceId: "",
  opDate: ["", ""],
  statisticsType: "date",
});
const searchList = async () => {
  if (!searchForm.opDate[0] || !searchForm.opDate[1] || !searchForm.deviceId) {
    Message("error", $t("PfillSearchCriteria"));
    return;
  }
  loading.value = true;
  const param = {
    deviceId: searchForm.deviceId,
    startDate: searchForm.opDate[0] || "",
    endDate: searchForm.opDate[1] || "",
    statisticsType: searchForm.statisticsType === "month" ? 2 : 1, // 1 日期 2 月份
  };
  const result = await getStatisticsInfo(param);
  detailedData.forEach((item) => {
    item.value = result[item.key];
  });
  const {
    deviceId,
    workStart,
    workEnd,
    projectName,
    areaName,
    statisticsType,
  } = result;
  Object.assign(baseData, {
    deviceId,
    workStart,
    workEnd,
    projectName,
    areaName,
    statisticsType,
  });
  dataLoaded.value = true;
  setTimeout(() => {
    loading.value = false;
  }, 1000);
};

// 重置
const resetSearchForm = () => {
  searchForm.deviceId = "";
  searchForm.opDate = ["", ""];
  searchForm.statisticsType = "date";
  Object.keys(baseData).forEach((key) => {
    baseData[key] = "";
  });
  detailedData.forEach((item) => {
    item.value = "";
  });
  dataLoaded.value = false;
};

// 详情
const recordDetail = reactive({
  show: false,
  deviceId: "",
  startDate: "",
  endDate: "",
  statisticsType: 1,
});
const openDetail = () => {
  recordDetail.deviceId = baseData.deviceId || "";
  recordDetail.startDate = baseData.workStart || "";
  recordDetail.endDate = baseData.workEnd || "";
  recordDetail.statisticsType = baseData.statisticsType || 1;
  recordDetail.show = true;
};

// 数据展示处理
const handleValue = (key: string, value: any, defaultValue: any) => {
  const hasVal = value !== null && value !== "" && value !== undefined;
  const fields = [
    "mileage",
    "finishedWorkArea",
    "usedWaterAmount",
    "usedElectricityAmount",
    "litter",
  ];
  if (fields.includes(key)) {
    return hasVal ? Number(value).toFixed(1) : defaultValue;
  } else if (key === "taskDuration") {
    return hasVal ? (value / 60).toFixed(1) : defaultValue;
  } else {
    return hasVal ? value : defaultValue;
  }
};

// 初始化车辆列表
(async () => {
  resetSearchForm();
  formOptions.deviceOptions = (await getVehList()).map((item) => ({
    label: item!,
    value: item!,
  }));
})();
</script>

<style lang="scss" scoped>
.veh-task-statistics {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px) {
      line-height: 36px;
    }
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .date-type-selector {
      width: 50px;
      :deep(.x-select-normal-selector) {
        background-color: #e9eafe;
        border-right: none;
        border-radius: 4px 0 0 4px;
      }
    }
    .date-range-selector {
      width: 320px;
      :deep(.x-date-picker-trigger) {
        border-left: none;
        border-radius: 0 4px 4px 0;
      }
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .info-container {
      @include wh(100%);
      background: rgba(244, 247, 254, 0.7);
      border-radius: 6px;
      position: relative;
      .loading {
        @include ct-p;
        img {
          @include wh(50px);
        }
      }
      .info-title {
        @include ct-f(y) {
          justify-content: space-between;
          padding: 20px;
        }
        @include bis("@/assets/images/task_bg.png");
        &-left {
          &-top {
            @include sc(22px, #5964fb) {
              font-weight: 700;
            }
          }
          &-bottom {
            @include sc(14px, #383838);
          }
        }
      }
      .info-content {
        padding-left: 20px;
        &-top {
          display: flex;
          flex-wrap: wrap;
          width: 100%;
          .info-item {
            display: flex;
            flex-direction: row;
            justify-content: space-between;
            @include wh(calc(25% - 20px), 100px);
            margin-bottom: 20px;
            margin-right: 20px;
            padding: 15px;
            background: #fff;
            border-radius: 4px;
            box-sizing: border-box;
            &-left {
              display: flex;
              justify-content: space-between;
              flex-direction: column;
              &-top {
                .ch {
                  @include sc(14px, #555555);
                }
                .en {
                  margin-top: -8px;
                  @include sc(16px, #e5edff);
                }
              }
              &-bottom {
                @include sc(16px, #383838) {
                  font-weight: 700;
                }
              }
            }
            &-right {
              display: flex;
              align-items: end;
            }
          }
        }
        &-bottom {
          .info-item {
            display: flex;
            flex-direction: column;
            justify-content: space-around;
            @include wh(calc(50% - 20px), 137px);
            padding: 15px;
            background: #fff;
            border-radius: 4px;
            &-title {
              @include sc(13px, #dcdcdc);
            }
            &-content {
              display: flex;
              .label {
                width: 100px;
                @include sc(13px, #9f9fa4);
              }
              .value {
                @include sc(13px, #383838);
                font-weight: 700;
              }
            }
          }
        }
      }
    }
  }
}
</style>
