<template>
  <section class="x-checkbox-tree">
    <div
      v-for="group in props.treeData"
      :key="group.value"
      class="x-checkbox-tree-group"
    >
      <template v-if="group.children.length > 0">
        <div
          class="x-checkbox-tree-group-head"
          @click="changeHandle(group.value, group.children)"
        >
          <div class="x-checkbox-tree-checkbox">
            <x-checkbox
              :stopPropagation="false"
              :checked="all(group)"
              :indeterminate="!all(group) && half(group)"
            />
          </div>
          <div class="x-checkbox-tree-group-head-label">
            {{ group.name }}
          </div>
        </div>
        <div class="x-checkbox-tree-group-body">
          <div
            v-for="item in group.children"
            :key="item.value"
            class="x-checkbox-tree-group-item"
          >
            <div
              class="x-checkbox-tree-group-item-inner"
              @click="changeHandle(item.value, undefined, group)"
            >
              <div class="x-checkbox-tree-checkbox">
                <x-checkbox
                  :stopPropagation="false"
                  :checked="props.value.includes(item.value)"
                />
              </div>
              <div class="x-checkbox-tree-group-item-inner-label">
                {{ item.name }}
              </div>
            </div>
          </div>
        </div>
      </template>
      <template v-else>
        <div class="x-checkbox-tree-group-body">
          <div class="x-checkbox-tree-group-item">
            <div
              class="x-checkbox-tree-group-item-inner"
              @click="changeHandle(group.value)"
            >
              <div class="x-checkbox-tree-checkbox">
                <x-checkbox
                  :stopPropagation="false"
                  :checked="props.value.includes(group.value)"
                />
              </div>
              <div class="x-checkbox-tree-group-item-inner-label">
                {{ group.name }}
              </div>
            </div>
          </div>
        </div>
      </template>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { treePostDfs } from "@/assets/ts/utils";
import xCheckbox from "@/components/x-checkbox.vue";
type PermItem = {
  value: number;
  name: number;
  children: { [key: string]: any }[];
  checkboxStatus?: string;
};
/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    treeData?: PermItem[];
    value: any[];
  }>(),
  {
    treeData: () => [],
    value: () => [],
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value"]);

/**
 * 改变处理
 */
const changeHandle = (id: any, children?: any, parent?: any) => {
  // 数组转set
  const valueSet = new Set(props.value);
  // 删除或添加
  if (valueSet.has(id)) {
    // 删除
    valueSet.delete(id);
    // 下级全部删除
    if (children) {
      treePostDfs(children, "children", (sub: any) => {
        valueSet.has(sub.value) && valueSet.delete(sub.value);
      });
    }
    // 父级删除
    if (parent) {
      valueSet.has(parent.value) && valueSet.delete(parent.value);
    }
  } else {
    // 添加
    valueSet.add(id);
    // 下级全部添加
    if (children) {
      treePostDfs(children, "children", (sub: any) => {
        valueSet.add(sub.value);
      });
    }
    // 父级添加
    if (
      parent &&
      parent.children.every((sub: any) => valueSet.has(sub.value))
    ) {
      valueSet.add(parent.value);
    }
  }
  // 更新组件值
  emits("update:value", [...valueSet]);
};

/**
 * 半选
 */
const half = (group: any) => {
  if (group.children.length > 0) {
    return group.children.some((sub: any) => props.value.includes(sub.value));
  }
  return false;
};
/**
 * 全选
 */
const all = (group: any) => {
  if (group.children.length > 0) {
    return group.children.every((sub: any) => props.value.includes(sub.value));
  }
  return false;
};
</script>

<style lang="scss" scoped>
.x-checkbox-tree {
  user-select: none;
  &-group {
    background: rgb(255, 255, 255);
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    border-radius: 4px;
    margin-top: 16px;

    &-head {
      @include fj(flex-start) {
        align-items: center;
        cursor: pointer;
      }
      @include wh(100%, 36px) {
        background: linear-gradient(
          270deg,
          rgba(232, 233, 251, 0),
          rgba(89, 100, 251, 0.12) 100%
        );
        padding: 0 16px;
      }
      &-label {
        @include sc(14px, #383838);
      }
    }
    &-body {
      padding: 10px 0;
    }
    &-item {
      display: inline-block;
      vertical-align: top;
      &-inner {
        padding: 6px 0;
        @include fj(flex-start) {
          cursor: pointer;
          margin: 0 32px 0 16px;
        }
        &-label {
          @include sc(14px, #5e5e5e);
          line-height: 18px;
        }
      }
    }
  }
}
.x-checkbox-tree-checkbox {
  margin-right: 8px;
}
.x-checkbox-tree-group-head:hover,
.x-checkbox-tree-group-item-inner:hover {
  .x-checkbox-tree-checkbox {
    :deep(.x-checkbox .content-inner) {
      background-color: #5964fb;
      border-color: #5964fb;
    }
    :deep(.x-checkbox .content-inner::after) {
      border-right-color: #fff;
      border-bottom-color: #fff;
    }
  }
}
</style>
