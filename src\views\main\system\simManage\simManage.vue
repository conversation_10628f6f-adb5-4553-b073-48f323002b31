<template>
  <section class="sim-manage" ref="simManageRef">
    <div class="sim-manage-top">
      <div class="top-title">{{ $t("simManage") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:sim:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addSIM.show = true"
        />
        <Add v-model:show="addSIM.show" @confirm="addSIM.confirm" />
      </div>
    </div>
    <div class="sim-manage-middle">
      <div class="sim-select">
        <div class="select-left">
          <x-select
            v-model:value="searchForm.entName"
            :options="searchForm.companyOptions"
            :popupContainer="simManageRef"
            :placeholder="$t('PEnterEnt')"
            style="width: 320px; margin-right: 16px"
            showSearch
          />
          <x-select
            v-model:value="searchForm.useStatus"
            :options="searchForm.statusOptions"
            :popupContainer="simManageRef"
            style="width: 120px; margin-right: 16px"
          />
          <x-input
            v-model:value="searchForm.simId"
            :placeholder="$t('PEnterSim')"
            :filter="simIdFilter"
            style="width: 200px; margin-right: 16px"
          />
        </div>
        <div class="select-right">
          <x-button
            v-if="permitList.includes('sys:sim:list')"
            @click="searchSIMList"
            type="blue"
            :text="$t('search')"
            style="margin-right: 12px"
          />
          <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
        </div>
      </div>
      <div class="date-picker" style="width: 380px; display: flex">
        <div class="date-picker-label">{{ $t("registerDate") }}</div>
        <DateRangePicker
          v-model:value="searchForm.opDate"
          :popupContainer="simManageRef"
          :placeholder="[$t('startDate'), $t('endDate')]"
        />
      </div>
    </div>
    <div class="sim-manage-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #simId="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-simId-popover"
            :container="simManageRef"
          >
            {{ record.simId }}
            <template #content>
              <div class="sim-manage-table-simId-hover-popover">
                {{ record.simId }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #statusText="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.status === 1 ? 'status_blue' : 'status_gray'"
              width="12"
              height="12"
            />
            <span>{{ record.status === 1 ? $t("inUse") : $t("unUsed") }}</span>
          </div>
        </template>
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="simManageRef"
          >
            {{ record.entName }}
            <template #content>
              <div class="sim-manage-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #vehicleNo="{ record }">
          <span>{{ record.vehicleNo }}</span>
        </template>
        <template #registerDateText="{ record }">
          <div class="table-time">
            <span>{{ record.registerDateText }}</span>
          </div>
        </template>
        <template #netOperatorText="{ record }">
          <img
            v-if="record.netOperator == 1"
            src="@/assets/images/sim_ChinaUnicom.png"
          />
          <img
            v-if="record.netOperator == 2"
            src="@/assets/images/sim_ChinaTelecom.png"
          />
          <img
            v-if="record.netOperator == 0"
            src="@/assets/images/sim_ChinaMobile.png"
          />
          <span
            :class="{
              'table-net-mobel': record.netOperator == 0,
              'table-net-telecom': record.netOperator == 2,
              'table-net-unicom': record.netOperator == 1,
            }"
            >{{ record.netOperatorText }}</span
          >
        </template>
        <template #operate="{ record }">
          <div class="table-operate">
            <div class="table-operate-text">
              <span
                v-if="permitList.includes('sys:sim:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:sim:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:sim:delete')"
                style="color: #e24562"
                @click="handleDelete(record.id)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <EditSIM
        v-model:show="editSIM.show"
        :id="editSIM.id"
        @confirm="addSIM.confirm"
      />
      <SimDetail v-model:show="SIMDetail.show" :id="SIMDetail.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import type { SimListRequest } from "@/services/type";
import { ref, reactive } from "vue";
import { simList, compList, delSim } from "@/services/api";
import { useMainStore } from "@/stores/main";
import { DateRangePicker } from "@/components/x-date-picker";
import EditSIM from "./components/editSIM.vue";
import SimDetail from "./components/SIMDetail.vue";
import Add from "./components/addSIM.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import { chineseRegexp } from "@/assets/ts/regexp";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("simManage");

const {
  userInfo: { permitList },
} = useMainStore();

const simManageRef = ref<any>();

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "100",
    },
    {
      key: "simId",
      title: $t("simId"),
      width: "220",
      slots: "simId",
    },
    {
      key: "statusText",
      title: $t("status"),
      width: "120",
      slots: "statusText",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "300",
      slots: "entName",
    },
    {
      key: "vehicleNo",
      title: $t("vehicleNo"),
      width: "180",
      slots: "vehicleNo",
    },
    {
      key: "registerDateText",
      title: $t("registerDate"),
      width: "180",
      slots: "registerDateText",
    },
    {
      key: "netOperatorText",
      title: $t("netOperator"),
      width: "180",
      slots: "netOperatorText",
    },
    {
      key: "operate",
      title: $t("opera"),
      slots: "operate",
      width: "330",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const searchForm = reactive({
  entName: "",
  companyOptions: [] as { label: string; value: string }[],
  statusOptions: [
    {
      value: 0,
      label: $t("unUsed"),
    },
    {
      value: 1,
      label: $t("inUse"),
    },
    {
      value: "",
      label: $t("all"),
    },
  ],
  useStatus: "",
  simId: "",
  opDate: ["", ""],
});

const addSIM = reactive({
  show: false,
  confirm: () => searchSIMList(),
});

const editSIM = reactive({
  show: false,
  id: "",
  confirm: () => searchSIMList(),
});

const SIMDetail = reactive({
  show: false,
  id: "",
});

const searchSIMList = async () => {
  table.loading = true;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.entName,
    simNo: searchForm.simId,
  } as SimListRequest;

  const [opDateStart, opDateEnd] = searchForm.opDate;
  opDateStart && (params.registerDateStart = opDateStart);
  opDateEnd && (params.registerDateEnd = opDateEnd);

  searchForm.useStatus !== "" &&
    (params.status = parseInt(searchForm.useStatus));

  const { totalCount, list } = await simList(params);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            operate: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

const simIdFilter = (v: string) => v.replace(chineseRegexp, "");

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchSIMList();
};

const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.useStatus = "";
  searchForm.simId = "";
  searchForm.opDate = ["", ""];
  searchSIMList();
};

const handleDelete = (id: number) => {
  xModal.confirm({
    title: $t("sureToDelSim"),
    content: <div></div>,
    width: "330px",
    confirm() {
      return delSim([id]).then(() => {
        Message("success", $t("deleteSuccess"));
        searchSIMList();
      });
    },
  });
};

const openEdit = (id: string) => {
  editSIM.id = id;
  editSIM.show = true;
};

const openDetail = (id: string) => {
  SIMDetail.id = id;
  SIMDetail.show = true;
};

(async () => {
  searchSIMList();
  searchForm.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
})();
</script>

<style lang="scss">
.sim-manage-table-company-hover-popover,
.sim-manage-table-simId-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
</style>

<style lang="scss" scoped>
.sim-manage {
  display: flex;
  flex-direction: column;

  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    display: flex;
    flex-direction: column;
    @include wh(100%, 76px) {
      margin-top: 20px;
    }
    .sim-select {
      @include fj;
      margin-bottom: 12px;
      .select-left {
        display: flex;
      }
      .select-right {
        display: flex;
      }
    }
    .date-picker {
      display: flex;

      &-label {
        flex: none;
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
          padding: 6px 8px 6px 0;
        }
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover,
    .table-simId-popover {
      @include ell;
    }
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-net-mobel {
      margin-left: 6px;
      color: rgb(77, 174, 255);
    }
    .table-net-telecom {
      margin-left: 6px;
      color: #004493;
    }
    .table-net-unicom {
      margin-left: 6px;
      color: #f9852e;
    }

    .table-time {
      color: rgb(159, 159, 164);
    }
    .table-operate {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
