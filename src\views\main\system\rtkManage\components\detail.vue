<template>
  <x-drawer
    :title="$t('rtkDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="672px"
  >
    <div class="content">
      <div class="content-body">
        <div class="content-body-title">{{ $t("baseInfo") }}</div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("rtkAccount") }}：</div>
          <div class="content-body-item-value content-body-item-value_bold">
            {{ formatAccount(detail.info.rtkAccount) }}
          </div>
          <div :class="['status', { pale: detail.info.status === 0 }]">
            {{ detail.info.statusText }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("rtkPassword") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.rtkPassword }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("operator") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.netOperatorText }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("startDate") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.registerDateText }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("vehicleNo") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.vehicleNo }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("entName") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.entName }}
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getRTKDetail } from "@/services/api";
import type { GetRTKDetailResponse } from "@/services/type";
import { formatAccount } from "@/assets/ts/utils";
import XDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("rtkManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const detail = reactive({
  info: {} as GetRTKDetailResponse,
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = (await getRTKDetail(props.id)) || {};
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
  border-radius: 8px;
  background: #fff;
  &-body {
    &-title {
      @include sc(16px, #999);
    }
    &-item {
      display: flex;
      margin-top: 20px;
      height: 21px;
      line-height: 21px;
      font-size: 14px;
      &-label {
        width: 80px;
        color: #999;
      }
      &-value {
        color: #383838;
        &_bold {
          @include sc(18px, rgb(16, 22, 55)) {
            font-weight: bold;
          }
        }
      }
      .status {
        @include ct-f(both) {
          margin: -3px 0 0 10px;
        }
        @include wh(58px, 28px) {
          border-radius: 50px;
        }
        @include sc(12px, #5964fb) {
          background: rgba(89, 100, 251, 0.06);
        }
        &.pale {
          background: #f0f0f0;
          color: #c9c3c3;
        }
      }
    }
  }
}
</style>
