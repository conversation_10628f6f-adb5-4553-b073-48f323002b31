<template>
  <section class="video-flv">
    <video :ref="(el) => (videoElementRef = el)" @click="pause">
      Your browser is too old which doesn't support HTML5 video.
    </video>
    <div class="video-poster" v-show="!controls.isPlaying" @click="playHandle">
      <div class="video-poster-center">
        <img src="@/assets/images/video_camera.png" alt="" />
        <span>{{ $t("PClickOpenVideo") }}</span>
      </div>
    </div>
    <div class="video-flv-top">
      <div class="top-left">{{ props.name }}</div>
      <div class="top-right">{{ props.position }}</div>
    </div>
    <x-icon
      class="play-pause-icon"
      :name="controls.isPlaying ? 'video_pause' : 'video_play'"
      width="18"
      height="18"
      @click="playHandle"
    />
  </section>
</template>
<script lang="ts" setup>
import { onMounted, onUnmounted, ref, reactive, watch } from "vue";
import flvjs from "flv.js";
// import flvjs from "flv-h265.js";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

export interface videoInfoType {
  url: string;
  name: string;
  position: string;
}

const props = withDefaults(defineProps<videoInfoType>(), {});
const videoElementRef = ref<HTMLVideoElement>();
const flvPlayer: any = ref();
const controls = reactive({
  isPlaying: false,
});
onMounted(() => {
  initFlv();
});
onUnmounted(() => {
  destroy();
});
watch(
  () => props.url,
  () => {
    destroy();
    initFlv();
  }
);

// 实时降低延迟
const progressHandle = () => {
  const { buffered, currentTime } = flvPlayer.value;
  if (buffered.length > 0) {
    const end = buffered.end(0); // 获取当前buffered值(缓冲区末尾)
    const delta = end - currentTime; // 获取buffered与当前播放位置的差值
    // 跳帧 (延迟过大)
    if (delta > 10 || delta < 0) {
      flvPlayer.value.currentTime = end - 1;
      return;
    }
    // 追帧 (延迟较低)
    if (delta > 1) {
      videoElementRef.value.playbackRate = 1.1;
    } else {
      videoElementRef.value.playbackRate = 1;
    }
  }
};
// 更新到最新帧
const updateVideo = () => {
  const { buffered } = flvPlayer.value;
  if (buffered.length > 0) {
    let end = flvPlayer.value.buffered.end(0) - 1;
    flvPlayer.value.currentTime = end;
  }
};

const initFlv = () => {
  // https://github.com/Bilibili/flv.js/blob/HEAD/docs/api.md
  // https://zhuguibiao.github.io/flv.js/assets/264.flv
  // https://zhuguibiao.github.io/flv.js/assets/265.flv
  if (flvjs.isSupported()) {
    flvPlayer.value = flvjs.createPlayer(
      {
        type: "flv",
        isLive: true,
        hasAudio: false,
        cors: true,
        url: props.url,
      },
      {
        enableWorker: false, // 启用Worker多线程运行flv.js提升解析速度
        enableStashBuffer: false, // 是否启用IO隐藏缓冲区
        stashInitialSize: 128, // 减少首帧显示等待时长
        lazyLoad: false, // 如果有足够的数据可供播放，则中止 http 连接
        lazyLoadMaxDuration: 0, // 60 指示数据要保留多少秒lazyLoad
        lazyLoadRecoverDuration: 0, // 10 指示lazyLoad恢复时间边界（以秒为单位）
      }
    );
    flvPlayer.value.attachMediaElement(videoElementRef.value);

    videoElementRef.value.addEventListener("progress", progressHandle);
    videoElementRef.value.addEventListener("play", updateVideo);
    window.addEventListener("focus", updateVideo);

    // 错误
    flvPlayer.value.on(
      flvjs.Events.ERROR,
      (errorType: any, errorDetail: any, errorInfo: any) => {
        console.log(
          "类型:" + JSON.stringify(errorType),
          "报错内容" + errorDetail,
          "报错信息" + errorInfo
        );
      }
    );
    // 播放统计信息
    // flvPlayer.value.on(
    //   flvjs.Events.STATISTICS_INFO,
    //   (errorType: any, errorDetail: any, errorInfo: any) => {
    //     console.log(
    //       "类型:" + JSON.stringify(errorType),
    //       "报错内容" + errorDetail,
    //       "报错信息" + errorInfo
    //     );
    //   }
    // );
    playHandle().then(pause);
  }
};

const playHandle = () => {
  pause();
  flvPlayer.value.load();
  return flvPlayer.value.play().then(() => {
    controls.isPlaying = true;
  });
};

const pause = () => {
  flvPlayer.value.pause();
  flvPlayer.value.unload();
  controls.isPlaying = false;
};

const destroy = () => {
  if (flvPlayer.value) {
    pause();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
    videoElementRef.value?.removeEventListener("progress", progressHandle);
    videoElementRef.value?.removeEventListener("play", updateVideo);
    window.removeEventListener("focus", updateVideo);
  }
};
</script>

<style lang="scss" scoped>
.video-flv {
  position: relative;
  @include wh(100%);
  img,
  video {
    cursor: pointer;
    @include wh(100%);
    object-fit: fill;
  }
  .video-poster {
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    @include bis("@/assets/images/video_poster.png");
    @include wh(100%);
    &-center {
      user-select: none;
      @include ct-p;
      @include fj {
        flex-direction: column;
        align-items: center;
      }
      height: 84px;
      img {
        @include wh(48px, 52px);
      }
      span {
        color: #fff;
      }
    }
  }
  &-top {
    position: absolute;
    left: 0;
    top: 0;
    @include fj {
      align-items: center;
    }
    @include wh(100%, 12%) {
      padding: 0 14px;
      // background-image: linear-gradient(
      //   to bottom,
      //   rgba(0, 0, 0, 0.4),
      //   rgba(0, 0, 0, 0)
      // );
      color: #fff;
    }
    .top-left {
      font-weight: 700;
    }
    .top-right {
      font-size: 12px;
      text-align: right;
    }
  }
  .play-pause-icon {
    position: absolute;
    bottom: 3%;
    left: 1.6%;
    cursor: pointer;
  }
}
</style>
