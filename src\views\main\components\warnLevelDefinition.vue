<template>
  <x-popover
    trigger="hover"
    placement="bottom"
    contentType="light"
    style="display: inline-block"
  >
    <img class="level-question" src="@/assets/images/alarm_question.png" />
    <template #content>
      <div
        class="table-level-popover-hover"
        v-for="(item, index) in levelTips.info"
        :key="index"
      >
        <x-icon :name="item.icon" width="18" height="18" />
        <span class="table-level-popover-hover-name">
          {{ item.label }}
        </span>
        <span class="table-level-popover-hover-definition">
          {{ $t("define") }}：{{ item.definition }}
        </span>
      </div>
    </template>
  </x-popover>
</template>

<script lang="ts" setup>
import { reactive } from "vue";
import { warnLevelDefType } from "@/assets/ts/config";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const levelTips = reactive({
  info: warnLevelDefType,
  show: false,
});
</script>

<style lang="scss" scoped>
.level-question {
  display: inline-block;
  margin-left: 5px;
  &:hover {
    cursor: pointer;
  }
}
.table-level-popover-hover {
  @include ct-f(y) {
    padding: 8px;
  }
  &-name {
    @include sc(14px, #242859) {
      margin: 0 20px;
    }
  }
  &-definition {
    @include sc(14px, #5e5e5e);
  }
}
</style>
