<template>
  <x-drawer
    :title="$t('viewAlarmDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="672px"
  >
    <div class="content">
      <div class="content-body">
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("alarmName") }}：</div>
          <div class="content-body-item-value content-body-item-value_bold">
            {{ detail.info.warnName }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("vehicleType") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.vehicleTypeStr }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("alarmModule") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.warnModule }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("alarmLevel") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.warnLevelStr }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">
            {{ "客户账号展示并接受" }}：
          </div>
          <div class="content-body-item-value">
            {{
              detail.info.isReceived === 1 ? $t("received") : $t("notReceive")
            }}
          </div>
        </div>
        <div class="content-body-item" style="margin-top: 40px">
          <div class="content-body-item-label">{{ $t("globalPopup") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.popup === 0 ? $t("hasPopup") : $t("noPopup") }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">
            {{ $t("interfaceDocking") }}：
          </div>
          <div class="content-body-item-value">
            {{ detail.info.interfaceDescription }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">
            {{ $t("alarmDescription") }}：
          </div>
          <div class="content-body-item-value">
            {{ detail.info.description }}
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getWarnConfigDetail } from "@/services/api";
import type { WarnConfigDetailResponse } from "@/services/type";
import xDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const detail = reactive({
  info: {} as WarnConfigDetailResponse,
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = (await getWarnConfigDetail({ id: props.id })) || {};
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
  height: 452px;
  border-radius: 8px;
  background: #fff;
  &-body {
    font-size: 14px;
    &-item {
      display: flex;
      margin-top: 20px;
      height: 21px;
      line-height: 21px;
      &-label {
        width: 80px;
        color: #9f9fa4;
      }
      &-value {
        flex: 1;
        color: #383838;
        &_bold {
          @include sc(18px, rgb(16, 22, 55)) {
            font-weight: bold;
          }
        }
      }
    }
  }
}
</style>
