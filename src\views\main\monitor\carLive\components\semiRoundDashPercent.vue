<template>
  <svg
    class="semi-round-dash-percent"
    :width="wh"
    :height="wh"
    viewBox="0 0 96 96"
    fill="none"
  >
    <g>
      <g>
        <!-- 基础半圆(外) -->
        <circle
          :cx="wh / 2"
          :cy="0"
          r="36"
          fill="none"
          stroke-width="1"
          stroke="url('#outside')"
          :stroke-dasharray="strokeDasharray.outBaseRound"
        />
      </g>
      <g>
        <!-- 基础半圆(内) -->
        <circle
          :cx="wh / 2"
          :cy="0"
          r="31"
          fill="none"
          stroke-width="4"
          stroke="#DCDCDC"
          stroke-dasharray="4 1"
        />
      </g>
      <g>
        <!-- 进度条 -->
        <circle
          :cx="wh / 2"
          :cy="0"
          r="31"
          fill="none"
          stroke-width="4"
          stroke="url('#progress')"
          :stroke-dasharray="strokeDasharray.inRound"
          mask="url('#mask')"
        />
      </g>
      <g>
        <!-- 进度条蒙版 -->
        <mask id="mask">
          <circle
            :cx="wh / 2"
            :cy="0"
            r="31"
            fill="none"
            stroke-width="4"
            stroke="url('#progress')"
            stroke-dasharray="4 1"
          />
        </mask>
      </g>
    </g>
    <defs>
      <linearGradient id="progress">
        <stop :stop-color="props.offline ? '#adadad' : '#27d4a1'" />
      </linearGradient>
      <linearGradient id="outside">
        <stop
          offset="0%"
          :stop-color="props.offline ? '#adadad' : 'rgba(89, 100, 251, 0.3)'"
        />
        <stop
          offset="100%"
          :stop-color="props.offline ? '#adadad' : 'rgba(5, 117, 245, 0.3)'"
        />
      </linearGradient>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { computed } from "vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  // 起始范围值
  start: {
    type: Number,
    default: 0,
  },
  // 结束范围值
  end: {
    type: Number,
    default: 100,
  },
  offline: {
    type: Boolean,
    default: () => false,
  },
});
const wh = 96;
const outPerimeter = Math.PI * 2 * 36;
const inPerimeter = Math.PI * 2 * 31;
const percentInRange = computed(() => {
  return (
    Math.min(Math.max(props.percent, props.start), props.end) - props.start
  );
});
const strokeDasharray = computed(() => {
  const range = props.end - props.start;
  const scale =
    percentInRange.value / range > 1 ? 1 : percentInRange.value / range;
  return {
    inRound: `${scale * ((180 / 360) * inPerimeter)} ${inPerimeter}`,
    outBaseRound: `${(180 / 360) * outPerimeter}`,
    inBaseRound: `${(180 / 360) * inPerimeter}`,
  };
});
</script>

<style lang="scss" scoped>
.semi-round-dash-percent {
  transform: rotate(180deg);
  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
