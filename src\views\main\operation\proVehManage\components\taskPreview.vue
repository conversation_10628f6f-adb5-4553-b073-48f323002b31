<template>
  <div
    class="task-preview"
    ref="taskPreviewRef"
  >
    <div
      class="task"
      :style="{
        background: `${getGradientColor(task.scheduleType, bgType)}`,
        color: mainTextColor,
      }"
    >
      <div class="column col-1">
        <span v-if="[4, 6].includes(task.scheduleType)">{{ task._switchType === 4 ? "定时开机" : "定时关机" }}</span>
        <span v-else>{{ getScheduleTypeLabel(task.scheduleType) }}</span>
        <span v-if="task.scheduleType === 1"> -{{ getTaskTypeLabel(task.taskType) }} </span>
      </div>
      <div class="column col-2">
        <div class="container">
          <span class="top">{{ task.executeTimeStart }}</span>
          <span
            class="bottom"
            :style="{ color: subTextColor }"
            >{{ getExecuteDaysLabel(task.executeDays) || "--" }}</span
          >
        </div>
      </div>
      <div class="column col-3">
        <div
          v-if="task.scheduleType === 4"
          class="container"
        >
          <span class="top">{{ task.executeTimeEnd || "--" }}</span>
          <span
            class="bottom"
            :style="{ color: subTextColor }"
            >{{ $t("offTime") }}</span
          >
        </div>
        <div v-else-if="task.scheduleType === 1">
          {{ task.relName || "--" }}
        </div>
        <div v-else>
          {{ task.stationName || "--" }}
        </div>
      </div>
      <div class="column col-4">
        <!-- 预计该任务要做多久的时间 -->
        --
      </div>
      <div
        class="column col-5"
        v-if="props.showSwitch"
      >
        <x-switch
          :checkedValue="1"
          :unCheckedValue="0"
          :unCheckedChildren="$t('off')"
          :checkedChildren="$t('on')"
          :checked="Boolean(task.switchOn)"
          @click="task.switchOn = task.switchOn === 0 ? 1 : 0"
        />
      </div>
      <div
        class="column col-6"
        v-if="props.showSwitchStatus"
      >
        {{ task.switchOn ? $t("open") : $t("close") }}
      </div>
      <div
        class="column col-7"
        v-if="props.showOpera"
      >
        <x-popover
          trigger="hover"
          placement="bottom"
          :container="popupContainer"
        >
          <x-icon
            name="edit"
            width="16"
            height="16"
            style="cursor: pointer"
            @click="onHandleEdit"
          />
          <template #content>
            <div class="task-preview-hover-popover">
              {{ $t("edit") }}
            </div>
          </template>
        </x-popover>
        <x-popover
          trigger="hover"
          placement="bottom"
          :container="popupContainer"
        >
          <x-icon
            name="delete"
            width="16"
            height="16"
            style="margin-left: 15px; cursor: pointer"
            @click="onHandleDelete"
          />
          <template #content>
            <div class="task-preview-hover-popover">
              {{ $t("delete") }}
            </div>
          </template>
        </x-popover>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { computed, type PropType } from "vue";
import {
  getGradientColor,
  getScheduleTypeLabel,
  getTaskTypeLabel,
  getExecuteDaysLabel,
  type TaskItemType,
} from "./utils";
import xPopover from "@/components/x-popover.vue";
import xSwitch from "@/components/x-switch.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  task: {
    type: Object as PropType<TaskItemType>,
    required: true,
  },
  bgType: {
    type: String,
    default: "light",
  },
  // 编辑删除
  showOpera: {
    type: Boolean,
    default: false,
  },
  // 开关
  showSwitch: {
    type: Boolean,
    default: false,
  },
  // 开关状态
  showSwitchStatus: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["editTask", "deleteTask"]);

const onHandleEdit = () => {
  emits("editTask");
};

const onHandleDelete = () => {
  emits("deleteTask");
};

const mainTextColor = computed(() => {
  return props.task.switchOn === 0 && (props.showSwitch || props.showSwitchStatus) ? "#9F9FA4" : "#383838";
});
const subTextColor = computed(() => {
  return props.task.switchOn === 0 && (props.showSwitch || props.showSwitchStatus) ? "#9F9FA4" : "#555";
});
</script>

<style lang="scss">
.task-preview-hover-popover {
  height: 28px;
  padding: 0 4px;
  line-height: 28px;
}
</style>
<style lang="scss" scoped>
.task-preview {
  background: #f5f8fe;
  border-radius: 8px;
  .task {
    display: flex;
    align-items: center;
    width: 100%;
    padding: 0 20px;
    margin: 10px 0;
    height: 66px;
    background: #fff;
    border-radius: 8px;
    @include sc(14px, #383838);
    font-weight: 500;
    .column {
      display: flex;
      align-items: center;
      .container {
        display: flex;
        flex-direction: column;
        .top {
          font-size: 16px;
          font-weight: 700;
        }
        .bottom {
          @include sc(12px, #555);
        }
      }
    }
    .col-1,
    .col-3 {
      flex: 3;
    }
    .col-2 {
      flex: 4;
    }
    .col-4,
    .col-5,
    .col-6 {
      flex: 2;
    }
    .col-7 {
      flex: 1;
      justify-content: flex-end;
    }
  }
}
</style>
