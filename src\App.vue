<template>
  <el-config-provider :locale="zhCn">
    <RouterView v-slot="{ Component }">
      <KeepAlive v-if="route.meta.keepAlive">
        <component :is="Component" />
      </KeepAlive>
      <component
        v-else
        :is="Component"
      />
    </RouterView>
  </el-config-provider>
</template>

<script setup lang="ts">
import { RouterView, useRoute } from "vue-router";
import zhCn from "element-plus/es/locale/lang/zh-cn";
const route = useRoute();
</script>
