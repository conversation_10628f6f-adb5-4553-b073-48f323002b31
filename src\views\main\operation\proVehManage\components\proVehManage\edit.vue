<template>
  <x-drawer
    :title="$t('edit')"
    :visible="props.show"
    :btnOption="btnOption"
    @update:visible="updateVisible"
    @confirm="confirmSetting"
    footerType="following"
    bodyPadding="20px 20px 20px 20px"
    width="544px"
  >
    <div
      class="content"
      ref="contentRef"
    >
      <div class="content-top">{{ props.settingName }}</div>
      <!-- 自动充电/自动加水  TODO: 后续需修改 "automaticWater" -->
      <div v-if="['automaticRecharge', 'automaticWater'].includes(props.settingKey)">
        <div class="content-center">
          <div class="content-center-item left">
            <div class="title">
              <div>{{ $t("status") }}</div>
              <div>
                <x-switch
                  :checkedValue="true"
                  :unCheckedValue="false"
                  :unCheckedChildren="$t('off')"
                  :checkedChildren="$t('on')"
                  :checked="baseInfo.status"
                  @change="baseInfo.status = $event"
                />
              </div>
            </div>
            <div class="image">
              <x-icon
                :name="baseInfo.status ? 'plug_on' : 'plug_off'"
                width="48"
                height="48"
              />
            </div>
          </div>
          <div class="content-center-item right">
            <div class="title">
              {{ $t(props.settingKey === "automaticRecharge" ? "batteryLevelBelow" : "waterLevelBelow") }}
            </div>
            <slider
              :barWidth="120"
              :min="15"
              :max="30"
              v-model:value="baseInfo.sliderVal"
              @change="handleChange"
              style="margin: 30px 0 15px 0"
            />
            <div class="tip">（{{ $t("range") }}:15%{{ $t("to") }}30%）</div>
          </div>
        </div>
        <!-- TODO: 待后端加接口字段排[自动加水] -->
        <div
          class="content-bottom"
          v-if="false"
        >
          <span>{{ props.settingKey === "automaticRecharge" ? "充电点" : "加水点" }}：</span>
          <x-select
            v-model:value="baseInfo.stationId"
            :options="stationOptions"
            :popupContainer="contentRef?.parentElement || undefined"
            style="flex: 1"
          />
        </div>
      </div>
      <!-- 自动关机 -->
      <div v-if="props.settingKey === 'forcedShutdown'">
        <div class="content-center">
          <div class="content-center-item right">
            <div class="title">{{ $t("batteryLevelBelow") }}</div>
            <slider
              :barWidth="120"
              :min="10"
              :max="15"
              v-model:value="baseInfo.sliderVal"
              @change="handleChange"
              style="margin: 30px 0 15px 0"
            />
            <div class="tip">（{{ $t("range") }}:10%{{ $t("to") }}15%）</div>
          </div>
        </div>
      </div>
      <!-- 电量低提醒 -->
      <div v-if="props.settingKey === 'powerReminder'">
        <div class="content-center">
          <div class="content-center-item right">
            <div class="title">{{ $t("batteryLevelBelow") }}</div>
            <div class="btn-box">
              <div
                v-for="(item, index) in settingConfig.list"
                :key="index"
                class="btn"
                :class="{ active: settingConfig.active === index }"
                @click="settingConfig.active = index"
              >
                {{ item }}%
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { getVehStationList, setVehicleSetup } from "@/services/api";
import Message from "@/components/x-message";
import XDrawer from "@/components/x-drawer.vue";
import xSwitch from "@/components/x-switch.vue";
import xIcon from "@/components/x-icon.vue";
import Slider from "./slider.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
import XSelect, { type OptionsType } from "@/components/x-select.vue";
const $t = i18nSimpleKey("proVehManage");

const btnOption = reactive({
  position: "center" as "center",
});

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  vehicleNo: {
    type: String,
    required: true,
  },
  settingName: {
    type: String,
    required: true,
  },
  settingKey: {
    type: String,
    required: true,
  },
  threshold: {
    type: Number,
    required: true,
  },
  status: {
    type: Boolean,
    required: true,
  },
  stationId: {
    type: Number,
    required: true,
  },
});

const contentRef = ref<HTMLElement>();

const settingConfig = reactive({
  list: [20, 25, 30, 35, 40],
  active: 0,
});

const baseInfo = reactive({
  sliderVal: 0,
  status: false,
  stationId: undefined as number | undefined,
});
const stationOptions = ref<OptionsType>([]);

const handleChange = (value: number) => {
  baseInfo.sliderVal = value;
};

const confirmSetting = async () => {
  const electricity =
    props.settingKey === "powerReminder" ? settingConfig.list[settingConfig.active] : baseInfo.sliderVal;
  // TODO: 待后端加接口字段[自定加水]
  const params = {
    vehNo: props.vehicleNo,
    [props.settingKey]: baseInfo.status,
    [`${props.settingKey}Electricity`]: electricity,
  };
  await setVehicleSetup(params);
  Message("success", $t("saveSuccess"));
  emits("update:show", false);
  emits("confirm");
};

const emits = defineEmits(["update:show", "confirm"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const getStationList = async () => {
  if (["automaticRecharge", "automaticWater"].includes(props.settingKey)) {
    const stationType = props.settingKey === "automaticRecharge" ? 2 : 3;
    const list = await getVehStationList(props.vehicleNo);
    stationOptions.value = list
      .filter((item) => item.stationType === stationType)
      .map((item) => ({
        label: item.stationName as string,
        value: item.id as string,
      }));
  }
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      baseInfo.status = props.status;
      baseInfo.sliderVal = props.threshold;
      baseInfo.stationId = props.stationId;
      getStationList();
      if (props.settingKey === "powerReminder") {
        const index = settingConfig.list.indexOf(props.threshold);
        if (index !== -1) {
          settingConfig.active = index;
        }
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  &-top {
    @include sc(14px, #9f9fa4) {
      font-weight: bold;
    }
    margin-bottom: 10px;
  }
  &-center {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    &-item {
      padding: 20px;
      @include wh(100%, 152px);
      border-radius: 8px;
      background-color: #fff;
      @include sc(14px, #383838);
      &.left {
        margin-right: 20px;
        .title {
          display: flex;
          justify-content: space-between;
        }
        .image {
          @include wh(100%);
          @include ct-f;
        }
      }
      &.right {
        .tip {
          @include sc(12px, #9f9fa4);
        }
        .btn-box {
          display: flex;
          margin-top: 20px;
          .btn {
            @include ct-f;
            @include wh(48px, 34px);
            @include sc(14px, #9f9fa4);
            margin-right: 10px;
            border-radius: 4px;
            background: #f4f7fe;
            cursor: pointer;
            &.active {
              background-color: #5964fb33;
              @include sc(14px, rgb(89, 100, 251));
            }
          }
        }
      }
    }
  }
  &-bottom {
    margin-right: 10px;
    padding: 0 20px;
    display: flex;
    align-items: center;
    column-gap: 10px;
    @include wh(100%, 53px) {
      line-height: 53px;
    }
    @include sc(14px, #383838);
    border-radius: 8px;
    background-color: #fff;
  }
}
</style>
