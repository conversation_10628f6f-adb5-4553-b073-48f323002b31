<template>
  <template v-if="props.show">
    <!-- Tab内面板内容 -->
    <div class="content-sites">
      <!-- <div
        class="content-sites-add"
        v-if="/^add|edit$/.test(props.type)"
      >
        <x-button
          type="paleBlue"
          :text="$t('addSite')"
          icon="button_add"
          @click="addSite()"
        />
      </div> -->
      <ul class="content-sites-list">
        <li
          :class="['list-item', { enable: index === sitesEnableIndex }]"
          v-for="(item, index) in props.sites"
          :key="index"
          @click="toggleSite(index)"
          draggable="true"
          @dragstart="handleDragstart($event, index)"
          @dragenter="handleDragenter($event, index)"
          @dragend="handleDragend"
          @dragover="handleDragover"
        >
          <div class="list-item-left">
            <x-icon
              :name="getStationIcon(item.stationType)"
              width="30px"
              height="30px"
            />
          </div>
          <div class="list-item-right">
            <div class="right-name">{{ item.stationName }}</div>
            <div class="right-type">{{ item.parkingList?.length || 0 }}{{ $t("parkingSiteInTotal") }}</div>
            <!-- <x-icon
              v-if="/^add|edit$/.test(props.type)"
              class="right-delete"
              name="del_x_blue"
              width="18px"
              height="18px"
              @click.stop="delSite(index)"
            /> -->
          </div>
        </li>
      </ul>
    </div>
    <!-- 底部表单区域 -->
    <Teleport :to="props.container || 'body'">
      <div
        v-show="showOperaAreaSite"
        :class="[
          'opera-area-frame',
          {
            sites: props.type !== 'view' && showOperaAreaSite,
            ['sites-view']: props.type === 'view' && showOperaAreaSite,
          },
        ]"
        ref="operaAreaFrame"
      >
        <!-- 新增/编辑 -->
        <x-form
          v-if="type !== 'view' && showOperaAreaSite"
          ref="sitesFormRef"
          :model="sitesForm"
          :rules="siteFormRules"
          labelFlex="80px"
        >
          <x-form-item
            :label="$t('stationName')"
            name="stationName"
            labelFlex="55px"
          >
            <x-input
              v-model:value="sitesForm.stationName"
              :maxlength="10"
              :placeholder="$t('PEnterStationName')"
            />
          </x-form-item>
          <x-form-item
            :label="$t('stationType')"
            name="stationType"
            labelFlex="55px"
          >
            <x-select
              :value="sitesForm.stationType"
              @update:value="toggleStationType"
              :options="sitesType"
              :popupContainer="operaAreaFrame"
              :placeholder="$t('PSelectStationType')"
              :disabled="false"
            />
          </x-form-item>
          <div style="display: flex">
            <x-form-item
              :label="$t('longitude')"
              name="longitude"
              labelFlex="55px"
            >
              <x-input
                v-model:value="sitesForm.longitude"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 98px"
                disabled
                :placeholder="$t('PEnterLongitude')"
                @blur="lnglatBlur"
              />
            </x-form-item>
            <x-form-item
              :label="$t('latitude')"
              name="latitude"
              labelFlex="40px"
            >
              <x-input
                v-model:value="sitesForm.latitude"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 98px"
                disabled
                :placeholder="$t('PEnterLatitude')"
                @blur="lnglatBlur"
              />
            </x-form-item>
          </div>
          <!-- <div style="color: #555555; margin-top: -10px; padding-bottom: 10px">
            高精度地图点位
          </div>
          <div style="display: flex">
            <x-form-item label="X点:" name="areaX" labelFlex="40px">
              <x-input
                v-model:value="sitesForm.areaX"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 90px"
                placeholder="请输入X点"
              />
            </x-form-item>
            <x-form-item label="Y点:" name="areaY" labelFlex="40px">
              <x-input
                v-model:value="sitesForm.areaY"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 90px"
                placeholder="请输入Y点"
              />
            </x-form-item>
            <x-form-item label="Z点:" name="areaZ" labelFlex="40px">
              <x-input
                v-model:value="sitesForm.areaZ"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 90px"
                placeholder="请输入Z点"
              />
            </x-form-item>
            <x-form-item label="Yaw角:" name="yaw" labelFlex="60px">
              <x-input
                v-model:value="sitesForm.yaw"
                :maxlength="10"
                :filter="(value: any) => value.replace(notFloatRegexp, '')"
                style="width: 90px"
                placeholder="请输入Y角"
              />
            </x-form-item>
          </div> -->
          <!-- 新协议临时处理 -->
          <!-- <x-form-item label="任务类型" name="cleanType" labelFlex="80px">
            <template class="opera-area-checkbox-container">
              <div
                v-for="(item, index) in siteCleaningType"
                :key="index"
                class="checkbox"
              >
                <x-checkbox
                  :text="item.label"
                  :checked="sitesForm.cleanType?.includes(item.value)"
                  @update:checked="updateSiteCleanType($event, item)"
                />
              </div>
            </template>
          </x-form-item> -->
          <x-form-item
            :label="$t('wayId')"
            name="wayId"
            labelFlex="55px"
          >
            <x-input
              v-model:value="sitesForm.wayId"
              :maxlength="40"
              :placeholder="$t('PEnter')"
            />
          </x-form-item>
          <x-form-item
            :label="$t('picture')"
            name="pic"
            labelFlex="55px"
          >
            <div
              class="pic"
              :class="{ picked: sitesForm.stationPic }"
              @click="showUploadModal"
              :style="{ backgroundImage: `url(${sitesForm.stationPic})` }"
            >
              {{ $t("addPicture") }}
            </div>
          </x-form-item>
          <div
            v-for="(item, index) in sitesForm.parkingList"
            :key="index"
            class="park-item"
          >
            <div class="park-item-title">
              <div class="park-item-title-left">
                <span class="park-item-title-left-text">{{ $t("parkingSpot") }} </span>
              </div>
              <span
                v-if="sitesForm.parkingList.length !== 1"
                v-html="closeText"
                @mousedown="delParkPlace(index)"
                class="park-item-title-right"
              ></span>
            </div>
            <div class="park-item-content">
              <div class="label required">ID</div>
              <x-input
                v-model:value="item.parkNo"
                :maxlength="50"
                :placeholder="$t('PEnterID')"
                @update:value="onInputParkId($event, index)"
                class="content"
                disabled
              />
            </div>
            <!-- <div class="park-item-content">
              <div class="label required">{{ $t("stationName") }}</div>
              <x-input
                v-model:value="item.parkName"
                :maxlength="20"
                :placeholder="$t('PEnterName')"
                class="content"
              />
            </div> -->
            <div
              class="park-item-content"
              v-if="sitesForm.stationType !== 4"
            >
              <div class="label">配件</div>
              <el-select
                v-model="item.chargingId"
                placeholder="请选择配件"
                filterable
                :teleported="false"
                no-data-text="暂无配件"
              >
                <el-option
                  v-for="opt in getChargingOptionsById(item.chargingId)"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                ></el-option>
              </el-select>
            </div>
            <div class="park-item-content">
              <div class="label">车辆</div>
              <el-select
                v-model="sitesForm.vehicleList"
                placeholder="请选择车辆"
                multiple
                collapse-tags
                collapse-tags-tooltip
                clearable
                filterable
                :teleported="false"
                no-data-text="暂无车辆"
              >
                <el-option
                  v-for="opt in vehicleOptions"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                ></el-option>
              </el-select>
            </div>
            <div class="park-item-content">
              <div class="label">避雨车位</div>
              <!-- <x-radio
                :options="rainShelterOptions"
                v-model:value="sitesForm.rainShelter"
              ></x-radio> -->
              <el-select
                v-model="sitesForm.rainShelter"
                placeholder=""
                :teleported="false"
                clearable
              >
                <el-option
                  v-for="opt in rainShelterOptions"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                ></el-option>
              </el-select>
            </div>
          </div>
        </x-form>
        <!-- 详情 -->
        <template v-if="props.type === 'view' && showOperaAreaSite">
          <div class="site-item">
            <span class="site-item-label">{{ $t("stationName") }}:</span>
            <span class="site-item-content">{{ sitesForm.stationName }}</span>
          </div>
          <div class="site-item">
            <span class="site-item-label">{{ $t("stationType") }}:</span>
            <span class="site-item-content">
              {{ sitesType.find((item) => item.value === sitesForm.stationType)?.label ?? "未知" }}</span
            >
          </div>
          <div style="display: flex; justify-content: space-between">
            <div class="site-item">
              <span class="site-item-label">{{ $t("longitude") }}:</span>
              <span class="site-item-content">{{ sitesForm.longitude }}</span>
            </div>
            <div class="site-item">
              <span class="site-item-label">{{ $t("latitude") }}:</span>
              <span class="site-item-content">{{ sitesForm.latitude }}</span>
            </div>
          </div>
          <div class="site-item">
            <span class="site-item-label">{{ $t("wayId") }}:</span>
            <span class="site-item-content">{{ sitesForm.wayId }}</span>
          </div>
          <div class="site-item">
            <div class="site-item-label">{{ $t("picture") }}:</div>
            <div class="site-item-content">
              <div
                class="pic"
                @click="showViewModal"
                v-if="sitesForm.stationPic"
                :style="{ backgroundImage: `url(${sitesForm.stationPic})` }"
              ></div>
            </div>
          </div>
          <div
            v-for="(item, index) in sitesForm.parkingList"
            :key="index"
            class="park-item"
          >
            <div class="park-item-title">
              <div class="park-item-title-left">
                <span class="park-item-title-left-text"
                  >{{ $t("parkingSpot") }}{{ index < 9 ? `0${index + 1}` : `${index + 1}` }}</span
                >
              </div>
            </div>
            <div class="park-item-content">
              <div class="label">ID:</div>
              {{ item.parkNo }}
            </div>
            <!-- <div class="park-item-content">
              <div class="label">{{ $t("stationName") }}:</div>
              {{ item.parkName }}
            </div> -->
            <div
              class="park-item-content"
              v-if="sitesForm.stationType === 2"
            >
              <div class="label">{{ $t("chargingPile") }}:</div>
              {{ item.serialNumber }}
            </div>
            <div class="park-item-content">
              <div class="label">避雨车位:</div>
              {{ sitesForm.rainShelter ? "是" : "否" }}
            </div>
            <div
              class="park-item-content"
              style="align-items: flex-start"
            >
              <div class="label">车辆:</div>
              <ul class="vehicle-list">
                <li
                  class="vehicle-list-item"
                  style="margin-bottom: 10px"
                  v-for="(vehicle, index) in sitesForm.vehicleList"
                  :key="index"
                >
                  <span>{{ vehicleOptions.find((v) => v.value === vehicle)?.label ?? vehicle }}</span>
                </li>
              </ul>
            </div>
          </div>
          <!-- <div class="high-point">
            <div class="high-point-title">高精度地图点位</div>
            <div class="high-point-item">
              <span>X点:</span><span>{{ sitesForm.areaX }}</span>
            </div>
            <div class="high-point-item">
              <span>Y点:</span><span>{{ sitesForm.areaY }}</span>
            </div>
            <div class="high-point-item">
              <span>Z点:</span><span>{{ sitesForm.areaZ }}</span>
            </div>
            <div class="high-point-item">
              <span>角:</span><span>{{ sitesForm.yaw }}</span>
            </div>
          </div> -->
          <!-- 新协议临时处理 -->
          <!-- <div class="task-type">
          <div class="task-type-title">任务类型</div>
          <div class="task-type-item">
            <span
              v-for="(item, index) in siteCleaningTypes"
              :key="index"
              style="margin-right: 15px"
            >
              {{ item.label }}
            </span>
          </div>
        </div> -->
        </template>
      </div>
      <upload-image-modal
        v-model:show="uploadModal.show"
        :fixedWidth="434"
        :fixedHeight="188"
        @imageSaved="saveImg"
      />
      <x-image
        :visible="viewModal.show"
        :src="viewModal.url"
        @cancel="viewModal.show = false"
      />
    </Teleport>
  </template>
</template>
<script lang="tsx" setup>
import { ref, reactive, computed, nextTick, watch } from "vue";
import type { PropType } from "vue";
import { sitesType } from "@/assets/ts/config";
import { markerStyle } from "@/services/wsconfig";
import { notFloatRegexp } from "@/assets/ts/regexp";
import { isLongitude, isLatitude } from "@/assets/ts/validate";
import { getChargingByEntId, getVehicleList } from "@/services/api";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xSelect, { type OptionsType } from "@/components/x-select.vue";
// eslint-disable-next-line no-unused-vars
import xRadio from "@/components/x-radio.vue";
import Message from "@/components/x-message";
import UploadImageModal from "../../../components/uploadImageModal.vue";
import { updateStationPic } from "@/services/api";
import xImage from "@/components/x-image.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  entId: {
    type: [String, Number],
    default: undefined,
  },
  type: {
    type: String as PropType<"add" | "edit" | "view">,
    default: "add",
  },
  mapInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  sites: {
    type: Array as PropType<typeof siteDefaultItem[]>,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  dataLoaded: {
    type: Boolean,
    default: false,
  },
  areaId: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["renderMarker", "toggleMarker", "togglePolyline", "togglePolygon"]);
/**
 * Tab内面板内容
 */
const siteDefaultItem = {
  id: "",
  wayId: "",
  parkNo: "",
  stationName: "场所",
  stationType: 4,
  stationTypeText: "",
  longitude: undefined as number | undefined,
  latitude: undefined as number | undefined,
  areaX: "",
  areaY: "",
  areaZ: "",
  yaw: "",
  rainShelter: false,
  cleanType: [] as any[],
  stationPic: "",
  stationId: "",
  vehicleList: [] as string[],
  parkingList: [
    {
      parkNo: "",
      parkName: "A1",
      chargingId: null as string | null,
    } as any,
  ],
};

// 车辆列表
const vehicleOptions = ref<OptionsType>([]);
// 避雨车位列表
const rainShelterOptions = ref([
  { label: "是", value: true },
  { label: "否", value: false },
]);
const getVehicleOptions = async () => {
  try {
    const res = await getVehicleList({ id: props.areaId.toString() });
    vehicleOptions.value = res.map((item) => ({
      value: item.id,
      label: item.vehicleNo,
    }));
  } catch (error) {
    console.error(error);
  }
};

// 站点图标
const getStationIcon = (stationType: number) => {
  const site = sitesType.find((site) => site.value === stationType);
  return site ? site.icon : "";
};

// 添加泊车位
// const addParkingPlace = () => {
//   const defaultParkName = `A${sitesForm.value.parkingList.length + 1}`;
//   sitesForm.value.parkingList.push({
//     parkNo: "",
//     parkName: defaultParkName,
//     chargingId: null,
//   });
// };

// 删除泊车位
const closeText = ref("&#10005");
const delParkPlace = (index: number) => {
  sitesForm.value.parkingList.splice(index, 1);
};

// 校验当前场所泊车位ID是否重复
const checkDuplicateParkIdOnInput = (index: number) => {
  const parkIds = sitesForm.value.parkingList.map((item) => item.parkNo);
  const currentParkId = sitesForm.value.parkingList[index].parkNo;
  const duplicateIndex = parkIds.findIndex((id, i) => id === currentParkId && i !== index);
  if (duplicateIndex !== -1) {
    Message("error", $t("parkingIDsCanNotBeDuplicated"));
  }
};
const onInputParkId = (event: InputEvent, index: number) => {
  checkDuplicateParkIdOnInput(index);
};

// 校验所有场所充电桩是否重复
const checkDuplicateChargingStation = () => {
  const allChargingStations = new Set();
  let duplicateFound = false;
  props.sites.forEach((site) => {
    site.parkingList.forEach((item) => {
      if (item.chargingId && allChargingStations.has(item.chargingId)) {
        duplicateFound = true;
        item.chargingId = null;
      } else {
        allChargingStations.add(item.chargingId);
      }
    });
  });
  if (duplicateFound) {
    Message("error", $t("chargingStationAlreadyBeenAssociate"));
  }
};
const onSelectChargingStation = () => {
  // checkDuplicateChargingStation();
};

// 查询充电桩列表
const formOptions = reactive({
  chaigingStationList: [] as any[],
});
const getChargingStationList = async (id: number | string, stationType: number | string) => {
  console.log(id, stationType, "id,stationType");
  const chargingList = await getChargingByEntId({
    entId: id as string,
    accessoriesType: stationType as number,
  });
  formOptions.chaigingStationList =
    chargingList?.map((item: any) => ({
      value: item.id,
      label: item.serialNumber,
      isBinding: item.isBinding,
    })) || [];
};
const getChargingOptionsById = (chargingId: string) => {
  return formOptions.chaigingStationList.filter((station) => !station.isBinding || station.value === chargingId);
};

// 选择的场所下标
const sitesEnableIndex = ref(0);

// 添加场所
const addSite = (site?: typeof siteDefaultItem) => {
  if (site) {
    mapAddSite(
      site.longitude as number,
      site.latitude as number,
      {
        dragging: (_: any, marker: any) => {
          site.longitude = marker._position[0];
          site.latitude = marker._position[1];
        },
      },
      sitesType.find((item) => item.value === site.stationType)?.icon || ""
    );
  }
  // 当前必须通过表单验证
  else if (props.sites.length === 0 || sitesFormRef.value.validate()) {
    const isParkingListValid = sitesForm.value?.parkingList.every((item) => {
      return sitesForm.value.stationType === 2
        ? item.parkNo !== "" && item.parkName !== "" && item.chargingId !== null
        : item.parkNo !== "" && item.parkName !== "";
    });
    if (props.sites.length && !isParkingListValid) {
      Message("error", "请将泊车位信息补充完整");
      return;
    }
    const newItem = JSON.parse(JSON.stringify(siteDefaultItem));
    const { lng, lat } = props.mapInfo.map.getCenter();
    newItem.stationName += props.sites.length + 1;
    newItem.longitude = lng;
    newItem.latitude = lat;
    newItem.cleanType = [];
    props.sites.push(newItem);
    sitesEnableIndex.value = props.sites.length - 1;
    nextTick(() => {
      sitesFormRef.value.validate();
    });
    mapAddSite(lng, lat, {
      dragging: (_: any, marker: any) => {
        sitesForm.value.longitude = marker._position[0];
        sitesForm.value.latitude = marker._position[1];
      },
    });
  }
};

// 删除场所
const delSite = (index: number) => {
  props.mapInfo.map.remove(props.mapInfo.markers[index]);
  props.mapInfo.markers.splice(index, 1);
  props.sites.splice(index, 1);
  if (sitesEnableIndex.value > index) {
    sitesEnableIndex.value -= 1;
    emits("toggleMarker", sitesEnableIndex.value);
  } else if (sitesEnableIndex.value === index) {
    sitesEnableIndex.value = 0;
    emits("toggleMarker", sitesEnableIndex.value);
  }
};

// 拖拽相关
let dragIndex: number | null = null;

const handleDragstart = (event: DragEvent, index: number) => {
  dragIndex = index;
};

const handleDragenter = (event: DragEvent, index: number) => {
  if (dragIndex !== null) {
    const dataArr = props.sites;
    const dragging = dataArr[dragIndex];
    dataArr.splice(dragIndex, 1);
    dataArr.splice(index, 0, dragging as any);

    const marker = props.mapInfo.markers[dragIndex];
    props.mapInfo.markers.splice(dragIndex, 1);
    props.mapInfo.markers.splice(index, 0, marker);
    toggleSite(index);
    dragIndex = index;
  }
};

const handleDragover = (event: DragEvent) => {
  event.preventDefault();
};

const handleDragend = () => {
  dragIndex = null;
};

/**
 * 底部表单区域
 */
const siteFormRules = reactive({
  stationName: [["required", "请输入场所名称"]],
  stationType: [["required", "请选择场所类型"]],
  longitude: [
    ["required", "请输入经度"],
    [isLongitude, "范围: [-180,180]"],
  ],
  latitude: [
    ["required", "请输入纬度"],
    [isLatitude, "范围: [-85,85]"],
  ],
  // wayId: [["required", "请输入路线ID"]],
  // cleanType: [
  //   ["required", "请选择任务类型"],
  //   [isChecked, "请选择任务类型"],
  // ],
});
const sitesForm = computed<typeof siteDefaultItem>(() => props.sites[sitesEnableIndex.value]);

const sitesFormRef = ref<any>();
const operaAreaFrame = ref<any>();

// 是否显示表单
const showOperaAreaSite = computed(() => props.show && props.sites?.length > 0);

// const siteCleaningTypes = computed(() =>
//   siteCleaningType.filter((item) =>
//     sitesForm.value.cleanType?.includes(item.value)
//   )
// );

// const updateSiteCleanType = (checked: boolean, item: any) => {
//   const taskTypeSet = new Set(sitesForm.value.cleanType);
//   if (checked) {
//     taskTypeSet.add(item.value);
//   } else {
//     taskTypeSet.delete(item.value);
//   }
//   sitesForm.value.cleanType = Array.from(taskTypeSet);
// };

const sitesMarker = computed(() => props.mapInfo.markers[sitesEnableIndex.value]);
const autoView = [60, 260, 40, 40];
const sitesFitView = () => props.mapInfo.map.setFitView(props.mapInfo.markers, false, autoView);

const toggleStationType = (stationType: number) => {
  sitesForm.value.stationType = stationType;

  // 重置泊车位信息
  sitesForm.value.parkingList = [
    {
      parkNo: "",
      parkName: "A1",
      chargingId: null,
    },
  ];

  const icon = getStationIcon(stationType);
  const { markerDiv } = sitesMarker.value.getExtData();
  sitesMarker.value.setExtData({
    ...sitesMarker.value.getExtData(),
    icon,
  });
  emits("renderMarker", markerDiv, true, icon, 0);
};

const lnglatBlur = () => {
  const lng = Number(sitesForm.value.longitude);
  const lat = Number(sitesForm.value.latitude);
  if (isLongitude(lng) && isLatitude(lat)) {
    sitesMarker.value.moveTo([lng, lat], {
      duration: 1000,
      delay: 0,
      autoRotation: false,
    });
    setTimeout(() => {
      sitesFitView();
    }, 1000);
  }
};

/**
 * 地图操作
 */
const mapAddSite = (
  lng: number,
  lat: number,
  eventHandles?: { [key: string]: Function },
  iconName = "map_garbage",
  number = 2,
  drag = true
) => {
  const markerDiv = document.createElement("div");
  const newMarker = new props.mapInfo.Amap.Marker({
    ...markerStyle,
    content: markerDiv,
    position: [lng, lat],
    cursor: drag ? "move" : "pointer",
    draggable: drag,
    extData: {
      icon: iconName,
      markerDiv: markerDiv,
      markerFocus: true,
      number,
    },
  });
  emits("renderMarker", markerDiv, true, iconName, 0);
  props.mapInfo.map.add(newMarker);
  const _markers = props.mapInfo.markers;
  _markers.push(newMarker);
  emits("toggleMarker", _markers.length - 1);
  if (eventHandles) {
    Object.keys(eventHandles).forEach((eName) => {
      newMarker.on(eName, (e: any) => eventHandles[eName](e, newMarker));
    });
  }
};

const toggleSite = (index: number) => {
  if (props.type === "view") {
    sitesEnableIndex.value = index;
  }
  // 当前必须通过表单验证
  else if (sitesFormRef.value?.validate()) {
    sitesEnableIndex.value = index;
    emits("toggleMarker", index);
    // sitesFitView();
  }
  console.log(props.sites, "props.sites");
  getChargingStationList(props.entId, props.sites[sitesEnableIndex.value].stationType);
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      props.type !== "add" && getVehicleOptions();
      emits("togglePolyline", "none");
      emits("togglePolygon", "none");
      props.mapInfo.polygonEditor.close();
      emits("toggleMarker", sitesEnableIndex.value);
      nextTick(() => {
        sitesFormRef.value?.validate();
      });
    }
  },
  { immediate: true }
);

// 编辑详情页面 地图场所标记回显
watch(
  () => props.dataLoaded,
  (newV) => {
    if (newV && props.type !== "add") {
      props.sites.forEach(addSite);
      viewModal.url = props.sites[sitesEnableIndex.value]?.stationPic || "";
      emits("toggleMarker", sitesEnableIndex.value);
    }
  }
);

// 监听企业名实时更新充电桩列表
watch(
  () => props.entId,
  (newV) => {
    if (newV) {
      getChargingStationList(newV, props.sites[sitesEnableIndex.value].stationType);
    }
  }
);

/**
 * 场所图片
 */
// 图片上传
const uploadModal = reactive({
  show: false,
});
const showUploadModal = () => {
  uploadModal.show = true;
};

const viewModal = reactive({
  show: false,
  url: "",
});
const showViewModal = () => {
  viewModal.show = true;
};

const saveImg = async (formData: FormData) => {
  const stationId = props.sites[sitesEnableIndex.value].id || "";
  const result = await updateStationPic(stationId, formData);
  sitesForm.value.stationPic = result.uploadUrl;
};
</script>

<style scoped lang="scss">
.content-sites {
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);
  overflow: hidden;
  &-add {
    height: 52px;
    width: 100%;
    padding: 10px 0 10px 14px;
  }
  &-list {
    flex: 1 0 auto;
    overflow-y: auto;
    @include wh(100%, 0);
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    padding-bottom: 56px;
    .list-item {
      cursor: pointer;
      @include fj;
      @include wh(100%, 66px);
      &-left {
        @include ct-f;
        @include wh(56px, 100%);
      }
      &-right {
        position: relative;
        height: 100%;
        flex: 1;
        .right-name {
          margin-top: 10px;
          color: #383838;
        }
        .right-type {
          margin-top: 4px;
          @include sc(12px, #555555);
        }
        .right-delete {
          display: none;
          position: absolute;
          top: 11px;
          right: 13px;
        }
      }
      &:hover {
        background-color: #f5f8ff;
        .right-delete {
          display: block;
        }
      }
      &.enable {
        background-color: #e8ecfd;
        .right-name {
          color: #5964fb;
          font-weight: bolder;
        }
      }
    }
  }
}
.opera-area-frame {
  position: absolute;
  right: 366px;
  top: 60px;
  padding: 14px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  &.sites {
    max-height: calc(100% - 80px);
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248));
  }
  &.sites-view {
    width: 320px;
    max-height: calc(100% - 180px);
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248));
    .site-item {
      display: flex;
      height: 47px;
      line-height: 47px;
      &-label {
        width: 75px;
        @include sc(14px, #555);
      }
      &-content {
        width: 100%;
        @include sc(14px, #333);
      }
    }
    .park-item {
      @include sc(14px, #555);
      .label {
        width: 58px;
      }
    }
    .lng-lat,
    .high-point,
    .task-type {
      margin-top: 8px;
      &-title {
        @include wh(100%, 24px) {
          color: #9f9fa4;
          line-height: 24px;
        }
      }
    }
    .lng-lat,
    .high-point {
      &-item {
        display: flex;
        @include wh(100%, 40px) {
          color: #555555;
          line-height: 40px;
        }
        span:first-child {
          width: 62px;
        }
      }
    }
    .task-type {
      &-item {
        display: flex;
        color: #555555;
        line-height: 40px;
      }
    }
  }
  .park-item {
    margin-bottom: 10px;
    padding-bottom: 10px;
    border-radius: 8px;
    background: #f4f7fe;
    &-title {
      display: flex;
      justify-content: space-between;
      height: 22px;
      line-height: 22px;
      &-left {
        width: 72px;
        background: linear-gradient(to right, #5964fb, #f4f7fe);
        border-radius: 8px 0 0 0;
        &-text {
          margin-left: 10px;
          @include sc(12px, #fff);
        }
      }
      &-right {
        display: block;
        margin-right: 15px;
        cursor: pointer;
      }
    }
    &-content {
      display: flex;
      align-items: center;
      padding: 0 10px;
      .label {
        position: relative;
        width: 90px !important;
        padding-left: 8px;
      }
      .required::before {
        position: absolute;
        left: 0;
        top: 0;
        content: "*";
        color: red;
      }
      .content {
        width: 100%;
      }
      &:not(:last-child) {
        margin: 10px 0;
      }
    }
  }
}
.opera-area-checkbox-container {
  @include ct-f(y) {
    height: 30px;
  }
  .checkbox {
    margin-right: 20px;
    :deep(.x-checkbox-text) {
      @include sc(14px, #555);
    }
  }
}
.pic {
  position: relative;
  @include wh(88px, 38px) {
    line-height: 38px;
  }
  @include sc(12px, #5964fb);
  text-align: center;
  border-radius: 4px;
  background: #f4f7fe;
  background-size: cover;
  cursor: pointer;
  &.picked {
    @include sc(12px, #fffffff6);
  }
}
</style>
