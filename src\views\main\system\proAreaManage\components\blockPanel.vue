<template>
  <template v-if="props.show">
    <!-- Tab内面板内容 -->
    <div class="content-block">
      <div
        v-if="/^edit$/.test(props.type)"
        class="content-block-tip"
      >
        {{ "修改区块注意调整相关模板区块" }}
      </div>
      <div
        v-if="/^add|edit$/.test(props.type)"
        class="content-block-header"
      >
        <!-- <x-button
          type="paleBlue"
          :text="'选择区块'"
          icon="button_add"
          @click="addBlock('select')"
        />
        <x-button
          type="paleBlue"
          :text="'自定义区块'"
          icon="button_add"
          @click="addBlock('draw')"
        /> -->
        <x-button
          type="paleBlue"
          :text="'新增区块'"
          icon="button_add"
          @click="addBlock('draw')"
        />
        <div
          class="hide-block"
          @click.stop="handleHideBlock()"
        >
          <x-icon
            :name="hideBlock ? 'eye_invisible' : 'eye'"
            width="16"
            height="16"
          />
          <span>{{ hideBlock ? "显示区块" : "隐藏区块" }}</span>
        </div>
      </div>
      <ul class="content-block-list">
        <li
          :class="['list-item', { enable: index === curBlockIndex }]"
          v-for="(item, index) in blocks"
          :key="index"
          @click="toggleBlock(index)"
          draggable="true"
          @dragstart="handleDragstart($event, index)"
          @dragenter="handleDragenter($event, index)"
          @dragend="handleDragend"
          @dragover="handleDragover"
        >
          <div class="list-item-left">
            <x-icon
              :name="`map_fences${index === curBlockIndex ? '_active' : ''}`"
              width="30px"
              height="30px"
            />
          </div>
          <div class="list-item-right">
            <div class="right-name">{{ item.blockName }}</div>
            <div class="right-type">{{ $t("total") }}{{ item.points.length }}{{ $t("points") }}</div>
            <x-icon
              v-if="/^add|edit$/.test(props.type)"
              class="right-delete"
              name="del_x_blue"
              width="18px"
              height="18px"
              @click.stop="delBlock(index)"
            />
          </div>
        </li>
      </ul>
    </div>
    <!-- 底部表单区域 -->
    <Teleport :to="props.container || 'body'">
      <div
        v-show="showOperaAreaBlock"
        :class="[
          'opera-area-frame',
          {
            block: props.type !== 'view' && showOperaAreaBlock,
            ['block-view']: props.type === 'view' && showOperaAreaBlock,
          },
        ]"
        ref="operaAreaFrame"
      >
        <!-- 新增 -->
        <x-form
          v-if="props.type !== 'view' && showOperaAreaBlock"
          ref="blockFormRef"
          :model="blockFormModel"
          :rules="blockFormRules"
        >
          <x-form-item
            :label="'区块名称'"
            name="blockName"
            labelFlex="80px"
          >
            <x-input
              v-model:value="blockFormModel.blockName"
              :maxlength="20"
              :placeholder="'请输入区块名称'"
            />
          </x-form-item>
          <!-- <div class="block-tip">
            {{ "" }}
          </div>
          <p>{{}}</p> -->
        </x-form>
        <!-- 详情 -->
        <template v-if="props.type === 'view' && showOperaAreaBlock">
          <div class="route-name-item">
            <span>{{ "区块名称" }}：</span><span>{{ blockFormModel.blockName }}</span>
          </div>
        </template>
      </div>
    </Teleport>
  </template>
</template>
<script lang="tsx" setup>
import { watch, computed, ref, nextTick, type PropType, onMounted, render, createVNode, onUnmounted } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");
import Message from "@/components/x-message";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xButton from "@/components/x-button.vue";
import XIcon from "@/components/x-icon.vue";
import { fixZeroToStr } from "@/assets/ts/dateTime";
import { blockPolygonEditorStyle, polylineHoverMarkerStyle } from "@/services/wsconfig";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String as PropType<"add" | "edit" | "view">,
    default: "add",
  },
  mapInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  blocks: {
    type: Array as PropType<typeof blockDefaultItem[]>,
    required: true,
  },
  dataLoaded: {
    type: Boolean,
    default: false,
  },
});
const emits = defineEmits(["delBlock", "toggleMarker", "togglePolyline", "togglePolygon"]);

const hideBlock = ref<boolean>(false);
const handleHideBlock = () => {
  hideBlock.value = !hideBlock.value;
  props.mapInfo.blockPolygons.forEach((polygon: AMap.Polygon, index: number) => {
    if (index !== curBlockIndex.value) hideBlock.value ? polygon.hide() : polygon.show();
  });
  props.mapInfo.blockNames.forEach((marker: AMap.Marker, index: number) => {
    if (index !== curBlockIndex.value) hideBlock.value ? marker.hide() : marker.show();
  });
};

/** 编辑中状态 */
let isEditing = false;
onMounted(() => {
  const blockEditorHandle = (event: any) => {
    isEditing = true;
    if (props.blocks[curBlockIndex.value]) {
      const polygon = event.target;
      if (!polygon.getPath().length) {
        props.mapInfo.blockEditor.open();
        return;
      }

      props.blocks[curBlockIndex.value].points = polygon.getPath().map(({ lng, lat }: any, index: number) => ({
        point: fixZeroToStr(index + 1),
        longitude: lng,
        latitude: lat,
      }));

      if (props.mapInfo.blockNames[curBlockIndex.value]) {
        if (polygon.getPath().length) {
          props.mapInfo.blockNames[curBlockIndex.value].setPosition(polygon.getBounds().getCenter());
        } else {
          props.mapInfo.blockNames.splice(curBlockIndex.value, 1);
        }
      }
    }
  };

  props.mapInfo.blockEditor.on("addnode", blockEditorHandle);
  props.mapInfo.blockEditor.on("removenode", blockEditorHandle);
  props.mapInfo.blockEditor.on("adjust", blockEditorHandle);
  props.mapInfo.blockEditor.on("add", (event: any) => {
    isEditing = false;
    const polygon = event.target;
    polygon.on("mouseover", mouseoverBlock);
    polygon.on("mouseout", mouseoutBlock);
    polygon.setOptions({ cursor: "pointer" });
    polygon.setExtData({ name: props.blocks[curBlockIndex.value].blockName });
    props.mapInfo.blockPolygons.push(polygon);

    const blockName = drawBlockNameMarker(polygon, props.blocks[curBlockIndex.value].blockName);
    props.mapInfo.map.add(blockName);
    props.mapInfo.blockNames.push(blockName);
  });

  window.addEventListener("keydown", escKeydown);
});
onUnmounted(() => window.removeEventListener("keydown", escKeydown));

const escKeydown = (event: KeyboardEvent) => {
  if (event.keyCode === 27 && isEditing) {
    props.blocks[curBlockIndex.value].points = [];
  }
};

/** 绘制名称marker */
const drawBlockNameMarker = (overlay: any, name: string) => {
  const markerDiv = document.createElement("div");
  const _marker = new props.mapInfo.Amap.Marker({
    ...polylineHoverMarkerStyle,
    content: markerDiv,
    position: overlay.getBounds().getCenter(),
    zooms: [12, 20],
    visible: false,
    clickable: false,
    cursor: "pointer",
    extData: {
      ...overlay.getExtData(),
      markerDiv: markerDiv,
      name: name,
    },
  });
  _marker.on("mouseover", mouseoverBlock);
  _marker.on("mouseout", mouseoutBlock);
  render(
    createVNode(PolylineNameMarker, {
      name: name,
      type: "light",
    }),
    markerDiv
  );
  return _marker;
};

/**
 * Tab内面板内容
 */
/** 区块初始数据 */
const blockDefaultItem = {
  id: "0" as string | undefined,
  blockName: "区块",
  proAreaId: undefined as string | undefined,
  drawMode: "select" as "draw" | "select",
  points: [] as { longitude: number; latitude: number }[],
};

const curBlockIndex = ref(0);

/** 添加区块 */
const addBlock = (drawMode: "draw" | "select", block?: typeof blockDefaultItem) => {
  let newItem: typeof blockDefaultItem;
  /** 验证所有区块是否已绘制 */
  const blockValid = props.blocks.every((block) => Boolean(block.points.length));
  if (!blockValid) return Message("error", `请将区块绘制完成`);
  if (block) {
    // 详情|编辑
    const polygon = new props.mapInfo.Amap.Polygon({
      ...blockPolygonEditorStyle.createOptions,
      cursor: "pointer",
      path: block.points.map(({ longitude, latitude }) => [longitude, latitude]),
      extData: {
        name: block.blockName,
      },
    });
    polygon.on("mouseover", mouseoverBlock);
    polygon.on("mouseout", mouseoutBlock);

    props.mapInfo.map.add(polygon);
    props.mapInfo.blockPolygons.push(polygon);

    const blockName = drawBlockNameMarker(polygon, block.blockName);
    props.mapInfo.blockNames.push(blockName);
    props.mapInfo.map.add(blockName);

    emits("togglePolygon", curBlockIndex.value);
  } else if (props.blocks.length === 0 || (blockFormRef.value && blockFormRef.value.validate())) {
    if (drawMode === "draw") {
      props.mapInfo.blockEditor.setTarget(null);
      props.mapInfo.blockEditor.open();
    }
    newItem = JSON.parse(JSON.stringify(blockDefaultItem));
    newItem.drawMode = drawMode;
    newItem.blockName += props.blocks.length + 1;
    props.blocks.push(newItem);
    curBlockIndex.value = props.blocks.length - 1;
    nextTick(() => {
      blockFormRef.value?.validate();
    });
  }
};

const mouseoverBlock = (e: any) => {
  props.mapInfo.blockNames.forEach((blockName: any, index: number) => {
    if (blockName.getExtData().name === e.target.getExtData().name && curBlockIndex.value !== index) {
      blockName.show();
    } else {
      blockName.hide();
    }
  });
};

const mouseoutBlock = () => {
  props.mapInfo.blockNames.forEach((blockName: any) => blockName.hide());
};

/** 点击区块 */
const toggleBlock = (index: number) => {
  const points = JSON.parse(JSON.stringify(props.blocks[curBlockIndex.value].points));
  if (points.at(-1)?.longitude === points.at(-2)?.longitude && points.at(-1)?.latitude === points.at(-2)?.latitude) {
    // 去除最后一个点和倒数第二个点的坐标相同
    points.splice(-1, 1);
  }
  if (points.length < 3) {
    Message("error", `请将区块绘制完成`);
    props.blocks[curBlockIndex.value].points = [];
    props.mapInfo.blockEditor.setTarget(null);
    props.mapInfo.blockEditor.open();
    return;
  }
  // props.mapInfo.blockNames.forEach((marker: any) => marker.hide());
  // props.mapInfo.blockNames[index] && props.mapInfo.blockNames[index].show();
  if (props.type === "view") {
    curBlockIndex.value = index;
  }
  // 当前必须通过表单验证
  else if (blockFormRef.value?.validate()) {
    curBlockIndex.value = index;
    emits("togglePolygon", index);
  }
};

/** 删除区块 */
const delBlock = (index: number) => {
  props.blocks.splice(index, 1);
  if (props.mapInfo.blockPolygons[index]) {
    props.mapInfo.map.remove(props.mapInfo.blockPolygons[index]);
    props.mapInfo.blockPolygons.splice(index, 1);
    props.mapInfo.map.remove(props.mapInfo.blockNames[index]);
    props.mapInfo.blockNames.splice(index, 1);
  } else {
    props.mapInfo.blockEditor.close();
  }
  if (curBlockIndex.value > index) {
    curBlockIndex.value -= 1;
    emits("togglePolygon", curBlockIndex.value);
  } else {
    curBlockIndex.value = 0;
    emits("togglePolygon", curBlockIndex.value);
  }
  if (props.blocks.length === 0) {
    emits("togglePolygon", "none");
  }
};

//#region 拖拽相关
let dragIndex: number | null = null;

const handleDragstart = (event: DragEvent, index: number) => {
  dragIndex = index;
};

const handleDragenter = (event: DragEvent, index: number) => {
  if (dragIndex !== null) {
    const dataArr = props.blocks;
    const dragging = dataArr[dragIndex];
    dataArr.splice(dragIndex, 1);
    dataArr.splice(index, 0, dragging as any);
    const polygon = props.mapInfo.blockPolygons[dragIndex];
    props.mapInfo.blockPolygons.splice(dragIndex, 1);
    props.mapInfo.blockPolygons.splice(index, 0, polygon);
    const blockName = props.mapInfo.blockNames[dragIndex];
    props.mapInfo.blockNames.splice(dragIndex, 1);
    props.mapInfo.blockNames.splice(index, 0, blockName);
    toggleBlock(index);
    dragIndex = index;
  }
};

const handleDragover = (event: DragEvent) => {
  event.preventDefault();
};

const handleDragend = () => {
  dragIndex = null;
};
//#endregion

//#region 底部表单区域
const blockFormRef = ref<InstanceType<typeof xForm>>();
const blockFormModel = computed(() => props.blocks[curBlockIndex.value]);
const blockFormRules = {
  blockName: [["required", "请输入区块名称"]],
};
//#endregion

const showOperaAreaBlock = computed(() => props.show && props.blocks?.length > 0);

watch(
  () => props.show,
  (newV) => {
    if (newV && props.type !== "view") {
      curBlockIndex.value = 0;
      emits("toggleMarker", "none");
      emits("togglePolyline", "none");
      props.mapInfo.polygonEditor.close();
      emits("togglePolygon", curBlockIndex.value);
      // props.mapInfo.blockNames.forEach((marker: any) => marker.hide());
      // props.mapInfo.blockNames[curBlockIndex.value] && props.mapInfo.blockNames[curBlockIndex.value].show();
      nextTick(() => {
        blockFormRef.value?.validate();
      });
    }
  },
  { immediate: true }
);

// 编辑|详情页面 地图路线标记回显
watch(
  () => props.dataLoaded,
  (newV) => {
    if (newV && props.type !== "add") {
      props.blocks.forEach((item) => addBlock(item.drawMode ?? "draw", item));
      props.mapInfo.blockEditor.close();
    }
  }
);
/** 实时更新下标 */
watch(curBlockIndex, (newV) => (props.mapInfo.curBlockIndex = newV));
</script>

<style scoped lang="scss">
.content-block {
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);
  overflow: hidden;
  &-tip {
    padding: 10px 0 0 20px;
    @include sc(12px, rgb(127, 131, 190));
  }
  &-header {
    position: relative;
    height: 52px;
    width: 100%;
    padding: 10px 0 10px 14px;
    display: flex;
    column-gap: 10px;
  }
  .hide-block {
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
    z-index: 1;
    cursor: pointer;
    display: flex;
    align-items: center;
    column-gap: 4px;
    font-size: 12px;
    user-select: none;
  }

  &-list {
    flex: 1 0 auto;
    overflow-y: auto;
    @include wh(100%, 0);
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    padding-bottom: 56px;
    .list-item {
      cursor: pointer;
      @include fj;
      @include wh(100%, 66px);
      &-left {
        @include ct-f;
        @include wh(56px, 100%);
      }
      &-right {
        position: relative;
        height: 100%;
        flex: 1;
        .right-name {
          margin-top: 10px;
          color: #383838;
        }
        .right-type {
          margin-top: 4px;
          @include sc(12px, #555555);
        }
        .right-delete {
          display: none;
          position: absolute;
          top: 11px;
          right: 13px;
        }
      }
      &:hover {
        background-color: #f5f8ff;
        .right-delete {
          display: block;
        }
      }
      &.enable {
        background-color: #e8ecfd;
        .right-name {
          color: #5964fb;
          font-weight: bolder;
        }
      }
    }
  }
}

.opera-area-frame {
  position: absolute;
  right: 366px;
  bottom: 70px;
  padding: 14px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  &.block {
    @include wh(350px, 100px);
  }
  &.block-view {
    @include wh(400px, 162px) {
      padding-left: 34px;
    }
    .task-type {
      &-item {
        display: flex;
        color: #555555;
        line-height: 40px;
      }
    }
    .route-name-item,
    .car-route-name-item {
      @include wh(100%, 47px);
      span {
        line-height: 47px;
      }
      span:first-child {
        color: #9f9fa4;
      }
      span:last-child {
        color: rgb(51, 51, 51);
      }
    }
    .clean-type-item {
      @include wh(100%, 47px);
      span {
        line-height: 47px;
        color: rgb(51, 51, 51);
      }
      span:first-child {
        color: #9f9fa4;
      }
    }
  }
  .block-tip {
    @include sc(12px, rgb(160, 165, 239));
  }
}
.opera-area-checkbox-container {
  @include ct-f(y) {
    height: 30px;
  }
  .checkbox {
    margin-right: 20px;
    :deep(.x-checkbox-text) {
      @include sc(14px, #555);
    }
  }
}
</style>
