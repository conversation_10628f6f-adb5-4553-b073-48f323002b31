<template>
  <Transition name="modal">
    <div v-show="_visible" class="modal">
      <Transition name="modal-mask">
        <div v-show="_visible" class="modal-mask" @click="handleCancel"></div>
      </Transition>
      <Transition name="modal-content">
        <div v-show="_visible" class="modal-content" :style="contentStyle">
          <sub-title :showIcon="false" class="content-header">{{
            props.title
          }}</sub-title>
          <div class="content-body">
            <div class="content-body-slot">
              <props.content v-if="props.content" />
            </div>
            <div
              v-if="props.confirm || props.cancel"
              class="content-body-button"
            >
              <x-button
                v-if="props.cancel"
                type="white"
                :text="props.cancelText"
                @click="handleCancel"
                style="margin-right: 9px"
              />
              <x-button
                v-if="props.confirm"
                type="blue"
                :text="props.confirmText"
                @click="handleConfirm"
              />
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import type { PropType } from "vue";
import xButton from "@/components/x-button.vue";
import subTitle from "../../views/screen/components/subTitle.vue";

const props = defineProps({
  container: {
    type: HTMLElement,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String as PropType<"warn" | "success" | "error">,
    default: "warn",
  },
  width: {
    type: String,
    default: "424px",
  },
  content: {
    type: Object,
    required: false,
  },
  confirm: {
    type: Function,
  },
  confirmText: {
    type: String,
    default: () => "确认",
  },
  cancel: {
    type: Function,
  },
  cancelText: {
    type: String,
    default: () => "取消",
  },
  closeBeforeConfirm: {
    type: Boolean,
    default: false,
  },
});
const contentStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width}`;
  return style;
});
const handleClose = () => {
  _visible.value = false;
  setTimeout(() => {
    document.body.removeChild(props.container);
  }, 300);
};
const handleCancel = () => {
  if (props.cancel) {
    props.cancel();
    handleClose();
  } else {
    handleClose();
  }
};
const handleConfirm = () => {
  if (props.confirm) {
    if (props.closeBeforeConfirm) {
      handleClose();
      props.confirm();
    } else {
      props.confirm();
      handleClose();
    }
  } else {
    handleClose();
  }
};
const _visible = ref(false);
onMounted(() => {
  _visible.value = true;
});
</script>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  &-enter {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-mask {
    @include wh(100%) {
      // background-color: rgba(4, 6, 22, 0.5);
    }
    //  &-enter {
    //    &-from {
    //      opacity: 0;
    //    }
    //    &-active {
    //      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    //    }
    //  }
    //  &-leave {
    //    &-active {
    //      transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
    //    }
    //    &-to {
    //      opacity: 0;
    //    }
    //  }
  }
  &-content {
    &-enter {
      &-from {
        transform: scale(0.2);
        transform-origin: left top;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: scale(0.2);
        transform-origin: left top;
      }
    }
    display: flex;
    flex-direction: column;
    @include ct-p;
    @include wh(363px, 21vh);
    @include bis("@/assets/images/screen_modal_confirm.png");
    .content-header {
      width: 240px;
      margin-left: 10px;
      margin-top: 0.5vh;
    }
    .content-body {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex: 1;
      &-slot {
        width: 100%;
        @include ct-f;
        flex: 1;
      }
      &-button {
        @include ct-f;
        @include wh(100%, 3.2vh) {
          margin: 2vh 0;
        }
      }
    }
    :deep(.sub-title span) {
      display: inline-block;
      margin-top: 1vh;
      padding-left: 20px;
    }
  }
}
</style>
