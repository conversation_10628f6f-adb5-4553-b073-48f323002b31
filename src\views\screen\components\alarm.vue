<template>
  <section class="alarm">
    <sub-title>告警</sub-title>
    <ul class="alarm-msg">
      <li class="alarm-msg-item" v-for="(item, index) in messages" :key="index">
        <div class="item-name">{{ item.carName }}</div>
        <div class="item-msg">{{ item.message }}</div>
        <div class="item-time">{{ item.dateTime }}</div>
      </li>
    </ul>
  </section>
</template>
<script lang="ts" setup>
import { ref, watch } from "vue";
import subTitle from "./subTitle.vue";
import { useMainStore } from "@/stores/main";

const messages = ref<{
  carName: string;
  message: string;
  dateTime: string;
}>([]);
const { socket } = useMainStore();
watch(
  () => socket.screenAlarm,
  (newV) => {
    messages.value = newV.map(({ device_id, message, time }) => ({
      carName: device_id,
      message,
      dateTime: time,
    }));
  }
);
</script>

<style lang="scss" scoped>
.alarm {
  @include wh(100%, 21vh);
  &-msg {
    @include wh(100%) {
      padding: 0 14px;
      overflow-y: overlay;
      scrollbar-width: none;
    }
    &-item {
      @include fj {
        align-items: center;
      }
      @include wh(100%, 4.4vh) {
        padding: 0 16px;
        margin-top: 0.5vh;
        border: 1px solid #455a86;
        border-radius: 8px;
        background: linear-gradient(
          to left,
          rgba(120, 122, 140, 0.02),
          rgba(109, 112, 157, 0.37)
        );
      }
      .item-name {
        width: 100px;
        @include sc(1.6vh, rgb(248, 250, 255)) {
          font-weight: bolder;
        }
      }
      .item-msg {
        flex: 1;
        @include sc(1.4vh, rgb(248, 250, 255));
      }
      .item-time {
        @include sc(1.4vh, rgba(226, 229, 246, 0.85));
        width: 108px;
      }
    }
  }
}
</style>
