import * as echarts from "echarts";

export default function (el: HTMLElement) {
  // 初始化echart实例
  const echartInstance = echarts.init(el);

  // 设置选项
  const setOptions = (options: echarts.EChartsOption) => {
    echartInstance.setOption(options);
  };

  // echart重新渲染
  const updateSize = () => {
    echartInstance.resize();
  };

  // 浏览器窗口变化重新渲染，宽度设为非固定值可达到响应式效果
  window.addEventListener("resize", () => {
    echartInstance.resize();
  });

  return {
    echartInstance,
    setOptions,
    updateSize,
  };
}
