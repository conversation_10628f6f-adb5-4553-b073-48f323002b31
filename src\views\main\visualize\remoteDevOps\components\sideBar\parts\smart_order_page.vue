<template>
  <div class="smart-order-page">
    <template v-if="!loading">
      <div
        class="smart-order-page-item"
        v-for="(item, index) in orderList"
        :key="index"
      >
        <div class="smart-order-page-item-image_wrapper">
          <div class="image-container">
            <el-image
              :src="item.picUrl"
              fit="cover"
              :preview-src-list="[item.picUrl]"
              :preview-teleported="true"
            >
              <template #placeholder>
                <div class="image-placeholder">
                  <XIcon
                    :width="20"
                    :height="20"
                    name="loading"
                  />
                </div>
              </template>
              <template #error>
                <div class="image-error">
                  <XIcon
                    :width="20"
                    :height="20"
                    name="alarm_img"
                  />
                </div>
              </template>
            </el-image>
            <div class="image-time">{{ item.createdTime }}</div>
          </div>
        </div>

        <div class="smart-order-page-item-info">
          <div class="smart-order-page-item-info-type">
            <span class="smart-order-page-item-info-type-label">工单类型：</span>
            <span class="smart-order-page-item-info-type-value">{{ showType(item.eventType) }}</span>
          </div>
          <div
            class="smart-order-page-item-info-result"
            :class="{ 'is-submit': item.isSubmit === 1 }"
          >
            <span class="smart-order-page-item-info-result-label"><span class="requried">*</span>结果：</span>
            <el-radio-group
              v-model="item.recognizeResult"
              :disabled="!(item.isSubmit === 0 && item.garbageReview === 1)"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
            <x-button
              class="submit-button"
              v-if="item.isSubmit === 0 && item.garbageReview === 1"
              type="green"
              text="提交"
              size="small"
              @click="handleSubmit(item)"
            />
          </div>
          <div
            v-if="item.recognizeResult === 1"
            class="smart-order-page-item-info-result"
            :class="{ 'is-submit': item.recognizeResult === 1 }"
          >
            <span
              class="smart-order-page-item-info-result-label"
              style="padding-left: 5px"
              >扫净：</span
            >
            <el-radio-group
              v-model="item.isClean"
              :disabled="!(item.isSubmit === 0 && item.garbageReview === 1)"
            >
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    </template>
    <div v-else>
      <div
        v-if="loading"
        class="loading-more"
      >
        加载中...
      </div>
      <div
        v-if="!hasMore && orderList.length > 0"
        class="no-more"
      >
        没有更多数据了
      </div>
    </div>
    <XEmpty
      v-if="orderList.length === 0"
      description="暂无数据"
    ></XEmpty>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, inject, type Ref, nextTick, onUnmounted } from "vue";
import { getSmartOrderList, upDateSmartOrderDetail } from "@/services/api";
import XEmpty from "@/components/x-empty.vue";
import { ElMessage } from "element-plus";
// import { Loading, Picture } from '@element-plus/icons-vue';
import type { CarInfo } from "../../../type";
import { debounce } from "@/assets/ts/utils";
const carInfo = inject("carInfo") as Ref<CarInfo>;
import XIcon from "@/components/x-icon.vue";
const orderList = ref<any[]>([]);
const loading = ref<boolean>(false);
const hasMore = ref(true);
const currentPage = ref(1);
const scrollLock = ref(false);

const ITEM_HEIGHT = 210; // 每个item的总高度（200px + 10px margin-bottom）
const PAGE_SIZE = 10; // 每页显示的数量

const showType = (type: number) => {
  switch (type) {
    case 0:
      return "垃圾";
    case 1:
      return "树叶";
    case 2:
      return "果皮";
    case 3:
      return "废纸";
    case 4:
      return "瓶子";
    case 5:
      return "垃圾";
    case 6:
      return "管线";
    case 18:
      return "碰撞";
    case 101:
      return "无人机";
    case 102:
      return "单车";
    default:
      return "未知";
  }
};

const handleScroll = debounce(() => {
  if (loading.value || scrollLock.value) return;

  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  if (!scrollbar) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollbar;
  const distanceToBottom = scrollHeight - scrollTop - clientHeight;

  if (distanceToBottom < ITEM_HEIGHT && hasMore.value) {
    loadMore();
  }
}, 200);

const loadMore = async () => {
  if (loading.value || !hasMore.value) return;

  try {
    loading.value = true;
    scrollLock.value = true;

    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    if (!scrollbar) return;

    const { scrollTop, scrollHeight } = scrollbar;

    const res = await getSmartOrderList({
      deviceId: carInfo.value.deviceId,
      page: currentPage.value + 1,
      limit: PAGE_SIZE,
      eventType: [0, 5, 102],
      cleanMode: "0",
    });

    const newList = res.list || [];
    if (newList.length > 0) {
      orderList.value = [...orderList.value, ...newList];
      currentPage.value++;
      hasMore.value = newList.length === PAGE_SIZE;

      // 等待DOM更新后恢复滚动位置
      await nextTick();
      if (scrollbar) {
        scrollbar.scrollTop = scrollTop;
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error("加载更多数据失败:", error);
  } finally {
    loading.value = false;
    scrollLock.value = false;
  }
};

const load = async () => {
  try {
    loading.value = true;
    currentPage.value = 1;
    hasMore.value = true;
    orderList.value = [];

    const res = await getSmartOrderList({
      deviceId: carInfo.value.deviceId,
      page: 1,
      limit: PAGE_SIZE,
      eventType: [0, 5, 102],
      cleanMode: "0",
    });

    orderList.value = res.list || [];
    hasMore.value = (res.list || []).length === PAGE_SIZE;
  } catch (error) {
    console.error("加载数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleSubmit = async (item: any) => {
  if (!item.recognizeResult && item.recognizeResult !== 0) {
    ElMessage.warning("请先识别结果");
    return;
  }
  try {
    const res = await upDateSmartOrderDetail({
      id: item.id,
      recognizeResult: item.recognizeResult,
      isClean: item.isClean,
    });
    ElMessage.success("提交成功");
    load();
  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  load();
  nextTick(() => {
    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    scrollbar?.addEventListener("scroll", handleScroll);
  });
});

onUnmounted(() => {
  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  scrollbar?.removeEventListener("scroll", handleScroll);
});

defineExpose({
  load,
});
</script>

<script lang="ts">
export default {
  name: "SmartOrderPage",
};
</script>

<style scoped lang="scss">
.smart-order-page {
  width: 100%;
  height: 100%;
  padding: 10px;
  padding-bottom: 100px;
  &-item {
    width: 100%;
    height: 100%;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    &-image_wrapper {
      width: 100%;
      height: 100%;
      display: flex;
      flex-direction: row;
      gap: 10px;
      margin-bottom: 10px;
      .image-container {
        position: relative;
        width: 100%;
        height: 100%;
        .image-placeholder,
        .image-error {
          width: 100%;
          height: 100%;
          display: flex;
          justify-content: center;
          align-items: center;
          background-color: #f5f7fa;
          color: #909399;
          font-size: 20px;
        }

        .el-image {
          width: 100%;
          height: 100%;
          border-radius: 5px;
          overflow: hidden;

          :deep(img) {
            width: 100%;
            height: 100%;
            object-fit: cover;
          }
        }
        .image-time {
          position: absolute;
          bottom: 5px;
          left: 5px;
          font-size: 10px;
          color: #ffffff;
        }
      }
    }
    &-info {
      color: #242859;
      font-size: 12px;
      &-type {
        color: #242859;
        font-size: 12px;
      }
      &-result {
        display: flex;
        flex-direction: row;
        gap: 10px;
        align-items: center;
        // justify-content: space-between;
        .submit-button {
          margin-left: auto;
        }
        .requried {
          color: red;
        }
        &.is-submit {
          border-radius: 4px;
          background: rgb(244, 247, 254);
          padding: 4px 8px;
        }
      }
    }
  }
  .loading-more,
  .no-more {
    text-align: center;
    padding: 10px 0;
    color: #999;
    font-size: 12px;
  }
}
</style>
