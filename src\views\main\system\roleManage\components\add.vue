<template>
  <x-drawer
    :title="$t('addRole')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    :btnOption="{ position: 'center' }"
    bodyPadding="0"
    width="570px"
  >
    <div ref="contentRef" class="content">
      <div class="content-top">
        <div class="content-title">{{ $t("baseInfo") }}</div>
        <x-form ref="formRef" :model="form" :rules="formRules">
          <x-form-item :label="$t('enterprise')" name="entId">
            <x-tree-select
              v-model:value="form.entId"
              :treeData="formOptions.company"
              :popupContainer="contentRef"
              :placeholder="$t('PSelectEnt')"
              showSearch
            />
          </x-form-item>
          <x-form-item :label="$t('roleName')" name="roleName">
            <x-input
              v-model:value="form.roleName"
              :placeholder="$t('PEnterRoleName')"
              :maxlength="20"
            />
          </x-form-item>
        </x-form>
      </div>
      <div class="content-bottom">
        <div class="content-title">{{ $t("functionConfig") }}</div>
        <x-checkbox-tree
          v-model:value="form.permitIdList"
          :treeData="formOptions.permTree"
        />
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { ref, reactive, watch } from "vue";
import {
  compTree,
  addRole,
  getRoleCurrentPermits,
  roleNameRepeat,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import Message from "@/components/x-message";
import xCheckboxTree from "@/components/x-checkbox-tree.vue";
import {
  resTreeToXTree,
  treeBfsParse,
  debounce,
  i18nSimpleKey,
} from "@/assets/ts/utils";
const $t = i18nSimpleKey("roleManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    formRef.value && formRef.value.resetFields();
    form.permitIdList = [];
  }
};

/**
 * 重复角色名称
 */
const repeatRoleName = async () => {
  if (!form.entId) return true;
  if (!form.roleName) return true;

  const result = await roleNameRepeat({
    roleName: form.roleName,
    entId: String(form.entId),
    isSaveOperation: true,
  });
  return !result?.repeat;
};

/**
 * 插入半选
 */
const appendHalfChecked = (tree: any[], values: any[]) => {
  const half = [] as any[];
  treeBfsParse(tree, "children", (item: any) => {
    if (!(item.children && item.children.length > 0)) return;
    if (values.includes(item.value)) return;
    if (item.children.some((sub: any) => values.includes(sub.value))) {
      half.push(item.value);
    }
  });
  return [...half, ...values];
};

/**
 * 表单项
 */
const form = reactive({
  entId: 0,
  roleName: "",
  permitIdList: [],
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  entId: [
    ["required", $t("PSelectEnt")],
    [debounce(repeatRoleName), $t("roleExist")],
  ],
  roleName: [
    ["required", $t("PEnterRoleName")],
    [debounce(repeatRoleName), $t("roleExist")],
  ],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    const permitIdList = appendHalfChecked(
      formOptions.permTree,
      form.permitIdList
    );
    await addRole({ ...form, permitIdList });
    emits("update:show", false);
    Message("success", $t("addSuccess"));
    emits("confirm");
    formRef.value.resetFields();
    form.permitIdList = [];
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  company: [] as TreeItemType[],
  permTree: [] as any[],
});

(async () => {
  // 企业树
  formOptions.company = reactive(resTreeToXTree([await compTree()]));
})();

const updatePermTree = async (entId: number) => {
  // 功能树
  formOptions.permTree =
    resTreeToXTree([await getRoleCurrentPermits({ entId })])?.[0]?.children ||
    [];
};

watch(
  () => form.entId,
  (v) => {
    if (v) {
      updatePermTree(v);
    } else {
      formOptions.permTree = [];
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 64px 20px;
  &-top,
  &-bottom {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    .content-title {
      @include sc(14px, #9f9fa4);
    }
  }
  &-top {
    margin-bottom: 15px;
    padding-bottom: 0;
    .content-title {
      padding-bottom: 15px;
    }
  }
  &-bottom {
    .content-title {
      &::before {
        content: "*";
        color: red;
      }
    }
  }
}
</style>
