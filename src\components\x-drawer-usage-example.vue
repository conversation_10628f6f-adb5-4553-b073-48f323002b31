<template>
  <div>
    <!-- 示例1: 使用传统的 title 属性 -->
    <x-drawer
      :visible="drawer1Visible"
      title="传统标题方式"
      @cancel="drawer1Visible = false"
    >
      <p>这是使用传统 title 属性的抽屉内容</p>
    </x-drawer>

    <!-- 示例2: 使用 title 插槽，支持更复杂的标题内容 -->
    <x-drawer
      :visible="drawer2Visible"
      title="默认标题"
      @cancel="drawer2Visible = false"
    >
      <template #title>
        <div class="custom-title">
          <el-icon><User /></el-icon>
          <span>用户管理</span>
          <el-badge :value="12" class="title-badge" />
        </div>
      </template>
      <p>这是使用插槽自定义标题的抽屉内容</p>
    </x-drawer>

    <!-- 示例3: 插槽中包含交互元素 -->
    <x-drawer
      :visible="drawer3Visible"
      title="默认标题"
      @cancel="drawer3Visible = false"
    >
      <template #title>
        <div class="interactive-title">
          <span>数据统计</span>
          <el-switch
            v-model="realTimeMode"
            active-text="实时"
            inactive-text="静态"
            style="margin-left: 16px;"
          />
        </div>
      </template>
      <p>实时模式: {{ realTimeMode ? '开启' : '关闭' }}</p>
    </x-drawer>

    <!-- 触发按钮 -->
    <div class="demo-buttons">
      <el-button @click="drawer1Visible = true">传统标题</el-button>
      <el-button @click="drawer2Visible = true">插槽标题</el-button>
      <el-button @click="drawer3Visible = true">交互标题</el-button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { User } from '@element-plus/icons-vue'

const drawer1Visible = ref(false)
const drawer2Visible = ref(false)
const drawer3Visible = ref(false)
const realTimeMode = ref(false)
</script>

<style scoped>
.custom-title {
  display: flex;
  align-items: center;
  gap: 8px;
}

.title-badge {
  margin-left: 8px;
}

.interactive-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
}

.demo-buttons {
  display: flex;
  gap: 12px;
  margin: 20px;
}
</style>
