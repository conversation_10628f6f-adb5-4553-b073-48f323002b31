<template>
  <section class="page-table-layout" ref="pageTableLayoutRef">
    <div class="page-table-layout-top">
      <div class="top-title">{{ $t("carType") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:vehicleModel:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addReactive.show = true"
        />
        <Add v-model:show="addReactive.show" @confirm="addReactive.confirm" />
      </div>
    </div>
    <div class="page-table-layout-middle">
      <div class="middle-left">
        <div class="middle-left-items">
          <div class="middle-left-item">
            <x-input
              v-model:value="searchForm.vehicleModel"
              :placeholder="$t('PEnterVehicleModelName')"
              suffix="input_search"
              :filter="filter"
              style="width: 240px"
            />
          </div>
          <div class="middle-left-item" style="width: 112px">
            <x-select
              v-model:value="searchForm.vehicleType"
              :options="formOptions.vehicleType"
              :popupContainer="pageTableLayoutRef"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:vehicleModel:list')"
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="page-table-layout-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:vehicleModel:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:vehicleModel:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:vehicleModel:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editReactive.show"
        :id="editReactive.id"
        @confirm="editReactive.confirm"
      />
      <Detail v-model:show="detailReactive.show" :id="detailReactive.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import type { VehicleModelListRequest } from "@/services/type";
import { ref, reactive } from "vue";
import {
  vehicleModelList,
  delVehicleModel,
  getConfigList,
} from "@/services/api";
import { useMainStore } from "@/stores/main";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carType");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const pageTableLayoutRef = ref<any>();

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  vehicleType: [] as any[],
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderId"),
      width: "40",
    },
    {
      key: "vehicleModel",
      title: $t("vehicleModelName"),
      width: "180",
    },
    {
      key: "vehicleTypeText",
      title: $t("vehicleTypeName"),
      width: "180",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "50",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  vehicleModel: "",
  vehicleType: 0,
});
/**
 * 表格-搜索
 */
const submitSearch = async () => {
  table.loading = true;

  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    vehicleModel: searchForm.vehicleModel,
  } as VehicleModelListRequest;

  searchForm.vehicleType && (params.vehicleType = searchForm.vehicleType);

  const { totalCount, list } = await vehicleModelList(params);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.vehicleModel = "";
  searchForm.vehicleType = 0;
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};
/**
 * 表格-首次加载
 */
(async () => {
  submitSearch();
  // 车辆类型
  const { operConfigList } = await getConfigList({
    configList: ["vehicle_type"],
  });
  formOptions.vehicleType = [
    { k: 0, val: $t("allType") },
    ...operConfigList,
  ].map((item) => ({ value: Number(item.k), label: item.val }));
})();

/**
 * 相关操作
 */
// 新增
const addReactive = reactive({
  show: false,
  confirm: () => submitSearch(),
});
// 编辑
const editReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openEdit = (id: string) => {
  editReactive.id = id;
  editReactive.show = true;
};
// 详情
const detailReactive = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailReactive.id = id;
  detailReactive.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("confirmDeleteVehicleModel"),
    content: (
      <div style="font-size:14px;color:#383838;">
        {$t("willDebondedVehicleSim")}
      </div>
    ),
    confirm() {
      return delVehicleModel([record.id]).then(() => {
        Message("success", $t("deleteVehicleModelSuccess"));
        submitSearch();
      });
    },
  });
};
const filter = (v: string) => v.trim();
</script>

<style lang="scss" scoped>
.page-table-layout {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }

  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-right {
      display: flex;
    }
    .middle-left-items {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
          padding: 6px 8px 6px 0;
        }
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
