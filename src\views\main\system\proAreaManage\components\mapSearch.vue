<template>
  <section class="map-search">
    <x-popover
      trigger="focus"
      :container="props.popupContainer"
      v-model:visible="searchInfo.popShow"
      placement="bottom"
      :triangle="false"
    >
      <div class="map-search-input">
        <x-input
          v-model:value="searchInfo.keyWords"
          @focus="showPop"
          @update:value="getPlaceList"
          :placeholder="$t('PEnterAddress')"
          style="width: 240px"
        />
        <button class="map-search-input-btn" @click="searchKeyWords">
          <x-icon name="input_search_white" />
        </button>
      </div>
      <template #content>
        <div
          v-if="searchFormOption.placeList.length > 0"
          class="map-search-panel"
        >
          <div
            :class="[
              'list-item',
              {
                'list-item-check': props.value === item.label,
              },
            ]"
            v-for="(item, index) in searchFormOption.placeList"
            :key="index"
            @click.stop="selectPlace(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </template>
    </x-popover>
  </section>
</template>
<script lang="tsx" setup>
import { reactive } from "vue";
import type { PropType } from "vue";
import { zoomConfig } from "@/services/wsconfig";
import xInput from "@/components/x-input.vue";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");

const props = defineProps({
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  placeSearch: {
    type: Object as PropType<any>,
    required: true,
  },
  popupContainer: {
    type: HTMLElement,
  },
});

const searchFormOption = reactive({
  placeList: [],
});

const searchInfo = reactive({
  keyWords: "",
  popShow: false,
});

// 展示面板
const showPop = () => {
  if (searchFormOption.placeList.length > 0) {
    searchInfo.popShow = true;
  }
};

// 获取地点列表
const getPlaceList = (query: string) => {
  if (query) {
    props.placeSearch.search(query, function (status: string, result: any) {
      if (status === "complete" && result.info === "OK") {
        searchFormOption.placeList = result.poiList.pois.map((v) => ({
          value: v.id,
          label: v.name,
          location: v.location,
          type: v.type,
        }));
      }
      showPop();
    });
  } else {
    searchFormOption.placeList = [];
  }
};

// 选择地点并定位
const selectPlace = (value: string) => {
  const place = searchFormOption.placeList.find(
    (v) => v.value === value
  ) as any;
  if (place) {
    searchInfo.keyWords = place.label;
    const mapZoom =
      zoomConfig.find((v) => place.type.includes(v.type))?.zoom || 16;
    props.map.setZoomAndCenter(mapZoom, [
      place.location.lng,
      place.location.lat,
    ]);
    searchInfo.popShow = false;
  }
};

// 搜索按钮查询
const searchKeyWords = () => {
  const query = searchInfo.keyWords;
  if (query && searchFormOption.placeList.length > 0) {
    const firstPlace = searchFormOption.placeList[0] as any;
    selectPlace(firstPlace.value);
  } else {
    searchFormOption.placeList = [];
    searchInfo.popShow = false;
  }
};
</script>

<style scoped lang="scss">
.map-search {
  :deep(.x-input-content) {
    border: none;
  }
  &-input {
    @include ct-f(y);
    @include wh(240px, 32px);
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    &-btn {
      position: absolute;
      right: 2px;
      @include ct-f;
      @include wh(30px, 30px);
      background: #5964fb;
      border-radius: 4px;
      cursor: pointer;
    }
  }
  &-panel {
    padding: 8px 5px;
    width: 240px;
    max-height: 256px;
    @include scrollbar(both, 4px) {
      overflow: auto;
    }
    background-color: #fff;
    box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2),
      0px 4px 30px rgba(0, 0, 0, 0.1);
    .list-item {
      @include wh(100%, 40px) {
        line-height: 40px;
        padding-left: 7px;
        border-radius: 4px;
      }
      color: #383838;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        color: #5964fb;
        background-color: #d9e0fb;
      }
    }
    .list-item-check {
      color: #5964fb;
      background-color: #d9e0fb;
    }
  }
}
</style>
