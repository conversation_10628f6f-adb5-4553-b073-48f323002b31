// 消除引入地图后的警告,但引入后拖拽maker也会报错
// import "default-passive-events";
import { createApp, type Directive } from "vue";
import { createPinia } from "pinia";
import i18n from "@/language";
import App from "./App.vue";
import router from "./router/router";
import xIcon from "@/components/x-icon.vue";
import "@/assets/css/common.scss";
import { initSentry } from "@/plugins/SentryPlugin";
import * as directive from "@/assets/ts/directives";
import "element-plus/dist/index.css";
const app = createApp(App);
if (import.meta.env.MODE === import.meta.env.VITE_SENTRY_SOURCE_MAP) {
  initSentry(app, router);
}
app.use(createPinia());
app.use(i18n);
app.use(router);
app.component("x-icon", xIcon);

Object.keys(directive).forEach((key) => {
  app.directive(key, (directive as { [key: string]: Directive })[key]);
});

app.mount("#app");
