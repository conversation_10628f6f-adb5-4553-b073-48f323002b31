<template>
  <div :class="`polyline-name ${props.type}`">
    <div class="polyline-name-text">{{ props.name }}</div>
    <div class="polyline-name-triangle"></div>
  </div>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";
const props = defineProps({
  name: {
    type: String,
    required: true,
  },
  type: {
    type: String as PropType<"dark" | "light">,
    default: "dark",
  },
});
</script>

<style lang="scss" scoped>
.polyline-name {
  position: relative;
  height: 42px;
  &-text {
    height: 32px;
    padding: 0 7px;
    border-radius: 4px;
    color: #fff;
    text-align: center;
    line-height: 32px;
    white-space: nowrap;
  }
  &-triangle {
    @include ct-p(x) {
      bottom: 0;
    }
  }
  &.dark {
    .polyline-name-text {
      background-color: #2a2e43;
    }
    .polyline-name-triangle {
      @include triangle(8px, 10px, #2a2e43, bottom);
    }
  }
  &.light {
    .polyline-name-text {
      background-color: rgba(1, 5, 31, 0.5);
    }
    .polyline-name-triangle {
      @include triangle(8px, 10px, rgba(1, 5, 31, 0.5), bottom);
    }
  }
}
</style>
