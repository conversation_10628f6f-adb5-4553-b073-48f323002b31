import type { Directive, DirectiveBinding } from "vue";
import LoadingImg from "@/assets/images/loading.gif";

/**
 * @desc v-inputFocus 指令 输入框聚焦
 * @use 使用方式：`<input v-inputFocus></input>`
 */
export const inputFocus: Directive = {
  mounted(el) {
    el.querySelector("input").focus();
  },
};

const createLoadingDiv = (el: HTMLDivElement & { attributes: any }, binding: DirectiveBinding<boolean>) => {
  // 判断是否为非[static]定位元素
  if (window.getComputedStyle(el).position === "static") {
    el.style.position = "relative";
  }
  const loadingDiv = document.createElement("div");
  loadingDiv.setAttribute("class", "x-loading");

  const loadingMaskDiv = document.createElement("div");
  loadingMaskDiv.setAttribute("class", "x-loading-mask");
  loadingDiv.appendChild(loadingMaskDiv);

  const loadingImg = document.createElement("img");
  loadingImg.setAttribute("src", LoadingImg);
  loadingImg.style.width = "50px";
  loadingImg.style.height = "50px";
  loadingDiv.appendChild(loadingImg);

  if (el.attributes["loading-text"]) {
    const loadingText = document.createElement("span");
    loadingText.innerText = el.attributes["loading-text"].value;
    loadingText.style.fontSize = "14px";
    loadingText.style.marginTop = "10px";
    loadingText.style.color = "#999";
    loadingDiv.appendChild(loadingText);
  }

  el.appendChild(loadingDiv);

  if (binding.value) {
    loadingDiv.style.display = "flex";
  } else {
    loadingDiv.style.display = "none";
  }
};
/**
 * @desc v-xloading 指令
 * @use 使用方式：`<div loading-text="" v-xloading="true|false"></div>`
 */
export const xloading: Directive = {
  mounted: (el, binding) => {
    if (typeof binding.value === "boolean") {
      if (binding.value) {
        createLoadingDiv(el, binding);
      }
    }
  },
  updated: (el: HTMLDivElement, binding) => {
    if (typeof binding.value === "boolean" && binding.value !== binding.oldValue) {
      const loadingDiv = el.querySelector<HTMLDivElement>(".x-loading");
      if (loadingDiv) {
        if (binding.value) {
          loadingDiv.style.display = "flex";
        } else {
          loadingDiv.style.display = "none";
        }
      } else {
        createLoadingDiv(el, binding);
      }
    }
  },
  unmounted: (el: HTMLDivElement) => {
    const loadingDiv = el.querySelector<HTMLDivElement>(".x-loading");
    if (!loadingDiv) return;
    el.removeChild(loadingDiv);
  },
};
