<template>
  <x-drawer
    :title="$t('editProInfo')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    :btnOption="{ position: 'center' }"
    bodyPadding="20px 0 20px 20px"
    footerType="following"
    width="672px"
  >
    <div
      ref="contentRef"
      class="content"
    >
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
      >
        <x-form-item
          :label="$t('entName')"
          name="entId"
        >
          <span style="padding-left: 10px">
            {{ formOptions.detail.entName }}
          </span>
        </x-form-item>
        <x-form-item
          :label="$t('proSeq')"
          name="proSeq"
        >
          <span style="padding-left: 10px">
            {{ formOptions.detail.proSeq }}
          </span>
        </x-form-item>
        <x-form-item
          :label="$t('proName')"
          name="proName"
        >
          <x-input
            v-model:value="form.proName"
            :placeholder="$t('PEnterProName')"
            :maxlength="25"
          />
        </x-form-item>
        <x-form-item
          :label="$t('userName')"
          name="userId"
        >
          <x-select
            v-model:value="form.userId"
            :options="formOptions.users"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectUserName')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('opDate')"
          name="openDate"
        >
          <DatePicker
            v-model:value="form.openDate"
            format="YYYY-MM-DD"
            :popupContainer="contentRef"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { GetProDetailResponse } from "@/services/type";
import { ref, reactive, watch } from "vue";
import { editPro, getProDetail, userList } from "@/services/api";
import { formatDateTime } from "@/assets/ts/dateTime";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import Message from "@/components/x-message";
import { DatePicker } from "@/components/x-date-picker";
import xSelect from "@/components/x-select.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("projectManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 表单项
 */
const form = reactive({
  id: "",
  proName: "",
  proSeq: "",
  openDate: "",
  userId: "",
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  proName: [["required", $t("PEnterProName")]],
  // userId: [["required", $t("PSelectUserName")]],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    await editPro(form);
    emits("update:show", false);
    Message("success", $t("saveSuccess"));
    emits("confirm");
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  detail: {} as GetProDetailResponse,
  users: [] as any[],
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      formOptions.detail = await getProDetail(props.id);
      const { id, proName, openDate, userId, proSeq } = formOptions.detail;
      form.id = String(id);
      form.proName = proName;
      form.proSeq = proSeq;
      form.openDate = formatDateTime(openDate, "YYYY-MM-DD");
      form.userId = userId;

      // 用户
      const users = await userList({
        page: 1,
        limit: 500,
        entIdList: [formOptions.detail.entId],
        station: 2, // 后台要求固定传参
      });
      formOptions.users = (users?.list || []).map((item) => ({
        value: item.userId,
        label: item.userName,
      }));
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
}
</style>
