<template>
  <div :class="['x-radio', { button: isButton }]">
    <div
      :class="['x-radio-wrap', { button: isButton, disabled: disabled || item.disabled }]"
      :style="props.options.length !== index + 1 ? styleObject : ''"
      @click="disabled || item.disabled ? (e: Event) => e.preventDefault() : onClick(item.value)"
      v-for="(item, index) in props.options"
      :key="index"
    >
      <span
        v-if="!isButton"
        :class="['x-radio-input', { 'x-radio-checked': props.value === item.value }]"
      ></span>
      <span
        :class="[
          'x-radio-label',
          {
            button: isButton,
            checked: isButton && props.value === item.value,
          },
        ]"
      >
        <slot
          name="content"
          :option="item"
        >
          {{ item.label }}
        </slot>
      </span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from "vue";
import type { PropType } from "vue";

interface Option {
  label: string;
  value: any;
  disabled?: boolean; // 禁用单个单选器
}

const props = defineProps({
  // 以配置形式设置子元素
  options: {
    type: Array<Option>,
    required: true,
  },
  // 用于设置当前选中的值
  value: {
    type: null,
    required: true,
  },
  optionType: {
    type: String as PropType<"default" | "button">,
    default: () => "default",
  },
  // 禁选所有子单选器
  disabled: {
    type: Boolean,
    required: false,
  },
  // 单选器间距
  gap: {
    type: Number,
    required: false,
  },
});

const isButton = computed(() => props.optionType === "button");
const styleObject = computed(() => {
  return {
    marginRight: props.gap + "px",
  };
});

const emits = defineEmits(["update:value", "change"]);

const onClick = (value: any) => {
  if (value !== props.value) {
    emits("update:value", value);
    emits("change", value);
  }
};
</script>

<style lang="scss" scoped>
.x-radio {
  display: inline-flex;
  &.button {
    padding: 5px 7px;
    border-radius: 4px;
    background-color: rgb(244, 247, 254);
  }

  .x-radio-wrap {
    @include sc(14px, #5e5e5e) {
      height: 24px;
      line-height: 24px;
      cursor: pointer;
    }
    &.button {
      height: auto;
      min-width: 72px;
    }

    .x-radio-input {
      @include wh(16px) {
        position: relative;
        top: 3px;
        display: inline-block;
        background: #fff;
        border: 2px solid #d9d9d9;
        border-radius: 50%;
        transition: all 0.3s;
      }
      &:after {
        content: "";
        @include wh(16px);
        @include ct-p(both) {
          margin-top: -8px;
          margin-left: -8px;
          background-color: #5964fb;
          border-radius: 100%;
          transform: scale(0);
          opacity: 0;
          transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
        }
      }
    }
    .x-radio-checked {
      border-color: #5964fb;
      &:after {
        transform: scale(0.5);
        opacity: 1;
      }
      & + span {
        color: #5964fb;
      }
    }
    .x-radio-label {
      @include sc(14px, #999) {
        padding: 0 8px;
      }
      &.button {
        display: block;
        min-height: 32px;
        border-radius: 4px;
        line-height: 32px;
        text-align: center;
      }
      &.checked {
        color: #5964fb;
        background-color: rgb(255, 255, 255);
        box-shadow: 0 0 10px 0 rgba(72, 111, 245, 0.14);
      }
    }
  }
  .disabled {
    color: #00000040;
    cursor: not-allowed;
    &:hover {
      .x-radio {
        border-color: #d9d9d9;
      }
    }
    .x-radio {
      border-color: #d9d9d9;
      background-color: #f5f5f5;
      &:after {
        background-color: rgba(0, 0, 0, 0.2);
      }
    }
  }
}
</style>
