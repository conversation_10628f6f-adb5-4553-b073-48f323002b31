server {
  listen 443 ssl;
  # ssl on;
  server_name test-yun.chengshizhiguang.com;
  ssl_certificate        /etc/nginx/ssl/test-yun.chengshizhiguang.com.pem;
  ssl_certificate_key    /etc/nginx/ssl/test-yun.chengshizhiguang.com.key;
  ssl_session_timeout    10m;
  ssl_session_cache      shared:SSL:1m;
  ssl_protocols          TLSv1 TLSv1.1 TLSv1.2 TLSv1.3;
  ssl_ciphers            HIGH:!aNULL:!MD5;
  ssl_prefer_server_ciphers on;

  # 微信小程序业务域名的校验文件
  location =/xDsEn8tBpq.txt {
    root   /etc/nginx/wx-webview;
    index  xDsEn8tBpq.txt;
  }

  location / {
    proxy_pass http://localhost:80/;
  }
}

server {
  listen       80;
  listen  [::]:80;
  server_name  localhost;

    client_max_body_size 30M;
  # client_header_buffer_size 1k;
  # client_header_timeout: 10m;
  # client_body_timeout: 10m;

  gzip_static on;

  gzip on;
  gzip_proxied expired no-cache no-store private auth;
  gzip_min_length 1k;
  gzip_comp_level 6;
  gzip_buffers 4 16k;
  gzip_http_version 1.0;
  gzip_disable msie6;
  gzip_vary on;
  # gzip_types text/css application/javascript text/javascript image/jpeg image/png image/gif;
  gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript image/jpeg image/png image/gif; # 压缩的 MIME 类型

  location / {
    root   /usr/share/nginx/html;
    index  index.html index.htm;
    try_files $uri $uri/ /index.html;  #使用history模式时配置，解决刷新404的问题
    error_page 405 =200  $request_uri;  #解决出现405 not allowed的问题
  }

  location /dev/ {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://***************:9020/;  #解决部署后的跨域问题
  }
  # 后端服务 api 测试环境
  location /test/ {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://***********:9020/;  #解决部署后的跨域问题
  }
  # websocket 测试环境
  location /wss-test/ {
    proxy_pass http://************:10020/ws/cszg;
    proxy_http_version 1.1;
    proxy_set_header Upgrade $http_upgrade;
    proxy_set_header Connection "Upgrade";
  }
  location /robot/ {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://***********:9020/robot/;  #解决部署后的跨域问题
  }
  location /mini/ {
    add_header 'Access-Control-Allow-Origin' '*';
    add_header 'Access-Control-Allow-Credentials' 'true';
    proxy_set_header Host $http_host;
    proxy_set_header X-Real-IP $remote_addr;
    proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
    proxy_set_header X-Forwarded-Proto $scheme;
    proxy_pass http://*************:9020/;  #解决部署后的跨域问题
  }
  error_page   500 502 503 504  /50x.html;
  location = /50x.html {
    root   /usr/share/nginx/html;
  }
}