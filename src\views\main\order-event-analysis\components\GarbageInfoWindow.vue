<template>
  <div class="garbage-info-window">
    <div class="info-header">
      <h4>{{ name || "未知名称" }}</h4>
      <button
        class="close-btn"
        @click="handleClose"
      >
        ×
      </button>
    </div>
    <div class="info-body">
      <div class="info-item">
        <span class="label">类型:</span>
        <span class="value">{{ type || "未知" }}</span>
      </div>
      <div class="info-item">
        <span class="label">所属社区:</span>
        <span class="value">{{ community || "未知" }}</span>
      </div>
      <div class="info-item">
        <span class="label">坐标:</span>
        <span class="value">{{ lng }}, {{ lat }}</span>
      </div>
      <div
        v-if="address"
        class="info-item"
      >
        <span class="label">地址:</span>
        <span class="value">{{ address }}</span>
      </div>
      <div
        v-if="dutyUnit"
        class="info-item"
      >
        <span class="label">责任单位:</span>
        <span class="value">{{ dutyUnit }}</span>
      </div>
      <div
        v-if="problemImageUrl"
        class="info-item"
      >
        <span class="label">问题图片:</span>
        <el-image  :src="problemImageUrl" preview-teleported :preview-src-list="[problemImageUrl]" style="width: 110px;height: 81px;" />
      </div>
      <div
        v-if="rectifyImageUrl"
        class="info-item"
      >
        <span class="label">整改图片:</span>
        <el-image :src="rectifyImageUrl" preview-teleported :preview-src-list="[rectifyImageUrl]" style="width: 110px;height: 81px;" />
      </div>
      <div
        v-if="problemDate"
        class="info-item"
      >
        <span class="label">问题日期:</span>
        <span class="value">{{problemDate }}</span>
      </div>
      <div
        v-if="remark"
        class="info-item"
      >
        <span class="label">备注:</span>
        <span class="value">{{ remark }}</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { sr } from "element-plus/es/locale";
import { onMounted } from "vue";

interface Props {
  name?: string;
  type?: string;
  community?: string;
  lng?: number;
  lat?: number;
  address?: string;
  dutyUnit?: string;
  problemDate?: string;
  remark?: string;
  problemImageUrl?: string;
  rectifyImageUrl?: string;
  window?: any;
}

const props = withDefaults(defineProps<Props>(), {
  name: "",
  type: "",
  community: "",
  lng: 0,
  lat: 0,
  address: "",
  dutyUnit: "",
  problemDate: "",
  remark: "",
  problemImageUrl: "",
  rectifyImageUrl: "",
});

const handleClose = () => {
  console.log("关闭信息窗口");
  if (props.window) {
    props.window.close();
  }
};



onMounted(() => {
  console.log("GarbageInfoWindow 组件已挂载", props);

  // 确保信息窗口可见
  if (props.window) {
    console.log("信息窗口实例存在，尝试显示");
    // 强制设置可见性
    setTimeout(() => {
      const infoWindowElement = document.querySelector('.amap-info-window');
      if (infoWindowElement) {
        console.log("找到信息窗口元素", infoWindowElement);
        (infoWindowElement as HTMLElement).style.display = 'block';
        (infoWindowElement as HTMLElement).style.visibility = 'visible';
        (infoWindowElement as HTMLElement).style.zIndex = '9999';
      } else {
        console.log("未找到信息窗口元素");
      }
    }, 100);
  }
});
</script>

<style scoped lang="scss">
.garbage-info-window {
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 240px;
  max-width: 320px;
  font-size: 14px;
  overflow: hidden;

  .info-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 20px 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    h4 {
      margin: 0;
      font-size: 16px;
      font-weight: 600;
      flex: 1;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }

    .close-btn {
      background: rgba(255, 255, 255, 0.2);
      border: none;
      color: white;
      font-size: 18px;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      margin-left: 12px;
      transition: background-color 0.2s;

      &:hover {
        background: rgba(255, 255, 255, 0.3);
      }
    }
  }

  .info-body {
    padding: 16px 20px 20px;

    .info-item {
      display: flex;
      margin-bottom: 12px;
      align-items: flex-start;

      &:last-child {
        margin-bottom: 0;
      }

      .label {
        font-weight: 600;
        color: #666;
        min-width: 70px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .value {
        color: #333;
        flex: 1;
        word-break: break-all;
      }
    }
  }
}
</style>
