<template>
  <section class="worker-infowindow">
    <div class="worker-infowindow-name">{{ props.userName }}</div>
    <div class="worker-infowindow-mobile">
      <div class="mobile-icon"></div>
      <div class="mobile-num">{{ props.mobile }}</div>
    </div>
  </section>
</template>
<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    userName: string;
    mobile: string;
  }>(),
  {}
);
</script>

<style lang="scss" scoped>
.worker-infowindow {
  @include fj {
    flex-direction: column;
  }
  @include wh(155px, 70px) {
    padding: 10px 0 34px 20px;
    @include bis("@/assets/images/screen_worker_bg.png");
  }
  &-name {
    @include sc(16px, #fff) {
      font-weight: 700;
    }
  }
  &-mobile {
    display: flex;
    align-items: center;
    .mobile-icon {
      @include wh(16px) {
        @include bis("@/assets/images/screen_worker_icon.png");
      }
    }
    .mobile-num {
      margin-left: 6px;
      @include sc(13px, #fff);
    }
  }
}
</style>
