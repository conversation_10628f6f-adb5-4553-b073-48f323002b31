<template>
  <x-drawer
    :title="$t('staffList')"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="0"
    width="584px"
  >
    <div class="content">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :loading="table.loading"
      />
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getRoleUserAccount } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xTable from "@/components/x-table.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("roleManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "100",
    },
    {
      key: "account",
      title: $t("account"),
      width: "185",
    },
    {
      key: "userName",
      title: $t("userName"),
      width: "150",
    },
    {
      key: "mobile",
      title: $t("mobile"),
      width: "150",
    },
  ],
  dataSource: [] as any[],
  loading: true,
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      table.loading = true;
      const { list } = await getRoleUserAccount({
        page: 1,
        limit: 1000,
        roleId: props.id,
      });
      table.dataSource = [
        ...(list
          ? list.map((item, index) => {
              return {
                orderId: index + 1,
                ...item,
              };
            })
          : []),
      ];
      setTimeout(() => {
        table.loading = false;
      }, 1000);
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  height: 100%;
  background-color: #fff;
  padding: 0 18px;
}
</style>
