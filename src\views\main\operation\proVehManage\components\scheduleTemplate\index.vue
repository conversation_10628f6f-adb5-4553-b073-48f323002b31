<template>
  <section
    class="setting-template"
    ref="settingTemplateRef"
  >
    <div class="setting-template-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.proId"
          :options="formOptions.project"
          :popupContainer="settingTemplateRef"
          :placeholder="$t('allProjects')"
          class="middle-left-select"
        />
        <x-select
          v-model:value="searchForm.proAreaId"
          :options="formOptions.area"
          :popupContainer="settingTemplateRef"
          :placeholder="$t('allAreas')"
          class="middle-left-select"
        />
      </div>
      <div class="middle-right">
        <x-button
          type="blue"
          :text="$t('search')"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button
          type="green"
          :text="$t('reset')"
          @click="resetSearchForm"
        />
      </div>
    </div>
    <div class="setting-template-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 操作 -->
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span @click="openEdit(record)">
                {{ $t("edit") }}
              </span>
              <span @click="openDetail(record)">
                {{ $t("detail") }}
              </span>
              <span @click="openAdd(record)">
                {{ $t("add") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive, watch } from "vue";
import type { PageSizeType } from "@/components/types";
import { getProjectAndArea, getAreaScheduleList } from "@/services/api";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

const settingTemplateRef = ref<any>();

/**
 * 表单
 */
// 搜索表单字段
const searchForm = reactive({
  proId: "",
  proAreaId: "",
});
// 组件数据支持
const formOptions = reactive({
  project: [] as any[],
  area: [] as any[],
});

/**
 * 表格
 */
// 字段
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "proAreaName",
      title: $t("areaName"),
      width: "80",
    },
    {
      key: "proName",
      title: $t("projectName"),
      width: "80",
    },
    {
      key: "tempNumber",
      title: $t("templateCount"),
      width: "60",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "60",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
// 搜索
const submitSearch = async () => {
  table.loading = true;
  const { proAreaId, proId } = searchForm;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    proId: proAreaId ? "" : proId,
    proAreaId,
  };
  const { totalCount, list } = await getAreaScheduleList(params);
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
// 重新搜索
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
// 重置
const resetSearchForm = () => {
  searchForm.proId = "";
  searchForm.proAreaId = "";
  table.pagination["current"] = 1;
  submitSearch();
};
// 分页
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};

/**
 * 项目与区域级联
 */
const getProjectAndAreaList = async (proId: any) => {
  const param = proId ? [proId] : [];
  const list = (await getProjectAndArea(param)).map((item) => ({
    label: item.name,
    value: String(item.id),
  }));
  return list;
};
// 获取项目列表
(async () => {
  submitSearch();
  formOptions.project = await getProjectAndAreaList(null);
})();
// 根据项目获取区域列表
watch(
  () => searchForm.proId,
  async (newV) => {
    if (newV) {
      searchForm.proAreaId = "";
      formOptions.area = await getProjectAndAreaList(newV);
    }
  }
);

/**
 * 相关操作
 *
 */ const emits = defineEmits(["openDetail", "openAdd", "openEdit"]);
// 查看详情
const openDetail = (record: any) => {
  emits("openDetail", record);
};
// 新增
const openAdd = (record: any) => {
  emits("openAdd", record);
};
// 编辑
const openEdit = (record: any) => {
  emits("openEdit", record);
};
</script>

<style lang="scss" scoped>
.setting-template {
  display: flex;
  flex-direction: column;
  @include wh(100%);
  &-top {
    @include ct-f(y);
    @include wh(100%, 36px);
    line-height: 36px;
    .top-title {
      display: flex;
      margin-right: 20px;
      font-weight: bold;
      @include sc(16px, #9f9fa4);
      cursor: pointer;
      position: relative;
      &.active {
        @include sc(18px, rgb(36, 40, 89));
        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 50%;
          @include wh(49px, 3px);
          transform: translateX(-50%);
          background-color: #5964fb;
        }
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
    .middle-left {
      &-input {
        width: 200px;
        margin-right: 16px;
      }
      &-select {
        width: 150px;
        margin-right: 16px;
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-project-popover {
      display: inline-block;
      .icon-edit {
        margin-left: 5px;
        text-align: center;
        cursor: pointer;
      }
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
