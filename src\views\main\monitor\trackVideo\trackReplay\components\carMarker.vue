<template>
  <section class="track-car-marker">
    <div class="marker-radar">
      <div class="marker-radar-ripple"></div>
    </div>
    <div class="marker-car-num">
      {{ props.status.deviceId }}
    </div>
    <div :style="markerCarStyle">
      <x-icon
        name="map_car"
        width="24px"
        height="35.4px"
      />
    </div>
  </section>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";
import { computed } from "vue";
import xIcon from "@/components/x-icon.vue";

const props = defineProps({
  status: {
    type: Object as PropType<{
      deviceId: string;
    }>,
    required: true,
  },
  direction: {
    type: Number,
    default: 0,
  },
});

const markerCarStyle = computed(() => {
  return {
    transform: `rotate(${180 + props.direction}deg)`,
    transition: `all 0.1s ease`,
  };
});
</script>

<style lang="scss" scoped>
.track-car-marker {
  position: relative;
  .marker-radar {
    @include wh(100%) {
      border-radius: 50%;
    }
    &-ripple {
      @include ct-p;
      @include wh(10px) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0px 0px 2px 4px rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: Ripple 1s linear infinite;
    }
    @keyframes Ripple {
      to {
        width: 40px;
        height: 40px;
        opacity: 0.3;
      }
    }
  }
  .marker-car-num {
    @include ct-p(x) {
      top: -20px;
    }
    height: 16px;
    padding: 0 4px;
    background-color: rgba(89, 100, 251, 0.8);

    border-radius: 2px;
    @include sc(12px, #fff) {
      line-height: 16px;
    }
    white-space: nowrap;
  }
}
</style>
