<template>
  <x-drawer
    :title="$t('setting')"
    :visible="props.show"
    :btnOption="btnOption"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    bodyPadding="0 0 32px 0"
    width="80%"
  >
    <div class="content" ref="alarmSettingRef">
      <div class="content-top">
        <div class="content-top-left">
          <div class="content-top-left-item">
            <x-select
              v-model:value="searchForm.warnName"
              :options="formOptions.warnName"
              :popupContainer="alarmSettingRef"
              :placeholder="$t('PEnterAlarmNameSearch')"
              showSearch
              style="width: 240px"
            />
          </div>
        </div>
        <div class="content-top-right">
          <x-button
            type="blue"
            :text="$t('search')"
            @click="search"
            style="margin-right: 12px"
          />
          <x-button @click="reset" type="green" :text="$t('reset')" />
        </div>
      </div>
      <div class="content-bottom">
        <div class="vertical-tabs">
          <div class="tab-container" v-show="!searchingStatus">
            <div
              v-for="(tab, index) in table.tabsData"
              :key="tab.title"
              @click="changeTab(index)"
              :class="['tab', { active: activeTab === index }]"
            >
              <span>{{ tab.title }}</span>
              <x-checkbox
                :text="$t('receive')"
                :checked="all(tab)"
                :indeterminate="!all(tab) && half(tab)"
                :stopPropagation="false"
                @update:checked="toggleAllReceived($event, tab.content)"
              />
            </div>
          </div>
          <div class="content-container">
            <div v-for="(tab, index) in table.tabsData" :key="tab.title">
              <div class="content" v-show="activeTab === index">
                <x-table
                  :loading="table.loading"
                  :cols="searchingStatus ? table.cols : table.cols.slice(1)"
                  :dataSource="searchingStatus ? table.dataSource : tab.content"
                >
                  <!-- 告警名称 -->
                  <template #warnName="{ record }">
                    <x-popover
                      trigger="hover"
                      placement="bottom"
                      :container="alarmSettingRef"
                      class="table-warnName-popover"
                    >
                      {{ record.warnName }}
                      <template #content>
                        <div class="alarm-manage-table-warnName-hover-popover">
                          {{ record.warnName }}
                        </div>
                      </template>
                    </x-popover>
                  </template>
                  <!-- 是否接收 -->
                  <template #isReceived="{ record }">
                    <x-switch
                      :checkedValue="1"
                      :unCheckedValue="0"
                      :checked="Boolean(record.isReceived)"
                      @change="modifyTableCell($event, record.id, 'isReceived')"
                    >
                      <template #checkedChildren>
                        <x-icon name="switch_checked"></x-icon>
                      </template>
                      <template #unCheckedChildren>
                        <x-icon name="switch_unChecked"></x-icon>
                      </template>
                    </x-switch>
                  </template>
                  <!-- 全局弹窗 -->
                  <template #globalModal="{ record }">
                    <x-checkbox
                      :text="$t('have')"
                      :checked="Boolean(record.popup)"
                      @update:checked="
                        modifyTableCell($event, record.id, 'popup')
                      "
                    >
                      <template #label>
                        <span :class="{ 'text-checked': record.popup }">
                          {{ $t("have") }}
                        </span>
                      </template>
                    </x-checkbox>
                  </template>
                  <!-- 告警等级 -->
                  <template #warnLevel="{ record }">
                    <x-radio
                      v-model:value="record.warnLevel"
                      :options="warnLevelType"
                      @change="modifyTableCell($event, record.id, 'warnLevel')"
                    />
                  </template>
                </x-table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  getWarnSettingList,
  batchSetWarnConfig,
  getWarnModuleAndName,
  getWarnConfigList,
} from "@/services/api";
import { warnLevelType } from "@/assets/ts/config";
import xButton from "@/components/x-button.vue";
import xTable from "@/components/x-table.vue";
import xDrawer from "@/components/x-drawer.vue";
import xSwitch from "@/components/x-switch.vue";
import xRadio from "@/components/x-radio.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import Message from "@/components/x-message";
import xSelect from "@/components/x-select.vue";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmManage");

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});

/**
 * 组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

/**
 * 引用
 */
const alarmSettingRef = ref<any>();

/**
 * 弹窗配置
 */
const btnOption = reactive({
  confirm: $t("save"),
  buttonMask: true,
  position: "center" as "center",
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  warnName: [] as any[],
});

/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  warnName: "",
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "warnModule",
      title: $t("system"),
      width: "80",
    },
    {
      key: "warnName",
      title: $t("alarmName"),
      width: "80",
      slots: "warnName",
    },
    {
      key: "isReceived",
      title: "客户账号展示并接受",
      width: "60",
      slots: "isReceived",
    },
    {
      key: "globalModal",
      title: $t("globalPopup"),
      width: "60",
      slots: "globalModal",
    },
    {
      key: "warnLevel",
      title: $t("alarmLevel"),
      width: "110",
      slots: "warnLevel",
    },
  ],
  dataSource: [] as any[],
  tabsData: [] as any[],
  loading: true,
});

/**
 * 表格-选项卡
 */
// 当前选项卡
const activeTab = ref(0);
// 切换选项卡
const changeTab = (index: number) => (activeTab.value = index);
// 格式化数据
const transformData = (data: any) => {
  return Object.keys(data).map((key) => {
    const { [key]: content } = data;
    return { title: key, content };
  });
};

/**
 * 表格-查询
 */
// 默认展示选项卡表格
const getTabList = async () => {
  const list = await getWarnSettingList();
  table.tabsData = transformData(list) || [];
};
// 查询后展示默认表格
const searchingStatus = ref(false);
const search = async () => {
  table.loading = true;
  searchingStatus.value = true;
  const { list } = await getWarnConfigList({
    warnModule: "",
    warnName: searchForm.warnName,
  });
  // 匹配已修改的列表项
  const matchedList = list
    ? list.map((listItem) => {
        const matchedItem = table.tabsData
          .flatMap((obj) => obj.content)
          .find((obj) => obj.id === listItem.id);
        return matchedItem ? { ...listItem, ...matchedItem } : listItem;
      })
    : [];
  table.dataSource = matchedList || [];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

/**
 * 表格-重置
 */
const reset = () => {
  activeTab.value = 0;
  searchingStatus.value = false;
  searchForm.warnName = "";
  getTabList();
};

/**
 * 表格-修改列表项
 */
// 修改指定列表项
const modifyTableCell = (value: any, id: string, key: string) => {
  const contentMap = new Map();
  for (const data of table.tabsData) {
    for (const content of data.content) {
      contentMap.set(content.id, content);
    }
  }
  const content = contentMap.get(id);
  if (content) {
    if (key === "popup") {
      value = value ? 1 : 0;
    }
    content[key] = value;
  }
};

// 批量修改系统下的是否接收项
const toggleAllReceived = (value: boolean, dataList: any) => {
  const number = value ? 1 : 0;
  for (const data of dataList) {
    data.isReceived = number;
  }
};
// 半选
const half = (system: any) => {
  if (system.content.length > 0) {
    return system.content.some((sub: any) => sub.isReceived);
  }
  return false;
};
// 全选
const all = (system: any) => {
  if (system.content.length > 0) {
    return system.content.every((sub: any) => sub.isReceived);
  }
  return false;
};

/**
 * 表格-提交设置
 */
const formSubmit = async () => {
  const param = table.tabsData.flatMap(({ content }) => content);
  await batchSetWarnConfig(param);
  Message("success", $t("setAlarmConfigSuccess"));
  emits("update:show", false);
  emits("confirm");
};

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      reset();
      table.loading = true;
      await getTabList();
      setTimeout(() => {
        table.loading = false;
      }, 1000);
      // 告警名称
      const warnNameList = await getWarnModuleAndName({
        warnName: "all",
        warnModule: "",
      });
      if (warnNameList) {
        formOptions.warnName = warnNameList.map((item: any) => ({
          value: item,
          label: item,
        }));
      } else {
        formOptions.warnName = [];
      }
    }
  }
);
</script>

<style lang="scss" scoped>
.alarm-manage-table-warnName-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.content {
  &-top {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    margin: 20px;
    background: #fff;
    &-right {
      display: flex;
      flex-direction: row;
    }
  }
  &-bottom {
    margin: 20px;
    height: 800px;
    :deep(.x-table) {
      position: static;
    }
    padding-top: 20px;
    .vertical-tabs {
      display: flex;
    }
    .tab-container {
      flex: 1;
    }
    .tab {
      padding: 10px 15px;
      cursor: pointer;
      &.active {
        background-color: #fff;
        border-left: 3px solid #5964fb;
      }
      span {
        display: block;
        margin-bottom: 5px;
      }
    }
    .content-container {
      height: calc(100% - 56px);
      background-color: #fff;
      flex: 5;
      overflow-y: auto;
      @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
    }
    .text-checked {
      color: #5964fb;
    }
    .table-warnName-popover {
      @include ell;
    }
  }
}
</style>
