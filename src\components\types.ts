export type PageSizeType = 10 | 15 | 20 | 25;

/** 表格列配置 */
export type ProTableColumn = {
  /** 字段名 */
  prop: string;
  /** 列标题 */
  label: string;
  /** 对齐方式 */
  align?: "left" | "center" | "right";
  /** 列宽 */
  width?: string | number;
  /** 最小列宽 */
  minWidth?: string | number;
  /** 是否固定列 */
  fixed?: boolean | "left" | "right";
  /** 是否可排序 */
  sortable?: boolean;
  /** - 格式化数据
   * - 注：formatter 存在时，formatter 优先级高于正常渲染即无插槽模式
   * - 参考：https://element-plus.org/zh-CN/component/table.html#table-column-api */
  formatter?: (row: any, column: TableColumnCtx<any>, cellvalue: any, index: number) => any;
  [key: string]: any;
};

/** 搜索表单的表单项 */
export type SearchFormItem = {
  /** 表单项的 label */
  label: string;
  /** 表单项的 key */
  prop: string;
  /** 表单项的类型 */
  type?: "input" | "select" | "date" | "daterange" | "datetimerange" | "slot";
  /** 表单项的占位符 */
  placeholder?: string;
  /** 是否隐藏 */
  hidden?: boolean;
  /** 输入框的宽度 */
  width?: string;
  /** 表单项的宽度比例 参考 `el-col` 的 span 属性 */
  span?: number;
  /** 输入框的后缀 */
  suffix?: string | Component;
  /** select 选择器的选项列表 */
  options?: Array<{ label: string; value: any; [x: string]: any }>;
  /** select 是否可搜索 */
  filterable?: boolean;
  /** 日期选择器的类型 */
  dateType?: "year" | "month" | "date" | "datetime";
  valueFormat?: string;
  /** 表单项类型为日期范围时，开始日期的占位符 */
  startPlaceholder?: string;
  /** 表单项类型为日期范围时，结束日期的占位符 */
  endPlaceholder?: string;
};
