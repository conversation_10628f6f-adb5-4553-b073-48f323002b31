<template>
  <div class="panel" ref="panelRef">
    <div class="panel-month">
      <!-- 面板标头 -->
      <div class="panel-month__head">
        <!-- 上一年按钮 -->
        <div
          v-if="props.partial === 'start'"
          class="month-icon"
          @click="changeYearMonth(props.year - 1)"
        >
          <x-icon name="calendar_left" width="12" height="12" />
        </div>
        <!-- 显示年份 -->
        <div style="width: 100%; text-align: center">
          <div>{{ props.year + $t("year") }}</div>
        </div>
        <!-- 下一年按钮 -->
        <div
          v-if="props.partial === 'end'"
          class="month-icon"
          @click="changeYearMonth(props.year + 1)"
        >
          <x-icon name="calendar_right" width="12" height="12" />
        </div>
      </div>
      <!-- 面板内容 -->
      <div class="panel-month__body">
        <table class="month-table">
          <tbody @mouseleave="emits('updatePreviewValue')">
            <tr v-for="(line, index) in data.months" :key="index">
              <td
                v-for="(month, i) in line"
                :key="i"
                class="month-cell"
                :class="[
                  {
                    'month-cell_today':
                      month.timestamp === getFirstDayOfMonthTimestamp(),
                    'month-cell_selected': props.range
                      ? data.range.includes(month.timestamp)
                      : month.timestamp === data.date,
                    'month-cell_disabled':
                      month.disabled ||
                      (props.touch?.has(0) &&
                        month.timestamp < data.range[0]) ||
                      (props.touch?.has(1) && month.timestamp > data.range[1]),
                    'month-cell_range':
                      data.range.includes(0) === false &&
                      month.timestamp > data.range[0] &&
                      month.timestamp < data.range[1],
                    'month-cell_range-start':
                      month.timestamp === data.range[0] && data.range[1],
                    'month-cell_range-end':
                      month.timestamp === data.range[1] && data.range[0],
                    'month-cell_range-alone':
                      (!data.range[0] || !data.range[1]) &&
                      (data.range[0] || data.range[1]) &&
                      (month.timestamp === data.range[0] ||
                        month.timestamp === data.range[1]),
                    'month-cell_ranging':
                      data.ranging.includes(0) === false &&
                      month.timestamp > data.ranging[0] &&
                      month.timestamp < data.ranging[1],
                    'month-cell_ranging-start':
                      data.ranging.includes(0) === false &&
                      month.timestamp === data.ranging[0],
                    'month-cell_ranging-end':
                      data.ranging.includes(0) === false &&
                      month.timestamp === data.ranging[1],
                  },
                ]"
                @click="onClickMonth(month)"
                @mouseenter="mouseenterMonth(month)"
              >
                <div class="month-cell__inner">
                  {{ month.value + 1 + $t("month") }}
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { watch, reactive, ref } from "vue";
import {
  genMonths,
  valueToDateTime,
  valueToRange,
  valueToSelecting,
} from "./utils";
import type { MonthType } from "@/assets/ts/dateTime";
import { formatDateTime } from "@/assets/ts/dateTime";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = withDefaults(
  defineProps<{
    format?: string;
    value: string | string[];
    previewValue?: string;
    visible?: boolean;
    partial?: string;
    focus?: number;
    year: number;
    month: number;
    popupContainer: HTMLElement;
    disabledDate?: Function;
    range?: boolean;
    touch?: any;
  }>(),
  {
    visible: false,
    partial: "",
    range: false,
  }
);

const emits = defineEmits([
  "updateRange",
  "changeYearMonth",
  "updatePreviewValue",
  "updateTempValue",
]);

const panelRef = ref();

const data = reactive({
  months: [[], [], []] as MonthType[][],
  date: 0,
  // 年下拉列表
  years: [
    ...Array.from({ length: 50 }, (_, i) => props.year - i).reverse(),
    ...Array.from({ length: 50 }, (_, i) => props.year + i + 1),
  ],
  dateFormat: "YYYY-MM",
  range: [0, 0],
  ranging: [0, 0],
});

// 生成月
genMonths({
  data,
  year: props.year,
  month: props.month,
  disabledDate: props.disabledDate,
});

// 值转换为日期时间
props.focus === undefined &&
  valueToDateTime({ data, value: props.value, type: "month" });

// 范围
valueToRange({
  data,
  value: props.value,
  format: data.dateFormat,
  type: "month",
});

// 值变化
watch(
  () => [props.value, props.focus],
  () => {
    if (props.focus === undefined) {
      valueToDateTime({ data, value: props.value, type: "month" });
    } else if (props.focus > -1) {
      valueToDateTime({ data, value: props.value[props.focus], type: "month" });
    }
    // 范围
    valueToRange({
      data,
      value: props.value,
      format: data.dateFormat,
      type: "month",
    });
  }
);

// 年月变化
watch(
  () => [props.year, props.month],
  () => {
    // 生成日期
    genMonths({
      data,
      year: props.year,
      month: props.month,
      disabledDate: props.disabledDate,
    });
  }
);

// 预览值
watch(
  () => props.previewValue,
  () => {
    // 选择中范围
    valueToSelecting(props, data, "month");
  }
);

// 获取本月第一天时间戳
const getFirstDayOfMonthTimestamp = () => {
  var currentDate = new Date();
  currentDate.setDate(1);
  currentDate.setHours(0, 0, 0, 0);
  var firstDayTimestamp = currentDate.getTime();
  return firstDayTimestamp;
};

// 修改年份
const changeYearMonth = (year: any) => {
  const change = {
    year: year,
    month: props.month,
    partial: props.partial,
  };
  emits("changeYearMonth", change);
};

/**
 * 禁用的日期
 */
const isDisabledMonth = (month: MonthType) => {
  if (month.disabled) return true;
  if (props.touch?.has(0) && month.timestamp < data.range[0]) return true;
  if (props.touch?.has(1) && month.timestamp > data.range[1]) return true;
  return false;
};

/**
 * 鼠标进入日期
 */
const mouseenterMonth = (month: MonthType) => {
  // 禁用的日期
  if (isDisabledMonth(month)) {
    // 不出现预览效果
    emits("updatePreviewValue");
    return;
  }
  // 发出事件
  emits("updatePreviewValue", {
    date: formatDateTime(month.timestamp, data.dateFormat),
  });
};

/**
 * 点击日期
 */
const onClickMonth = (month: MonthType) => {
  // 发出事件
  emits("updateTempValue", {
    date: formatDateTime(month.timestamp, data.dateFormat),
  });
  // 选中的瞬间不再出现预览效果
  emits("updatePreviewValue");
};
</script>

<style lang="scss" scoped>
.panel {
  &-month {
    width: 220px;
    &__head {
      display: flex;
      align-items: center;
      height: 41px;
      padding: 0 8px;
      border-bottom: 1px solid #e5e6eb;
    }
    &__body {
      padding: 8px 10px;
    }
  }
}
.month-icon {
  width: 60px;
  height: 24px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 2px;
  &:hover {
    background-color: #f2f3f5;
  }
  &_state-hidden {
    visibility: hidden;
  }
}
.year-month {
  flex: 1;
  display: flex;
  justify-content: center;
}
.month-table {
  width: 100%;
  table-layout: fixed;
  border-collapse: collapse;
  td {
    height: 50px;
    vertical-align: middle;
    box-sizing: border-box;
    text-align: center;
  }
}
.month-cell {
  position: relative;
  cursor: pointer;
  &::before {
    position: absolute;
    top: 50%;
    inset-inline-start: 0;
    inset-inline-end: 0;
    z-index: 1;
    height: 30px;
    transform: translateY(-50%);
    transition: all 0.3s;
    content: "";
  }
  &::after {
    position: absolute;
    top: 50%;
    z-index: 0;
    height: 30px;
    border-top: 1px dashed #7cb3ff;
    border-bottom: 1px dashed #7cb3ff;
    transform: translateY(-50%);
    box-sizing: border-box;
    content: "";
  }
  &__inner {
    position: relative;
    z-index: 2;
    display: inline-block;
    min-width: 30px;
    height: 30px;
    line-height: 30px;
    border-radius: 50%;
    transition: background 0.3s, color 0.3s, border 0.3s;
  }
  &:hover:not(&_disabled) &__inner {
    background-color: rgb(89, 100, 251);
    color: rgb(255, 255, 255);
    animation-duration: 1.3s;
    animation-name: heartBeat1;
    animation-timing-function: ease-in-out;
  }
  // 范围
  &_range::before {
    background: #e5edff;
  }
  // 范围-开始
  &_range-start::before {
    background: #e5edff;
    inset-inline-start: 2px;
    border-radius: 50% 0 0 50%;
    margin-left: 8px;
  }
  // 范围-结束
  &_range-end::before {
    background: #e5edff;
    inset-inline-end: 2px;
    border-radius: 0 50% 50% 0;
    margin-right: 8px;
  }
  // 范围-开始 + 范围-结束
  @at-root #{& + "_range-start" + & + "_range-end"}::before {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }
  // 范围-单独
  &_range-alone::before {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }
  // 范围中
  &_ranging::after {
    inset-inline-start: 2px;
    inset-inline-end: 0;
  }
  // 范围中开始
  &_ranging-start::after {
    inset-inline-start: 2px;
    inset-inline-end: 0;
    border-left: 1px dashed #7cb3ff;
    border-radius: 50% 0 0 50%;
    margin-left: 8px;
  }
  // 范围中结束
  &_ranging-end::after {
    inset-inline-start: 2px;
    inset-inline-end: 2px;
    border-right: 1px dashed #7cb3ff;
    border-radius: 0 50% 50% 0;
    margin-right: 8px;
  }
  // 范围中-开始 + 范围中-结束
  @at-root #{& + "_ranging-start" + & + "_ranging-end"}::after {
    border-radius: 50%;
    inset-inline-start: 2px;
    inset-inline-end: 2px;
  }
  // 范围 + 范围中
  @at-root #{& + "_range" + & + "_ranging"}::before,
    #{& + "_range-start" + & + "_ranging"}::before,
    #{& + "_range-end" + & + "_ranging"}::before,
    #{& + "_range" + & + "_ranging-start"}::before,
    #{& + "_range-start" + & + "_ranging-start"}::before,
    #{& + "_range-end" + & + "_ranging-start"}::before,
    #{& + "_range" + & + "_ranging-end"}::before,
    #{& + "_range-start" + & + "_ranging-end"}::before,
    #{& + "_range-end" + & + "_ranging-end"}::before {
    background: rgb(217, 224, 251);
  }
  // 今天
  &_today &__inner::before {
    position: absolute;
    top: 0;
    inset-inline-end: 0;
    bottom: 0;
    inset-inline-start: 0;
    z-index: 1;
    border: 1px solid #5964fb;
    border-radius: 50%;
    content: "";
  }
  // 选中
  &_selected &__inner {
    color: #fff;
    background: #5964fb;
  }
  // 禁用
  &_disabled {
    cursor: default;
  }
  &_disabled::before {
    background: rgba(0, 0, 0, 0.04);
  }
  &_disabled &__inner {
    color: #c9cdd4;
    background: transparent;
  }
}
</style>
