<svg width="70.000000" height="67.302246" viewBox="0 0 70 67.3022" fill="none" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
	<desc>
			Created with Pixso.
	</desc>
	<defs>
		<filter id="filter_135_1673_dd" x="0.000000" y="18.666504" width="70.000000" height="37.333496" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="3.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.5 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<clipPath id="clip244_1489">
			<rect id="视频" width="26.000000" height="26.000000" transform="translate(22.000000 14.000000)" fill="white" fill-opacity="0"/>
		</clipPath>
		<filter id="filter_135_1688_dd" x="3.980957" y="45.975586" width="62.670410" height="21.326660" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
			<feFlood flood-opacity="0" result="BackgroundImageFix"/>
			<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
			<feOffset dx="0" dy="0"/>
			<feGaussianBlur stdDeviation="1.33333"/>
			<feComposite in2="hardAlpha" operator="out" k2="-1" k3="1"/>
			<feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0"/>
			<feBlend mode="normal" in2="BackgroundImageFix" result="effect_dropShadow_1"/>
			<feBlend mode="normal" in="SourceGraphic" in2="effect_dropShadow_1" result="shape"/>
		</filter>
		<radialGradient gradientTransform="translate(70 44.8668) rotate(180) scale(70 201.922)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_135_1665_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3A4B9A" stop-opacity="0.717647"/>
			<stop offset="0.468309" stop-color="#EFF1FF"/>
			<stop offset="1.000000" stop-color="#3A4B9A"/>
		</radialGradient>
		<linearGradient x1="35.000000" y1="29.000000" x2="35.000000" y2="53.266747" id="paint_linear_135_1668_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#6A81F0"/>
			<stop offset="1.000000" stop-color="#AEBBF7"/>
		</linearGradient>
		<linearGradient x1="34.996651" y1="0.000000" x2="34.996651" y2="40.413948" id="paint_linear_135_1669_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#8F8BFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#445DFA"/>
		</linearGradient>
		<radialGradient gradientTransform="translate(60 37.3332) rotate(180) scale(50 144.231)" cx="0.000000" cy="0.000000" r="1.000000" id="paint_radial_135_1673_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#3D4B8B" stop-opacity="0.717647"/>
			<stop offset="0.541144" stop-color="#EFF1FF"/>
			<stop offset="1.000000" stop-color="#3D4B8B"/>
		</radialGradient>
		<linearGradient x1="35.000000" y1="26.000000" x2="35.000000" y2="43.333344" id="paint_linear_135_1676_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#5A76FC"/>
			<stop offset="1.000000" stop-color="#F0F3FF"/>
		</linearGradient>
		<linearGradient x1="46.045227" y1="38.757576" x2="46.045227" y2="31.000000" id="paint_linear_135_1677_0" gradientUnits="userSpaceOnUse">
			<stop offset="0.145038" stop-color="#0B1036" stop-opacity="0.800000"/>
			<stop offset="0.618321" stop-color="#13152A" stop-opacity="0.666667"/>
			<stop offset="1.000000" stop-color="#152340" stop-opacity="0.662745"/>
		</linearGradient>
		<linearGradient x1="34.997620" y1="0.000000" x2="34.997620" y2="34.500000" id="paint_linear_135_1679_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#8F8BFF" stop-opacity="0.000000"/>
			<stop offset="1.000000" stop-color="#445DFA"/>
		</linearGradient>
		<linearGradient x1="42.393806" y1="20.987305" x2="42.393806" y2="32.903973" id="paint_linear_244_1490_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#9EAEFA"/>
		</linearGradient>
		<linearGradient x1="32.622040" y1="19.795898" x2="32.622040" y2="34.106728" id="paint_linear_244_1492_0" gradientUnits="userSpaceOnUse">
			<stop stop-color="#FFFFFF"/>
			<stop offset="1.000000" stop-color="#9EAEFA"/>
		</linearGradient>
	</defs>
	<path id="合并" d="M35 32.7334C20.4398 32.7334 7.9563 35.8154 2.68201 40.2007L2 40.2007C0.895386 40.2007 0 41.0957 0 42.2007L0 44.8672C0.000610352 51.5679 15.6704 57 35 57C54.3296 57 69.9994 51.5679 70 44.8672L70 42.2007C70 41.0957 69.1046 40.2007 68 40.2007L67.318 40.2007C62.0437 35.8154 49.5602 32.7334 35 32.7334Z" clip-rule="evenodd" fill="url(#paint_radial_135_1665_0)" fill-opacity="0.800000" fill-rule="evenodd"/>
	<path id="椭圆 631" d="M0 41.13C0 34.43 15.81 29 35.31 29C54.81 29 70 34.43 70 41.13C70 47.83 54.81 53.26 35.31 53.26C15.81 53.26 0 47.83 0 41.13Z" fill="url(#paint_linear_135_1668_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<g opacity="0.290000">
		<path id="减去顶层" d="M69.9933 40.2173L68.6274 0L0 0L0 40.4141C0 34.915 15.67 30.457 35 30.457C54.0992 30.457 69.6255 34.8091 69.9933 40.2173Z" clip-rule="evenodd" fill="url(#paint_linear_135_1669_0)" fill-opacity="0.900000" fill-rule="evenodd"/>
	</g>
	<g filter="url(#filter_135_1673_dd)">
		<path id="合并" d="M35 28.6665C24.5979 28.6665 15.6796 30.8687 11.9136 34.002C10.8491 34.0474 10 34.9243 10 36L10 37.3335C10.0004 42.1196 21.1931 46 35 46C48.8069 46 59.9996 42.1196 60 37.3335L60 36C60 34.9243 59.1509 34.0474 58.0864 34.002C54.3204 30.8687 45.4021 28.6665 35 28.6665Z" clip-rule="evenodd" fill="url(#paint_radial_135_1673_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
	<path id="椭圆 631" d="M10 34.66C10 29.88 21.29 26 35.22 26C49.15 26 60 29.88 60 34.66C60 39.45 49.15 43.33 35.22 43.33C21.29 43.33 10 39.45 10 34.66Z" fill="url(#paint_linear_135_1676_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="椭圆 631" d="M23 35C23 32.79 28.64 31 35.61 31C42.57 31 48 32.79 48 35C48 37.2 42.57 39 35.61 39C28.64 39 23 37.2 23 35Z" fill="url(#paint_linear_135_1677_0)" fill-opacity="1.000000" fill-rule="evenodd"/>
	<path id="减去顶层" d="M10 0L10 34.5C10 29.8057 21.1929 26 35 26C48.6425 26 59.7327 29.7153 59.9952 34.3325L59.0197 0L10 0Z" clip-rule="evenodd" fill="url(#paint_linear_135_1679_0)" fill-opacity="0.900000" fill-rule="evenodd"/>
	<circle id="椭圆 644" cx="53.000000" cy="33.000000" r="1.000000" fill="#DDE5FA" fill-opacity="1.000000"/>
	<circle id="椭圆 645" cx="15.000000" cy="26.000000" r="1.000000" fill="#DDE5FA" fill-opacity="1.000000"/>
	<circle id="椭圆 646" cx="53.000000" cy="21.000000" r="1.000000" fill="#DDE5FA" fill-opacity="1.000000"/>
	<g clip-path="url(#clip244_1489)">
		<g opacity="0.990000">
			<path id="path" d="M38.95 25.08L38.95 28.79L45.83 32.9L45.83 20.98L38.95 25.08Z" fill="url(#paint_linear_244_1490_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
		</g>
		<g opacity="0.990000">
			<path id="path" d="M39.79 19.79L25.43 19.79C24.74 19.79 24.16 20.35 24.16 21.06L24.16 32.83C24.16 33.53 24.73 34.1 25.43 34.1L39.8 34.1C40.5 34.1 41.07 33.54 41.07 32.83L41.07 21.05C41.06 20.35 40.5 19.79 39.79 19.79ZM29.64 31.38L29.64 22.5L36.88 26.94L29.64 31.38Z" fill="url(#paint_linear_244_1492_0)" fill-opacity="1.000000" fill-rule="nonzero"/>
		</g>
		<path id="多边形 18" d="M38.2 27.25L29.21 22.06L29.21 32.44L38.2 27.25Z" fill="#28286C" fill-opacity="1.000000" fill-rule="evenodd"/>
	</g>
</svg>
