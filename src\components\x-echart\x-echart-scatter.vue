<!-- 
<x-echart-scatter style="width: 500px; height: 500px" :scatterData="scatterData" />
const scatterData: [number, number][] = [
  [10.0, 8.04],
  [8.07, 6.95],
  [13.0, 7.58],
  [9.05, 8.81],
  [11.0, 8.33],
  [14.0, 7.66],
  [13.4, 6.81],
  [10.0, 6.33],
  [14.0, 8.96],
  [12.5, 6.82],
  [9.15, 7.2],
  [11.5, 7.2],
  [3.03, 4.23],
  [12.2, 7.83],
  [2.02, 4.47],
  [1.05, 3.33],
  [4.05, 4.96],
  [6.03, 7.24],
  [12.0, 6.26],
  [12.0, 8.84],
  [7.08, 5.82],
  [5.02, 5.68],
]; 
-->

<template>
  <section class="x-echart-scatter">
    <echart-base :options="options"></echart-base>
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import type { EChartsOption } from "echarts";
import EchartBase from "./base.vue";

const props = withDefaults(
  defineProps<{
    scatterOptionType?: "default" | "test";
    title?: string;
    scatterData: [number, number][];
  }>(),
  {
    title: "",
    scatterOptionType: "default",
  }
);

const options = computed<EChartsOption>(() => {
  const baseOption = {
    tooltip: {
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    legend: {},
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
  };
  return {
    default: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: {},
      yAxis: {},
      series: [
        {
          name: "销量",
          type: "scatter",
          symbolSize: 20,
          data: props.scatterData,
        },
      ],
    },
    test: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: {},
      yAxis: {},
      series: [
        {
          name: "销量",
          type: "scatter",
          data: props.scatterData,
        },
      ],
    },
  }[props.scatterOptionType] as EChartsOption;
});
</script>

<style lang="scss" scoped>
.x-echart-scatter {
  @include wh(100%);
}
</style>
