<template>
  <x-drawer
    :title="$t('editRole')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    :btnOption="{ position: 'center' }"
    bodyPadding="0"
    width="570px"
  >
    <div ref="contentRef" class="content">
      <div class="content-top">
        <div class="content-title">{{ $t("baseInfo") }}</div>
        <x-form ref="formRef" :model="form" :rules="formRules">
          <x-form-item :label="$t('enterprise')" name="entId">
            <span style="padding-left: 10px">{{ form.entName }}</span>
          </x-form-item>
          <x-form-item :label="$t('roleName')" name="roleName">
            <x-input
              v-model:value="form.roleName"
              :placeholder="$t('PEnterRoleName')"
              :maxlength="20"
            />
          </x-form-item>
        </x-form>
      </div>
      <div class="content-bottom">
        <div class="content-title">{{ $t("functionConfig") }}</div>
        <x-checkbox-tree
          v-model:value="form.permitIdList"
          :treeData="formOptions.permTree"
        />
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  editRole,
  getRoleDetail,
  getRoleCurrentPermits,
  roleNameRepeat,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import Message from "@/components/x-message";
import xCheckboxTree from "@/components/x-checkbox-tree.vue";
import {
  resTreeToXTree,
  treePostDfs,
  treeBfsParse,
  debounce,
  i18nSimpleKey,
} from "@/assets/ts/utils";
import areaOptions from "@/assets/ts/area";
const $t = i18nSimpleKey("roleManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 重复角色名称
 */
const repeatRoleName = async () => {
  if (!form.entId) return true;
  if (!form.roleName) return true;
  if (form.roleName === formOptions.detail.roleName) return true;

  const result = await roleNameRepeat({
    roleId: form.roleId,
    roleName: form.roleName,
    entId: String(form.entId),
    isSaveOperation: false,
  });
  return !result?.repeat;
};

/**
 * 插入半选
 */
const appendHalfChecked = (tree: any[], values: any[]) => {
  const half = [] as any[];
  treeBfsParse(tree, "children", (item: any) => {
    if (!(item.children && item.children.length > 0)) return;
    if (values.includes(item.value)) return;
    if (item.children.some((sub: any) => values.includes(sub.value))) {
      half.push(item.value);
    }
  });
  return [...half, ...values];
};

/**
 * 表单项
 */
const form = reactive({
  entId: 0,
  entName: "",
  roleId: "",
  roleName: "",
  permitIdList: [] as any[],
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  roleName: [
    ["required", $t("PEnterRoleName")],
    [debounce(repeatRoleName), $t("roleExist")],
  ],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    const permitIdList = appendHalfChecked(
      formOptions.permTree,
      form.permitIdList
    );
    await editRole({ ...form, permitIdList });
    emits("update:show", false);
    Message("success", $t("saveSuccess"));
    emits("confirm");
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  area: areaOptions,
  permTree: [] as any[],
  detail: {} as any,
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      formOptions.detail = await getRoleDetail(props.id);
      const { roleId, roleName, entId, entName, permitTree } =
        formOptions.detail;
      form.entId = entId;
      form.entName = entName || $t("noData");
      form.roleId = roleId;
      form.roleName = roleName || $t("noData");
      form.permitIdList = [];

      // 已选功能
      treePostDfs(permitTree.subPermList, "subPermList", (sub: any) => {
        form.permitIdList.push(sub.menuId);
      });

      // 功能树
      updatePermTree(entId, roleId);
    }
  }
);

const updatePermTree = async (entId: number, roleId: number) => {
  // 功能树
  formOptions.permTree =
    resTreeToXTree([await getRoleCurrentPermits({ entId, roleId })])?.[0]
      ?.children || [];
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 64px 20px;
  &-top,
  &-bottom {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    .content-title {
      @include sc(14px, #9f9fa4);
    }
  }
  &-top {
    margin-bottom: 15px;
    padding-bottom: 0;
    .content-title {
      padding-bottom: 15px;
    }
  }
  &-bottom {
    .content-title {
      &::before {
        content: "*";
        color: red;
      }
    }
  }
}
</style>
