<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="{ position: 'center' }"
    :title="$t('addSim')"
    width="672px"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    @confirm="addSIMConfirm"
    @cancel="formRef.resetFields()"
  >
    <div ref="contentRef" class="content">
      <x-form ref="formRef" :model="form" :rules="formRules">
        <x-form-item :label="$t('enterprise')" name="entId">
          <x-tree-select
            v-model:value="form.entId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PEnterEnt')"
            showSearch
          />
        </x-form-item>
        <x-form-item :label="$t('simId')" name="simId">
          <x-input
            v-model:value="form.simId"
            :placeholder="$t('PEnterSim')"
            :maxlength="30"
            :filter="simIdFilter"
          />
        </x-form-item>
        <x-form-item :label="$t('operator')" name="netOperator">
          <x-select
            v-model:value="form.netOperator"
            :options="formOptions.netOperator"
            :popupContainer="contentRef"
            :placeholder="$t('PSelect')"
          />
        </x-form-item>
        <x-form-item :label="$t('registerDate')" name="registerDate">
          <DatePicker
            v-model:value="form.registerDate"
            format="YYYY-MM-DD"
            :popupContainer="contentRef"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { compTree, addSim } from "@/services/api";
import { resTreeToXTree } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import { DatePicker } from "@/components/x-date-picker";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import { isSimId } from "@/assets/ts/validate";
import { chineseRegexp } from "@/assets/ts/regexp";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("simManage");

const formRef = ref<any>();
const contentRef = ref<any>();

const simIdFilter = (v: string) => v.replace(chineseRegexp, "");

const form = reactive({
  netOperator: 1,
  registerDate: "",
  entId: "",
  simId: "",
});

const formOptions = reactive({
  company: [] as TreeItemType[],
  netOperator: [
    {
      value: 0,
      label: $t("ChinaMobile"),
    },
    {
      value: 2,
      label: $t("ChinaTelecom"),
    },
    {
      value: 1,
      label: $t("ChinaUnicom"),
    },
  ],
});

const addSIMConfirm = async () => {
  if (formRef.value.validate()) {
    await addSim({
      simId: form.simId,
      netOperator: form.netOperator,
      registerDate: form.registerDate,
      entId: form.entId,
    });
    emits("update:show", false);
    Message("success", $t("addSuccess"));
    emits("confirm");
    formRef.value.resetFields();
  }
};

(async () => {
  formOptions.company = reactive(resTreeToXTree([await compTree()]));
})();

const formRules = reactive({
  entId: [["required", $t("PSelectEnt")]],
  simId: [
    ["required", $t("PEnterSim")],
    [isSimId, $t("simNoIncorrect")],
  ],
});

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
}
</style>
