<template>
  <form class="x-form">
    <slot></slot>
  </form>
</template>
<script lang="ts" setup>
import { ref, reactive, provide, onMounted, watch, nextTick } from "vue";
import { getter, deepCopy, deepReplace } from "@/assets/ts/utils";
import type { PropType } from "vue";
export type ModelType = {
  [index: string]: any;
};
export type FormRulesType = { [x: string]: RulesItemType };
type RulesItemType = (
  | string
  // eslint-disable-next-line no-unused-vars
  | ((v?: string, m?: any) => boolean)
  // eslint-disable-next-line no-unused-vars
  | ((v?: string, m?: any) => Promise<boolean>)
  | Function
)[][];
const props = defineProps({
  model: {
    type: Object as PropType<ModelType>,
    required: true,
  },
  rules: {
    type: Object as PropType<FormRulesType>,
    default: () => ({}),
  },
  labelFlex: {
    type: [String, Number],
  },
  wrapperFlex: {
    type: Number,
  },
});

// const emits = defineEmits(["finish", "finishFailed"]);

// 深度复制
const resetValues = deepCopy(props.model);

const _rules = ref(props.rules);

defineExpose({
  // 重置表单
  resetFields: () => {
    // const _model = props.model;
    // for (const key of Object.keys(_model)) {
    //   _model[key] = "";
    // }
    // 深度替换
    deepReplace(props.model, resetValues);
    // 清除校验提示
    nextTick(() => {
      for (const key of Object.keys(formExplain)) {
        formExplain[key] = "";
      }
    });
  },
  // 验证表单
  validate: () => {
    Object.keys(_rules.value).forEach(async (key) => {
      for (const [condition, message] of _rules.value[key]) {
        const value = getter(props.model, key);
        if (
          (condition === "required" &&
            (["", undefined, null].includes(value) ||
              (typeof value === "number" && isNaN(value)))) ||
          (typeof condition === "function" &&
            !(await condition(value, props.model)))
        ) {
          formExplain[key] = message as string;
          break;
        }
      }
    });
    return !Object.keys(formExplain).some((key) => formExplain[key]);
  },
  // 验证表单-rules包含异步
  asyncValidate: async () => {
    for (const key of Object.keys(_rules.value)) {
      for (const [condition, message] of _rules.value[key]) {
        const value = getter(props.model, key);
        if (
          (condition === "required" &&
            (["", undefined, null].includes(value) ||
              (typeof value === "number" && isNaN(value)))) ||
          (typeof condition === "function" &&
            !(await condition(value, props.model)))
        ) {
          formExplain[key] = message as string;
          break;
        }
      }
    }
    return !Object.keys(formExplain).some((key) => formExplain[key]);
  },
});
const formExplain = reactive<{ [index: string]: string }>({});
onMounted(() => {
  Object.keys(_rules.value).forEach((key) => {
    formExplain[key] = "";
    watch(
      () => getter(props.model, key),
      async (newV) => {
        for (const [condition, message] of _rules.value[key]) {
          if (
            (condition === "required" &&
              (["", undefined, null].includes(newV) ||
                (typeof newV === "number" && isNaN(newV)))) ||
            (typeof condition === "function" &&
              !(await condition(newV, props.model)))
          ) {
            formExplain[key] = message as string;
            return;
          }
        }
        formExplain[key] = "";
      }
    );
  });
});
provide("formContextKey", {
  // formModel: props.model,
  formRules: props.rules,
  formExplain,
  formLabelFlex: props.labelFlex,
  formWrapperFlex: props.wrapperFlex,
  addRule: (key: string, rules: RulesItemType) => {
    if (!_rules.value[key]) _rules.value[key] = [];
    _rules.value[key].push(...rules);
  },
});
</script>

<style lang="scss" scoped>
.x-form {
}
</style>
