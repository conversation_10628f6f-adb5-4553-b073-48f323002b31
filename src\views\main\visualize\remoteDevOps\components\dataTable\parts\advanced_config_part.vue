<template>
  <div
    class="config-panel"
    v-loading="loading"
  >
    <div
      class="column"
      v-for="(group, index) in columns"
      :key="index"
    >
      <el-scrollbar height="280px">
        <div class="block-box">
          <div
            class="block"
            v-for="(el, i) in group"
            :key="i"
          >
            <div
              class="item"
              v-for="item in el"
              :key="item.key"
              :class="{
                'flex-row': item.type === 'switch' || item.type === 'time-range' || item.type === 'button-group',
                'flex-column': item.type === 'slider' || item.type === 'radio-group',
              }"
            >
              <template v-if="item.type === 'switch'">
                <div class="item-label">
                  <span style="margin-right: 10px">{{ item.label }}</span>
                  <el-tooltip
                    effect="dark"
                    :content="item.tipText"
                    placement="right"
                  >
                    <template #content>
                      <div style="width: 200px">
                        {{ item.tipText }}
                      </div>
                    </template>
                    <xIcon
                      v-if="item.tipText"
                      name="warningFilled"
                      width="16"
                      height="16"
                    ></xIcon>
                  </el-tooltip>
                </div>
                <el-switch
                  v-model="item.value"
                  :active-icon="Check"
                  :inactive-icon="Close"
                  :active-value="1"
                  :inactive-value="0"
                  inline-prompt
                  :disabled="getDisabled(item.key)"
                  @change="handleChange(item)"
                />
              </template>

              <template v-else-if="item.type === 'slider'">
                <div
                  v-if="item.label"
                  class="item-label"
                  style="display: flex; align-items: center; justify-content: space-between"
                >
                  <div>
                    <span style="margin-right: 10px">{{ item.label }}</span>
                    <el-tooltip
                      effect="dark"
                      :content="item.tipText"
                      placement="right"
                    >
                      <xIcon
                        name="warningFilled"
                        width="16"
                        height="16"
                      ></xIcon>
                    </el-tooltip>
                  </div>
                  <span
                    v-if="item.Interval"
                    style="margin-left: 60px; color: #555555"
                    >{{ item.Interval }}</span
                  >
                </div>

                <div style="display: flex; align-items: center; justify-content: space-between">
                  <el-slider
                    v-model="item.value"
                    :key="item.key + '-' + item.value"
                    :min="item.min"
                    :max="item.max"
                    :step="item.step"
                    size="small"
                    :disabled="getDisabled(item.key)"
                    @change="handleChange(item)"
                  />
                  <span style="margin-left: 10px; color: #5062ec">{{ item.value ?? 0 }}{{ item.unit ?? "" }}</span>
                </div>
              </template>

              <template v-else-if="item.type === 'radio-group'">
                <div class="item-label">
                  <span style="margin-right: 10px">{{ item.label }}</span>
                  <el-tooltip
                    effect="dark"
                    :content="item.tipText"
                    placement="right"
                  >
                    <xIcon
                      name="warningFilled"
                      width="16"
                      height="16"
                    ></xIcon>
                  </el-tooltip>
                </div>
                <el-radio-group
                  v-model="item.value"
                  :disabled="getDisabled(item.key)"
                  @change="handleChange(item)"
                >
                  <el-radio-button
                    v-for="(label, val) in item.options"
                    :label="val"
                    :key="val"
                  >
                    {{ label }}
                  </el-radio-button>
                </el-radio-group>
              </template>

              <template v-else-if="item.type === 'button-group'">
                <div
                  class="item-label"
                  v-if="item.label"
                >
                  <span style="margin-right: 10px">{{ item.label }}</span>
                  <el-tooltip
                    effect="dark"
                    :content="item.tipText"
                    placement="right"
                  >
                    <xIcon
                      v-if="item.tipText"
                      name="warningFilled"
                      width="16"
                      height="16"
                    ></xIcon>
                  </el-tooltip>
                </div>
                <el-button-group>
                  <el-button
                    v-for="btn in item.buttons"
                    :key="btn.label"
                    :type="item.value === btn.value ? 'primary' : 'default'"
                    :disabled="getDisabled(item.key)"
                    @click="() => handleButtonGroup(item, btn.value)"
                  >
                    {{ btn.label }}
                  </el-button>
                </el-button-group>
              </template>

              <template v-else-if="item.type === 'time-range'">
                <div class="item-label">
                  <span style="margin-right: 10px">{{ item.label }}</span>
                  <el-tooltip
                    effect="dark"
                    :content="item.tipText"
                    placement="right"
                  >
                    <xIcon
                      v-if="item.tipText"
                      name="warningFilled"
                      width="16"
                      height="16"
                    ></xIcon>
                  </el-tooltip>
                </div>
                <el-time-picker
                  v-model="item.value"
                  is-range
                  range-separator="-"
                  start-placeholder="开始时间"
                  end-placeholder="结束时间"
                  style="width: 126px"
                  size="small"
                  format="HH:mm"
                  value-format="HH:mm"
                  :disabled="getDisabled(item.key)"
                  @change="handleChange(item)"
                />
              </template>
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { CarInfo } from "../../../type";
import { getVehicleAdvancedConfig, setVehicleAdvancedConfig } from "@/services/api";
import { ref, reactive, onMounted, inject, type Ref, computed } from "vue";
import { ElMessage } from "element-plus";
import { Check, Close } from "@element-plus/icons-vue";
import type { GetVehicleAdvancedConfigResponse } from "@/services/type";
import xIcon from "@/components/x-icon.vue";
const loading = ref<boolean>(false);
const formModel = ref({
  leftCollisionSensor: 0,
  rightCollisionSensor: 0,
  buzzer: 0,
  idleSchedulingSwitch: 0,
  idleScheduling: 0,
  lowPowerOffProtection: 20,
  lowPowerForcedRecharge: 20,
  taskChargePower: 80,
  trafficLight: 0,
  remoteControlFrame: 0,
  pipelineIdentification: 0,
  automaticContinuingScan: 0,
  automaticContinuingStartTime: "",
  automaticContinuingEndTime: "",
  automaticWaterSwitch: 0,
  automaticWater: 50,
  automaticDumpingSwitch: 0,
  automaticDumping: 50,
  ccoverPathOverlapInterval: 0.2,
  institution: 0,
  smartDrivingProtect: 0,
  snapEnable: 0,
  drivingHabit: 0,
  garbageReport: 0,
  garbageReview: 0,
  bikeReport: 0,
  bikeReview: 0,
  vehicleScheduling: 0,
  transferSpeed: 4.3,
  id: "",
}) as unknown as GetVehicleAdvancedConfigResponse;
const carInfo = inject("carInfo") as Ref<CarInfo>;
const validityPeriod = ref<any[]>([]);
const columns = computed(
  () =>
    [
      [
        [
          {
            label: "左边刷碰撞传感器",
            type: "switch",
            key: "leftCollisionSensor",
            value: formModel.value.leftCollisionSensor,
          },
          {
            label: "右边刷碰撞传感器",
            type: "switch",
            key: "rightCollisionSensor",
            value: formModel.value.rightCollisionSensor,
          },
          {
            label: "蜂鸣器响声开关",
            type: "switch",
            key: "buzzer",
            value: formModel.value.buzzer,
          },
        ],
        [
          {
            label: "空闲调度",
            type: "switch",
            key: "idleSchedulingSwitch",
            value: formModel.value.idleSchedulingSwitch,
            tipText: `返航充电位：车辆空闲后，返回就近的充电位充电。
                  就近停车：车辆空闲后，返回就近的临停区或停车位。`,
          },
          {
            label: "",
            type: "button-group",
            key: "idleScheduling",
            value: formModel.value.idleScheduling,
            buttons: [
              { label: "返航充电位", value: 0 },
              { label: "就近停车", value: 1 },
            ],
          },
        ],
        [
          {
            label: "低电量关机保护",
            type: "slider",
            key: "lowPowerOffProtection",
            value: formModel.value.lowPowerOffProtection,
            min: 15,
            max: 30,
            step: 1,
            range: "",
            tipText: "车辆低于此电量，会自动关机保护；15%~30%可调。",
            unit: "%",
          },
          {
            label: "低电回充电量",
            type: "slider",
            key: "lowPowerForcedRecharge",
            value: formModel.value.lowPowerForcedRecharge,
            min: 15,
            max: 30,
            step: 1,
            range: "",
            tipText: "车辆低于此电量，会自动返航充电；15%~30%可调。",
            Interval: "(15%-30%)",
            unit: "%",
          },
          {
            label: "充电中可作业电量",
            type: "slider",
            key: "taskChargePower",
            value: formModel.value.taskChargePower,
            min: 0,
            max: 100,
            step: 1,
            range: "",
            tipText: "电量大于此值时，才能执行任务。防止车辆电量不足，短暂作业后又要返航。从低电回充电量~100%可调。",
            unit: "%",
          },
        ],
      ],
      [
        [
          {
            label: "红绿灯弹框设置",
            type: "switch",
            key: "trafficLight",
            value: formModel.value.trafficLight,
          },
          {
            label: "远控弹框设置",
            type: "switch",
            key: "remoteControlFrame",
            value: formModel.value.remoteControlFrame,
          },
          {
            label: "管线识别",
            type: "switch",
            key: "pipelineIdentification",
            value: formModel.value.pipelineIdentification,
          },
        ],
        [
          {
            label: "充电中自动续扫",
            type: "switch",
            key: "automaticContinuingScan",
            value: formModel.value.automaticContinuingScan,
            tipText: "打开开关后，每天在生效时段内执行此逻辑。充电到可作业电量时，车辆自动续扫。",
          },
          {
            label: "有效时段",
            type: "time-range",
            key: "automaticContinuingStartTime",
            value: validityPeriod.value,
          },
        ],
        [
          {
            label: "按水量自动加水",
            type: "switch",
            key: "automaticWaterSwitch",
            value: formModel.value.automaticWaterSwitch,
            tipText: "车辆低于此水量，会自动返航加水；15%~80%可调",
          },
          {
            label: "",
            type: "slider",
            key: "automaticWater",
            value: computed({
              get: () => formModel.value.automaticWater,
              set: (val) => (formModel.value.automaticWater = val),
            }),
            min: 15,
            max: 80,
            step: 1,
            range: "",
            unit: "%",
          },
          {
            label: "按垃圾量倾倒",
            type: "switch",
            key: "automaticDumpingSwitch",
            value: formModel.value.automaticDumpingSwitch,
            min: 0,
            max: 100,
            step: 10,
            range: "",
            tipText: "车辆高于此垃圾量，会自动返航倾倒垃圾；10%~100%可调。",
          },
          {
            label: "",
            type: "slider",
            key: "automaticDumping",
            value: formModel.value.automaticDumping,
            min: 10,
            max: 100,
            step: 1,
            range: "",
            unit: "%",
          },
        ],
        [
          {
            label: "覆盖清扫重叠宽度",
            type: "slider",
            key: "ccoverPathOverlapInterval",
            value: formModel.value.ccoverPathOverlapInterval,
            min: 0.15,
            max: 0.5,
            step: 0.05,
            range: "",
            tipText: "车辆进行覆盖清扫时，相邻趟次之间的清扫重叠宽度",
            unit: "m",
          },
        ],
      ],
      [
        [
          {
            label: "巡拣清扫机构",
            type: "switch",
            key: "institution",
            value: formModel.value.institution,
          },
          {
            label: "智驾保护开关",
            type: "switch",
            key: "smartDrivingProtect",
            value: formModel.value.smartDrivingProtect,
            tipText: "特殊场景下可以关闭智驾保护，如路线采集",
          },
          {
            label: "市政设施抓拍上报",
            type: "switch",
            key: "snapEnable",
            value: formModel.value.snapEnable,
          },
          {
            label: "行驶习惯",
            type: "button-group",
            key: "drivingHabit",
            value: formModel.value.drivingHabit,
            buttons: [
              { label: "靠左", value: "charge" },
              { label: "靠右", value: "park" },
            ],
          },
        ],
        [
          {
            label: "工单垃圾上报",
            type: "switch",
            key: "garbageReport",
            value: formModel.value.garbageReport,
          },
          {
            label: "工单垃圾复核",
            type: "switch",
            key: "garbageReview",
            value: formModel.value.garbageReview,
          },
          {
            label: "工单单车事件上报",
            type: "switch",
            key: "bikeReport",
            value: formModel.value.bikeReport,
          },
          {
            label: "工单单车事件复核",
            type: "switch",
            key: "bikeReview",
            value: formModel.value.bikeReview,
          },
          {
            label: "工单车辆调度",
            type: "switch",
            key: "vehicleScheduling",
            value: formModel.value.vehicleScheduling,
          },
        ],
        [
          {
            label: "转场速度",
            type: "slider",
            key: "transferSpeed",
            value: formModel.value.transferSpeed,
            min: 3.5,
            max: 5.4,
            step: 0.1,
            tipText: "默认：4.3km/h；范围3.6km/h~5.4km/h可设置",
            Interval: "(3.6km/h~5.4km/h)",
            isUnit: true,
            unit: "km/h",
          },
        ],
      ],
    ] as any[]
);

const getDisabled = (key: string) => {
  if (
    [
      "trafficLight",
      "leftCollisionSensor",
      "rightCollisionSensor",
      "buzzer",
      "lowPowerOffProtection",
      "remoteControlFrame",
      "pipelineIdentification",
      "drivingHabit",
      "bikeReview" < "vehicleScheduling",
      "institution",
    ].includes(key)
  ) {
    return true;
  } else {
    return false;
  }
};

const handleChange = async (item: any) => {
  loading.value = true;
  const params = {
    id: formModel.value.id,
    [item.key]: item.value,
  } as any;
  try {
    await setVehicleAdvancedConfig(params);
    ElMessage.success("设置成功");
    load();
  } catch (error) {
    console.log(error);
    ElMessage.error("设置失败");
  } finally {
    loading.value = false;
  }
};

const handleButtonGroup = (item: any, val: any) => {
  item.value = val;
  formModel.value[item.key] = val;
  handleChange(item);
};

const load = async () => {
  loading.value = true;
  try {
    const res = await getVehicleAdvancedConfig({ deviceId: carInfo.value.deviceId });
    Object.entries(res ?? {}).forEach(([key, value]) => {
      // 只赋值已声明的 key
      if (key in formModel.value) {
        // 处理类型
        if (typeof formModel.value[key] === "number") {
          formModel.value[key] =
            value === null || value === undefined || value === "" ? formModel.value[key] : Number(value);
        } else if (typeof formModel.value[key] === "string") {
          formModel.value[key] = value === null || value === undefined ? formModel.value[key] : String(value);
        } else {
          formModel.value[key] = value ?? formModel.value[key];
        }
      }
    });
    validityPeriod.value = [res.automaticContinuingStartTime, res.automaticContinuingEndTime];
  } catch (error) {
    console.log("load error", error);
  } finally {
    loading.value = false;
  }
};

onMounted(() => {
  load();
});
</script>

<style lang="scss" scoped>
.config-panel {
  display: flex;
  gap: 16px;
  padding: 10px;
}
.column {
  flex: 1;
  // display: flex;
  // flex-direction: column;
  // gap: 16px;
  width: 100%;
}
.scrollbar-view {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.block-box {
  display: flex;
  flex-direction: column;
  gap: 16px;
}
.block {
  border-radius: 8px;
  background: rgba(244, 247, 254, 1);
  padding: 15px;
  box-shadow: 0 0 4px rgba(0, 0, 0, 0.05);
}
.item-label {
  font-weight: 500;
  margin-bottom: 6px;
  color: #555555;
}

.flex-row {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-column {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}

:deep(.el-scrollbar__bar) {
  &.is-horizontal {
    display: none;
  }

  &.is-vertical {
    width: 6px;

    .el-scrollbar__thumb {
      background-color: rgba(144, 147, 153, 0.3);
      border-radius: 3px;

      &:hover {
        background-color: rgba(144, 147, 153, 0.5);
      }
    }
  }
}

:deep(.el-slider__button) {
  width: 12px !important;
  height: 12px !important;
  border: 1px solid rgba(255, 255, 255, 1) !important;
  background-color: rgba(80, 98, 236, 1) !important;
}
</style>
