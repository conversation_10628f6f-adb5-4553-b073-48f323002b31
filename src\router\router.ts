import { createRouter, createWebHistory, type RouteRecordRaw } from "vue-router";
import { getLocalStorage, setLocalStorage } from "@/assets/ts/storage";
import { getUserInfo, loginByToken } from "@/services/api";
import { useMainStore } from "@/stores/main";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainMenus");

export const routes = [
  {
    path: "/canvas",
    name: "canvas",
    component: () => import("@/views/canvas/canvas.vue"),
    meta: { keepAlive: false },
  },
  {
    path: "/threejs",
    name: "threejs",
    component: () => import("@/views/threejs/threejs.vue"),
    meta: { keepAlive: false },
  },
  {
    path: "/screen",
    name: "screen",
    component: () => import("@/views/screen/screen.vue"),
    meta: { keepAlive: false },
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/login/login.vue"),
    meta: { keepAlive: false },
  },
  {
    path: "/main",
    name: "main",
    redirect: "/main/monitor/carLive",
    component: () => import("@/views/main/main.vue"),
    meta: { keepAlive: false },
    children: [
      {
        path: "visualize",
        name: "visualize",
        redirect: "/main/visualize/companyData/companyData",
        meta: { title: $t("visualize"), isRoute: true },
        children: [
          {
            path: "companyData",
            name: "companyData",
            component: () => import("@/views/main/visualize/companyData/companyData.vue"),
            meta: {
              keepAlive: false,
              title: $t("companyData"),
              authorizationCode: "sys/entdata",
            },
          },
          // 项目数据
          // {
          //   path: "projectData",
          //   name: "projectData",
          //   component: () => import("@/views/main/visualize/projectData/projectData.vue"),
          //   meta: {
          //     keepAlive: false,
          //     title: $t("projectData"),
          //     authorizationCode: "sys/prodata",
          //   },
          // },
          {
            path: "remoteDevOps",
            name: "remoteDevOps",
            component: () => import("@/views/main/visualize/remoteDevOps/remoteDevOps.vue"),
            meta: {
              keepAlive: false,
              title: "远程运维系统",
              // authorizationCode: "sys/remoteDevOps"
              authorizationCode: "sys/prodata",
            },
          },
          // {
          //   path: "operatorChart",
          //   name: "operatorChart",
          //   component: () => import("@/views/main/visualize/operatorChart.vue"),
          //   meta: { keepAlive: false, title: "操作员图表" },
          // },
        ],
      },
      {
        path: "monitor",
        name: "monitor",
        redirect: "/main/monitor/carLive",
        meta: { title: $t("monitor"), isRoute: true },
        children: [
          {
            path: "carLive",
            name: "carLive",
            component: () => import("@/views/main/monitor/carLive/carLive.vue"),
            meta: {
              keepAlive: false,
              title: $t("carLive"),
              authorizationCode: "sys/vehicleOnTime",
            },
          },
          {
            path: "trackReplay",
            name: "trackReplay",
            component: () => import("@/views/main/monitor/trackVideo/trackVideo.vue"),
            meta: {
              keepAlive: false,
              title: $t("trackReplay"),
              authorizationCode: "sys/vehicleLocus",
            },
          },
          {
            path: "alarmStatistics",
            name: "alarmStatistics",
            component: () => import("@/views/main/monitor/alarmStatistics/alarmStatistics.vue"),
            meta: {
              keepAlive: false,
              title: $t("alarmStatistics"),
              authorizationCode: "sys/vehicleWarn",
            },
          },
        ],
      },
      {
        path: "system",
        name: "system",
        redirect: "/main/system/userManage",
        meta: { title: $t("system"), isRoute: true },
        children: [
          {
            path: "companyManage",
            name: "companyManage",
            component: () => import("@/views/main/system/companyManage/companyManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("companyManage"),
              authorizationCode: "sys/ent",
            },
          },
          {
            path: "roleManage",
            name: "roleManage",
            component: () => import("@/views/main/system/roleManage/roleManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("roleManage"),
              authorizationCode: "sys/role",
            },
          },
          {
            path: "userManage",
            name: "userManage",
            component: () => import("@/views/main/system/userManage/userManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("userManage"),
              authorizationCode: "sys/user",
            },
          },
          {
            path: "simManage",
            name: "simManage",
            component: () => import("@/views/main/system/simManage/simManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("simManage"),
              authorizationCode: "sys/sim",
            },
          },
          {
            path: "rtkManage",
            name: "rtkManage",
            component: () => import("@/views/main/system/rtkManage/rtkManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("rtkManage"),
              authorizationCode: "sys/rtk",
            },
          },
          {
            path: "carInfoManage",
            name: "carInfoManage",
            component: () => import("@/views/main/system/carInfoManage/carInfoManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("carInfoManage"),
              authorizationCode: "sys/vehicleInfo",
            },
          },
          {
            path: "chargingStationManage",
            name: "chargingStationManage",
            component: () => import("@/views/main/system/chargingStationManage/chargingStationManage.vue"),
            meta: {
              keepAlive: false,
              title: "配件管理",
              authorizationCode: "sys/chargingStation",
            },
          },
          {
            path: "projectManage",
            name: "projectManage",
            component: () => import("@/views/main/system/projectManage/projectManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("projectManage"),
              authorizationCode: "sys/pro",
            },
          },
          {
            path: "proAreaManage",
            name: "proAreaManage",
            component: () => import("@/views/main/system/proAreaManage/proAreaManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("proAreaManage"),
              authorizationCode: "sys/proArea",
            },
          },
        ],
      },
      {
        path: "operation",
        name: "operation",
        redirect: "/main/operation/proUserManage",
        meta: { title: $t("operation"), isRoute: true },
        children: [
          {
            path: "proUserManage",
            name: "proUserManage",
            component: () => import("@/views/main/operation/proUserManage/proUserManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("proUserManage"),
              authorizationCode: "sys/proUser",
            },
          },
          {
            path: "proVehManage",
            name: "proVehManage",
            component: () => import("@/views/main/operation/proVehManage/proVehManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("proVehManage"),
              authorizationCode: "sys/vehManage",
            },
          },
          {
            path: "vehOperationRecord",
            name: "vehOperationRecord",
            component: () => import("@/views/main/operation/vehOperationRecord/vehOperationRecord.vue"),
            meta: {
              keepAlive: false,
              title: $t("vehOperationRecord"),
              authorizationCode: "sys/VehTaskInfo",
            },
          },
        ],
      },
      {
        path: "report",
        name: "report",
        redirect: "/main/report/vehTaskStatistics",
        meta: { title: $t("report"), isRoute: true },
        children: [
          {
            path: "vehTaskStatistics",
            name: "vehTaskStatistics",
            component: () => import("@/views/main/report/vehTaskStatistics/vehTaskStatistics.vue"),
            meta: {
              keepAlive: false,
              title: $t("vehTaskStatistics"),
              authorizationCode: "sys/vehCalculate",
            },
          },
          {
            path: "vehicleCleanReport",
            name: "vehicleCleanReport",
            component: () => import("@/views/main/report/vehicleCleanReport/vehicleCleanReport.vue"),
            meta: {
              keepAlive: false,
              title: $t("vehicleCleanReport"),
              authorizationCode: "sys/vehCalculate",
            },
          },
          {
            path: "vehTaskSummary",
            name: "vehTaskSummary",
            component: () => import("@/views/main/report/vehTaskSummary/vehTaskSummary.vue"),
            meta: {
              keepAlive: false,
              title: $t("vehTaskSummary"),
              authorizationCode: "sys/vehDataCalculate",
            },
          },
          {
            path: "proAreaSummary",
            name: "proAreaSummary",
            component: () => import("@/views/main/report/proAreaSummary/proAreaSummary.vue"),
            meta: {
              keepAlive: false,
              title: $t("proAreaSummary"),
              authorizationCode: "sys/proDataCalculate",
            },
          },
          {
            path: "vehEventStatistics",
            name: "vehEventStatistics",
            component: () => import("@/views/main/report/vehEventStatistics/vehEventStatistics.vue"),
            meta: {
              keepAlive: false,
              title: $t("vehEventStatistics"),
              authorizationCode: "sys/vehCaptureEvent",
            },
          },
          {
            path: "securityEventStatistics",
            name: "securityEventStatistics",
            component: () => import("@/views/main/report/securityEventStatistics/securityEventStatistics.vue"),
            meta: {
              keepAlive: false,
              title: $t("securityEventStatistics"),
              authorizationCode: "sys/security",
            },
          },
        ],
      },
      {
        path: "config",
        name: "config",
        redirect: "/main/config/carType",
        meta: { title: $t("config"), isRoute: true },
        children: [
          {
            path: "carType",
            name: "carType",
            component: () => import("@/views/main/config/carType/carType.vue"),
            meta: {
              keepAlive: false,
              title: $t("carType"),
              authorizationCode: "sys/vehicleModel",
            },
          },
          {
            path: "alarmManage",
            name: "alarmManage",
            component: () => import("@/views/main/config/alarmManage/alarmManage.vue"),
            meta: {
              keepAlive: false,
              title: $t("alarmManage"),
              authorizationCode: "sys/warnConfig",
            },
          },
          {
            path: "eventCapture",
            name: "eventCapture",
            component: () => import("@/views/main/config/eventCapture/eventCapture.vue"),
            meta: {
              keepAlive: false,
              title: $t("eventCapture"),
              authorizationCode: "sys/capture",
            },
          },
          {
            path: "car-capture",
            name: "car-capture",
            component: () => import("@/views/main/car-capture/car-capture.vue"),
            meta: {
              title: "车辆抓拍查看",
              keepAlive: false,
              authorizationCode: "sys/capture",
            },
          },
          {
            path: "car-capture-upload",
            name: "car-capture-upload",
            component: () => import("@/views/main/car-capture-upload/car-capture-upload.vue"),
            meta: {
              title: "车辆抓拍上传",
              keepAlive: false,
              authorizationCode: "sys/capture",
            },
          },
          {
            path: "order-event-analysis",
            name: "order-event-analysis",
            component: () => import("@/views/main/order-event-analysis/order-event-analysis.vue"),
            meta: {
              title: "工单事件分析",
              keepAlive: false,
              authorizationCode: "sys/capture",
            },
          },
        ],
      },
    ],
  },
  {
    path: "/h5",
    name: "h5",
    redirect: "/h5/live-player",
    component: () => import("@/views/h5/index.vue"),
    meta: { keepAlive: false },
    children: [
      {
        path: "live-player",
        name: "live-player",
        component: () => import("@/views/h5/live-player/index.vue"),
        meta: { title: "直播播放器", isRoute: true },
      },
    ],
  },
  {
    path: "/:pathMatch(.*)*",
    redirect: "/login",
  },
] as RouteRecordRaw[];
import.meta.env.VITE_BASE_URL !== "/prod" &&
  routes.splice(-1, 0, {
    path: "/demo/components",
    name: "components",
    component: () => import("@/views/components/components.vue"),
    meta: { keepAlive: false, title: "组件" },
  } as any);

export const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: routes,
});

router.beforeEach((to, from, next) => {
  console.log(
    `%c${from.fullPath}  ${to.fullPath}  ${String(to.name)}  query:${JSON.stringify(to.query)}`,
    "color:#5964fb;font-weight:bold;font-size:14px;"
  );

  useMainStore().updateActiveCarIdVideo(null); // 切换页面时清空，避免实时查看页面打不开视频
  const authCodeList = getLocalStorage("userInfo")?.urlList;
  const token = getLocalStorage("token");

  // 跳转h5页面
  if (to.fullPath.includes("/h5")) {
    if (to.query.token) {
      setLocalStorage("token", to.query.token);
      const query = JSON.parse(JSON.stringify(to.query));
      delete query.token;
      getUserInfo()
        .then(useMainStore().autoLogin)
        .finally(() => router.push({ name: to.name as string, query }));
    } else {
      next();
    }
  }
  // 携带token参数 == 自动登录
  else if (to.query.token) {
    setLocalStorage("token", to.query.token);
    const query = JSON.parse(JSON.stringify(to.query));
    delete query.token;
    getUserInfo()
      .then(useMainStore().autoLogin)
      .finally(() => router.push({ name: to.name as string, query }));
  }
  // 携带param参数 == 自动登录
  else if (to.query.param) {
    try {
      const param = JSON.parse(atob(to.query.param as string));
      const query = JSON.parse(JSON.stringify(to.query));
      delete query.param;
      loginByToken({ token: param.token })
        .then(useMainStore().autoLogin)
        .finally(() => router.push({ name: to.name as string, query }));
    } catch (error) {
      router.push({ name: "login" });
      throw Error(JSON.stringify(error));
    }
  }
  // 未登录 (跳转登录页) >> 页面无权限 (跳转有权限页/登录页)
  else if (to.fullPath !== "/login" && !token) {
    next({ name: "login" });
  } else if (
    to.matched[0].path === "/main" &&
    to.matched[2].meta.authorizationCode &&
    !authCodeList.includes(to.matched[2].meta.authorizationCode)
  ) {
    const authRouteList =
      routes
        ?.find((v) => v.name === "main")
        ?.children?.filter((v) => v.meta?.isRoute)
        .reduce((acc, cur) => {
          acc.push(...(cur?.children?.filter((v) => authCodeList.includes(v.meta?.authorizationCode)) || []));
          return acc;
        }, [] as typeof routes[0][]) || [];
    // console.log("authRouteList", authRouteList);
    next({ name: authRouteList[0]?.name || "login" });
  } else {
    next();
  }
});
export default router;
