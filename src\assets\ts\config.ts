import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("common");

export const vehOperateStatusType = [
  {
    value: 0,
    label: "部署中",
  },
  {
    value: 1,
    label: "运营中",
  },
  {
    value: 2,
    label: "维修中",
  },
];

export const JobType = [
  {
    value: 1,
    label: $t("entAdmin"),
  },
  {
    value: 2,
    label: $t("proAdmin"),
  },
  {
    value: 3,
    label: $t("custService"),
  },
  {
    value: 6,
    label: "售后员",
  },
  {
    value: 4,
    label: $t("operator"),
  },
  {
    value: 5,
    label: $t("other"),
  },
];

export const warnDealType = [
  {
    value: 0,
    label: $t("unsolved"),
  },
  {
    value: 1,
    label: $t("solved"),
  },
];

export const warnLevelDefType = [
  {
    value: 0,
    label: $t("ordinaryAlarm"),
    icon: "fault_tip0",
    color: "#F8C939",
    definition: "不影响车辆任务执行",
  },
  {
    value: 1,
    label: $t("mildAlarm"),
    icon: "fault_tip1",
    color: "#F9852E",
    definition: "可能影响车辆任务执行",
  },
  {
    value: 2,
    label: $t("urgentAlarm"),
    icon: "fault_tip2",
    color: "#E24562",
    definition: "影响车辆任务执行，需人工介入",
  },
  {
    value: 3,
    label: $t("deadlyAlarm"),
    icon: "fault_tip3",
    color: "#AE1717",
    definition: "影响车辆任务执行且车辆下电，需人工介入",
  },
];

export const isReceivedType = [
  {
    value: 1,
    label: $t("receive"),
  },
  {
    value: 0,
    label: $t("notReceive"),
  },
];

export const vehicleType = [
  {
    value: 1,
    label: "PB",
  },
  {
    value: 2,
    label: "PC",
  },
];

export const warnLevelType = [
  {
    value: 0,
    label: $t("ordinary"),
  },
  {
    value: 1,
    label: $t("mild"),
  },
  {
    value: 2,
    label: $t("urgent"),
  },
  {
    value: 3,
    label: $t("deadly"),
  },
];

export const globalModalType = [
  {
    value: 1,
    label: $t("havePopUp"),
  },
  {
    value: 0,
    label: $t("notPopUp"),
  },
];

export const rtkStatusType = [
  {
    value: 0,
    label: $t("unused"),
  },
  {
    value: 1,
    label: $t("used"),
  },
];

export const netOperatorType = [
  {
    value: 1,
    label: $t("ChinaMobile"),
  },
  {
    value: 2,
    label: $t("QianXun"),
  },
];

export const areaType = [
  {
    value: 2,
    label: $t("full"),
  },
  {
    value: 0,
    label: $t("park"),
  },
  {
    value: 1,
    label: $t("street"),
  },
];

export const sitesType = [
  {
    value: 4,
    label: "停车",
    icon: "map_park",
    btnIcon: "map_park_btn",
    operaText: "去停车",
    stationText: "停车",
    paramText: "parking_in",
  },
  {
    value: 10,
    label: "无人机",
    icon: "map_drone",
    btnIcon: "map_drone_btn",
    operaText: "去起飞",
    stationText: "无人机",
    paramText: "drone_takeoff",
  },
  {
    value: 2,
    label: "自动充电",
    icon: "map_battery",
    btnIcon: "map_battery_btn",
    operaText: "去充电",
    stationText: "自动充电",
    paramText: "parking_charging",
  },
  {
    value: 6,
    label: "手动充电",
    icon: "map_battery",
    btnIcon: "map_battery_btn",
    operaText: "去充电",
    stationText: "手动充电",
    paramText: "parking_charging",
  },
  {
    value: 3,
    label: "自动加水",
    icon: "map_water",
    btnIcon: "map_water_btn",
    operaText: "去加水",
    stationText: "自动加水",
    paramText: "parking_water",
  },
  {
    value: 8,
    label: "手动加水",
    icon: "map_water",
    btnIcon: "map_water_btn",
    operaText: "去加水",
    stationText: "手动加水",
    paramText: "parking_water",
  },
  {
    value: 1,
    label: "对桶倾倒",
    icon: "map_garbage",
    btnIcon: "map_garbage_btn",
    operaText: "倒垃圾",
    stationText: "对桶倾倒",
    paramText: "parking_garbage",
  },
  {
    value: 7,
    label: "就地倒垃圾",
    icon: "map_garbage",
    btnIcon: "map_garbage_btn",
    operaText: "倒垃圾",
    stationText: "就地倒垃圾",
    paramText: "parking_garbage",
  },
  {
    value: 9,
    label: "智能垃圾桶",
    icon: "map_garbage",
    btnIcon: "map_garbage_btn",
    operaText: "倒垃圾",
    stationText: "智能垃圾桶",
    paramText: "parking_garbage",
  },
  {
    value: 5,
    label: "补给点",
    icon: "map_supply",
    btnIcon: "map_supply_btn",
    operaText: "去补给",
    stationText: "补给点",
    paramText: "parking_supply",
  },
];

// export const cleaningType = [
//   {
//     value: "transfer_sweeping",
//     label: $t("normalClean"),
//   },
//   {
//     value: "edgewise",
//     label: $t("edgeClean"),
//   },
//   {
//     value: "transfer_no_sweeping",
//     label: $t("transfer"),
//   },
//   {
//     value: "parking",
//     label: $t("parkCar"),
//   },
// ];

// 新协议临时处理
export const cleaningType = [
  {
    value: "transfer_sweeping",
    label: $t("normalClean"),
  },
  {
    value: "edgewise",
    label: $t("edgeClean"),
  },
  {
    value: "transfer",
    label: $t("transfer"),
  },
  {
    value: "full_covering_clean",
    label: $t("fullCoverClean"),
  },
  {
    value: "garbage_pick",
    label: $t("garbageRecognizeClean"),
  },
  {
    value: "transfer_no_sweeping",
    label: $t("transfer"),
  },
];

export const siteCleaningType = [
  {
    value: "transfer_no_sweeping",
    label: $t("transfer"),
  },
  {
    value: "parking",
    label: $t("parkCar"),
  },
];

export const taskSwitchType = [
  {
    value: 1,
    label: $t("enabled"),
  },
  {
    value: 0,
    label: $t("closed"),
  },
];

export const machineStatusType = [
  {
    value: 0,
    label: $t("notTurnedOff"),
  },
  {
    value: 1,
    label: $t("turnedOff"),
  },
];

export const speedType = [
  {
    label: $t("normalSpeed"),
    value: "2.0", // km/h
    prop: 0.6, // m/s
    icon: "speed_normal",
  },
  {
    label: $t("mediumSpeed"),
    value: "2.8",
    prop: 1,
    icon: "speed_medium",
  },
  {
    label: $t("highSpeed"),
    value: "3.5",
    prop: 1.5,
    icon: "speed_high",
  },
];

export const vehResultType = [
  {
    value: "1",
    label: $t("success"),
  },
  {
    value: "0",
    label: $t("fail"),
  },
];

export const isBindingType = [
  {
    value: true,
    label: $t("bind"),
  },
  {
    value: false,
    label: $t("unBind"),
  },
];

export const accessoriesType = [
  { value: 1, label: "智能垃圾桶" },
  { value: 2, label: "充电加水桩" },
  { value: 3, label: "充电桩" },
];

export const supplierType = [
  {
    value: $t("YiChuangZhiLian"),
    label: $t("YiChuangZhiLian"),
  },
];

export const chargingStationType = [
  {
    value: "CS-01",
    label: "CS-01",
  },
];
// 风机档位类型
export const sweepingType = [
  {
    label: $t("standard"),
    value: "standard",
  },
  {
    label: $t("soft"),
    value: "soft",
  },
  {
    label: $t("power"),
    value: "power",
  },
];
// 基础清扫类型及档位
export const baseCleaningType = [
  {
    label: $t("normalClean"),
    value: "transfer_sweeping",
    children: sweepingType,
  },
  {
    label: $t("travel"),
    value: "transfer",
  },
  {
    label: $t("garbageRecognize"),
    value: "garbage_pick",
  },
];
// 完整清扫类型及档位
export const fullCleaningType = [
  {
    label: $t("normalClean"),
    value: "transfer_sweeping",
    children: sweepingType,
  },
  {
    label: $t("edgeClean"),
    value: "edgewise",
    children: sweepingType,
  },
  {
    label: $t("travel"),
    value: "transfer",
  },
  {
    label: $t("garbageRecognize"),
    value: "garbage_pick",
  },
];
// 安全事件类型
export const securityEventType = [
  {
    label: $t("missManholeCover"),
    value: 1,
  },
  {
    label: $t("FallRecognize"),
    value: 2,
  },
  {
    label: $t("garbageFull"),
    value: 3,
  },
];
// 事件抓拍类型
export const captureEventType = [
  {
    label: $t("garbageDetection"),
    value: 1,
  },
  {
    label: $t("pitDetection"),
    value: 2,
  },
  {
    label: $t("barePicture"),
    value: 3,
  },
];
// 摄像头类型(四轮)
export const fourCameraType = [
  {
    label: $t("frontSide"),
    value: "frontCenter",
  },
  {
    label: $t("leftSide"),
    value: "leftCenter",
  },
  {
    label: $t("rightSide"),
    value: "rightCenter",
  },
  {
    label: $t("backSide"),
    value: "backCenter",
  },
];
// 摄像头类型(三轮)
export const threeCameraType = [
  {
    label: `${$t("back")}190`,
    value: "surrounding_back",
  },
  {
    label: `${$t("left")}190°`,
    value: "surrounding_left",
  },
  {
    label: `${$t("right")}190°`,
    value: "surrounding_right",
  },
  {
    label: `${$t("front")}120°`,
    value: "front_center",
  },
  {
    label: `${$t("right")}120°`,
    value: "right_center",
  },
  {
    label: `${$t("front")}190°`,
    value: "surrounding_front",
  },
];
// 充电桩类型
export const chargingType = [
  {
    label: $t("AC"),
    value: "2",
  },
  {
    label: $t("DC"),
    value: "1",
  },
  {
    label: $t("wireless"),
    value: "3",
  },
];
// 清扫类型
export const taskType = [
  {
    label: $t("coverClean"),
    value: 0,
  },
  // {
  //   label: $t("recognizeClean"), // 识别清扫
  //   value: 1,
  // },
  {
    label: $t("inspectionClean"),
    value: 1,
  },
  {
    label: $t("weltClean"),
    value: 2,
  },
];
// 作业类型
export const scheduleType = [
  {
    label: $t("scheduledTask"),
    value: 1,
    checkStatus: true,
    bgColorLight: "linear-gradient(to right, rgba(117, 132, 246, 0.4) 0%, rgba(255, 255, 255, 1) 10%)",
    bgColorDark: "linear-gradient(to right, rgba(117, 132, 246, 0.4) 0%, rgba(245, 248, 254, 1) 10%)",
  },
  {
    label: $t("scheduledGarbageDisposal"),
    value: 2,
    checkStatus: true,
    bgColorLight: "linear-gradient(to right, rgba(39, 212, 161, 0.4) 0%, rgba(255, 255, 255, 1) 10%)",
    bgColorDark: "linear-gradient(to right, rgba(39, 212, 161, 0.4) 0%, rgba(245, 248, 254, 1) 10%)",
  },
  {
    label: $t("scheduledCharging"),
    value: 3,
    checkStatus: true,
    bgColorLight: "linear-gradient(to right, rgb(253, 226, 205) 0%,   rgba(255, 255, 255, 1) 10%)",
    bgColorDark: "linear-gradient(to right, rgb(253, 226, 205) 0%,   rgba(245, 248, 254, 1) 10%)",
  },
  {
    label: $t("scheduledPowerOnOff"),
    value: 4,
    checkStatus: true,
    bgColorLight: "linear-gradient(to right, rgb(220, 199, 255) 0%, rgba(255, 255, 255, 1) 10%)",
    bgColorDark: "linear-gradient(to right, rgb(220, 199, 255) 0%, rgba(245, 248, 254, 1) 10%)",
  },
  {
    label: $t("scheduledWatering"),
    value: 5,
    checkStatus: true,
    bgColorLight: "linear-gradient(to right, rgb(220, 199, 165) 0%, rgba(255, 255, 255, 1) 10%)",
    bgColorDark: "linear-gradient(to right, rgb(220, 199, 165) 0%, rgba(245, 248, 254, 1) 10%)",
  },
];
// 风机档位
export const blowType = [
  {
    label: $t("close"),
    value: 0,
  },
  {
    label: $t("firstGear"),
    value: 3,
  },
  {
    label: $t("secondGear"),
    value: 4,
  },
  {
    label: $t("thirdGear"),
    value: 5,
  },
];

export const dayType = [
  {
    label: $t("Monday"),
    value: "1",
  },
  {
    label: $t("Tuesday"),
    value: "2",
  },
  {
    label: $t("Wednesday"),
    value: "3",
  },
  {
    label: $t("Thursday"),
    value: "4",
  },
  {
    label: $t("Friday"),
    value: "5",
  },
  {
    label: $t("Saturday"),
    value: "6",
  },
  {
    label: $t("Sunday"),
    value: "7",
  },
];

export const executeDayType = [
  {
    label: $t("daily"),
    value: "daily",
  },
  {
    label: $t("weekly"),
    value: "weekly",
  },
];

export const templateVersionMap: Record<number, string> = {
  1: "路线",
  2: "区块",
  3: "自录路线",
  4: "路线&区块",
};

export const getTemplateVersionName = (version: number = -1) => {
  if ([1].includes(version)) return "（1.0）";
  else if ([2].includes(version)) return "（2.0区块）";
  else if ([3].includes(version)) return "（2.0路线）";
  else if ([4].includes(version)) return "（3.0）";
  else return "";
};
