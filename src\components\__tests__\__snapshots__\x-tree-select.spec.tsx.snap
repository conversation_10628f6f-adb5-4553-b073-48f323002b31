// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 内部快照 1`] = `
<div
  class="x-popover-container"
  style="display: none;"
>
  <!--v-if-->
  <div
    class="x-popover-content-default"
  >
    
    <div
      class="x-tree-select-content"
      data-v-cf21f104=""
      style="width: 0px;"
    >
      <section
        class="x-tree"
        data-v-144535da=""
        data-v-cf21f104=""
      >
        
        <section
          class="tree"
          data-v-093cd503=""
          data-v-144535da=""
        >
          <div
            class="tree-root"
            data-v-093cd503=""
            style="padding-left: 0px;"
          >
            <!--v-if-->
            <div
              class="tree-root-triangle"
              data-v-093cd503=""
            >
              <span
                class=""
                data-v-093cd503=""
              />
            </div>
            <div
              class="tree-root-title"
              data-v-093cd503=""
            >
              
              
              
              
              
              t
              
              
              
              
              i
              
              
              
              
              t
              
              
              
              
              l
              
              
              
              
              e
              
              
              
              
              -
              
              
              
              
              1
              
              
              
              
              
            </div>
          </div>
          <div
            class="tree-child"
            data-v-093cd503=""
            style="display: none;"
          >
            
            <section
              class="tree"
              data-v-093cd503=""
            >
              <div
                class="tree-root"
                data-v-093cd503=""
                style="padding-left: 20px;"
              >
                <!--v-if-->
                <div
                  class="tree-root-triangle"
                  data-v-093cd503=""
                >
                  <!--v-if-->
                </div>
                <div
                  class="tree-root-title"
                  data-v-093cd503=""
                >
                  
                  
                  
                  
                  
                  
                  t
                  
                  
                  
                  
                  i
                  
                  
                  
                  
                  t
                  
                  
                  
                  
                  l
                  
                  
                  
                  
                  e
                  
                  
                  
                  
                  -
                  
                  
                  
                  
                  1
                  
                  
                  
                  
                  -
                  
                  
                  
                  
                  1
                  
                  
                  
                  
                  
                  
                </div>
              </div>
              <div
                class="tree-child"
                data-v-093cd503=""
                style="display: none;"
              >
                
                
              </div>
            </section>
            <section
              class="tree"
              data-v-093cd503=""
            >
              <div
                class="tree-root"
                data-v-093cd503=""
                style="padding-left: 20px;"
              >
                <!--v-if-->
                <div
                  class="tree-root-triangle"
                  data-v-093cd503=""
                >
                  <!--v-if-->
                </div>
                <div
                  class="tree-root-title"
                  data-v-093cd503=""
                >
                  
                  
                  
                  
                  
                  
                  t
                  
                  
                  
                  
                  i
                  
                  
                  
                  
                  t
                  
                  
                  
                  
                  l
                  
                  
                  
                  
                  e
                  
                  
                  
                  
                  -
                  
                  
                  
                  
                  1
                  
                  
                  
                  
                  -
                  
                  
                  
                  
                  2
                  
                  
                  
                  
                  
                  
                </div>
              </div>
              <div
                class="tree-child"
                data-v-093cd503=""
                style="display: none;"
              >
                
                
              </div>
            </section>
            
          </div>
        </section>
        <section
          class="tree"
          data-v-093cd503=""
          data-v-144535da=""
        >
          <div
            class="tree-root"
            data-v-093cd503=""
            style="padding-left: 0px;"
          >
            <!--v-if-->
            <div
              class="tree-root-triangle"
              data-v-093cd503=""
            >
              <!--v-if-->
            </div>
            <div
              class="tree-root-title"
              data-v-093cd503=""
            >
              
              
              
              
              
              t
              
              
              
              
              i
              
              
              
              
              t
              
              
              
              
              l
              
              
              
              
              e
              
              
              
              
              -
              
              
              
              
              2
              
              
              
              
              
            </div>
          </div>
          <div
            class="tree-child"
            data-v-093cd503=""
            style="display: none;"
          >
            
            
          </div>
        </section>
        
      </section>
    </div>
    
  </div>
</div>
`;

exports[`Snapshot > 正确渲染 外部快照 1`] = `
"<section class="x-tree-select" data-v-cf21f104="">
  <section class="x-popover" data-v-cf21f104="">
    <div class="x-tree-select-search-selector" data-v-cf21f104="">
      <section class="x-input" data-v-073bd060="" data-v-cf21f104="">
        <div class="x-input-content" data-v-073bd060="">
          <!--v-if--><input autocomplete="on" class="content-input" style="padding-left: 10px;" type="text" placeholder="" data-v-073bd060="">
          <div class="content-suffix" data-v-073bd060="">
            <x-icon name="input_search" data-v-073bd060=""></x-icon>
          </div>
        </div>
        <div class="x-input-message" data-v-073bd060=""></div>
      </section><span class="" data-v-cf21f104=""></span>
    </div>
    <!--teleport start-->
    <!--teleport end-->
  </section>
</section>"
`;
