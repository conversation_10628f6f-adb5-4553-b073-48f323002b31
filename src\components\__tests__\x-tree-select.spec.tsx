import { describe, it, expect, vi, beforeEach } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue'
import XTreeSelect from '@/components/x-tree-select.vue';
import XInput from '@/components/x-input.vue'

beforeEach(() => {
  document.body.outerHTML = '';
});

const baseProps = {
  showSearch: true,
  value: 'value-1-1',
  treeData: [
    {
      title: 'title-1',
      value: 'value-1',
      children: [
        {
          title: 'title-1-1',
          value: 'value-1-1'
        },
        {
          title: 'title-1-2',
          value: 'value-1-2'
        }
      ]
    },
    {
      title: 'title-2',
      value: 'value-2'
    }
  ]
}

describe('Snapshot', () => {
  it('正确渲染 外部快照', () => {
    const wrapper = mount(XTreeSelect,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 内部快照', () => {
    mount(XTreeSelect,{
      props: { ...baseProps },
    });
    expect(document.querySelector('.x-popover-container')).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = mount(XTreeSelect,{
      props: {
        ...baseProps,
        value: '', // 为验证placeholder
        placeholder: 'placeholder',
        disabled: true,
      },
    });
    const inputProps = wrapper.findComponent(XInput).props();
    expect(inputProps.placeholder).toBe('placeholder');
    expect(inputProps.disabled).toBe(true);
  });
});

describe('Events', () => {
  it('验证searchValue', () => {
    const wrapper = mount(XTreeSelect, {
      props: {
        ...baseProps,
        searchValue: 'searchValue'
      }
    });
    expect(wrapper.vm.config.popShow).toBe(false);

    // 验证触发了focus
    wrapper.find('input').trigger('focus');
    expect(wrapper.vm.config.popShow).toBe(true); 

    // 验证触发了update:searchValue
    wrapper.find('input').trigger('input');
    expect(wrapper.emitted()['update:searchValue'][0][0]).toBe('searchValue');

  });
});