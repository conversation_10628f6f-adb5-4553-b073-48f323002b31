<template>
  <x-drawer
    :title="$t('editCompany')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    :btnOption="{ position: 'center' }"
    bodyPadding="0"
    width="570px"
  >
    <div ref="contentRef" class="content">
      <div class="content-top">
        <div class="content-title">{{ $t("baseInfo") }}</div>
        <x-form ref="formRef" :model="form" :rules="formRules">
          <x-form-item :label="$t('entName')" name="cszgSysEnterprise.entName">
            <x-input
              v-model:value="form.cszgSysEnterprise.entName"
              :placeholder="$t('PEnterEntName')"
              :maxlength="100"
            />
          </x-form-item>
          <x-form-item
            :label="$t('parentEnt')"
            name="cszgSysEnterprise.parentEntId"
          >
            <span style="padding-left: 10px">
              {{ data.detail.parentEnt }}
            </span>
            <!--
          <x-tree-select
            v-model:value="form.cszgSysEnterprise.parentEntId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectParentEnt')"
            showSearch
          />
          -->
          </x-form-item>
          <div class="content-contact">
            <div class="content-contact-user-name">
              <x-form-item
                :label="$t('userName')"
                name="sysUser.userName"
                :labelFlex="21"
              >
                <x-input
                  v-model:value="form.sysUser.userName"
                  :placeholder="$t('PEnterUserName')"
                  :maxlength="10"
                />
              </x-form-item>
            </div>
            <div class="content-contact-mobile">
              <x-form-item
                :label="$t('mobile')"
                name="sysUser.mobile"
                :labelFlex="17"
              >
                <x-input
                  v-model:value="form.sysUser.mobile"
                  :placeholder="$t('PEnterMobile')"
                  :maxlength="11"
                  :filter="mobileFilter"
                />
              </x-form-item>
            </div>
          </div>
          <x-form-item :label="$t('account')" name="sysUser.userAccount">
            <span style="padding-left: 10px">{{
              form.sysUser.userAccount
            }}</span>
          </x-form-item>
          <div class="content-area">
            <div class="content-area__label">{{ $t("entAddress") }}</div>
            <div class="content-area__value">
              <x-form-item
                label=""
                name="cszgSysEnterprise.provinceCityDistrict"
                wrapperStyle="width:100%;"
              >
                <x-cascader
                  v-model:value="form.cszgSysEnterprise.provinceCityDistrict"
                  :options="formOptions.area"
                  :popupContainer="contentRef"
                />
              </x-form-item>
            </div>
          </div>
          <div class="content-addr">
            <x-form-item label="" name="cszgSysEnterprise.entAddr">
              <x-textarea
                v-model:value="form.cszgSysEnterprise.entAddr"
                :placeholder="$t('PEnterEntAddress')"
                :auto-size="{ minRows: 2, maxRows: 5 }"
                :maxlength="150"
                style="margin-left: 74px"
              />
            </x-form-item>
          </div>
        </x-form>
      </div>
      <div class="content-bottom">
        <div class="content-title">{{ $t("functionConfig") }}</div>
        <x-checkbox-tree
          v-model:value="form.permitIdList"
          :treeData="formOptions.permTree"
        />
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  compTree,
  editCompany,
  getCompanyDetail,
  getEntCurrentPermits,
  mobileRepeat,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xCascader from "@/components/x-cascader.vue";
import Message from "@/components/x-message";
import xTextarea from "@/components/x-textarea.vue";
import xCheckboxTree from "@/components/x-checkbox-tree.vue";
import {
  resTreeToXTree,
  treePostDfs,
  treeBfsParse,
  i18nSimpleKey,
} from "@/assets/ts/utils";
import areaOptions from "@/assets/ts/area";
import { isPhoneNumber, isArea } from "@/assets/ts/validate";

import type { TreeItemType } from "@/components/x-tree/tree.vue";
import type { GetCompanyDetailResponse } from "@/services/type";
const $t = i18nSimpleKey("companyManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 引用
 */
const contentRef = ref<any>();
const formRef = ref<any>();

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 重复手机号
 */
const repeatPhoneNumber = async (value: string) => {
  const result = await mobileRepeat({
    mobile: value,
    userId: data.detail.userId,
  });
  return !result?.repeat;
};

/**
 * 插入半选
 */
const appendHalfChecked = (tree: any[], values: any[]) => {
  const half = [] as any[];
  treeBfsParse(tree, "children", (item: any) => {
    if (!(item.children && item.children.length > 0)) return;
    if (values.includes(item.value)) return;
    if (item.children.some((sub: any) => values.includes(sub.value))) {
      half.push(item.value);
    }
  });
  return [...half, ...values];
};

/**
 * 动态值
 */
const data = reactive({
  detail: {} as GetCompanyDetailResponse,
});

/**
 * 表单项
 */
const form = reactive({
  cszgSysEnterprise: {
    entName: "",
    parentEntId: 0,
    entAddr: "",
    entId: 0,
    provinceCityDistrict: [] as string[],
  },
  sysUser: {
    userAccount: "",
    userId: "",
    userName: "",
    mobile: "",
  },
  permitIdList: [] as number[],
});
/**
 * 表单校验规则
 */
const formRules = reactive({
  "cszgSysEnterprise.entName": [["required", $t("PEnterEntName")]],
  "cszgSysEnterprise.parentEntId": [["required", $t("PSelectParentEnt")]],
  "cszgSysEnterprise.provinceCityDistrict": [
    [isArea, $t("PSelectProvinceCityDistrict")],
  ],
  "sysUser.mobile": [
    ["required", $t("PEnterMobile")],
    [isPhoneNumber, $t("mobileIncorrect")],
    [repeatPhoneNumber, $t("mobileExist")],
  ],
});
/**
 * 表单提交
 */
const formSubmit = async () => {
  if (await formRef.value.asyncValidate()) {
    const [province, city, district] =
      form.cszgSysEnterprise.provinceCityDistrict;
    const permitIdList = appendHalfChecked(
      formOptions.permTree,
      form.permitIdList
    );
    await editCompany({
      ...form,
      cszgSysEnterprise: {
        ...form.cszgSysEnterprise,
        province: Number(province),
        city: Number(city),
        district: Number(district),
      },
      permitIdList,
    });
    emits("update:show", false);
    Message("success", $t("saveSuccess"));
    emits("confirm");
  }
};

// 为地区选项添加索引
treeBfsParse(areaOptions, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  area: areaOptions,
  company: [] as TreeItemType[],
  permTree: [] as any[],
});

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      data.detail = await getCompanyDetail(String(props.id));

      const {
        entId,
        entName,
        parentEntId,
        entAddr,
        userAccount,
        userName,
        userId,
        mobile,
        permitTree,
        province,
        city,
        district,
      } = data.detail;

      form.cszgSysEnterprise.entName = entName;
      form.cszgSysEnterprise.parentEntId = parentEntId;
      form.cszgSysEnterprise.entAddr = entAddr;
      form.cszgSysEnterprise.entId = entId;
      form.cszgSysEnterprise.provinceCityDistrict = [];
      form.sysUser.userAccount = userAccount;
      form.sysUser.userId = userId;
      form.sysUser.userName = userName;
      form.sysUser.mobile = mobile;
      form.permitIdList = [];

      if (province) {
        form.cszgSysEnterprise.provinceCityDistrict.push(String(province));
      }
      if (city) {
        form.cszgSysEnterprise.provinceCityDistrict.push(String(city));
      }
      if (district) {
        form.cszgSysEnterprise.provinceCityDistrict.push(String(district));
      }

      // 已选功能
      treePostDfs(permitTree.subPermList, "subPermList", (sub: any) => {
        form.permitIdList.push(sub.menuId);
      });

      // 企业树
      formOptions.company = reactive(resTreeToXTree([await compTree()]));

      // 功能树
      updatePermTree(parentEntId);
    }
  }
);

const updatePermTree = async (parentEntId: number) => {
  // 功能树
  formOptions.permTree =
    resTreeToXTree([await getEntCurrentPermits({ parentEntId })])?.[0]
      ?.children || [];
};

const mobileFilter = (v: string) => v.replace(/\D/g, "");
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 64px 20px;
  &-top,
  &-bottom {
    background: #fff;
    border-radius: 8px;
    padding: 15px;
    .content-title {
      @include sc(14px, #9f9fa4);
    }
  }
  &-top {
    margin-bottom: 15px;
    padding-bottom: 0;
    .content-title {
      padding-bottom: 15px;
    }
    .content-contact {
      display: flex;
      &-user-name {
        flex: 1;
      }
      &-mobile {
        margin-left: 19px;
        width: 258px;
      }
    }
    &-addr {
      padding-left: 71px;
    }
  }
  &-bottom {
    .content-title {
      &::before {
        content: "*";
        color: red;
      }
    }
  }
}
.content-area {
  display: flex;
  &__label {
    flex-basis: 15%;
    flex-shrink: 0;
    flex-grow: 0;
    line-height: 38px;
    padding-left: 8px;
    color: #5e5e5e;
  }
  &__value {
    flex-basis: 0;
    flex-shrink: 1;
    flex-grow: 1;
    overflow: hidden;
  }
}
</style>
