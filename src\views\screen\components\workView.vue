<template>
  <section class="work-view">
    <sub-title>作业概览 - 汇总</sub-title>
    <div class="select-date">
      <x-select
        v-model:value="workView.selValue"
        :options="workView.selOptions"
      />
    </div>
    <div class="work-view-top">
      <div class="top-mile">
        <div class="top-mile-left"></div>
        <div class="top-mile-right">
          <div class="top-number">
            {{ thousandSeparator(formatRoundNum(workView.mile, 1)) }}
          </div>
          <div class="top-unit">里程(km)</div>
        </div>
      </div>
      <div class="top-area">
        <div class="top-area-left"></div>
        <div class="top-area-right">
          <div class="top-number">
            {{ thousandSeparator(formatRoundNum(workView.area, 1)) }}
          </div>
          <div class="top-unit">面积(㎡)</div>
        </div>
      </div>
      <div class="top-time">
        <div class="top-time-left"></div>
        <div class="top-time-right">
          <div class="top-number">
            {{ thousandSeparator(formatRoundNum(workView.time, 1)) }}
          </div>
          <div class="top-unit">时长(h)</div>
        </div>
      </div>
    </div>
    <div class="work-view-bottom">
      <div class="bottom-garbage">
        <div class="bottom-garbage-light"></div>
        <div class="bottom-garbage-number">
          {{ formatRoundNum(workView.garbage, 1) }}
        </div>
        <div class="bottom-garbage-bg"></div>
        <div class="bottom-garbage-unit">垃圾量(L)</div>
      </div>
      <div class="bottom-water">
        <div class="bottom-water-light"></div>
        <div class="bottom-water-number">
          {{ formatRoundNum(workView.water, 1) }}
        </div>
        <div class="bottom-water-bg"></div>
        <div class="bottom-water-unit">耗水量(L)</div>
      </div>
      <div class="bottom-battery">
        <div class="bottom-battery-light"></div>
        <div class="bottom-battery-number">
          {{ formatRoundNum(workView.battery, 1) }}
        </div>
        <div class="bottom-battery-bg"></div>
        <div class="bottom-battery-unit">耗电量(kW·h)</div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { reactive, onMounted, watch } from "vue";
import { toggleWorkViewDate } from "@/services/wsapi";
import subTitle from "./subTitle.vue";
import xSelect from "./x-select.vue";
import { thousandSeparator, formatRoundNum } from "@/assets/ts/utils";
import { useMainStore } from "@/stores/main";
const props = withDefaults(
  defineProps<{
    proId: string;
  }>(),
  {}
);
const workView = reactive({
  selValue: 1,
  selOptions: [
    {
      value: 1,
      label: "当天",
    },
    {
      value: 2,
      label: "昨天",
    },
    {
      value: 3,
      label: "近7天",
    },
    {
      value: 4,
      label: "近30天",
    },
  ],
  mile: "",
  area: "",
  time: "",
  garbage: "",
  water: "",
  battery: "",
});
const { socket } = useMainStore();
watch(
  () => socket.screenWorkView,
  (newV) => {
    workView.mile = newV.calculate.mileage;
    workView.area = newV.calculate.finishedWorkArea;
    workView.time = newV.calculate.taskDuration / 60;
    workView.garbage = newV.calculate.litter;
    workView.water = newV.calculate.usedWaterAmount;
    workView.battery = newV.calculate.usedElectricityAmount;
  }
);

onMounted(() => {
  // 切换项目
  watch(
    () => props.proId,
    (newV) => {
      if (workView.selValue === workView.selOptions[0].value) {
        toggleWorkViewDate(newV, workView.selValue);
      } else {
        workView.selValue = workView.selOptions[0].value;
      }
    }
  );
  // 切换日期
  watch(
    () => workView.selValue,
    (newV) => {
      toggleWorkViewDate(props.proId, newV);
    }
  );
});
</script>

<style lang="scss" scoped>
.work-view {
  position: relative;
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);
  .select-date {
    position: absolute;
    top: 0.2vh;
    right: 20px;
    width: 80px;
  }
  &-top {
    @include fj;
    @include wh(100%, 6.6vh) {
      padding: 1.1vh 34px 1.1vh 20px;
    }
    .top-mile,
    .top-area,
    .top-time {
      width: 26%;
      height: 100%;
      display: flex;
      align-items: center;
      &-left {
        @include wh(40px, 100%);
      }
      &-right {
        margin-left: 4px;
        @include fj {
          flex-direction: column;
        }
      }
    }
    .top-number {
      @include sc(2vh, #fff) {
        font-weight: 600;
      }
    }
    .top-unit {
      @include sc(1.4vh, rgba(255, 255, 255, 0.7)) {
        font-weight: 500;
      }
    }
    .top-mile-left {
      @include bis("@/assets/images/screen_work_view_mile.png");
    }
    .top-area-left {
      @include bis("@/assets/images/screen_work_view_area.png");
    }
    .top-time-left {
      @include bis("@/assets/images/screen_work_view_time.png");
    }
  }
  &-bottom {
    @include fj;
    flex: 1;
    width: 100%;
    padding: 3vh 30px 2vh 30px;
    .bottom-garbage,
    .bottom-water,
    .bottom-battery {
      @include fj {
        flex-direction: column;
        align-items: center;
      }
      position: relative;
      @include wh(30%, 100%);
      &-light {
        position: absolute;
        top: -2.2vh;
        @include ct-f;
        @include wh(56px, 6.1vh);
        animation: animation 1s;
        animation-iteration-count: infinite;
        @keyframes animation {
          0% {
            opacity: 1;
          }
          25% {
            opacity: 0.5;
          }
          50% {
            opacity: 1;
          }
          75% {
            opacity: 0.5;
          }
          100% {
            opacity: 1;
          }
        }
      }
      &-number {
        position: absolute;
        top: -1.4vh;
        @include sc(2.1vh, rgb(243, 255, 255));
      }
      &-bg {
        @include wh(64px, 7.1vh) {
        }
      }
      &-unit {
        margin-top: 1vh;
        @include sc(1.6vh, rgb(205, 206, 225));
      }
    }
    .bottom-garbage {
      &-light {
        @include bis("@/assets/images/screen_work_view_garbage_light.png");
      }
      &-bg {
        @include bis("@/assets/images/screen_work_view_garbage.png");
      }
    }
    .bottom-water {
      &-light {
        @include bis("@/assets/images/screen_work_view_water_light.png");
      }
      &-bg {
        @include bis("@/assets/images/screen_work_view_water.png");
      }
    }
    .bottom-battery {
      &-light {
        @include bis("@/assets/images/screen_work_view_battery_light.png");
      }
      &-bg {
        @include bis("@/assets/images/screen_work_view_battery.png");
      }
    }
  }
}
</style>
