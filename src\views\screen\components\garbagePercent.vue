<template>
  <section class="garbage-percent">
    <img
      class="garbage-percent-top"
      src="@/assets/images/map_garbage_cover.png"
    />
    <div
      v-show="props.percent > 0"
      :class="[
        'garbage-percent-center',
        props.offline
          ? 'offline'
          : showGarbageWarn(props.percent)
          ? 'warn'
          : '',
      ]"
      :style="style.center"
    >
      <div class="garbage-percent-center-top" :style="style.centerTop"></div>
    </div>
    <x-icon
      v-if="showIcon"
      class="garbage-percent-icon"
      name="map_garbage_icon"
      width="12"
      height="12"
    />
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { showGarbageWarn } from "@/services/wsconfig";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  offline: {
    type: <PERSON>olean,
    default: () => false,
  },
  showIcon: {
    type: <PERSON><PERSON>an,
    default: () => false,
  },
});
const style = computed(() => {
  const percent = props.percent / 100;
  const paddingLeft = 3,
    baseWidth = 26,
    gaps = 3;
  const leftTopX = paddingLeft + (1 - percent) * gaps;
  const leftBottomX = paddingLeft + gaps;
  const rightBottomX = paddingLeft + gaps + baseWidth;
  const rightTopX = paddingLeft + gaps + baseWidth + percent * gaps;
  const rightTopY = percent;
  const baseEllipseHeight = 4;
  const baseEllipseTop = 2;
  return {
    center: `
      height: ${percent * 42}px;
      clip-path: polygon(0 -30px, ${leftTopX}px 0, ${leftBottomX}px 100%, ${rightBottomX}px 100%, ${rightTopX}px ${rightTopY}%, 100% -30px);
    `,
    centerTop: `
      left: ${leftTopX}px;
      width: ${rightTopX - leftTopX}px;
      top: -${baseEllipseTop + percent * 2}px;
      height: ${baseEllipseHeight + percent * 3}px;
    `,
  };
});
</script>

<style lang="scss" scoped>
.garbage-percent {
  position: relative;
  @include wh(39px, 60px) {
    @include bis("@/assets/images/map_garbage.png");
  }
  &-icon {
    position: absolute;
    @include ct-p(both) {
      margin-top: 3px;
    }
    z-index: 3;
  }
  &-top {
    z-index: 2;
    position: absolute;
    top: 0;
    left: 0;
    @include wh(39px, 18px);
  }
  &-center {
    transition: all 0.6s ease-in-out;
    position: absolute;
    left: 0;
    bottom: 3px;
    width: 39px;
    background: linear-gradient(
      90deg,
      rgba(53, 193, 154, 1),
      rgba(38, 177, 141, 1) 19.084%,
      rgba(79, 218, 177, 1) 39.695%,
      rgba(68, 207, 166, 1) 75.043%,
      rgba(73, 212, 171, 1) 100%
    );
    &-top {
      transition: all 0.6s ease-in-out;
      z-index: 1;
      position: absolute;
      border-radius: 50%;
      background: linear-gradient(
        180deg,
        rgba(139.34, 225.4, 200.02, 1),
        rgba(74, 213, 172, 1) 99.237%
      );
    }
    &.warn {
      .garbage-percent-center-top {
        background: linear-gradient(
          180deg,
          rgba(253, 109, 136, 1),
          rgba(225, 29, 32, 1) 100%
        );
      }
      background: linear-gradient(
        90deg,
        rgba(244, 21, 21, 1),
        rgba(223, 20, 20, 1) 19.847%,
        rgba(252, 90, 90, 1) 41.985%,
        rgba(244, 21, 21, 1) 73.282%,
        rgba(244, 21, 21, 1) 100%
      );
    }
    &.offline {
      .garbage-percent-center-top {
        background: linear-gradient(
          180deg,
          rgb(235, 236, 236),
          rgb(173, 173, 173) 99.237%
        );
      }
      background: linear-gradient(
        90deg,
        rgb(128, 131, 143),
        rgba(33, 35, 59, 0.74) 24.427%,
        rgb(255, 255, 255) 50.382%,
        rgb(164, 164, 164) 75.573%,
        rgb(144, 143, 151) 100%
      );
    }
  }
}
</style>
