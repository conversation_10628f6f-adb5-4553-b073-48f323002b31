<template>
  <section class="x-timer-image">
    <div
      class="x-timer-image-frames"
      :style="{
        width: `${(props.vertical ? 1 : props.frameNum) * 100}%`,
        height: `${(props.vertical ? props.frameNum : 1) * 100}%`,
        transform: `translate${props.vertical ? 'Y' : 'X'}(${
          -frameIndex * (100 / props.frameNum)
        }%)`,
        backgroundImage: `url(${imageUrl})`,
      }"
    ></div>
    <div class="x-timer-image-content">
      <slot></slot>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, computed } from "vue";
import { animateTime } from "@/assets/ts/animate";
const props = withDefaults(
  defineProps<{
    imageName: string;
    frameNum: number;
    frameTime: number;
    vertical?: boolean; // 是否纵向
  }>(),
  {
    vertical: false,
  }
);

const frameIndex = ref(0);
const imageUrl = computed(
  () => new URL(`/src/assets/images/${props.imageName}`, import.meta.url).href
);

animateTime(() => {
  if (frameIndex.value === props.frameNum - 1) {
    frameIndex.value = 0;
  } else {
    frameIndex.value += 1;
  }
}, props.frameTime);
</script>

<style lang="scss" scoped>
.x-timer-image {
  position: relative;
  @include wh(100%) {
    overflow: hidden;
  }
  &-frames {
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }
  &-content {
    position: absolute;
    top: 0;
    left: 0;
    @include wh(100%);
  }
}
</style>
