<template>
  <x-drawer
    :title="$t('carAuth')"
    :visible="props.show"
    :btnOption="{ position: 'end', confirm: $t('save') }"
    @update:visible="updateVisible"
    @cancel="carDrawerCancel"
    @confirm="carDrawerConfirm"
    width="800px"
  >
    <x-transfer
      :titles="carDrawer.transfer.titles"
      :dataSource="carDrawer.transfer.dataSource"
      :targetSource="carDrawer.transfer.targetSource"
      :locale="carDrawer.transfer.locale"
      showSearch
    />
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getVehAuthList, saveVehAuth } from "@/services/api";
import {
  resTreeToXTree,
  xTreeToXTransferTree,
  i18nSimpleKey,
} from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xTransfer from "@/components/x-transfer.vue";
import Message from "@/components/x-message";
const $t = i18nSimpleKey("proUserManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  userId: {
    type: Number,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const carDrawer = reactive({
  transfer: {
    locale: {
      itemUnit: $t("vehicle"),
      searchPlaceholder: $t("PEnterAreaVehNo"),
    },
    titles: [$t("allCars"), $t("authorizedCars")],
    dataSource: [] as TreeItemType[],
    targetSource: [] as TreeItemType[],
  },
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const resTree = await getVehAuthList(props.id);
      carDrawer.transfer.dataSource = xTreeToXTransferTree(
        resTreeToXTree(resTree.proAreaVelList ? resTree.proAreaVelList : []),
        carDrawer.transfer.targetSource
      );
    }
  }
);

const carDrawerCancel = () => {
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
};

const carDrawerConfirm = async () => {
  await saveVehAuth({
    userId: props.userId,
    velIds: carDrawer.transfer.targetSource.map((v) => v.value) as number[],
  });
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
  Message("success", $t("saveSuccess"));
  emits("update:show", false);
  emits("confirm");
};
</script>
