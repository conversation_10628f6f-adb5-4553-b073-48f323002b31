@charset "UTF-8";
@import "normalize.css";
@import "./animate.css";
@import "./element-plus-override.scss";

:root{
  --font-family: -apple-system, sans-serif;
  --font-title1: 12px; // <h1>
  --font-title2: 12px; // <h2>
  --font-title3: 12px; // <h3>
  --font-title4: 12px; // <h4>
  --font-title5: 12px; // <h5>
  --font-title6: 12px; // <h6>

  --font-content: 14px; // 默认内容
  --font-line-height: 1.5; // 文字行高
  --letter-spacing: normal; // 字符间距
  --word-spacing: normal; // 单词间距

  --z-index-text: 10; // 文字遮盖::after/before
  --z-index-popover: 200; // popover
  --z-index-modal: 9999; // 弹窗
}

body,header,nav,section,article,footer,aside,div,span,hr,
h1, h2, h3, h4, h5, h6, p, pre,
ul,ol,li,dl,dt,dd,
table,tr,th,td
form,input,button,textarea{
  margin: 0;
  padding: 0;
  -webkit-box-sizing: border-box;
  box-sizing: border-box;
  -webkit-tap-highlight-color: transparent;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
body,button,input,select,textarea{
  font: var(--font-content)/var(--font-line-height) var(--font-family);
  letter-spacing: var(--letter-spacing);
  word-spacing: var(--word-spacing);
}
img {vertical-align: middle;border:0;}
a{text-decoration: none;}
a:link,a:visited,a:hover,a:active{text-decoration: none;color:#000;}
ul,ol,li{list-style: none;}
table{border-collapse:collapse;border-spacing:0}
input,button{border-style: none;outline-style: none;display: inline-block;}
textarea{resize: none; outline: none;}
video{object-fit: fill;}

h1{font-size: var(--font-title1);}
h2{font-size: var(--font-title2);}
h3{font-size: var(--font-title3);}
h4{font-size: var(--font-title4);}
h5{font-size: var(--font-title5);}
h6{font-size: var(--font-title6);}
em,i{font-style: normal;}

body{
  @extend %fullscreen;
  #app {
    overflow: hidden;
    @include wh(100%){
      // min-width: 1000px;
      // min-width: 1366px;
      // min-height: 768px;
    };
  }
}

@media screen and (max-width: 1280px) {
  .main, .x-drawer {
    zoom: 0.75;
  }
  .amap-container { // 排除地图缩放问题
    zoom: 1.35;
  }
}

/** v-xloading中的 class['x-loading'] 样式 */
.x-loading {
  user-select: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 99;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  &-mask {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255,255,255,0.8);
    z-index: -1;
  }
  &-gif {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 50px;
    height: 50px;
    background-image: url("@/assets/images/loading.gif");
    background-size: 100% 100%;
    background-repeat: no-repeat;
    z-index: 2;
  }
}