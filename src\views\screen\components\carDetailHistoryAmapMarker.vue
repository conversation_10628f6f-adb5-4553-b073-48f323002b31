<template>
  <section class="car-live-marker">
    <div
      v-show="props.status.online && props.status.warn"
      @click="socket.alarmStatusPop = props.status.deviceId"
      class="marker-warn-icon"
    >
      <x-icon
        class="flicker-animate"
        name="map_warn"
        width="25px"
        height="22.5px"
      />
    </div>
    <div class="marker-radar">
      <template v-if="props.focus">
        <div
          :class="[
            'marker-radar-focus-ripple',
            { disable: !props.status.online },
          ]"
        ></div>
        <div
          :class="[
            'marker-radar-focus-ripple',
            { disable: !props.status.online },
          ]"
        ></div>
      </template>
      <template v-else-if="props.status.online">
        <div
          :class="['marker-radar-ripple', { disable: !props.status.online }]"
        ></div>
      </template>
    </div>
    <div :class="['marker-car-num', { disable: !props.status.online }]">
      {{ props.status.deviceId }}
    </div>
    <div @click="socket.statusPop = props.status.deviceId">
      <div :style="markerInitialStyle">
        <x-icon
          :name="props.status.online ? 'map_car' : 'screen_map_car_disable'"
          :width="props.focus ? '2vw' : '1.6vw'"
          :height="props.focus ? '2.8vw' : '2.4vw'"
        />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import type { PropType } from "vue";
import { computed } from "vue";
import { useMainStore } from "@/stores/main";
import xIcon from "@/components/x-icon.vue";

const props = defineProps({
  status: {
    type: Object as PropType<{
      online: boolean;
      warn: boolean;
      deviceId: string;
    }>,
    required: true,
  },
  focus: {
    type: Boolean,
    default: () => false,
  },
  direction: {
    type: Number,
    default: 0,
  },
});

const { socket } = useMainStore();

// 旋转角
const markerInitialStyle = computed(() => {
  const angle = Math.abs(props.direction);
  const transformAngle =
    props.direction > 0 ? (angle > 180 ? 360 - angle : -angle) : angle;
  return {
    transform: `rotate(${-90 + transformAngle}deg)`,
  };
});
</script>

<style lang="scss" scoped>
.car-live-marker {
  position: relative;
  .marker-radar {
    @include wh(100%) {
      border-radius: 50%;
    }
    &-ripple {
      @include ct-p;
      @include wh(0.6vw) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0 0 0.06vw 0.13vw rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: Ripple 1s linear infinite;
      &.disable {
        background-color: rgba(140, 140, 140, 0.55);
        box-shadow: 0 0 0.13vw 0.27vw rgba(140, 140, 140, 0.55);
        border: 1px solid rgba(140, 140, 140, 0.55);
      }
    }
    @keyframes Ripple {
      to {
        width: 2.7vw;
        height: 2.7vw;
        opacity: 0.3;
      }
    }
    &-focus-ripple {
      @include ct-p;
      @include wh(0.6vw) {
        background-color: rgba(89, 100, 251, 0.4);
        box-shadow: 0 0 0.13vw 0.27vw rgba(89, 100, 251, 0.4);
        border-radius: 50%;
      }
      animation: focusRipple 2.4s linear infinite;
      &:nth-child(1) {
        animation-delay: 0s;
      }
      &:nth-child(2) {
        animation-delay: 1.2s;
      }
      &.disable {
        background-color: rgba(140, 140, 140, 0.55);
        box-shadow: 0 0 0.13vw 0.27vw rgba(140, 140, 140, 0.55);
        border: 0.06vw solid rgba(140, 140, 140, 0.55);
      }
    }
    @keyframes focusRipple {
      80% {
        width: 4vw;
        height: 4vw;
        opacity: 0.3;
      }
      100% {
        width: 2.7vw;
        height: 2.7vw;
        opacity: 0.3;
      }
    }
  }
  .marker-car-num {
    @include ct-p(x) {
      top: -1.6vw;
    }
    height: 1.1vw;
    padding: 0 0.27vw;
    background-color: rgba(89, 100, 251, 0.8);

    border-radius: 0.13vw;
    @include sc(0.8vw, #fff) {
      line-height: 1.1vw;
    }
    white-space: nowrap;
    &.disable {
      background-color: rgba(90, 90, 90, 0.8);
    }
  }
  .marker-warn-icon {
    @include ct-p(x) {
      top: -3.5vw;
    }
    .flicker-animate {
      animation-duration: 0.8s;
      animation-name: flicker;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
      @keyframes flicker {
        0% {
          opacity: 1;
        }
        100% {
          opacity: 0.4;
        }
      }
    }
  }
}
</style>
