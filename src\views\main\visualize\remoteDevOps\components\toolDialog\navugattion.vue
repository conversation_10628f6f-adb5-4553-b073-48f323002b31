<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="导航"
    append-to-body
    @close="emit('update:modelValue', false)"
    width="50%"
  >
    <div class="navigation-dialog-content">
      <div class="navigation-dialog-content-item">
        <div class="navigation-dialog-content-item-title">
          <XEmpty description="暂无数据" />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted } from "vue";
import XEmpty from "@/components/x-empty.vue";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});

const emit = defineEmits(["update:modelValue"]);
</script>
<style scoped lang="scss">
.navigation-dialog {
  .navigation-dialog-content {
    .navigation-dialog-content-item {
      .navigation-dialog-content-item-title {
      }
    }
  }
}
</style>
