<template>
  <div class="route-item-container" ref="taskContentRef">
    <div class="tab-panel-top">
      <div class="tab-panel-top-label">{{ $t("taskType") }}</div>
      <div class="tab-panel-top-content">
        <x-cascader
          v-model:value="form.sweepingTypeList"
          :options="formOptions.options"
          :popupContainer="props.container"
          :allowClear="false"
          :autoSelectFirstChild="true"
        />
      </div>
    </div>
    <div
      class="route-item"
      v-for="(item, index) in form.routeList"
      :key="index"
      @mouseover="toggleHover(index, true)"
      @mouseleave="toggleHover(index, false)"
    >
      <div class="route-item-content">
        <div class="route-item-content-left">
          {{ (index + 1).toString().padStart(2, "0") }}
        </div>
        <div class="route-item-content-center">
          <x-select
            v-model:value="item.vehRouteNo"
            :options="props.routeList"
            :popupContainer="props.container"
            @update:value="changeRouteNo"
            :placeholder="$t('PSelectRoute')"
            style="width: 385px"
          />
        </div>
        <div class="route-item-content-right" v-if="item.hovered">
          <x-icon
            v-if="form.routeList.length > 1"
            class="minus-icon"
            name="minus_circle"
            width="20"
            height="20"
            @click="delRoute(index)"
          />
          <x-icon
            v-if="form.routeList.length < maxRouteLength"
            class="plus-icon"
            name="plus_circle"
            width="20"
            height="20"
            @click="addRoute(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref } from "vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import xCascader from "@/components/x-cascader.vue";
import { baseCleaningType } from "@/assets/ts/config";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

interface RouteOption {
  routeId: string;
  vehRouteNo: string;
  taskType: string;
}

const props = defineProps({
  dataSource: {
    type: Array<RouteOption>,
    required: true,
  },
  routeList: {
    type: Array,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  // 最大路线条数
  maxRouteLength: {
    type: Number,
    default: 10,
  },
});

const emits = defineEmits(["update:value", "delRoute", "changeRoute"]);

const taskContentRef = ref<any>();

const formOptions = reactive({
  options: baseCleaningType,
});

const form = reactive({
  routeList: [
    {
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
      hovered: false,
    },
  ],
  sweepingTypeList: [],
});

// 添加路线
const addRoute = (index: number) => {
  const newRoute = {
    routeId: "0",
    vehRouteNo: "",
    taskType: form.sweepingTypeList[0], // 清扫类型
    sweepingType: form.sweepingTypeList[1] || "", // 清扫强度
    hovered: false,
  };
  form.routeList.splice(index + 1, 0, newRoute);
  emits("update:value", form.routeList);
};

// 删除路线
const delRoute = (index: number) => {
  form.routeList.splice(index, 1);
  emits("update:value", form.routeList);
  emits("delRoute", index);
};

// 更改路线
const changeRouteNo = () => {
  emits("changeRoute");
};

watch(
  () => form.routeList,
  (newV) => {
    emits("update:value", newV);
  }
);

watch(
  () => props.dataSource,
  (newV) => {
    form.routeList = newV;
  },
  {
    immediate: true,
  }
);

watch(
  () => form.sweepingTypeList,
  (newV) => {
    form.routeList.forEach((item) => {
      item.taskType = newV[0]; // 清扫类型
      item.sweepingType = newV[1] || ""; // 清扫强度
    });
    emits("update:value", form.routeList);
  },
  {
    immediate: true,
  }
);

const toggleHover = (index: number, isHovered: boolean) => {
  form.routeList[index].hovered = isHovered;
};
</script>
<style lang="scss" scoped>
.route-item-container {
  .tab-panel-top {
    @include ct-f(y);
    margin: 10px;
    &-label {
      width: 65px;
      @include sc(14px, #383838);
    }
    &-content {
      width: calc(100% - 127px);
    }
  }
  .route-item {
    padding: 10px;
    &-content {
      @include ct-f(y);
      &-left {
        @include wh(20px, 20px) {
          line-height: 20px;
          text-align: center;
        }
        @include sc(14px, #9f9fa4) {
          border: 1px solid #9f9fa4;
          border-radius: 50px;
        }
      }
      &-center {
        display: flex;
        margin-left: 10px;
      }
      &-right {
        display: flex;
        margin-left: 10px;
        .minus-icon,
        .plus-icon {
          cursor: pointer;
        }
        .minus-icon {
          margin-right: 5px;
        }
      }
    }
    &:hover {
      box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease, border 0.3s ease;
    }
  }
}
</style>
