<template>
  <svg
    class="semi-round-percent"
    :width="wh"
    :height="wh"
    viewBox="0 0 96 96"
    fill="none"
  >
    <g>
      <g>
        <!-- 基础半圆 -->
        <circle
          :cx="wh / 2"
          :cy="2"
          r="36"
          fill="none"
          stroke-width="1"
          stroke="#DCDCDC"
          :stroke-dasharray="strokeDasharray.baseRound"
        />
      </g>
      <g>
        <!-- 进度条 -->
        <circle
          :cx="wh / 2"
          :cy="2"
          r="36"
          fill="none"
          stroke-width="4"
          stroke="url('#progress')"
          stroke-linecap="round"
          :stroke-dasharray="`${strokeDasharray.outRound} ${strokeDasharray.baseRound}`"
        />
      </g>
    </g>
    <defs>
      <linearGradient id="progress">
        <stop :stop-color="props.offline ? '#adadad' : '#27d4a1'" />
      </linearGradient>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { computed } from "vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  // 起始范围值
  start: {
    type: Number,
    default: 0,
  },
  // 结束范围值
  end: {
    type: Number,
    default: 100,
  },
  offline: {
    type: Boolean,
    default: () => false,
  },
});
const wh = 96;
const perimeter = Math.PI * 2 * 36;
const percentInRange = computed(() => {
  return (
    Math.min(Math.max(props.percent, props.start), props.end) - props.start
  );
});
const strokeDasharray = computed(() => {
  const range = props.end - props.start;
  const scale =
    percentInRange.value / range > 1 ? 1 : percentInRange.value / range;
  return {
    baseRound: `${(180 / 360) * perimeter}`,
    outRound: `${scale * ((180 / 360) * perimeter)} ${perimeter}`,
  };
});
</script>

<style lang="scss" scoped>
.semi-round-percent {
  transform: rotate(180deg);
  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
