<template>
  <section
    class="car-live"
    ref="carLiveRef"
  >
    <div id="mapContainer"></div>
    <div
      class="car-live-control"
      v-if="permitList.includes('sys:vehicleOnTime:route') || permitList.includes('sys:vehicleOnTime:area')"
    >
      <template v-if="permitList.includes('sys:vehicleOnTime:route')">
        <x-popover
          trigger="hover"
          placement="bottom"
        >
          <template #content>
            <div style="padding: 8px">{{ $t("showRoute") }}</div>
          </template>
          <div
            :class="['control-routes', { enable: socket.showRoutes }]"
            @click="toggleRoutes"
          >
            <x-icon
              width="20px"
              height="20px"
              :name="`map_route${socket.showRoutes ? '_active' : ''}`"
            />
          </div>
        </x-popover>
      </template>
      <template v-if="permitList.includes('sys:vehicleOnTime:area')">
        <x-popover
          trigger="hover"
          placement="bottom"
        >
          <template #content>
            <div style="padding: 8px">{{ $t("showArea") }}</div>
          </template>
          <div
            :class="['control-fences', { enable: socket.showFences }]"
            @click="toggleFences"
          >
            <x-icon
              width="20px"
              height="20px"
              :name="`map_fences${socket.showFences ? '_active' : ''}`"
            />
          </div>
        </x-popover>
      </template>
    </div>
    <carStats
      v-if="mapLoaded"
      :map="mapInfo.map"
      :Amap="mapInfo.Amap"
      v-model:car-filter-show="carFilterProps.show"
      :car-filter-active-id="carFilterProps.activeId"
    />
    <carAlarms
      v-if="mapLoaded && !isMobile"
      :map="mapInfo.map"
      :Amap="mapInfo.Amap"
    />
    <carList
      v-if="socket.enableCarIds.length && mapLoaded"
      :map="mapInfo.map"
      :Amap="mapInfo.Amap"
    />
    <CarFilter
      v-if="mapLoaded"
      :map="mapInfo.map"
      :Amap="mapInfo.Amap"
      v-model:show="carFilterProps.show"
      v-model:active-id="carFilterProps.activeId"
    />
    <carDetail v-if="socket.statusPop" />
    <AlarmModal v-if="socket.alarmStatusPop" />
    <!-- DVR视频流属于临时方案，所以不考虑组件通用性，在内部监听activeCarIdVideo -->
    <!-- <Video /> -->
    <!-- 远程驾驶视频流属于长期方案，所以考虑组件通用性，在外部监听activeCarIdVideo -->
    <!-- <video-multi-flv
      style="position: absolute; left: 0; top: 0"
      :videoList="info.videoArr"
      @close="updateActiveCarIdVideo(null)"
    /> -->
    <VideoRtvs />
    <video-uav />
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, onMounted, onUnmounted, createVNode, render, watch } from "vue";
import { useRouter } from "vue-router";
import { autoView } from "@/services/wsconfig";
import xPopover from "@/components/x-popover.vue";
import carStats from "./components/carStats.vue";
import carAlarms from "./components/carAlarms.vue";
import carList from "./components/carList.vue";
import carDetail from "./components/carDetail.vue";
import Marker from "./components/marker.vue";
import SiteMarker from "@/views/main/components/siteMarker.vue";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
import SiteMarkerInfoWindow from "@/views/main/components/siteMarkerInfoWindow.vue";
import InfoWindow from "./components/infoWindow.vue";
import AlarmModal from "./components/alarmModal.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
// import Video from "./components/video.vue";
// import VideoMultiFlv from "./components/videoMultiFlv.vue";
import VideoRtvs from "./components/videoRtvs.vue";
import { useMainStore } from "@/stores/main";
import { liveCarSites, liveCarRoutes, liveCarFences, changeDriveMode, liveCarVideoInfo } from "@/services/api";
import { sitesType } from "@/assets/ts/config";
import {
  addCar,
  delCar,
  onBaseStatus,
  offBaseStatus,
  offAllStatus,
  openChargingStatus,
  closeChargingStatus,
} from "@/services/wsapi";
import { polylineCarLiveStyle, polylineHoverMarkerStyle, polygonStyle, mapZoom } from "@/services/wsconfig";
import { isMobile, i18nSimpleKey } from "@/assets/ts/utils";
import CarFilter from "./components/carFilter.vue";
import type { UserAreaStationInfo } from "@/services/type";
import UavInfoWindow from "./components/uavInfoWindow.vue";
import VideoUav from "@/components/video_uav/index.vue";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
const $t = i18nSimpleKey("carLive");
const router = useRouter();

const carLiveRef = ref<any>();

const mapInfo = {
  map: null as any,
  Amap: null as any,
  markers: [] as any[],
  siteMarkerHoverMarkersMap: new Map(), // 站点hover浮框
  polylines: [] as any[], // 路线
  polylineHoverMarkersMap: new Map(), // 路线hover浮框
  polygons: [] as any[], // 围栏
};

const info = reactive({
  routeArr: [] as any[],
  fenceArr: [] as any[],
  videoArr: [] as any[],
});
const {
  socket,
  updateShowRoutes,
  updateShowFences,
  updateActiveCarId,
  userInfo: { permitList },
} = useMainStore();

const toggleRoutes = () => {
  if (!socket.showRoutes) {
    addRoutes(info.routeArr);
  } else {
    mapInfo.polylines.map((polyline) => mapInfo.map.remove(polyline));
    mapInfo.polylines = [];
  }
  updateShowRoutes(!socket.showRoutes);
};

const toggleFences = () => {
  if (!socket.showFences) {
    addFences(info.fenceArr);
  } else {
    mapInfo.polygons.map((polygon) => mapInfo.map.remove(polygon));
    mapInfo.polygons = [];
  }
  updateShowFences(!socket.showFences);
};

const stopReq = ref(false);
onMounted(() => {
  // 等待ws连接完成
  const reqWebsocketData = () => {
    setTimeout(() => {
      // ws断开的情况在ws.ts里已经处理
      // const timeoutId = setTimeout(() => {
      //   // ws已断开
      //   if (!socket.baseStatus) {
      //     reqWebsocketData();
      //   }
      // }, 1000);
      Promise.all([
        onBaseStatus(),
        addCar(socket.activeCarId ? [...new Set([...socket.enableCarIds, socket.activeCarId])] : socket.enableCarIds),
      ]).catch(() => {
        // ws还在连接中
        !stopReq.value && reqWebsocketData();
      });
      // .finally(() => {
      //   clearTimeout(timeoutId);
      // });
    }, 200);
  };
  reqWebsocketData();
});
onUnmounted(() => {
  closeChargingStatus();
  socket.instance && offBaseStatus();
  socket.instance && offAllStatus();
  socket.statusPop = false;
  socket.alarmStatusPop = false;
  stopReq.value = true;
});

const mapLoaded = ref(false);
// eslint-disable-next-line no-unused-vars
let removePrevConfirmPopDev = null as null | Function;
lazyAMapApiLoader().then((AMap) => {
  mapInfo.map = new AMap.Map("mapContainer", {
    zooms: [2, 26],
    mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
  });
  mapInfo.Amap = AMap;
  mapLoaded.value = true;

  const scale = new AMap.Scale({
    position: {
      left: "10px",
      bottom: "50px",
    },
  });
  mapInfo.map.addControl(scale);

  liveCarSites().then((res) => {
    addSiteMarker([
      ...res.map((v: any) => ({
        ...v,
        icon: sitesType.find((site) => site.value === v.stationType)?.icon || "",
        point: [v.longitude, v.latitude],
        name: v.stationName,
        stationPic: v.stationPic,
        id: v.id,
        areaId: v.proAreaId,
        stationType: v.stationType,
        stationList: v.stationParkingRelEntityList,
        stationRealTimeInfo: v.stationRealTimeInfo,
      })),
    ]);
  });

  liveCarRoutes().then((res: any) => {
    info.routeArr = res.map(({ gps, routeName }: any) => ({
      routeName,
      position: [...gps.map(({ longitude, latitude }: any) => [longitude, latitude])],
    }));
    socket.showRoutes && addRoutes(info.routeArr);
  });

  liveCarFences().then((res) => {
    info.fenceArr = res.map(({ gps }: any) => gps.map(({ longitude, latitude }: any) => [longitude, latitude]));
    socket.showFences && addFences(info.fenceArr);
  });

  watch(
    () => socket.baseStatus,
    (newV) => {
      if (newV) {
        addMarker(newV);
        updateInfoWindowPosition(newV);
      }
    }
  );
  // watch(
  //   () => socket.activeCarIdVideo,
  //   async (newV) => {
  //     const carInfo = await liveCarVideoInfo(newV);
  //     if (newV && carInfo?.sn) {
  //       info.videoArr = [
  //         {
  //           url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch0`,
  //           name: newV,
  //           position: $t("front"),
  //         },
  //         {
  //           url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch3`,
  //           name: newV,
  //           position: $t("back"),
  //         },
  //         {
  //           url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch1`,
  //           name: newV,
  //           position: $t("left"),
  //         },
  //         {
  //           url: `${import.meta.env.VITE_VIDEO_BASE_URL}${carInfo.sn}_ch2`,
  //           name: newV,
  //           position: $t("right"),
  //         },
  //         // {
  //         //   url: "https://zhuguibiao.github.io/flv.js/assets/264.flv",
  //         //   name: "A0007",
  //         //   position: "环",
  //         // },
  //       ];
  //     } else {
  //       info.videoArr = [];
  //     }
  //   }
  // );
  watch(
    () => socket.taskContinueConfirm,
    (newV) => {
      if (newV) {
        removePrevConfirmPopDev = xModal.confirm({
          title: `"${newV}"${$t("DoCarContinueTask")}`,
          confirmText: $t("continue"),
          cancelText: $t("notNeed"),
          confirm() {
            return changeDriveMode({
              driveMode: 2,
              vehNo: newV,
            }).then(() => {
              Message("success", `${newV}${$t("carContinueTaskSuccess")}`);
              const carInfo = socket.baseStatus?.find((v) => v.deviceId === newV);
              carInfo && mapInfo.map!.setZoomAndCenter(mapZoom, [carInfo.longitude, carInfo.latitude]);
            });
          },
          cancel() {
            return new Promise<void>((resolve) => {
              removePrevConfirmPopDev = null;
              resolve();
            });
          },
        });
      } else {
        removePrevConfirmPopDev && removePrevConfirmPopDev();
        removePrevConfirmPopDev = null;
      }
    }
  );
});

// 实时更新信息窗体位置
const updateInfoWindowPosition = (data: typeof socket.baseStatus) => {
  const openInfoWindow = mapInfo.markers.find((marker) => {
    return marker.getExtData().infoWindow.getIsOpen();
  });
  if (openInfoWindow) {
    const deviceId = openInfoWindow.getExtData().key;
    const dataItem = data?.find((item) => item.deviceId === deviceId);
    if (dataItem && dataItem.online) {
      const marker = mapInfo.markers.find((marker) => {
        return marker.getExtData().key === deviceId;
      });
      if (marker) {
        const { infoWindow } = marker.getExtData();
        infoWindow.open(mapInfo.map, marker.getPosition());
      }
    }
  }
};

const addSiteMarker = (
  sites: ({
    icon: string;
    point: (number | undefined)[];
    name: string | undefined;
    areaId: string | undefined;
    stationList: { id: string; parkName: string }[] | undefined;
  } & UserAreaStationInfo)[]
) => {
  sites.forEach((site) => {
    const markerDiv = document.createElement("div");
    const newMarker = new mapInfo.Amap.Marker({
      content: markerDiv,
      zIndex: 10,
      anchor: "bottom-center",
      // offset: [0, -15],
      position: site.point,
      cursor: "pointer",
      draggable: false,
      bubble: true,
    });
    render(
      createVNode(SiteMarker, {
        focus: false,
        icon: site.icon,
      }),
      markerDiv
    );

    newMarker.on("click", async () => {
      const infoWindowDiv = document.createElement("div");
      const infoWindow = new mapInfo.Amap.InfoWindow({
        isCustom: true,
        content: infoWindowDiv,
        position: site.point,
        offset: [0, -38],
      });
      render(
        createVNode(SiteMarkerInfoWindow, {
          name: `${site?.name ?? ""} - ${site.stationList?.at(0)?.parkName ?? ""}`,
          popupContainer: carLiveRef.value,
          window: infoWindow,
          type: site.icon,
          id: site.id,
          areaId: site.areaId,
          stationPic: site.stationPic,
          stationType: site.stationType,
          stationStatus: site.stationStatus,
          stationRealTimeInfo: site.stationRealTimeInfo,
          stationList:
            site.stationList?.map((station: any) => ({
              ...station,
              status: "idle",
              warn: false,
            })) || [],
        } as InstanceType<typeof SiteMarkerInfoWindow>["$props"]),
        infoWindowDiv
      );
      infoWindow.open(mapInfo.map, site.point);
      if (site.icon === "map_battery") {
        const stationNoArr = site.stationList?.map((v: any) => v.chargingStationNo) || [];
        openChargingStatus(stationNoArr);
      }
    });
    mapInfo.map.add(newMarker);
    // mapInfo.markers.push(newMarker);
  });
};

const renderNameMarker = (markerDiv: HTMLElement, site: any) => {
  render(
    site.type === "siteMarkerInfoWindow"
      ? createVNode(SiteMarkerInfoWindow, {
          type: site.icon,
          name: site.name,
          user: site.user,
          description: site.description,
          location: site.location,
          stationType: site.stationType,
        })
      : createVNode(PolylineNameMarker, { name: site.name }),
    markerDiv
  );
};

const addRoutes = (routes: typeof info.routeArr) => {
  routes.forEach(({ position, routeName }) => {
    const newPathLine = new mapInfo.Amap.Polyline({
      path: position,
      ...polylineCarLiveStyle,
    });
    newPathLine.on("mouseover", (event: any) => {
      const { lng, lat } = event.lnglat;
      const markerDiv = document.createElement("div");
      const _marker = new mapInfo.Amap.Marker({
        ...polylineHoverMarkerStyle,
        content: markerDiv,
        position: [lng, lat],
        extData: {
          markerDiv: markerDiv,
          name: routeName,
        },
      });
      mapInfo.map.add(_marker);
      mapInfo.polylineHoverMarkersMap.set(event.target, _marker);
      renderNameMarker(markerDiv, { name: routeName });
    });
    newPathLine.on("mouseout", (event: any) => {
      const tarMarker = mapInfo.polylineHoverMarkersMap.get(event.target);
      if (tarMarker) {
        mapInfo.map.remove(tarMarker);
        mapInfo.polylineHoverMarkersMap.delete(event.target);
      }
    });
    mapInfo.map.add([newPathLine]);
    mapInfo.polylines.push(newPathLine);
  });
};

const addFences = (fences: typeof info.fenceArr) => {
  fences.forEach((paths) => {
    const newPolygon = new mapInfo.Amap.Polygon({
      path: paths,
      ...polygonStyle,
    });
    mapInfo.map.add([newPolygon]);
    mapInfo.polygons.push(newPolygon);
  });
};

const renderMarker = (
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  info: _BaseStatusType,
  focus: boolean
) => {
  render(
    createVNode(Marker, {
      status: {
        online: info.online,
        warn: info.warn,
        deviceId: info.deviceId,
      },
      focus,
      direction: info.direction,
      deviceType: info.deviceType,
    }),
    markerDiv
  );
};

const renderInfoWindow = (
  infoWindow: typeof mapInfo.Amap.InfoWindow,
  infoWindowDiv: HTMLElement,
  marker: typeof mapInfo.Amap.Marker,
  markerDiv: HTMLElement,
  data: _BaseStatusType
) => {
  if (data.deviceType === 2) {
    // 无人机类型
    render(
      createVNode(UavInfoWindow, {
        router,
        window: infoWindow,
        map: mapInfo.map,
        Amap: mapInfo.Amap,
        id: data.deviceId,
        vin: data.vin,
        markerBlur: () => toggleMarkerFocus(marker, markerDiv, data, false),
      }),
      infoWindowDiv
    );
  } else {
    render(
      createVNode(InfoWindow, {
        router,
        window: infoWindow,
        map: mapInfo.map,
        Amap: mapInfo.Amap,
        id: data.deviceId,
        markerBlur: () => toggleMarkerFocus(marker, markerDiv, data, false),
      }),
      infoWindowDiv
    );
  }
};

const updateExtData = (target: any, extData: object) => {
  target.setExtData({
    ...target.getExtData(),
    ...extData,
  });
};

const toggleMarkerFocus = (marker: typeof mapInfo.Amap.Marker, markerDiv: HTMLElement, info: any, status: boolean) => {
  updateExtData(marker, {
    markerFocus: status,
  });
  // renderMarker(marker, markerDiv, info, status);
};

let delOtherMarker: Function | undefined;
const addMarker = (data: typeof socket.baseStatus) => {
  const map: any = mapInfo.map;
  const Amap: any = mapInfo.Amap;
  data?.forEach((v) => {
    let marker: any = mapInfo.markers.find((item) => item.getExtData().key === v.deviceId);
    if (marker) {
      const { markerDiv, markerFocus } = marker.getExtData();
      marker.setPosition([v.longitude, v.latitude]);
      renderMarker(marker, markerDiv, v, markerFocus);
    } else {
      // 添加InfoWindow
      const infoWindowDiv = document.createElement("div");
      const infoWindow = new Amap.InfoWindow({
        isCustom: true,
        content: infoWindowDiv,
        position: [v.longitude, v.latitude], // 基点位置
        offset: [0, -38], // 位置偏移量 默认[0,0] 基准点在信息窗体的底部中心
      });

      // 添加Marker
      const markerDiv = document.createElement("div");
      marker = new Amap.Marker({
        content: markerDiv,
        position: [v.longitude, v.latitude],
        anchor: "center",
        // offset: [0, 0], // 若设置了anchor，则以anchor设置位置为基准点
        extData: {
          key: v.deviceId,
          markerDiv: markerDiv,
          infoWindowDiv: infoWindowDiv,
          infoWindow: infoWindow,
          markerFocus: false,
        },
      });
      updateExtData(marker, {
        marker: marker,
      });
      renderMarker(marker, markerDiv, v, false);

      map.add(marker);
      mapInfo.markers.push(marker);

      // 辅助看中心点
      // const _marker = new Amap.Marker({
      //   position: [v.longitude, v.latitude],
      // });
      // map.add(_marker);

      const showInfoWindow = (marker: any, markerDiv: any, infoWindow: any, infoWindowDiv: any, deviceId: string) => {
        delOtherMarker && delOtherMarker();
        addCar([deviceId]);
        updateActiveCarId(deviceId);
        toggleMarkerFocus(marker, markerDiv, v, true);
        renderInfoWindow(infoWindow, infoWindowDiv, marker, markerDiv, v);
        infoWindow.open(map, marker.getPosition());
      };

      if (isMobile) {
        // 单击展示窗口
        marker.on("click", () => {
          showInfoWindow(marker, markerDiv, infoWindow, infoWindowDiv, v.deviceId);
        });
        // 双击展示详情
        marker.on("dblclick", () => {
          socket.statusPop = v.deviceId;
        });
      } else {
        marker.on("mouseover", (e: { originEvent: MouseEvent }) => {
          const { top } = markerDiv.getBoundingClientRect();
          // const gap =
          //   window.getComputedStyle(document.querySelector(".main")!).zoom ==
          //   "1"
          //     ? 46
          //     : 194;
          // const markerPixelY = map.lngLatToContainer(marker.getPosition()).y;
          // const mousePixelY = e.pixel.y;
          // const mapHeight = document
          //   .querySelector("#mapContainer")!
          //   .getBoundingClientRect().height;
          // console.log(
          //   "-----------------",
          //   mousePixelY,
          //   markerPixelY,
          //   markerPixelY - mousePixelY,
          //   markerPixelY / mapHeight
          //   // window.innerHeight
          // );
          const mainStyle: any = window.getComputedStyle(document.querySelector(".main")!);
          if (e.originEvent.pageY > top - 46 || mainStyle.zoom != "1") {
            showInfoWindow(marker, markerDiv, infoWindow, infoWindowDiv, v.deviceId);
            closeChargingStatus();
          }
        });
        marker.on("mouseout", () => {
          delOtherMarker = () => {
            // infoWindow.close();
            // map.clearInfoWindow();
            toggleMarkerFocus(marker, markerDiv, v, false);
            !socket.enableCarIds.includes(v.deviceId) && delCar([v.deviceId]);
          };
        });
      }
      // 根据地图上添加的覆盖物分布情况，自动缩放地图到合适的视野级别
      mapInfo.map.setFitView(mapInfo.markers, false, autoView);
    }
  });
};

const carFilterProps = reactive({
  show: false,
  activeId: "",
});
</script>

<style lang="scss" scoped>
.car-live {
  position: relative;
  @include wh(100%);
  #mapContainer {
    @include wh(100%);
  }
  &-control {
    position: absolute;
    top: 8px;
    left: 8px;
    height: 42px;
    padding: 5px;
    border-radius: 4px;
    background-color: #fff;
    display: flex;
    .control-routes,
    .control-fences {
      cursor: pointer;
      @include ct-f;
      @include wh(32px) {
        border-radius: 4px;
        background-color: rgba(229, 230, 232, 0.4);
      }
      &.enable {
        background-color: rgb(229, 237, 255);
      }
    }
    .control-routes {
      margin-right: 6px;
    }
  }
}
::v-deep(.car-live-radar) {
  width: 100%;
  height: 100%;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  z-index: 2;
  .ripple {
    width: 0px;
    height: 0px;
    border-radius: 50%;
    position: absolute;
    z-index: 1;
    box-shadow: 0px 0px 2px 4px #f00;
    border: 1px solid #f00;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    animation: ripple 2s linear infinite;
    &:nth-child(1) {
      animation-delay: 0.666s;
    }
    &:nth-child(2) {
      animation-delay: 1.322s;
    }
  }
}
@keyframes ripple {
  to {
    width: 60px;
    height: 60px;
    opacity: 0;
  }
}
</style>
