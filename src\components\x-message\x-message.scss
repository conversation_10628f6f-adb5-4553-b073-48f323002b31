
.x-message {
  z-index: var(--z-index-modal);
  &-error {
    @include ct-f(y);
    @include ct-p(x){
      top: 58px;
    }
    @include wh(auto, 46px) {
      padding: 0 16px;
      background-color: #fff;
      box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2),
        0px 4px 30px rgba(0, 0, 0, 0.1);
      border-radius: 4px;
    }
    img {
      @include wh(20px);
    }
    span {
      margin-left: 8px;
    }
  }
  &-enter {
    &-from {
      top: 40px;
      opacity: 0;
    }
    &-active {
      transition: all .5s;
    }
    // &-to {
    //   top: 58px;
    //   opacity: 1;
    // }
  }
  &-leave {
    // &-from {
    //   top: 58px;
    //   opacity: 1;
    // }
    &-active {
      transition: all .5s;
    }
    &-to {
      top: 40px;
      opacity: 0;
    }
  }
}