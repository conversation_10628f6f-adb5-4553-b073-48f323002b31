import { describe, it, expect, afterEach, vi } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick, createVNode } from 'vue';
import XForm from '@/components/x-form.vue';
import XFormItem from '@/components/x-form-item.vue';

const XFormBaseProps = { 
  model: {
    username: "",
    password: "",
  }
}
const XFormRules = {
  username: [['required', '请输入用户名']],
  password: [[str=>str.length>=6, '请输入6位以上的密码']]
}
const usernameBaseProps = {
  label: '用户名',
  name: 'username'
}
const passwordBaseProps = {
  label: '密码',
  name: 'password'
}

// x-form + x-form-item
describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = mount(XForm, {
      props: { ...XFormBaseProps },
      slots: {
        default: () => [
          createVNode(XFormItem, {...usernameBaseProps}),
          createVNode(XFormItem, {...passwordBaseProps})
        ],
      }
    })
    expect(wrapper.html()).toMatchSnapshot();
  });
});

// x-form
describe('Props Render + Events', () => {
  it('render + events', () => {
    // const wrapper = mount({
    //   setup() {
    //     return () => <XForm {...XFormBaseProps} labelFlex={11} wrapperFlex={22} rules={XFormRules}>
    //       <XFormItem {...usernameBaseProps}></XFormItem>
    //       <XFormItem {...passwordBaseProps}></XFormItem>
    //     </XForm>
    //   }
    // });
    const wrapper = mount(XForm, {
      props: {
        ...XFormBaseProps,
        labelFlex: 11,
        wrapperFlex: 22,
        rules: XFormRules
      },
      slots: {
        default: () => [
          createVNode(XFormItem, {...usernameBaseProps}),
          createVNode(XFormItem, {...passwordBaseProps})
        ],
      }
    })
    const [userXFormItem, passXFormItem] = wrapper.findAllComponents(XFormItem);
    expect(userXFormItem.find('.x-form-item-label').attributes().style).toBe('flex: 11;');
    expect(userXFormItem.find('.x-form-item-control').attributes().style).toBe('flex: 22;');
    expect(passXFormItem.find('.x-form-item-label').attributes().style).toBe('flex: 11;');
    expect(passXFormItem.find('.x-form-item-control').attributes().style).toBe('flex: 22;');

    // 验证rules
    wrapper.setProps({ 
      model: {
        username: null,
        password: "111",
      }
    })
    nextTick(()=>{
      expect(wrapper.vm.formExplain.username).toBe('请输入用户名')
      expect(wrapper.vm.formExplain.password).toBe('请输入6位以上的密码')

      // 无法调用resetFields，暂不验证model的变化
      expect(wrapper.vm.resetFields).toBeUndefined()
    })
  });
});

// x-form-item
describe('Props Render + Events', () => {
  it('render + events', () => {
    const wrapper = mount(XForm, {
      props: { ...XFormBaseProps },
      slots: {
        default: () => [
          createVNode(XFormItem, {...usernameBaseProps,
            rules: [[str=>str.length>=6, '请输入用户名']],
            labelFlex: 33,
            wrapperFlex: 44,
          }),
          createVNode(XFormItem, {...passwordBaseProps, 
            rules: [['required', '请输入6位以上的密码']],
            labelFlex: 55,
            wrapperFlex: 66,
            wrapperStyle: 'width: 66%;',
            extra: '77'
          })
        ],
      }
    })
    const [userXFormItem, passXFormItem] = wrapper.findAllComponents(XFormItem);
    expect(userXFormItem.find('.x-form-item-label').attributes().style).toBe('flex: 33;');
    expect(userXFormItem.find('.x-form-item-control').attributes().style).toBe('flex: 44;');
    expect(passXFormItem.find('.x-form-item-label').attributes().style).toBe('flex: 55;');
    expect(passXFormItem.find('.x-form-item-control').attributes().style).toBe('width: 66%;');
    expect(passXFormItem.find('.x-form-item-extra').text()).toBe('77');
    
    // 成功传递给x-form
    expect(wrapper.props().rules.username[0][1]).toBe('请输入用户名')
    expect(wrapper.props().rules.password[0][1]).toBe('请输入6位以上的密码')
  });
});