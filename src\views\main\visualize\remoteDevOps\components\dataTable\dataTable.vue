<template>
  <div class="data-table">
    <alarm-page :deviceId="deviceId" />
    <car-info-page
      ref="carInfoPageRef"
      :deviceId="deviceId"
    />
    <all-alarm-dispose-page
      :deviceId="deviceId"
      @skip="handleSkip"
    />
  </div>
</template>

<script setup lang="ts">
import alarmPage from "./parts/alarm_page.vue";
import carInfoPage from "./parts/car_info_page.vue";
import allAlarmDisposePage from "./parts/all_alarm_dispose_page.vue";
import { ref } from "vue";
const props = defineProps({
  deviceId: {
    type: String,
    default: "",
  },
});

const emit = defineEmits(["skip"]);
const carInfoPageRef = ref();

const handleSkip = (deviceId: string) => {
  emit("skip", deviceId);
};

const reset = () => {
  carInfoPageRef.value?.reset();
};

defineExpose({
  reset,
});
</script>
<style scoped lang="scss">
.data-table {
  position: absolute;
  bottom: 0px;
  left: 0px;
  width: calc(100% - 8px);
  height: 379px;
  background-color: #fff;
  z-index: 160;
  margin-left: 8px;
  display: flex;
}
</style>
