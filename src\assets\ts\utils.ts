import type { TreeItemType } from "@/components/x-tree/tree.vue";
import i18n from "@/language";
import { ref, onUnmounted } from "vue";
export const i18nSimpleKey = (firstKey?: string) => (secondKey: string) =>
  i18n.global.t(`${firstKey ? firstKey + "." : ""}${secondKey}`);

export const uuid = () => {
  const s = [];
  const hexDigits = "0123456789abcdef";
  for (let i = 0; i < 36; i++) {
    s[i] = hexDigits.substr(Math.floor(Math.random() * 0x10), 1);
  }
  s[8] = s[13] = s[18] = s[23] = "-";

  const uuid = s.join("");
  return uuid;
};

export const debounce = (func: (...args: any[]) => void, wait = 200, immediate = false) => {
  let timeout: NodeJS.Timeout | null = null;
  const _debounce = (...args: any[]) =>
    new Promise((resolve, reject) => {
      if (timeout) clearTimeout(timeout);
      if (immediate) {
        const callNow = !timeout;
        timeout = setTimeout(() => {
          timeout = null;
        }, wait);
        if (callNow) {
          try {
            const result = func.apply(this, args);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }
      } else {
        timeout = setTimeout(() => {
          try {
            const result = func.apply(this, args);
            resolve(result);
          } catch (error) {
            reject(error);
          }
        }, wait);
      }
    });
  return _debounce;
};

export const throttle = (func: { (): void; (): void; apply?: any }, wait = 1000, immediate = false) => {
  let timeout: string | number | NodeJS.Timeout | undefined,
    canRun = true;
  const _throttle = (...args: any[]) => {
    if (!canRun) return;
    canRun = false;
    if (immediate) {
      // eslint-disable-next-line prefer-spread
      func.apply(null, args);
      timeout = setTimeout(() => {
        canRun = true;
      }, wait);
    } else {
      timeout = setTimeout((...args) => {
        // eslint-disable-next-line prefer-spread
        func.apply(null, args);
        canRun = true;
      }, wait);
    }
  };
  _throttle.cancel = function () {
    clearTimeout(timeout);
    canRun = true;
  };
  return _throttle;
};

/**
 * tree 树结构数据
 * childKey 子节点key
 * parse 处理函数
 */
export const treeBfsParse = (tree: any[], childKey: string, parseFunc: Function) => {
  const _itemStack = [...tree];
  let _item = _itemStack[0];
  let _count = 0;
  while (_item) {
    parseFunc(_item, _count);
    if (_item[childKey] && _item[childKey].length > 0) {
      _itemStack.push(..._item[childKey]);
    }
    _item = _itemStack[++_count];
  }
};

/**
 * tree 树结构数据
 * childKey 子节点key
 * parse 处理函数
 */
export const treePostDfs = (tree: any[], childKey: string, parseFunc: Function) => {
  const _dfs = (_tree: any[]) => {
    _tree.forEach((_item: { [x: string]: any }) => {
      if (_item[childKey] && _item[childKey].length > 0) {
        _dfs(_item[childKey]);
      }
      parseFunc(_item);
    });
  };
  _dfs(tree);
};

export const isProject = (item: TreeItemType) => item.velList !== undefined;
export const isAreaProject = (item: TreeItemType) => item.areaList !== undefined;

// 企业树
// 企业    subEntList子企业   proList项目    velList车辆
// 项目    velList车辆

// 企业权限树
// 企业    subPermList权限/子权限

// 项目人员(车辆授权)
// 项目    areaList区域       vehNoList车辆
// 区域    vehNoList车辆
export const resTreeToXTree = (tree: any[]) => {
  const _itemStack = [...tree];
  let _item = _itemStack[0] as TreeItemType;
  let _count = 0;
  while (_item) {
    _item._type = (() => {
      if (_item.subEntList !== undefined || _item.proList !== undefined) {
        return "ent";
      } else if (isProject(_item)) {
        return "pro";
      } else if (isAreaProject(_item)) {
        return "proA";
      } else if (_item.subPermList !== undefined) {
        return "per";
      } else if (_item.vehNoList !== undefined) {
        return "area";
      } else {
        return "car";
      }
    })();
    _item.title = {
      ent: _item.entName,
      pro: _item.proName,
      proA: _item.proName,
      per: _item.name,
      area: _item.areaName,
      car: _item.vehicleNo,
    }[_item._type as string];
    _item.value = {
      ent: _item.entId,
      pro: _item.id,
      proA: _item.id,
      per: _item.menuId,
      area: _item.id,
      car: _item.id,
    }[_item._type as string];
    _item.children = {
      // 由于后端会过滤proList所以无需onlyEnt
      ent: [...(_item.subEntList || []), ...(_item.proList || [])],
      pro: _item.velList,
      proA: [...(_item.areaList || []), ...(_item.vehNoList || [])],
      per: _item.subPermList,
      area: _item.vehNoList,
      car: [],
    }[_item._type as string];

    _itemStack.push(...(_item.children || []));
    _item = _itemStack[++_count];
  }
  return tree;
};
export const xTreeToXTransferTree = (dataTree: TreeItemType[], targetList: TreeItemType[] = []) => {
  treeBfsParse(dataTree, "children", (item: { _type: string; checked: boolean; disabled: boolean }) => {
    if (item._type === "car" && item.checked) {
      item.checked = false;
      targetList.push(JSON.parse(JSON.stringify(item)));
      item.disabled = true;
    }
  });

  treePostDfs(dataTree, "children", (item: { children: any[]; disabled: any }) => {
    if (item.children && item.children.length > 0) {
      item.disabled = item.children.every((v: { disabled: any }) => v.disabled);
    }
  });
  return dataTree;
};

export const fixXTreeChecked = (tree: TreeItemType[]) => {
  treePostDfs(tree, "children", (item: { children: any[]; checked: any; indeterminate: boolean }) => {
    if (item.children && item.children.length > 0) {
      item.checked = item.children.every((v) => v.checked);
      item.indeterminate = Boolean(
        item.children?.some((v) => v.checked || v.indeterminate) && item.children?.some((v) => !v.checked)
      );
    }
  });
  return tree;
};

export const getTreeCheckedItems = (tree: TreeItemType[]): TreeItemType[] => {
  const result: TreeItemType[] = [];
  treeBfsParse(tree, "children", (item: TreeItemType) => {
    if (item.checked) {
      result.push(item);
    }
  });
  return result;
};

/**
 * 动态获取属性值
 */
export const getter = (obj: any, path: string, defaultValue?: any) => {
  const paths = path.split(".");

  let result = obj;

  try {
    for (let i = 0; i < paths.length; i++) {
      const key = paths[i];
      const value = result[key];
      if (value === undefined) {
        return defaultValue;
      }
      result = value;
    }
  } catch (error) {
    return defaultValue;
  }

  return result;
};

/**
 * 深度复制
 */
export function deepCopy<T>(obj: any, hash = new WeakMap()): T {
  if (Object(obj) !== obj) return obj as any; // 原始类型

  if (hash.has(obj)) return hash.get(obj) as any; // 解决循环引用

  const result =
    obj instanceof Date
      ? new Date(obj)
      : obj instanceof RegExp
      ? new RegExp(obj.source, obj.flags)
      : obj.constructor
      ? new obj.constructor()
      : Object.create(null);

  hash.set(obj, result);

  if (obj instanceof Map) {
    Array.from(obj, ([key, val]) => result.set(key, deepCopy(val, hash)));
  }

  return Object.assign(
    result,
    ...Object.keys(obj).map((key) => ({
      [key]: deepCopy((obj as Record<string, any>)[key], hash),
    }))
  ) as T;
}

/**
 * 深度替换
 */
export function deepReplace(whole: any, partial: any): void {
  // 判断类型是否匹配
  const isTypeMatch = (a: any, b: any, type?: string) => {
    const match = Object.prototype.toString.call(a) === Object.prototype.toString.call(b);
    if (type) {
      return match && Object.prototype.toString.call(b) === type;
    }
    return match;
  };

  // 外层类型必须匹配，且只能是普通对象和数组
  if (!(isTypeMatch(whole, partial, "[object Object]") || isTypeMatch(whole, partial, "[object Array]"))) {
    return;
  }

  // 解决循环引用
  const map = new WeakMap<object, object>();
  // 栈
  const stack = [{ oldv: whole, newv: partial }];

  while (stack.length) {
    // 出栈
    const item = stack.pop()!;
    const newv = item.newv;
    let oldv = item.oldv;

    // 循环依赖
    map.set(newv, oldv);

    // 是普通对象或数组
    if (isTypeMatch(whole, partial, "[object Object]") || isTypeMatch(whole, partial, "[object Array]")) {
      Object.keys(newv).forEach((key) => {
        // 旧值必须有对应的键
        if (Object.prototype.hasOwnProperty.call(oldv, key)) {
          // 是普通对象或数组
          if (
            isTypeMatch(oldv[key], newv[key], "[object Object]") ||
            isTypeMatch(oldv[key], newv[key], "[object Array]")
          ) {
            if (map.has(newv[key])) {
              // 循环依赖
              oldv[key] = map.get(newv[key]);
            } else {
              // 入栈
              stack.push({ oldv: oldv[key], newv: newv[key] });
              oldv[key] = newv[key];
            }
          } else {
            // 直接覆盖
            oldv[key] = newv[key];
          }
        }
      });
    } else {
      // 直接覆盖
      oldv = newv;
    }
  }
}
export const formatRoundNum = (num: number, pre: number) =>
  !isNaN(num) ? (Math.round(num * Math.pow(10, pre)) / Math.pow(10, pre)).toFixed(pre) : 0;

// 千位分隔符形式字符串
export const thousandSeparator = (num: number) => num.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");

// 账号 每四位添加一空格
export function formatAccount(str: string) {
  if (!str) {
    return "";
  }
  const formatted = str.replace(/(.{4})(?=.)/g, "$1 ");
  return formatted;
}

// 手机号 344形式空格分割
export function formatPhoneNum(str: string) {
  if (str && str.length === 11) {
    const reg = /(?=(\d{4})+$)/g;
    return str.replace(reg, " ");
  } else {
    return str;
  }
}

// 是否移动端
export const isMobile = navigator.userAgent.match(/Mobi|Android|iPhone/i);

// 全屏展示切换
export const toggleFullScreen = () => {
  const methodMap = {
    request: {
      standard: "requestFullscreen",
      webkit: "webkitRequestFullScreen",
      moz: "mozRequestFullScreen",
      ms: "msRequestFullscreen",
    },
    exit: {
      standard: "exitFullscreen",
      webkit: "webkitExitFullscreen",
      moz: "mozCancelFullScreen",
      ms: "msExitFullscreen",
    },
  };
  const element = document.documentElement;
  if (!document.fullscreenElement) {
    for (const key in methodMap.request) {
      // @ts-ignore
      if (element[methodMap.request[key]]) {
        // @ts-ignore
        element[methodMap.request[key]]();
        break;
      }
    }
  } else {
    for (const key in methodMap.exit) {
      // @ts-ignore
      if (document[methodMap.exit[key]]) {
        // @ts-ignore
        document[methodMap.exit[key]]();
        break;
      }
    }
  }
};

// 转换分钟数为xxhxxmin形式
// export const convertToHoursAndMinutes = (minutes: number): string => {
//   if (isNaN(minutes) || minutes < 0) {
//     return "Invalid input";
//   }
//   const hours = Math.floor(minutes / 60);
//   const remainingMinutes = minutes % 60;
//   if (hours === 0) {
//     return `${remainingMinutes}min`;
//   } else if (remainingMinutes === 0) {
//     return `${hours}h`;
//   } else {
//     return `${hours}h${remainingMinutes}min`;
//   }
// };

// 下载excel文件
export const download = (response: any, fileName?: string) => {
  const blob = new Blob([response.data], { type: "application/vnd.ms-excel" });
  const downloadElement = document.createElement("a");
  const href = window.URL.createObjectURL(blob);
  downloadElement.href = href;
  const disposition = response.headers["content-disposition"];
  let defaultName = "download.xlsx";
  if (disposition) {
    const match = disposition.match(/filename=(.+)/);
    if (match && match[1]) {
      defaultName = decodeURIComponent(match[1]);
    }
  }
  downloadElement.download = fileName || defaultName;
  document.body.appendChild(downloadElement);
  downloadElement.click();
  document.body.removeChild(downloadElement);
  window.URL.revokeObjectURL(href);
};

/** 分钟数 转换为 时分秒 HH:mm:ss */
export const minutesToHourMinute = (minutes: number) => {
  const hour = Math.floor(minutes / 60);
  const minute = minutes % 60;
  return `${hour}:${minute}:00`;
};
/** 时分 转换为 总分钟数 */
export const hourMinuteToMinutes = (hourMinute: string) => {
  const [hour, minute] = hourMinute.split(":").map(Number);
  return hour * 60 + minute;
};

/**
 * 轮询工具函数。
 * @param {Function} pollingFn 需要轮询执行的函数，该函数应该返回一个 Promise。
 * @param {number} intervalSeconds 轮询的间隔秒数。
 * @returns {object} 一个包含状态和控制方法的对象。
 * - `isRunning`: ref<boolean> - 指示轮询是否正在运行。
 * - `startPolling`: Function - 启动轮询。
 * - `stopPolling`: Function - 停止轮询。
 */
export function usePolling(pollingFn: () => Promise<any>, intervalSeconds: number) {
  const isRunning = ref(false);
  let timerId: NodeJS.Timeout | null = null;
  const intervalMilliseconds = intervalSeconds * 1000;

  const poll = async () => {
    if (!isRunning.value) {
      return;
    }
    try {
      await pollingFn();
    } catch (error) {
      console.error("轮询函数发生错误:", error);
      // 这里可以添加更复杂的错误处理逻辑，例如重试机制
    } finally {
      if (isRunning.value) {
        timerId = setTimeout(poll, intervalMilliseconds);
      }
    }
  };

  const startPolling = () => {
    if (!isRunning.value) {
      isRunning.value = true;
      poll();
    }
  };

  const stopPolling = () => {
    isRunning.value = false;
    if (timerId) {
      clearTimeout(timerId);
      timerId = null;
    }
  };

  // 在组件卸载时停止轮询，防止内存泄漏
  onUnmounted(stopPolling);

  return {
    isRunning,
    startPolling,
    stopPolling,
  };
}
