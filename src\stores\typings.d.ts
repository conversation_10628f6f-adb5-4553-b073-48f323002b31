/** type === 0 车辆信息 */
declare type _AllStatusType = {
  /** 区域id */
  areaId: string;
  /** 设备id */
  deviceId: string;
  /** 电量 */
  electricPoint: number;
  /** 更新时间00:00:00时分秒 */
  updateTime: string;
  /** 更新时间0000:00:00年月日 */
  updateDate: string;
  /** 电量 */
  electric: number;
  /** 充电状态 */
  recharge: boolean;
  /** 电量 */
  electric2: number;
  /** 充电状态 */
  recharge2: boolean;
  /** 驾驶模式,人工驾驶：manual, 自动驾驶：automatic, 遥控驾驶：remote, 急停模式：emergency_braking */
  driveMode: "manual" | "automatic" | "remote" | "emergency_braking";
  /** 详情页在线状态 */
  onlineDetail: "online" | "offline" | "charge";
  /** 速度*/
  speed: number;
  /** 档位*/
  gear: string;
  /** 左前置边刷实际外展距离*/
  leftFrontBrushDistance: number;
  /** 右前置边刷实际外展距离*/
  rightFrontBrushDistance: number;
  /** 执行次数*/
  executeTimes: number;
  /** 车辆里程小计*/
  currentMileage: number;
  /** 车辆里程总计*/
  totalMileage: number;
  /** 照明灯状态 0：false，1:true*/
  headLamp: boolean;
  /** 刹车灯状态 0：false，1:true*/
  brakeLamp: boolean;
  /** 双闪状态 0：false，1:true*/
  doubleFlash: boolean;
  /** 左转向灯状态 0：false，1:true*/
  turnLeftLamp: boolean;
  /** 右左转向灯状态 0：false，1:true*/
  turnRightLamp: boolean;
  /** 倒车灯状态 0：false，1:true*/
  backOffLamp: boolean;
  /** 警灯状态 0：false，1:true*/
  alarmLamp: boolean;
  /** 车辆急停开关，使能:true， 解除:false */
  emergencyBrakeSwitch: boolean;
  /** 车辆电源状态，车辆上电：true,车辆下电：false*/
  power: boolean;
  /** EPB开关状态，未操作：nop, EPB拉起（驻车）:pull, EPB释放：release*/
  EPB: string;
  /** 当前驻车状态, 驻车完成：park_done, 释放完成：release_done, 执行中：operating*/
  park: string;
  /** 扫刷旋转状态，打开：true,关闭：false*/
  broomWork: boolean;
  /** 扫刷喷头状态， 打开：true, 关闭：false*/
  sprayWork: boolean;
  /** 吸盘状态，停止：stop, 上升：up, 下降：down*/
  suctionWork: string;
  /** 垃圾箱门升降开合状态：停止：stop, 打开：open, 关闭：close*/
  boxDoorWork: string;
  /** 水箱*/
  tank: number;
  tankPoint: number;
  /** 垃圾*/
  litter: number;
  /** 人员名称 */
  worker: string;
  /** 人员电话 */
  workerPhone: string;
  /** 速度百分比 */
  speedPercent: string;
  /** 地址*/
  addr: string;
  /** 电池电压*/
  voltage: number;
  /** 电池电流*/
  current: number;
  /** 电池组最高温度*/
  temperatureMax: number;
  /** 最新告警*/
  latestWarn: string;
  /** 清扫面积*/
  cleanArea: number;
  direction: number;
  /** 左前轮胎压，单位bar*/
  leftFrontTirePressure: number;
  /** 左前轮胎温，单位摄氏度*/
  leftFrontTireTemperature: number;
  /** 右前轮胎压，单位bar*/
  rightFrontTirePressure: number;
  /** 右前轮胎温，单位摄氏度*/
  rightFrontTireTemperature: number;
  /** 左后轮胎压，单位bar*/
  leftReverseTirePressure: number;
  /** 左后轮胎温，单位摄氏度*/
  leftReverseTireTemperature: number;
  /** 右后轮胎压，单位bar*/
  rightReverseTirePressure: number;
  /** 右后轮胎温，单位摄氏度*/
  rightReverseTireTemperature: number;
  /** 水泵状态*/
  waterPump: boolean;
  /** 风机状态*/
  fan: boolean;
  /** 风机转速*/
  fanMotorSpeed: number;
  /** 放水阀状态*/
  waterDrainValue: boolean;
  /** 边刷喷水阀状态*/
  brushWaterSparyStatus: string;
  /** 工作状态 */
  workStatus: number;
  /** 在线状态*/
  online: boolean;
  /** 底盘在线状态 0下线， 1在线 ， 2不显示地盘状态 */
  chassisOnline: 2;
  /** 0暂停，1继续 */
  taskStatus: number;
  /** 0下电,1上电*/
  digIoSwitch: number;
  warnNum: number;
  /** 更新时间00:00:00时分秒*/
  gpsTime: string;
  /** 更新时间0000:00:00年月日*/
  gpsDate: string;
  /** 任务名称*/
  taskName: string;
  /** 厂家*/
  manufacturer: string;
  /** 任务工作状态*/
  taskWorkStatus: string;
  /** 0加载结束，1加载中*/
  loadingMark: number;
  /** 加载时间戳*/
  loadingTimeStart: number;
  /** 当前时间戳*/
  loadingTimeCurrent: number;
  /** 风机降尘水阀状态, open:打开，close:关闭*/
  fanDustFallStatus: string;
  /** 吸口降尘水阀状态, open:打开，close:关闭*/
  suctionDustFallStatus: string;
  /** 项目名称*/
  proName: string;
  /** 项目id*/
  proId: string;
  /** 区域名称*/
  areaName: string;
  /** 区域id*/
  areaId: string;
  /** 充电时间*/
  chargeTime: number;
  /** 边刷升降位置状态，invalid: 无效，fall:落下，rise:上升*/
  brushPosition: string;
  faultCacheMap: {
    [x: number]: {
      id: number;
      /** 告警内容*/
      faultContent: string;
      warnLevel: number;
      /** 告警类型，0：无，1，电池告警，2，告警，3报错*/
      faultModule: string;
      /** 告警内容文字*/
      faultDesc: string;
      /** 告警码*/
      code: string;
      /** 创建时间*/
      createdTime: string;
      /** 创建时间*/
      updateTime: string;
      /** 故障地址 保留字段 */
      faultAddress: string;
    };
  };
  /** 1,四轮车，2三轮车 */
  vehType: number;
  /** not_connect:未连接，connect:连接 */
  chargeConnectStatus: string;
  /** 模版id */
  tempId: string;
  /**  站点id */
  stationId: string;
  /** 是否为2.0协议 1.0:false,2.0:true */
  vehicleVersion: boolean;
  /** 车端软件版本 */
  vehicleSoftVersion: number;
  /** 当前任务类型： 0：待机，1：正常泊车，2：充电类型，3：垃圾触发类型，4：水箱触发类型，5：任务中，6：任务规划中 */
  currentTaskType: number;
  /** 自检结果： 1自检成功 2自检失败 3正在进行自检 */
  selfCheckResult: number;
  /** 车端自驾系统版本 */
  sysVersion: string;
  /** 红绿灯图片地址 [如果不为空就需要弹窗展示图片, 反之关闭弹窗] */
  trafficLightPics: string[];
  /** 垃圾箱举升状态 0-Dropped 1-Lifted */
  dustbinLiftPoseStatus: number;
  /** 垃圾箱斗翻转状态 0-Retracted 1-Overturn */
  dustbinOverturnPoseStatus: number;
  /** 车辆维护状态 */
  inCharge: number;
  /** 急停原因 0: 非急停状态 1: 急停按键触发 2: 前触边触发 3: 后触边触发 4: 整车故障触发 5: 遥控急停触发 6: 远程急停触发 */
  emgcyStopReason: number;
  /** 车辆维护状态 */
  inCharge: number;
  emgcyStopReason: number; //0: 非急停状态 1: 急停按键触发 2: 前触边触发 3: 后触边触发 4: 整车故障触发 5: 遥控急停触发 6: 远程急停触发，100软急停
  allowPassTime: number; //时间戳
};
/** type === 1 gps信息 */
declare type _BaseStatusType = {
  deviceId: string;
  latitude: number;
  longitude: number;
  online: boolean;
  warn: boolean;
  warnLevel: number;
  warnWater: boolean;
  warnElec: boolean;
  setMachineStatus: string;
  attendanceTimes: number;
  workStatus: number;
  direction: number;
  areaName: string;
  gpsDate: string;
  gpsTime: string;
  wgs84Latitude: number;
  wgs84Longitude: number;
  deviceType: number;
  /**绿灯类型 CROSSWALK=人行道 INTERSECTION=机动车道 UNKNOWN:未知*/
  trafficLightType: string;
  warnTrafficTime: string;
  warnTime: string;
  warnTrafficForecast: string;
  warnTrafficForecastTime: string;
  warnHistory: string;
  warnHistoryTime: string;
  trafficLightType: string;
  vin: string;
};
/** type === 2 状态数据 */
declare type _StatsStatusType = {
  online: number;
  offline: number;
  warnNum: number;
};
/** type === 3 告警 */
declare type _SystemWarnNumType = {
  warnNum: number;
};
/** type === 4 消息提醒 */
declare type _AlarmMessageStatusType = {
  device_id: string;
  id: string;
  /** 未读数量 */
  numUnread?: number;
  /** 0：未读 1：已读 */
  readType?: number;
  /** 消息内容 */
  message: string;
  /** 消息类型： 0：全部（默认）1:缺水，2:缺电，3：倒垃圾，4：下发指令，5：事件,6红绿灯， 7：管线， 8：碰撞 */
  messageType: number;
  picUrl: string;
  /** 当天实际时间 YYYY-MM-DD HH:mm:ss */
  time: string;
  /** 弹框 */
  alert?: boolean;
  /** 消息内容 */
  dataContent?: {
    /** 上传时间 */
    dataTime: string;
    /** 验证时间 */
    updateTime: string;
    /** 状态0未验证，1已验证 */
    status: number;
    /** 0红灯1绿灯 */
    result: number;
    /** 校验人 */
    verifier: string;
    snapId: string;
  };
  /** 红绿灯id */
  trafficLightId: string;
};
/** type === 5 主动通知 */
declare type _TaskContinueConfirmType = {
  /** （1:急停） */
  type: string;
  content: {
    /** 车牌号 */
    deviceId?: string;
    emergencyOut?: string;
  };
};
/** type === 6 充电桩状态 */
declare type _ChargingStationStatusType = {
  /** 车牌号 */
  deviceId: string;
  /** idle:空闲状态，precharge:车辆连接wifi成功，准备充电， chargeing:充电状态，charge_full:已经充满 */
  status: "idle" | "precharge" | "chargeing" | "charge_full";
  /** 充电桩告警 */
  warn: boolean;
  /** 电池箱SOC */
  soc: number;
  /** 充电电压,单位为伏特 */
  voltage: number;
  /** 充电电流，单位为安培 */
  current: number;
  /** 温度，单位为摄氏度 */
  temperature: number;
  /** 更新时间 */
  lastTime: string;
};
/** type === 7 大屏-项目下所有区域数据，作业概览汇总 */
declare type _ScreenWorkViewType = {
  calculate: ScreenWorkViewItem;
  totalCalculate: ScreenWorkViewItem;
};
/** type === 7 大屏-项目下所有区域数据，作业概览汇总[子项] */
declare type _ScreenWorkViewItem = {
  /** 编号 */
  id: number;
  /** 区域id */
  proId: number;
  /** 用水 */
  usedWaterAmount: number;
  /** 用电 */
  usedElectricityAmount: number;
  /** 清扫面积 */
  finishedWorkArea: number;
  /** 里程 */
  mileage: number;
  /** 垃圾量 */
  litter: number;
  /** 任务持续时间 单位：分钟 */
  taskDuration: number;
  /** 创建时间 */
  createdTime: string;
};
/** type === 8 大屏报警 */
declare type _ScreenAlarmType = {
  device_id: string;
  message: string;
  time: string;
  picUrl: string;
};
/** type === 9 人员列表 */
declare type _ScreenWorkerType = {
  /** 纬度 */
  lat: number;
  /** 经度 */
  lon: number;
  /** 名称 */
  userName: string;
  /** 电话 */
  mobile: string;
  /** 在线状态 */
  onlineStatus: boolean;
  /** 头像url */
  userImage: string;
  /** GPS上传时间 */
  updateTime: string;
};
/** type === 10 车辆列表 */
declare type _VehicleListInfo = {
  /** 设备id */
  deviceId: string;
  /** 项目名称 */
  proName: string;
  /** 区域名称 */
  areaName: string;
  /** 在线状态 */
  online: boolean;
  /** 水箱 */
  tank: number;
  /** 垃圾 */
  litter: number;
  /** 电量  */
  electric: number;
  /** 作业状态 1: 作业中 0: 未作业 2: 未定义 3: 充电中 */
  workStatus: number;
  vin: string;
  /** 区域id */
  areaId: string;
  /** 项目id */
  proId: string;
};
/** type === 11 行车路线 */
declare type _FahrtrouteInfo = {
  /** 已经过的路径点 */
  traveled_path_waypoints: FahrtroutePoint[];
  /** 未经过的路径点 */
  untraveled_path_waypoints: FahrtroutePoint[];
  lonOrigin: number;
  latOrigin: number;
  deviceId: string;
};
/** type === 12 站点信息 */
declare type _SiteInformation = {
  [areaId: string]: {
    [parkNo: string]: {
      parkingSpaceId: string;
      stationId: string;
      stationName: string;
      stationType: number;
      stationStatus: number;
      parkingOccupancyState: string;
      areaId: string;
      occupancyTime: string;
      occupyInfoList: {
        deviceId: string;
        updateTime: string;
        valetParkingStage: string;
        workNo: string;
      }[];
      occupancyTime: string;
    };
  };
};
type FahrtroutePoint = {
  type: number;
  point: {
    pose_xyz: {
      x: number;
      y: number;
      z: number;
    };
    id: number;
    heading: number;
    clean_mode: number;
    navigation_mode: number;
  };
};
declare type _PositionType = {
  right: number | string;
  bottom: number | string;
  left: number | string;
  top: number | string;
};
declare type _TabItem = {
  title: string;
  routeName: string;
  enable: boolean;
};
