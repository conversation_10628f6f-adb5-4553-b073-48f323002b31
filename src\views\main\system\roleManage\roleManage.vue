<template>
  <section class="page-table-layout" ref="pageTableLayoutRef">
    <div class="page-table-layout-top">
      <div class="top-title">{{ $t("roleManage") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:role:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addReactive.show = true"
        />
        <Add v-model:show="addReactive.show" @confirm="addReactive.confirm" />
      </div>
    </div>
    <div class="page-table-layout-middle">
      <div class="middle-left">
        <div class="filter">
          <div class="filter__item" style="width: 320px">
            <x-select
              v-model:value="searchForm.entName"
              :options="formOptions.companyOptions"
              :popupContainer="pageTableLayoutRef"
              :placeholder="$t('PEnterEntName')"
              showSearch
            />
          </div>
          <div class="filter__item">
            <x-input
              v-model:value="searchForm.roleName"
              :placeholder="$t('PEnterRoleName')"
              suffix="input_search"
              style="width: 200px"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:role:list')"
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="page-table-layout-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.entName }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:role:update')"
                @click="openEdit(record.roleId)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:role:info')"
                @click="openDetail(record.roleId)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:role:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editReactive.show"
        :id="editReactive.id"
        @confirm="editReactive.confirm"
      />
      <Detail v-model:show="detailReactive.show" :id="detailReactive.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import { ref, reactive } from "vue";
import { roleList, delRole, compList } from "@/services/api";
import { useMainStore } from "@/stores/main";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import xModal from "@/components/x-modal";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("roleManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const pageTableLayoutRef = ref<any>();

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  companyOptions: [] as { label: string; value: string }[],
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "roleName",
      title: $t("roleName"),
      width: "80",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "80",
      slots: "entName",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "60",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  entName: "",
  roleName: "",
});
/**
 * 表格-搜索
 */
const submitSearch = async () => {
  table.loading = true;
  const { totalCount, list } = await roleList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.entName,
    roleName: searchForm.roleName,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.roleName = "";
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};
/**
 * 表格-首次加载
 */
(async () => {
  submitSearch();
  formOptions.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
})();

/**
 * 相关操作
 */
// 新增
const addReactive = reactive({
  show: false,
  confirm: () => submitSearch(),
});
// 编辑
const editReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openEdit = (id: string) => {
  editReactive.id = id;
  editReactive.show = true;
};
// 详情
const detailReactive = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailReactive.id = id;
  detailReactive.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("sureToDelete"),
    content: (
      <div style="font-size:14px;color:#383838;">
        {$t("willRemoveAccPermission")}
      </div>
    ),
    confirm() {
      return delRole([record.roleId]).then(() => {
        Message("success", $t("deleteSuccess"));
        submitSearch();
      });
    },
  });
};
</script>

<style lang="scss" scoped>
.user-manage-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.page-table-layout {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }

  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
.filter {
  display: flex;
  margin-top: 14px;
  &:first-child {
    margin-top: 0;
  }

  &__item {
    margin-left: 16px;
    &:first-child {
      margin-left: 0;
    }
  }
}
</style>
