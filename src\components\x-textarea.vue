<template>
  <section class="x-textarea">
    <div
      :class="['x-textarea-content', { focus: focusRef }]"
      @click.stop="textareaRef.focus()"
    >
      <textarea
        ref="textareaRef"
        class="x-textarea-content-textarea"
        :value="props.value"
        :placeholder="props.placeholder"
        :maxlength="props.maxlength"
        @input="inputHandle"
        @focus="focusRef = true"
        @blur="focusRef = false"
      />
      <div v-if="props.showCount" class="x-textarea-content-count">
        {{ props.value.length }}/{{ props.maxlength }}
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { ref, onMounted, nextTick } from "vue";
type AutoSize = { minRows: number; maxRows: number };
/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    // allowClear?: boolean;
    autoSize?: boolean | AutoSize;
    // bordered?: boolean;
    // disabled?: boolean;
    // id?: string;
    maxlength?: number;
    placeholder?: string;
    // prefix?: string | any;
    showCount?: boolean;
    // size?: string;
    // suffix?: string | any;
    value: string;
  }>(),
  {
    autoSize: false,
    // bordered: true,
    // disabled: false,
    placeholder: "请输入",
    showCount: true,
    // size: "default",
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value"]);

/**
 * 引用
 */
const textareaRef = ref<any>();
const focusRef = ref<boolean>(false);

/**
 * 行高
 */
const lineHeight = 20;

/**
 * 自动高度
 */
const autoSizeHandle = (that: HTMLTextAreaElement) => {
  // 不能自动高度
  if (props.autoSize === false) {
    that.style.overflow = "auto";
    that.style.height = `${lineHeight * 2}px`;
    return;
  }

  // scrollHeight 作为只读属性，scrollHeight 高度重新计算只有改变高度
  that.style.height = "auto";

  // 无限制的自动高度
  if (props.autoSize === true) {
    that.style.height = `${that.scrollHeight}px`;
    that.style.overflow = "hidden";
    return;
  }

  // 限制的自动高度
  const { minRows, maxRows } = props.autoSize as AutoSize;
  const rows = that.scrollHeight / lineHeight;
  // 行 > 最大行
  if (rows > maxRows) {
    that.style.height = `${maxRows * lineHeight}px`;
    that.style.overflow = "auto";
  } else if (rows < minRows) {
    that.style.height = `${minRows * lineHeight}px`;
    that.style.overflow = "hidden";
  } else {
    that.style.height = `${that.scrollHeight}px`;
    that.style.overflow = "hidden";
  }
};

/**
 * 最大长度
 */
const maxlengthHandle = (that: HTMLTextAreaElement) => {
  if (props.maxlength && props.maxlength < props.value.length) {
    // 更新组件值
    emits("update:value", that.value.substring(0, props.maxlength));
  }
};

/**
 * 输入处理
 */
const inputHandle = (e: Event) => {
  const that = e.target as HTMLTextAreaElement;
  // 更新组件值
  emits("update:value", that.value);
  // 自动高度
  autoSizeHandle(that);
};

/**
 * 组件挂载后
 */
onMounted(() => {
  const that = textareaRef.value;
  // 最大长度
  maxlengthHandle(that);
  // 在 nextTick 中执行 autoSizeHandle 才能获取到 scrollHeight
  nextTick(() => {
    // 自动高度
    autoSizeHandle(that);
  });
});
</script>

<style lang="scss" scoped>
.x-textarea {
  &-content {
    @include wh(100%, auto) {
      border: 1px solid rgb(220, 220, 220);
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.9);
      transition: all 1.3s;
      padding: 6px 0;
    }
    &.focus {
      border-color: #5964fb;
    }
    &-textarea {
      transition: all 0.2s linear;
      @include sc(14px, #000) {
        padding: 0 10px;
        line-height: 20px;
        background-color: transparent;
        text-align: justify;
        vertical-align: top;
      }
      @include wh(100%, auto) {
        border: none;
        overflow: hidden;
      }
      @include scrollbar(
        y,
        4px,
        rgb(233, 234, 248),
        rgba(89, 100, 251, 0.7),
        4px
      ) {
        &::-webkit-scrollbar-track {
          border-radius: 4px;
        }
      }
    }
    &-count {
      @include sc(12px, #9f9fa4) {
        text-align: right;
        line-height: 14px;
        padding: 4px 10px 0 0;
      }
    }
  }
}
</style>
