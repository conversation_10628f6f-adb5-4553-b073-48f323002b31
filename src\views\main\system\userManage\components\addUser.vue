<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="{ position: 'center' }"
    :title="$t('addUser')"
    width="565px"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    @confirm="addDrawerConfirm"
    @cancel="formRef.resetFields()"
  >
    <div ref="xAddDrawerContentRef" class="content">
      <x-form ref="formRef" :model="addForm" :rules="addFormRules">
        <x-form-item :label="$t('name')" name="name">
          <x-input
            v-model:value="addForm.name"
            :maxlength="20"
            :placeholder="$t('PEnterUserName')"
          />
        </x-form-item>
        <x-form-item :label="$t('phone')" name="phone">
          <x-input
            v-model:value="addForm.phone"
            :maxlength="11"
            :filter="mobileFilter"
            :placeholder="$t('PEnterPhone')"
          />
        </x-form-item>
        <x-form-item :label="$t('entName')" name="company">
          <x-tree-select
            v-model:value="addForm.company"
            @change="companyChange"
            :treeData="addFormOption.company"
            :popupContainer="xAddDrawerContentRef"
            :placeholder="$t('PSelectCompany')"
            showSearch
          />
        </x-form-item>
        <x-form-item
          :label="$t('loginAcc')"
          name="username"
          :extra="$t('PEntCharNumberLetterAcc')"
        >
          <x-input
            v-model:value="addForm.username"
            :maxlength="15"
            :placeholder="$t('PSetLoginAcc')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('loginPWD')"
          name="password"
          :extra="$t('PEntCharNumberLetterPWD')"
        >
          <x-input
            v-model:value="addForm.password"
            :maxlength="15"
            :placeholder="$t('PEnterPWD')"
          />
        </x-form-item>
        <x-form-item :label="$t('confirmationPWD')" name="confirmPassword">
          <x-input
            v-model:value="addForm.confirmPassword"
            :maxlength="15"
            :placeholder="$t('PConfirmPWD')"
          />
        </x-form-item>
        <x-form-item :label="$t('stationType')" name="jobType">
          <x-select
            v-model:value="addForm.jobType"
            :options="addFormOption.jobType"
            :popupContainer="xAddDrawerContentRef"
            :placeholder="$t('PSelectStationType')"
          />
        </x-form-item>
        <x-form-item :label="$t('role')" name="role">
          <x-tree-select
            v-model:value="addForm.role"
            :treeData="addFormOption.role"
            :popupContainer="xAddDrawerContentRef"
            :placeholder="$t('PEnterRoleName')"
            showSearch
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>
<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  compTree,
  userSearchRole,
  addUser,
  mobileRepeat,
  getPublicKey,
} from "@/services/api";
import { resTreeToXTree, i18nSimpleKey } from "@/assets/ts/utils";
import { enRSACrypt } from "@/assets/ts/encrypt";
import { JobType } from "@/assets/ts/config";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import Message from "@/components/x-message";
import { isPhoneNumber, includeNumberCharacter } from "@/assets/ts/validate";
const $t = i18nSimpleKey("userManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
const xAddDrawerContentRef = ref<any>();
const addForm = reactive({
  name: "",
  phone: "",
  company: 0,
  username: "",
  password: "",
  confirmPassword: "",
  jobType: 0,
  role: 0,
});
const addFormOption = reactive({
  jobType: JobType.filter(({ value }) => value !== 3 && value !== 6),
  company: [] as TreeItemType[],
  role: [] as TreeItemType[],
});
const repeatPhoneNumber = async (value: string) => {
  const result = await mobileRepeat({ mobile: value });
  return !result?.repeat;
};
const addFormRules = reactive({
  name: [["required", $t("PEnterUserName")]],
  phone: [
    ["required", $t("PEnterPhone")],
    [isPhoneNumber, $t("phoneIncorrect")],
    [repeatPhoneNumber, $t("phoneExist")],
  ],
  company: [["required", $t("PEnterCompany")]],
  username: [
    ["required", $t("PSetLoginAcc")],
    [includeNumberCharacter, $t("PEntCharNumberLetterAcc")],
  ],
  password: [
    ["required", $t("PEnterPWD")],
    [includeNumberCharacter, $t("PEntCharNumberLetterPWD")],
  ],
  confirmPassword: [
    ["required", $t("PConfirmPWD")],
    [(v: string) => v === addForm.password, $t("notMatchPassword")],
  ],
  jobType: [["required", $t("PSelectStationType")]],
  role: [["required", $t("PEnterRoleName")]],
});
const mobileFilter = (v: string) => v.replace(/\D/g, "");
(async () => {
  addFormOption.company = reactive(resTreeToXTree([await compTree()]));
})();

watch(
  () => addForm.company,
  async (newV) => {
    if (newV) {
      const { roleList } = await userSearchRole({ entId: newV });
      addFormOption.role = roleList.map((item) => ({
        title: item.roleName,
        value: item.roleId,
      }));
    }
  }
);
const companyChange = (item: TreeItemType) => {
  addFormOption.jobType =
    item.entLevel === 1
      ? JobType
      : JobType.filter(({ value }) => value !== 3 && value !== 6);
};
const formRef = ref<any>();
const addDrawerConfirm = async () => {
  if (formRef.value.validate()) {
    const publicKey = (await getPublicKey()).publicKey;
    await addUser({
      entId: addForm.company,
      mobile: addForm.phone,
      password: enRSACrypt(addForm.password, publicKey),
      roleIdList: [addForm.role],
      station: addForm.jobType,
      userAccount: addForm.username,
      userName: addForm.name,
    });
    emits("update:show", false);
    Message("success", $t("createSuccess"));
    emits("confirm");
    formRef.value.resetFields();
  }
};
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
}
</style>
