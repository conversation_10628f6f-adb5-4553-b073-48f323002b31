<!-- 基于element封装的表格组件 -->
<template>
  <section class="pro-table">
    <!-- 表格工具栏 -->
    <div
      class="table-toolbar"
      v-if="withMenu.length"
    >
      <div class="left-btns">
        <el-button
          v-if="withMenu.includes('create')"
          type="primary"
          @click="$emit('create')"
        >
          新增
        </el-button>
        <el-button
          v-if="withMenu.includes('import')"
          type="primary"
          @click="handleImport"
        >
          导入
        </el-button>
        <el-button
          v-if="withMenu.includes('export')"
          type="primary"
          @click="handleExport"
        >
          导出
        </el-button>
        <el-button
          v-if="withMenu.includes('delete') && selectedRows.length"
          type="danger"
          @click="handleBatchDelete"
        >
          批量删除
        </el-button>
      </div>
    </div>

    <!-- 表格主体 -->
    <el-table
      ref="elTableRef"
      v-loading="loading"
      :data="tableData"
      :row-key="rowKey"
      :size="size"
      v-bind="tableProps"
      @selection-change="handleSelectionChange"
      @row-click="handleRowClick"
      :header-cell-style="{ background: '#f5f7fa', color: '#606266' }"
      :max-height="maxHeight"
    >
      <!-- 选择列 -->
      <el-table-column
        v-if="isSelection"
        type="selection"
        width="55"
        align="center"
      />

      <!-- 序号列 -->
      <el-table-column
        v-if="showIndex"
        type="index"
        width="55"
        label="序号"
        align="center"
      />

      <!-- 动态列 -->
      <template
        v-for="column in columns"
        :key="column.prop"
      >
        <el-table-column
          v-bind="column"
          :align="column.align || 'center'"
          :width="column.width"
          :min-width="column.minWidth || 150"
          :fixed="column.fixed"
          :formatter="column.formatter"
        >
          <template #default="scope">
            <!-- 使用 prop 作为插槽名 -->
            <slot
              v-if="$slots[column.prop]"
              :name="column.prop"
              :row="scope.row"
              :index="scope.$index"
            ></slot>
            <!-- [column.formatter] 有值时, 优先使用 formatter 处理 -->
            <template v-else-if="!column.formatter">
              {{ scope.row[column.prop] || (showPlaceholder ? "-" : "") }}
            </template>
          </template>
          <template
            #header="scope"
            v-if="$slots[`header-${column.prop}`]"
          >
            <slot
              :name="`header-${column.prop}`"
              :column="scope.column"
              :index="scope.$index"
            ></slot>
          </template>
        </el-table-column>
      </template>
    </el-table>

    <!-- 分页器 -->
    <el-pagination
      ref="elPaginationRef"
      v-if="showPagination"
      v-model:current-page="pagination.current"
      v-model:page-size="pagination.pageSize"
      background
      :total="pagination.total"
      :layout="pageLayout"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </section>
</template>

<script lang="ts" setup>
import { propTypes } from "@/utils/propTypes";
import type { Column, ElPagination, TableInstance, TableProps } from "element-plus";
import { PropType, onMounted, ref } from "vue";

/** 表格配置 */
const props = defineProps({
  /** 行键 */
  rowKey: {
    type: String,
    default: "id",
  },
  /** 列配置 */
  columns: {
    type: Array as PropType<ProTableColumn[]>,
    required: true,
    default: () => [],
  },
  /** 表格尺寸 */
  size: {
    type: String as PropType<"large" | "default" | "small">,
    default: "default",
  },
  /** 表格配置 */
  tableProps: {
    type: Object as PropType<Partial<TableProps<any>>>,
    default: () => ({}),
  },
  /** 是否显示选择列 */
  isSelection: {
    type: Boolean,
    default: false,
  },
  /** 是否显示序号列 */
  showIndex: {
    type: Boolean,
    default: true,
  },
  /** 是否显示分页器 */
  showPagination: propTypes.bool.def(true),
  /** 表格工具栏按钮 */
  withMenu: {
    type: Array as PropType<("create" | "delete" | "import" | "export")[]>,
    default: () => [""],
  },
  /** 表格请求函数 */
  readRequest: {
    type: Function as PropType<(params: ProTableRequestParams) => Promise<ProTableResponse>>,
    required: true,
  },
  /** 分页器布局 */
  pageLayout: {
    type: String as PropType<InstanceType<typeof ElPagination>["layout"]>,
    default: "total,  prev, pager, next, sizes,jumper",
  },
  /** 是否显示占位符 */
  showPlaceholder: {
    type: Boolean,
    default: true,
  },
  /** 表格最高高度 */
  maxHeight: { type: Number, default: 600 },
});
defineOptions({
  name: "ProTable",
});
const emit = defineEmits(["create", "selection-change", "row-click", "import", "export", "batch-delete"]);

// 状态管理
const elTableRef = ref<TableInstance>();
const loading = ref(false);
const tableData = ref<any[]>([]);
const selectedRows = ref<any[]>([]);
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
});

// 获取表格数据
const load = async () => {
  try {
    loading.value = true;
    const response = await props.readRequest({
      pageNo: pagination.current,
      pageSize: pagination.pageSize,
    });
    tableData.value = response.data?.list ?? [];
    pagination.total = response.data?.total ?? pagination.total;
    pagination.current = response.current ?? pagination.current;
    pagination.pageSize = response.pageSize ?? pagination.pageSize;
  } catch (error) {
    console.error("获取表格数据失败:", error);
  } finally {
    loading.value = false;
  }
};

const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection;
  emit("selection-change", selection);
};

const handleRowClick = (row: any, column: Column, event: Event) => {
  emit("row-click", row, column, event);
};

const handleSizeChange = (val: number) => {
  pagination.pageSize = val;
  pagination.current = 1;
  load();
};

const handleCurrentChange = (val: number) => {
  pagination.current = val;
  load();
};

/** 导入 */
const handleImport = () => {
  emit("import");
};

/** 导出 */
const handleExport = () => {
  emit("export");
};

/** 批量删除 */
const handleBatchDelete = () => {
  emit("batch-delete", selectedRows.value);
};

/** 清空筛选条件并重新加载表格数据 */
const reload = async () => {
  pagination.current = 1;
  pagination.pageSize = 10;
  pagination.total = 0;
  await load();
};

onMounted(load);

defineExpose({
  elTableRef,
  load,
  reload,
});
</script>

<style lang="scss" scoped>
.pro-table {
  padding-bottom: 20px;

  .table-toolbar {
    display: flex;
    padding-bottom: 20px;
    margin-bottom: 16px;
    justify-content: space-between;
    align-items: center;

    .left-btns {
      display: flex;
      gap: 8px;
    }
  }

  .el-pagination {
    display: flex;
    margin-top: 16px;
    justify-content: flex-end;

    // 添加响应式布局
    @media screen and (width <= 768px) {
      justify-content: center;
    }
  }
}
</style>
