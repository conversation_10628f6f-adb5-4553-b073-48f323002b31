<template>
  <x-drawer
    title="详情"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="20px 0 20px 20px"
    width="90%"
  >
    <div class="content">
      <div class="content-top">
        <div>
          {{ props.startDate }} ——
          {{ props.endDate }}
        </div>
        <div>
          <x-button
            @click="exportExcel"
            icon="export_white"
            type="blue"
            text="导出"
          />
        </div>
      </div>
      <div class="content-bottom">
        <x-table
          :cols="table.cols"
          :dataSource="table.dataSource"
          :pagination="table.pagination"
          :loading="table.loading"
          @change="tableChange"
        >
        </x-table>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getStatisticsList, exportStatistics } from "@/services/api";
import type { PageSizeType } from "@/components/types";
import { formatRoundNum, download } from "@/assets/ts/utils";
import xButton from "@/components/x-button.vue";
import Message from "@/components/x-message";
import XDrawer from "@/components/x-drawer.vue";
import xTable from "@/components/x-table.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  deviceId: {
    type: String,
    required: true,
  },
  startDate: {
    type: String,
    required: true,
  },
  endDate: {
    type: String,
    required: true,
  },
  statisticsType: {
    type: Number,
    required: true,
  },
});

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: "序号",
      width: "20",
    },
    {
      key: "deviceId",
      title: "车牌号",
      width: "40",
    },
    {
      key: "areaName",
      title: "所属区域",
      width: "80",
    },
    {
      key: "projectName",
      title: "所属项目",
      width: "80",
    },
    {
      key: "taskDateText",
      title: "日期",
      width: "40",
    },
    {
      key: "taskTimes",
      title: "任务次数",
      width: "30",
    },
    {
      key: "taskDuration",
      title: "时长(h)",
      width: "30",
    },
    {
      key: "mileage",
      title: "里程(km)",
      width: "40",
    },
    {
      key: "finishedWorkArea",
      title: "清扫面积(m²)",
      width: "40",
    },
    {
      key: "usedWaterAmount",
      title: "用水(L)",
      width: "30",
    },
    {
      key: "usedElectricityAmount",
      title: "用电(kW·h)",
      width: "40",
    },
    {
      key: "litter",
      title: "垃圾量(L)",
      width: "40",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const searchList = async () => {
  table.loading = true;
  const { totalCount, list } = await getStatisticsList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceId: props.deviceId || "",
    startDate: props.startDate || "",
    endDate: props.endDate || "",
    statisticsType: props.statisticsType || 1,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderNumber: (index + 1).toString().padStart(3, "0"),
            ...item,
            taskDuration: formatRoundNum(item.taskDuration / 60, 1),
            mileage: formatRoundNum(item.mileage, 1),
            finishedWorkArea: formatRoundNum(item.finishedWorkArea, 1),
            usedWaterAmount: formatRoundNum(item.usedWaterAmount, 1),
            usedElectricityAmount: formatRoundNum(
              item.usedElectricityAmount,
              1
            ),
            litter: formatRoundNum(item.litter, 1),
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchList();
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      searchList();
    }
  }
);

// 导出
const exportExcel = async () => {
  const param = {
    deviceId: props.deviceId,
    startDate: props.startDate,
    endDate: props.endDate,
    statisticsType: props.statisticsType,
  };
  try {
    const response = await exportStatistics(param);
    download(response);
    Message("success", "导出成功");
  } catch (error) {
    Message("error", "导出失败");
  }
};
</script>

<style lang="scss" scoped>
.content {
  margin-right: 20px;
  height: 100%;
  &-top {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 15px;
    @include sc(14px, #383838);
  }
  &-bottom {
    height: calc(100% - 40px);
  }
}
</style>
