<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    :btnOption="{ position: 'end' }"
    :title="$t('carAuth')"
    width="800px"
    @cancel="carDrawerCancel"
    @confirm="carDrawerConfirm"
  >
    <x-transfer
      :titles="carDrawer.transfer.titles"
      :dataSource="carDrawer.transfer.dataSource"
      :targetSource="carDrawer.transfer.targetSource"
      :locale="carDrawer.transfer.locale"
      showSearch
    />
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getProAreaCarAuth, proAreaCarAuth } from "@/services/api";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xTransfer from "@/components/x-transfer.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  proId: {
    type: String,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const carDrawer = reactive({
  transfer: {
    locale: {
      itemUnit: $t("vehicle"),
      searchPlaceholder: $t("PEnterVehicleNo"),
    },
    titles: [$t("allVehicles"), $t("authorizedVehicles")],
    dataSource: [] as TreeItemType[],
    targetSource: [] as TreeItemType[],
  },
});
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      (
        await getProAreaCarAuth({
          id: props.id,
          proId: props.proId,
        })
      )
        .map(({ checked, id, vehicleNo }) => ({
          title: vehicleNo,
          value: id,
          checked,
        }))
        .forEach((item) => {
          if (item.checked) {
            item.checked = false;
            item.disabled = true;
            carDrawer.transfer.dataSource.push(
              JSON.parse(JSON.stringify(item))
            );
            item.disabled = false;
            carDrawer.transfer.targetSource.push(item);
          } else {
            carDrawer.transfer.dataSource.push(item);
          }
        });
    }
  }
);

const carDrawerCancel = () => {
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
};
const carDrawerConfirm = async () => {
  await proAreaCarAuth({
    proAreaId: props.id,
    vehicleId: carDrawer.transfer.targetSource.map((v) => v.value) as number[],
  });
  carDrawer.transfer.dataSource = [];
  carDrawer.transfer.targetSource = [];
  emits("update:show", false);
  Message("success", $t("saveSuccess"));
  emits("confirm");
};
</script>

<style lang="scss" scoped></style>
