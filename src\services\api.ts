import type { AxiosRequestConfig } from "axios";
import http from "./http";
import type {
  AddCompanyRequest,
  AddProAreaRequest,
  AddProRequest,
  AddRTKRequest,
  AddRoleRequest,
  AddScheduleTemplateRequest,
  AddSimRequest,
  AddUserRequest,
  AddVehicleModelRequest,
  AddVehicleRequest,
  AddWarnConfigRequest,
  AvailableRTKRequest,
  AvailableRTKResponse,
  BatchSetWarnConfigRequest,
  CameraSnapAndUploadTempResponse,
  CarAuthRequest,
  ChangeDriveModeRequest,
  ChangeReceiveRequest,
  CheckTempNameRequest,
  CompListResponse,
  CompanyListRequest,
  CompanyListResponse,
  DandelionAccountRepeatRequest,
  DeleteWarnConfigRequest,
  EditCompanyRequest,
  EditProRequest,
  EditRTKRequest,
  EditRoleRequest,
  EditSimRequest,
  EditUserRequest,
  EditVehicleModelRequest,
  EditVehicleRequest,
  EditWarnConfigRequest,
  EntSimRequest,
  EntSimResponse,
  ExportCalculateListRequest,
  ExportProCalculateListRequest,
  ExportStatisticsRequest,
  GetAreaRangeResponse,
  GetAreaRouteTemplateResponse,
  GetAreaScheduleListRequest,
  GetAreaScheduleListResponse,
  GetChargingStationInfoResponse,
  GetChargingStationListRequest,
  GetChargingStationListResponse,
  GetCompanyDetailResponse,
  GetCurrentScheduleRequest,
  GetCurrentScheduleResponse,
  GetEventCaptureInfoRequest,
  GetEventCaptureInfoResponse,
  GetEventCaptureListRequest,
  GetEventCaptureListResponse,
  GetOperationRecordDeatilResponse,
  GetOperationRecordListRequest,
  GetOperationRecordListResponse,
  GetProAreaCarAuthRequest,
  GetProAreaListRequest,
  GetProAreaListResponse,
  GetProAreaResponse,
  GetProCalculateListRequest,
  GetProDetailResponse,
  GetProListResponse,
  GetProUserDetailResponse,
  GetProUserListRequest,
  GetProUserListResponse,
  GetProVehListRequest,
  GetProVehListResponse,
  GetProVehManagerListRequest,
  GetProVehManagerListResponse,
  GetProjectAndAreaRequest,
  GetProjectAndAreaResponse,
  GetPublicKeyResponse,
  GetRTKDetailResponse,
  GetRedLightListResponse,
  GetRoleDetailResponse,
  GetRouteListRequest,
  GetRouteTemplateRequest,
  GetRouteTemplateResponse,
  GetScheduleDetailRequest,
  GetScheduleDetailResponse,
  GetScheduleTempDetailRequest,
  GetScheduleTempDetailResponse,
  GetSecurityEventListRequest,
  GetSecurityEventListResponse,
  GetSimDetailResponse,
  GetSmartOrderListResponse,
  GetStaionDetailRequest,
  GetStaionDetailResponse,
  GetStationListByAreaRequest,
  GetStationListByAreaResponse,
  GetStatisticsInfoRequest,
  GetStatisticsInfoResponse,
  GetStatisticsListRequest,
  GetStatisticsListResponse,
  GetTempListByAreaRequest,
  GetTempListByAreaResponse,
  GetUserDetailResponse,
  GetVehAuthListResponse,
  GetVehBlockListResponse,
  GetVehEventListRequest,
  GetVehEventListResponse,
  GetVehEventTypeResponse,
  GetVehLocusDatesRequest,
  GetVehLocusDatesResponse,
  GetVehLocusPositionRequest,
  GetVehLocusPositionResponse,
  GetVehOperationTypeResponse,
  GetVehRouteListResponse,
  GetVehStationListResponse,
  GetVehTaskInfoRequest,
  GetVehTaskInfoResponse,
  GetVehTypeByIdRequest,
  GetVehTypeByIdResponse,
  GetVehicleAdvancedConfigResponse,
  GetVehicleAlarmDetailResponse,
  GetVehicleAlarmListRequest,
  GetVehicleAlarmListResponse,
  GetVehicleDetailResponse,
  GetVehicleModelDetailResponse,
  GetVehicleSetupRequest,
  GetVehicleSetupResponse,
  GetVehicleTaskDetailResponse,
  ListWarnDownloadRequest,
  LiveCarTreeResponse,
  LiveCarVideoInfoResponse,
  LoginRequest,
  LoginResponse,
  OperConfigRequest,
  OperConfigResponse,
  PermTreeResponse,
  ProAreaCarAuthRequest,
  ProListRequest,
  ProListResponse,
  ProjectListResponse,
  QueryAreaRecordingRouteResponse,
  QueryCustomAreaResponse,
  QueryVehRealTimeInfoByVehNoResponse,
  QueryVehRecordingRouteData,
  RTKListRequest,
  RTKListResponse,
  RoleListRequest,
  RoleListResponse,
  RoleUserAccountRequest,
  RoleUserAccountResponse,
  SaveAreaRouteTemplateRequest,
  SaveChargingStationInfoRequest,
  SaveEventCaptureConfigRequest,
  SaveVehAuthRequest,
  ScreenCarWorkRequest,
  SetEventCaptureStatusRequest,
  SetVehicleAdvancedConfigParams,
  SetVehicleSetupRequest,
  SimListRequest,
  SimListResponse,
  StatisticsNumberRequest,
  StatisticsNumberResponse,
  TrafficLightExamineParams,
  UpdateChargingStationInfoRequest,
  UpdateLampBlowWaterRequest,
  UpdateProVehManagerRequest,
  UpdateScheduleTemplateRequest,
  UpdateStationPicResponse,
  UpdateTaskStatusRequest,
  UserAreaStationInfo,
  UserListInEntResponse,
  UserListRequest,
  UserListResponse,
  UserSearchRoleRequest,
  UserSearchRoleResponse,
  UserStatusRequest,
  VehListInEntResponse,
  VehicleListRequest,
  VehicleListResponse,
  VehicleModelListRequest,
  VehicleModelListResponse,
  VehicleNoRepeatRequest,
  VehicleTreeInEntResponse,
  VehicleVinRepeatRequest,
  WarnConfigDetailRequest,
  WarnConfigDetailResponse,
  WarnConfigListRequest,
  WarnConfigListResponse,
  WarnDetailResponse,
  WarnListRequest,
  WarnListResponse,
  WarnModuleAndNameRequest,
  WarnModuleAndNameResponse,
  WarnSettingListResponse,
  carCleanReportRequest,
  changePwdRequest,
  checkPwdRequest,
  checkPwdResponse,
  updateHeadPicResponse,
  uavRouteListResponse,
  GetTrafficLightListResponse,
  MunicipalFacilitiesListResponse,
} from "./type";

// -------------------------------- 依赖websocket --------------------------------
// 设置灯光喷水
export const updateLampBlowWater = (param: UpdateLampBlowWaterRequest) =>
  http<UpdateLampBlowWaterRequest>("post", "/cszg/vehicleOnTime/lampBlowWater", param, 0);
// 车辆模式切换
export const changeDriveMode = (param: ChangeDriveModeRequest) =>
  http<ChangeDriveModeRequest>("post", "/cszg/vehicleOnTime/changeDriveMode", param, 0);

// ----------------数字大屏
// 项目列表
export const screenProList = () => http("get", "/cszg/dataCalculateSort/currentUser/allProject");

// 车辆作业排名
export const screenCarWork = (param: ScreenCarWorkRequest) =>
  http<ScreenCarWorkRequest>("post", "/cszg/dataCalculateSort/getVehsCleanTimeAndCleanAreaData", param);

// 区域作业排名
export const screenAreaWork = (param: ScreenCarWorkRequest) =>
  http<ScreenCarWorkRequest>("post", "/cszg/dataCalculateSort/getProAreaCleanTimeAndCleanAreaData", param);

// 车辆详情 - 历史回放 - 任务列表
export const historyTaskList = (param: any) => http<any>("post", "/cszg/vehicleLocus/queryVehWorkNoList", param);

// ------------------------------------- HTTP -------------------------------------
// 获取公钥
export const getPublicKey = () => http<any, GetPublicKeyResponse>("get", "/publicKey");

// 登录
export const login = (param: LoginRequest) => http<LoginRequest, LoginResponse>("post", "/loginByPWD", param);

// 跳转自动登录
export const getUserInfo = () => http<any, LoginResponse>("post", "/common/queryUserInfo");

// ----------------车辆实时查看
export const liveCarTree = (idList?: string[]) =>
  http<{ deviceNoList?: string[] }, LiveCarTreeResponse>("post", "/cszg/vehicleInfo/common/queryUserVehPro", {
    deviceNoList: idList,
  });

// 查询登录账号的站点 - 大屏的地图需要传项目id
export const liveCarSites = (proId?: string) =>
  http<any, UserAreaStationInfo[]>("post", "/cszg/proArea/common/queryUserAreaStationInfo", {
    id: proId || "",
  });

// 查询登录账号的路线
export const liveCarRoutes = () => http("get", "/cszg/proArea/common/queryUserAreaRouteInfo", {}, 30000);

// 查询登录账号的区域范围
export const liveCarFences = () => http("get", "/cszg/proArea/common/queryUserAreaFenceInfo");

// 查询车辆视频流相关信息
export const liveCarVideoInfo = (keyword: string | any) =>
  http<any, LiveCarVideoInfoResponse>("post", "/cszg/vehicleOnTime/carList", { keyword });

// 打开客户端
export const openClient = (vehicleNo: string) => http("post", "/cszg/rvehicleOnTime/remote", { vehicleNo });

// ----------------用户管理
// 查询用户列表
export const userList = (param?: UserListRequest) =>
  http<UserListRequest, UserListResponse>("post", "/cszg/user/list", param);

// 用户查询用户角色
export const userSearchRole = (param: UserSearchRoleRequest) =>
  http<UserSearchRoleRequest, UserSearchRoleResponse>("post", "/cszg/user/entRole", param);

// 新增普通用户
export const addUser = (param: AddUserRequest) => http<AddUserRequest>("post", "/cszg/user/save", param);

// 删除用户信息
export const delUser = (userId: number) => http<number[]>("post", `/cszg/user/delete/${userId}`);

// 编辑用户
export const editUser = (param: EditUserRequest) => http<EditUserRequest>("post", "/cszg/user/update", param);

// 更改用户状态
export const userStatus = (param: UserStatusRequest) =>
  http<UserStatusRequest>("post", "/cszg/user/changeStatus", param);

// 重置密码
export const resetUserPassword = (userId: number) => http<any, { pwd: string }>("get", `/cszg/user/resetPWD/${userId}`);

// 查询单个用户详情
export const getUserDetail = (id: number) => http<any, GetUserDetailResponse>("post", `/cszg/user/info/${id}`);

// 车辆授权 - 获取用户树
export const getCarTree = (userId: number) =>
  http<any, { entId: string }>("get", `/cszg/user/userEntTreeWithVelSelected/${userId}`);

// 车辆授权 - 保存
export const carAuth = (param: CarAuthRequest) => http<CarAuthRequest>("post", "/cszg/user/saveUserRelVel", param);

// 校验用户手机是否重复
export const mobileRepeat = (param: { mobile: string; userId?: string }) =>
  http<{ mobile: string; userId?: string }, any>("post", "/cszg/user/common/mobileRepeat", param);

// 校验用户账号是否重复
export const accountRepeat = (param: { userAccount: string }) =>
  http<{ userAccount: string }, any>("post", "/cszg/user/accountRepeat", param);

// ----------------企业管理
// 查询企业树
export const compTree = () => http<any>("post", "/cszg/ent/common/entTree");
// 查询企业列表
export const companyList = (param: CompanyListRequest) =>
  http<CompanyListRequest, CompanyListResponse>("post", "/cszg/ent/list", param);
// 新增企业
export const addCompany = (param: AddCompanyRequest) => http<AddCompanyRequest>("post", "/cszg/ent/save", param);
// 查询单个企业信息
export const getCompanyDetail = (id: string) => http<any, GetCompanyDetailResponse>("post", `/cszg/ent/info/${id}`);
// 编辑企业
export const editCompany = (param: EditCompanyRequest) => http<EditCompanyRequest>("post", "/cszg/ent/update", param);
// 删除用户信息
export const delCompany = (param: number[]) => http<number[]>("post", "/cszg/ent/delete", param);
// 企业功能配置
export const getEntCurrentPermits = (param: { parentEntId: number }) =>
  http<{ parentEntId: number }, PermTreeResponse>("post", "/cszg/ent/queryCurrentPermits", param);

// 查询登录用户所在及下级企业列表
export const compList = () => http<any, CompListResponse>("get", "/cszg/ent/common/entIdAndNameList");

// ----------------查询配置信息列表
export const getConfigList = (param: OperConfigRequest) =>
  http<OperConfigRequest, OperConfigResponse>("post", "/cszg/operConfig/list", param);

// ----------------车辆型号
// 查询车辆型号列表
export const vehicleModelList = (param: VehicleModelListRequest) =>
  http<VehicleModelListRequest, VehicleModelListResponse>("post", "/cszg/vehicleModel/list", param);
// 新增车辆型号
export const addVehicleModel = (param: AddVehicleModelRequest) =>
  http<AddVehicleModelRequest>("post", "/cszg/vehicleModel/save", param);
// 查询单个车辆型号信息
export const getVehicleModelDetail = (id: string) =>
  http<any, GetVehicleModelDetailResponse>("post", `/cszg/vehicleModel/info/${id}`);
// 编辑车辆型号
export const editVehicleModel = (param: EditVehicleModelRequest) =>
  http<EditVehicleModelRequest>("post", "/cszg/vehicleModel/update", param);
// 删除车辆型号
export const delVehicleModel = (param: number[]) => http<number[]>("post", "/cszg/vehicleModel/delete", param);
// 校验车辆型号是否重复
export const vehicleModelRepeat = (param: { id?: string; vehicleModel: string }) =>
  http<{ vehicleModel: string }, any>("post", "/cszg/vehicleModel/repeat", param);

// ----------------SIM卡
// 查询SIM卡列表
export const simList = (param: SimListRequest) =>
  http<SimListRequest, SimListResponse>("post", "/cszg/sim/list", param);
// 新增SIM卡
export const addSim = (param: AddSimRequest) => http<AddSimRequest>("post", "/cszg/sim/save", param);
// 查询单个SIM卡信息
export const getSimDetail = (id: string) => http<any, GetSimDetailResponse>("post", `/cszg/sim/info/${id}`);
// 编辑SIM卡
export const editSim = (param: EditSimRequest) => http<EditSimRequest>("post", "/cszg/sim/update", param);
// 删除用户信息
export const delSim = (param: number[]) => http<number[]>("post", "/cszg/sim/delete", param);
// 查询可分配的sim卡
export const getEntSim = (param: EntSimRequest) =>
  http<EntSimRequest, EntSimResponse>("post", "/cszg/sim/common/queryEntSimNotUsedOrOfOneVeh", param);

// ----------------项目管理
// 查询项目列表
export const proList = (param: ProListRequest) =>
  http<ProListRequest, ProListResponse>("post", "/cszg/pro/list", param);
// 新增项目
export const addPro = (param: AddProRequest) => http<AddProRequest>("post", "/cszg/pro/save", param);
// 查询单个项目信息
export const getProDetail = (id: string) => http<string, GetProDetailResponse>("post", `/cszg/pro/info/${id}`);
// 编辑项目
export const editPro = (param: EditProRequest) => http<EditProRequest>("post", "/cszg/pro/update", param);
// 删除用户信息
export const delPro = (param: string[]) => http<string[]>("post", "/cszg/pro/delete", param);
// 查询企业下所有用户
export const getUserListInEnt = (entId: string) =>
  http<any, UserListInEntResponse>("post", `/cszg/pro/queryUserByEntId/${entId}`);
// 查询企业下所有车辆
export const getVehListInEnt = (param: { entId: string; proId: string }) =>
  http<{ entId: string; proId: string }, VehListInEntResponse>(
    "post",
    "/cszg/vehicleInfo/common/queryVehBelongEnt",
    param
  );
// 项目人员分配
export const saveProUserRel = (param: { id: string; userIdList: Array<string | number> }) =>
  http<{ id: string; userIdList: Array<string | number> }, any>("post", "/cszg/pro/saveRelUser", param);
// 项目车辆分配
export const saveProVehRel = (param: { id: string; vehIdList: Array<string | number> }) =>
  http<{ id: string; vehIdList: Array<string | number> }, any>("post", "/cszg/pro/saveRelVeh", param);

// ----------------车辆管理
// 查询车辆列表
export const vehicleList = (param: VehicleListRequest) =>
  http<VehicleListRequest, VehicleListResponse>("post", "/cszg/vehicleInfo/list", param);
// 新增车辆
export const addVehicle = (param: AddVehicleRequest) =>
  http<AddVehicleRequest>("post", "/cszg/vehicleInfo/save", param);
// 查询单个车辆信息
export const getVehicleDetail = (id: string) =>
  http<any, GetVehicleDetailResponse>("post", `/cszg/vehicleInfo/info/${id}`);
// 编辑车辆
export const editVehicle = (param: EditVehicleRequest) =>
  http<EditVehicleRequest>("post", "/cszg/vehicleInfo/update", param);
// 删除车辆信息
export const delVehicle = (param: number[]) => http<number[]>("post", "/cszg/vehicleInfo/delete", param);
// 查询企业项目车辆树
export const vehicleTreeInEnt = () =>
  http<any, VehicleTreeInEntResponse>("post", "/cszg/vehicleInfo/queryVehBelongTree");
// 校验VIN码是否重复
export const vehicleVinRepeat = (param: VehicleVinRepeatRequest) =>
  http<VehicleVinRepeatRequest, any>("post", "/cszg/vehicleInfo/checkVin", param);
// 校验车牌号是否重复
export const vehicleNoRepeat = (param: VehicleNoRepeatRequest) =>
  http<VehicleNoRepeatRequest, any>("post", "/cszg/vehicleInfo/checkVehNo", param);
// 校验蒲公英账号是否重复
export const dandelionAccountRepeat = (param: DandelionAccountRepeatRequest) =>
  http<DandelionAccountRepeatRequest, any>("post", "/cszg/vehicleInfo/checkDandelionAccount", param);

// ----------------角色管理
// 查询角色列表
export const roleList = (param: RoleListRequest) =>
  http<RoleListRequest, RoleListResponse>("post", "/cszg/role/list", param);
// 新增角色
export const addRole = (param: AddRoleRequest) => http<AddRoleRequest>("post", "/cszg/role/save", param);
// 查询单个角色信息
export const getRoleDetail = (id: string) => http<any, GetRoleDetailResponse>("post", `/cszg/role/info/${id}`);
// 编辑角色
export const editRole = (param: EditRoleRequest) => http<EditRoleRequest>("post", "/cszg/role/update", param);
// 删除角色信息
export const delRole = (param: string[]) => http<string[]>("post", "/cszg/role/delete", param);
// 查询角色对应的帐号
export const getRoleUserAccount = (param: RoleUserAccountRequest) =>
  http<RoleUserAccountRequest, RoleUserAccountResponse>("post", "/cszg/role/userAccount", param);
// 校验角色名称是否重复
export const roleNameRepeat = (param: { roleId?: string; roleName: string; entId: string; isSaveOperation: boolean }) =>
  http<{ roleName: string; entId: string; isSaveOperation: boolean }, any>("post", "/cszg/role/checkRoleName", param);
// 角色功能配置
export const getRoleCurrentPermits = (param: { entId: number; roleId?: number }) =>
  http<{ entId: number; roleId?: number }, PermTreeResponse>("post", "/cszg/role/queryRolePermit", param);

// ----------------角色管理
// 查询角色列表
export const permTree = () => http<any, PermTreeResponse>("get", "/cszg/role/queryPermTree");

// ----------------个人中心
// 更新头像
export const updateHeadPic = (usrId: string, param: FormData) =>
  http<FormData, updateHeadPicResponse>("post", `/cszg/user-center/saveHeadImg/${usrId}`, param);
// 校验原密码
export const checkPwd = (param: checkPwdRequest) =>
  http<checkPwdRequest, checkPwdResponse>("post", "/cszg/user-center/checkOriginal", param);
// 修改密码
export const changePwd = (param: changePwdRequest) =>
  http<changePwdRequest>("post", "/cszg/user-center/changePwd", param);
// 登出
export const logout = () => http<any>("post", "/logout");

// ----------------告警统计
// 查询车辆告警列表
export const getWarnList = (param: WarnListRequest) =>
  http<WarnListRequest, WarnListResponse>("post", "/cszg/vehicleWarn/list", param);
// 查询车辆告警统计数字
export const statisticsNumber = (param: StatisticsNumberRequest) =>
  http<StatisticsNumberRequest, StatisticsNumberResponse>("post", "/cszg/vehicleWarn/statistics", param);
// 更新车辆告警状态
export const updateWarnStatus = (param: any) => http<any>("post", "/cszg/vehicleWarn/status", param);
// 查询车辆详情
export const getWarnDetail = (param: any) => http<any, WarnDetailResponse>("post", "/cszg/vehicleWarn/detail", param);

// ----------------告警配置
// 查询告警配置列表(主页)
export const getWarnConfigList = (param: WarnConfigListRequest) =>
  http<WarnConfigListRequest, WarnConfigListResponse>("post", "/cszg/warnConfig/list", param);
// 修改是否接收
export const changeReceiveStatus = (param: ChangeReceiveRequest) =>
  http<ChangeReceiveRequest>("post", "/cszg/warnConfig/changeStatus", param);
// 新增告警配置
export const addWarnConfig = (param: AddWarnConfigRequest) =>
  http<AddWarnConfigRequest>("post", "/cszg/warnConfig/save", param);
// 编辑告警配置
export const editWarnConfig = (param: EditWarnConfigRequest) =>
  http<EditWarnConfigRequest>("post", "/cszg/warnConfig/update", param);
// 查询告警配置详情
export const getWarnConfigDetail = (param: WarnConfigDetailRequest) =>
  http<WarnConfigDetailRequest, WarnConfigDetailResponse>("post", "/cszg/warnConfig/info", param);
// 删除告警配置
export const deleteWarnConfig = (param: DeleteWarnConfigRequest) =>
  http<DeleteWarnConfigRequest>("post", "/cszg/warnConfig/delete", param);
// 查询告警模块与告警名称
export const getWarnModuleAndName = (param: WarnModuleAndNameRequest) =>
  http<WarnModuleAndNameRequest, WarnModuleAndNameResponse>("post", "/cszg/warnConfig/warnAssociation", param);
// 查询告警配置列表(设置页)
export const getWarnSettingList = () => http<any, WarnSettingListResponse>("get", "/cszg/warnConfig/configList");
// 批量设置告警
export const batchSetWarnConfig = (param: BatchSetWarnConfigRequest) =>
  http<BatchSetWarnConfigRequest>("post", "/cszg/warnConfig/batch", param);

// ----------------RTK账号
// 查询RTK账号列表
export const rtkList = (param: RTKListRequest) =>
  http<RTKListRequest, RTKListResponse>("post", "/cszg/rtk/list", param);
// 新增RTK账号
export const addRTK = (param: AddRTKRequest) => http<AddRTKRequest>("post", "/cszg/rtk/save", param);
// 查询单个RTK账号信息
export const getRTKDetail = (id: string) => http<any, GetRTKDetailResponse>("get", `/cszg/rtk/info/${id}`);
// 编辑RTK账号
export const editRTK = (param: EditRTKRequest) => http<EditRTKRequest>("post", "/cszg/rtk/update", param);
// 删除RTK账号
export const delRTK = (id: string) => http<number[]>("post", `/cszg/rtk/delete/${id}`);
// 获取未使用的RTK账号
export const getEntRTK = (param: AvailableRTKRequest) =>
  http<AvailableRTKRequest, AvailableRTKResponse>("post", "/cszg/rtk/common/getNotUsingRTKList", param);
// 校验RTK账号
export const rtkRepeat = (param: any) => http<any, any>("post", "/cszg/rtk/checkRTK", param);

// ----------------项目区域管理
const mockAPi = "https://mock.apifox.cn/m1/1987833-0-default";
// 项目列表
export const getProList = (ids: number[] = []) =>
  http<any, GetProListResponse>("post", "/cszg/ent/common/proList", ids);
// 查询列表
export const getProAreaList = (param: GetProAreaListRequest) =>
  http<GetProAreaListRequest, GetProAreaListResponse>("post", "/cszg/proArea/list", param);
// 新增项目区域
export const addProArea = (param: AddProAreaRequest) =>
  http<any, AddProAreaRequest>("post", "/cszg/proArea/save", param);
// 编辑项目区域
export const editProArea = (param: AddProAreaRequest) =>
  http<any, AddProAreaRequest>("post", "/cszg/proArea/update", param);
// 查看项目区域
export const getProAreaInfo = (id?: string, options?: AxiosRequestConfig) =>
  http<any, GetProAreaResponse>("post", "/cszg/proArea/info", { id }, 30000, undefined, options);
// 删除项目区域
export const delProArea = (id?: string) => http("post", "/cszg/proArea/delete", { id });
// 车辆授权分配情况
export const getProAreaCarAuth = (param: GetProAreaCarAuthRequest) =>
  http<any, GetProAreaCarAuthRequest>("post", "/cszg/proArea/allocatedInfo", param);
// 分配车辆
export const proAreaCarAuth = (param: ProAreaCarAuthRequest) =>
  http<any, ProAreaCarAuthRequest>("post", "/cszg/proArea/allocate", param);
/* 查询路线模板 /cszg/proArea/getAreaRouteTemplate**/
export const getAreaRouteTemplate = (proAreaId: string) =>
  http<any, GetAreaRouteTemplateResponse>("get", `/cszg/proArea/getAreaRouteTemplate/${proAreaId}`);
// 新增路线模板
export const saveAreaRouteTemplate = (param: SaveAreaRouteTemplateRequest) =>
  http<any, SaveAreaRouteTemplateRequest>("post", "/cszg/proArea/saveAreaRouteTemplate", param);
// 上传场所图片
export const updateStationPic = (stationId: string, param: FormData) =>
  http<any, UpdateStationPicResponse>("post", `/cszg/proArea/saveImage/${stationId}`, param);

// ----------------项目人员管理
// 查询列表
export const getProUserList = (param: GetProUserListRequest) =>
  http<GetProUserListRequest, GetProUserListResponse>("post", "/cszg/proUser/list", param);
// 查询项目名称列表名称
export const projectList = () => http<any, ProjectListResponse>("get", "/cszg/proUser/proList");
// 项目人员管理查询具体信息
export const getProUserDetail = (id: string) => http<any, GetProUserDetailResponse>("get", `/cszg/proUser/info/${id}`);
// 查询分级车辆列表
export const getVehAuthList = (id: string) =>
  http<any, GetVehAuthListResponse>("get", `/cszg/proUser/vehAuthList/${id}`);
// 重置用户密码
export const resetProUserPassword = (userId: string) =>
  http<any, { pwd: string }>("get", `/cszg/proUser/pwdReset/${userId}`);
//保存车辆授权列表
export const saveVehAuth = (param: SaveVehAuthRequest) =>
  http<SaveVehAuthRequest>("post", "/cszg/proUser/saveVehAuth", param);

// ----------------项目车辆管理
// 查询列表
export const getProVehList = (param: GetProVehListRequest) =>
  http<GetProVehListRequest, GetProVehListResponse>("post", "/cszg/vehManage/list", param);
// 查询负责人列表
export const getProVehManagerList = (param: GetProVehManagerListRequest) =>
  http<GetProVehManagerListRequest, GetProVehManagerListResponse>("post", "/cszg/vehManage/person", param);
// 设置车辆负责人
export const updateProVehManager = (param: UpdateProVehManagerRequest) =>
  http<UpdateProVehManagerRequest>("post", "/cszg/vehManage/setUp", param);
// 查询项目及区域  空数组=>查项目列表  项目id数组 => 查区域列表
export const getProjectAndArea = (param: GetProjectAndAreaRequest) =>
  http<GetProjectAndAreaRequest, GetProjectAndAreaResponse>("post", "/cszg/vehManage/common/proToArea", param);
// 修改排班计划状态
export const updateTaskStatus = (param: UpdateTaskStatusRequest) =>
  http<UpdateTaskStatusRequest>("post", "/cszg/vehManage/switchStatus", param);
// 查询区域下路线信息
export const getRouteList = (param: GetRouteListRequest) =>
  http<GetRouteListRequest, any>("post", "/cszg/vehManage/route", param);
// 保存车辆排班
export const updateVehTask = (param: any) => http("post", "/cszg/vehManage/vehTask", param);
// 查询车辆排班信息
export const getVehTaskInfo = (param: GetVehTaskInfoRequest) =>
  http<GetVehTaskInfoRequest, GetVehTaskInfoResponse>("post", "/cszg/vehManage/taskInfo", param);
// 查询项目车辆的设置
export const getVehicleSetup = (param: GetVehicleSetupRequest) =>
  http<GetVehicleSetupRequest, GetVehicleSetupResponse>("post", "/cszg/vehManage/getVehicleSetup", param);
// 设置项目车辆的配置
export const setVehicleSetup = (param: SetVehicleSetupRequest) =>
  http<SetVehicleSetupRequest>("post", "/cszg/vehManage/setVehicleSetup", param);
// 查询车当前排班任务
export const getCurrentSchedule = (param: GetCurrentScheduleRequest) =>
  http<GetCurrentScheduleRequest, GetCurrentScheduleResponse>(
    "post",
    "/cszg/vehManage/queryScheduleAlreadySYNC",
    param
  );
// 同步排班
export const saveCarSchedule = (param: any) => http("post", "/cszg/vehManage/vehicle/arrangement", param);
// 查询排班详情
export const getScheduleDetail = (param: GetScheduleDetailRequest) =>
  http<GetScheduleDetailRequest, GetScheduleDetailResponse>("post", "/cszg/vehManage/areaInfo", param);
// 重置hmi密码
export const resetHMIPassword = (id: string) => http<{ id: string }>("post", "/cszg/vehManage/resetHMIPWD", { id });

// ----------------项目车辆管理-排班模板
// 查询排班列表
export const getAreaScheduleList = (param: GetAreaScheduleListRequest) =>
  http<GetAreaScheduleListRequest, GetAreaScheduleListResponse>(
    "post",
    "/cszg/vehManage/areaScheduleTemplate/list",
    param
  );
// 新增排班模板
export const addScheduleTemplate = (param: AddScheduleTemplateRequest) =>
  http<AddScheduleTemplateRequest>("post", "/cszg/vehManage/areaScheduleTemplate/save", param);
// 根据项目区域获取路线模版列表
export const getRouteTempListByArea = (param: GetTempListByAreaRequest) =>
  http<GetTempListByAreaRequest, GetTempListByAreaResponse>(
    "post",
    "/cszg/vehManage/areaScheduleTemplate/common/getRouteTempList",
    param
  );
// 根据区域获取站点列表
export const getStationListByArea = (param: GetStationListByAreaRequest) =>
  http<GetStationListByAreaRequest, GetStationListByAreaResponse>(
    "post",
    "/cszg/vehManage/areaScheduleTemplate/common/getStationList",
    param
  );
// 查询排班模板详情
export const getScheduleTempDetail = (param: GetScheduleTempDetailRequest) =>
  http<GetScheduleTempDetailRequest, GetScheduleTempDetailResponse>(
    "post",
    "/cszg/vehManage/areaScheduleTemplate/info4Page",
    param
  );
/** 保存修改的模板列表 */
export const updateScheduleTemplate = (param: UpdateScheduleTemplateRequest) =>
  http<UpdateScheduleTemplateRequest>("post", "/cszg/vehManage/areaScheduleTemplate/update", param);
/** 检查模版名称是否重复 */
export const checkTempName = (param: CheckTempNameRequest) =>
  http<CheckTempNameRequest>("post", "/cszg/vehManage/areaScheduleTemplate/common/checkTempName", param);

// ----------------多任务下发
/** 查询车辆的站点 */
export const getVehStationList = (vehNo: string, options?: AxiosRequestConfig) =>
  http<any, GetVehStationListResponse>(
    "get",
    `/cszg/proArea/common/queryVehicleStationInfo?vehNo=${vehNo}`,
    undefined,
    undefined,
    undefined,
    options
  );
/** 查询车辆的路线 */
export const getVehRouteList = (vehNo: string, options?: AxiosRequestConfig) =>
  http<any, GetVehRouteListResponse>(
    "get",
    `/cszg/proArea/common/queryVehicleRouteInfo?vehNo=${vehNo}`,
    undefined,
    undefined,
    undefined,
    options
  );
/** 根据车辆查询区域区块 */
export const getVehBlockList = (vehNo: string, options?: AxiosRequestConfig) =>
  http<any, GetVehBlockListResponse>(
    "post",
    `/cszg/proArea/common/getProAreaBlockListByVehId`,
    { vehicleNo: vehNo },
    undefined,
    undefined,
    options
  );
/** 项目区域范围查询 */
export const getProAreaRange = (
  params: { vehNo?: string; areaId?: string; [x: string]: any },
  options?: AxiosRequestConfig
) =>
  http<any, GetAreaRangeResponse>(
    "get",
    `/cszg/proArea/common/getAreaRange?${Object.keys(params)
      .map((key) => `${key}=${params[key]}`)
      .join("&")}`,
    undefined,
    undefined,
    undefined,
    options
  );
// 查询路线模板
export const getRouteTemplate = (param: GetRouteTemplateRequest) =>
  http<GetRouteTemplateRequest, GetRouteTemplateResponse>("get", "/cszg/proArea/common/getAreaRouteTemplate", param);

// ----------------轨迹回放
// 查询车辆列表
export const getTrackVehicles = () => http("post", "/cszg/vehicleLocus/vehicles");
// 查询有轨迹的日期
export const getTrackDates = (param: GetVehLocusDatesRequest) =>
  http<GetVehLocusDatesRequest, GetVehLocusDatesResponse>("post", "/cszg/vehicleLocus/times", param);
// 查询轨迹节点列表
export const getTrackPositions = (param: GetVehLocusPositionRequest) =>
  http<GetVehLocusPositionRequest, GetVehLocusPositionResponse>("post", "/cszg/vehicleLocus/vehLocusList", param);
// ----------------车辆操作记录
// 查询车辆操作记录列表
export const getOperationRecordList = (param: GetOperationRecordListRequest) =>
  http<GetOperationRecordListRequest, GetOperationRecordListResponse>("post", "/cszg/VehTaskInfo/list", param);
// 查询操作事项列表
export const getVehOperationType = () =>
  http<any, GetVehOperationTypeResponse>("get", "/cszg/VehTaskInfo/getAllTaskItem");
// 车辆操作记录详情
export const getOperationRecordDeatil = (id: string) =>
  http<any, GetOperationRecordDeatilResponse>("post", `/cszg/VehTaskInfo/info/${id}`);

// ----------------车辆任务数据详情
export const carCleanReport = (param: carCleanReportRequest) =>
  http<carCleanReportRequest>("post", "/cszg/vehCalculate/vehCalculateList", param);
// 车辆任务数据详情导出
export const exportCalculateList = (param: ExportCalculateListRequest) =>
  http<ExportCalculateListRequest>("post", "/cszg/vehCalculate/vehCalculateListDownLoad", param, 5000, "blob");

// 车辆告警统计列表导出
export const exportVehicleAlarmStatistics = (param: ListWarnDownloadRequest) =>
  http<ListWarnDownloadRequest>("post", "/cszg/vehicleWarn/listWarnDownload", param, 5000, "blob").catch((error) => {
    console.error("导出车辆告警统计列表时发生错误:", error);
  });

// 查询车牌号列表
export const getVehList = () => http("get", "/cszg/vehCalculate/common/getVehNo");

// ----------------充电桩信息管理
// 查询充电桩信息列表
export const getChargingStationList = (param: GetChargingStationListRequest) =>
  http<GetChargingStationListRequest, GetChargingStationListResponse>("post", "/cszg/chargingStation/list", param);

// 新增充电桩信息
export const saveChargingStationInfo = (param: SaveChargingStationInfoRequest) =>
  http<SaveChargingStationInfoRequest>("post", "/cszg/chargingStation/save", param);

// 修改充电桩信息
export const updateChargingStationInfo = (param: UpdateChargingStationInfoRequest) =>
  http<UpdateChargingStationInfoRequest>("post", "/cszg/chargingStation/update", param);

// 充电桩信息详情
export const getChargingStationInfo = (id: string) =>
  http<any, GetChargingStationInfoResponse>("post", `/cszg/chargingStation/getSysChargingEntity/${id}`);

// 删除充电桩信息
export const delChargingStation = (id: string) => http("get", `/cszg/chargingStation/delete/${id}`);

// 通过企业获取充电桩
export const getChargingByEntId = (params: { entId: string; accessoriesType: number }) =>
  http("post", `/cszg/chargingStation/common/getChargingByEntId`, params);

// ----------------自动泊车充电
// 模拟充电状态
export const chargeCar = (id: string) => http<any>("post", "/cszg/vehicleOnTime/chargeItem", { id });

// ----------------车辆任务数据统计
// 查询主页统计数据信息
export const getStatisticsInfo = (param: GetStatisticsInfoRequest) =>
  http<GetStatisticsInfoRequest, GetStatisticsInfoResponse>("post", "/cszg/vehCalculate/statisticsInfo", param, 0);
// 查询详情列表
export const getStatisticsList = (param: GetStatisticsListRequest) =>
  http<GetStatisticsListRequest, GetStatisticsListResponse>("post", "/cszg/vehCalculate/statisticsList", param, 0);
// 统计数据导出
export const exportStatistics = (param: ExportStatisticsRequest) =>
  http<ExportStatisticsRequest>("post", "/cszg/vehCalculate/statisticsDownLoadData", param, 5000, "blob");

// ----------------车辆任务汇总统计
// 查询列表
export const getVehTaskSummaryInfo = (param: any) =>
  http<any, any>("post", "/cszg/vehDataCalculate/vehCalculateList", param);

// 导出列表
export const exportVehTaskSummaryInfo = (param: any) =>
  http<any>("post", "/cszg/vehDataCalculate/vehCalculateListDownload", param, 5000, "blob");

// vin和车牌号列表
export const getVehAndVinList = () =>
  http<any, { vin: string; vehicleNo: string }[]>("post", "/cszg/vehDataCalculate/common/queryVehWithUserCondition");

// ----------------车辆事件处理统计
// 查询车辆事件处理列表
export const getVehEventList = (param: GetVehEventListRequest) =>
  http<GetVehEventListRequest, GetVehEventListResponse>("post", "/cszg/vehCaptureEvent/list", param, 0);
// 查询事件处理类型列表
export const getVehEventType = () => http<any, GetVehEventTypeResponse>("get", "/cszg/vehCaptureEvent/getAllEventType");

// ----------------安全事件上报统计
export const getSecurityEventList = (param: GetSecurityEventListRequest) =>
  http<GetSecurityEventListRequest, GetSecurityEventListResponse>("post", "/cszg/security/list", param);

// ----------------事件抓拍设置
// 列表查询
export const getEventCaptureList = (param: GetEventCaptureListRequest) =>
  http<GetEventCaptureListRequest, GetEventCaptureListResponse>("post", "/cszg/capture/list", param);
// 新增
export const saveEventCaptureConfig = (param: SaveEventCaptureConfigRequest) =>
  http<SaveEventCaptureConfigRequest>("post", "/cszg/capture/save", param);
// 查询车辆类型
export const getVehTypeById = (param: GetVehTypeByIdRequest) =>
  http<GetVehTypeByIdRequest, GetVehTypeByIdResponse>("post", "/cszg/capture/vehType", param);
// 读取
export const getEventCaptureInfo = (param: GetEventCaptureInfoRequest) =>
  http<GetEventCaptureInfoRequest, GetEventCaptureInfoResponse>("post", "/cszg/capture/info", param);
// 开关状态
export const setEventCaptureStatus = (param: SetEventCaptureStatusRequest) =>
  http<SetEventCaptureStatusRequest>("post", "/cszg/capture/operate", param);

// ----------------充电桩浮窗
// 查询站点详情
export const getStaionDetail = (param: GetStaionDetailRequest) =>
  http<GetStaionDetailRequest, GetStaionDetailResponse>("post", "/cszg/proArea/common/queryDetail", param);

// ----------------项目区域汇总统计
// 查询项目汇总统计列表
export const getProCalculateList = (param: GetProCalculateListRequest) =>
  http<GetProCalculateListRequest, any>("post", "/cszg/proDataCalculate/proCalculateList", param);
// 查询项目汇总统计列表导出
export const exportProCalculateList = (param: ExportProCalculateListRequest) =>
  http<ExportProCalculateListRequest>("post", "/cszg/proDataCalculate/proCalculateListDownload", param, 5000, "blob");

// ----------------大屏
// 查询项目人员
export const getProUser = (proId: string) =>
  http<any, any>("post", "/cszg/dataCalculateSort/proUser/realTimeInfo", {
    proId,
  });
// 查询作业展示图表数据
export const getWorkView = (proId: string) =>
  http<any, any>("post", "/cszg/dataCalculateSort/getProWorkInfo", {
    proId,
  });
/** 查区域的录制路线（不带gps） */
export const queryAreaRecordingRoute = (params: {
  /** 区域id */
  id: string;
}) => http<any, QueryAreaRecordingRouteResponse>("post", "/cszg/proArea/common/queryAreaRecordingRoute", params);
/** 查车辆的录制路线 */
export const queryVehRecordingRoute = (
  params: {
    /** 车牌号 */
    id: string;
  },
  options: AxiosRequestConfig
) =>
  http<any, QueryVehRecordingRouteData>(
    "post",
    "/cszg/proArea/common/queryVehRecordingRoute",
    params,
    undefined,
    undefined,
    options
  );
/** 删除录制路线 */
export const deleteAreaRecordingRoute = (params: {
  /** 路线id */
  id: string;
}) => http<any, any>("post", "/cszg/proArea/deleteAreaRecordingRoute", params);
/** 根据车牌号查询车辆基础实时信息 */
export const queryVehRealTimeInfoByVehNo = (params: {
  /** 车牌号 */
  id?: string;
}) =>
  http<any, QueryVehRealTimeInfoByVehNoResponse>(
    "post",
    "/cszg/vehicleInfo/common/queryVehRealTimeInfoByVehNo",
    params
  );
/** 抓拍识别 */
export const cameraSnapAndUploadTemp = (
  params: {
    /** 车牌号 */
    vehNo?: string;
  },
  options?: AxiosRequestConfig
) =>
  http<any, CameraSnapAndUploadTempResponse[]>(
    "post",
    "/vehicle/control/common/cameraSnapAndUploadTemp",
    {
      vehNo: params.vehNo,
      topic: [
        "/center_back/rgb/image_raw",
        "/center_front/rgb/image_raw",
        "/center_left/rgb/image_raw",
        "/center_right/rgb/image_raw",
        "/surrounding_back/rgb/image_raw",
        "/surrounding_front/rgb/image_raw",
        "/surrounding_left/rgb/image_raw",
        "/surrounding_right/rgb/image_raw",
      ],
    },
    undefined,
    undefined,
    options
  );
/** 设置是否是星河专用车辆(即是否锁定HDMI) */
export const vehManageSetScheduleChange = (params: {
  /** 车牌号 */
  id: string;
  /** 是否锁定 0是，1否 */
  scheduleChange: number;
}) => http<any, QueryVehRealTimeInfoByVehNoResponse>("post", "/cszg/vehManage/setScheduleChange", params);
/** 针对门户进入云平台的token登录 */
export const loginByToken = (param: { token: string }) =>
  http<typeof param, LoginResponse>("post", "/loginByToken", param);

/** 查询车辆列表 */
export const getVehicleList = (params: {
  /** 项目id */
  id: string;
}) => http<any, VehListInEntResponse["velList"]>("post", "/cszg/proArea/common/proAreaVeh", params);
/** 更改录制路线状态 */
export const changeTheRecordingRouteStatus = (params: {
  /** 录制路线id */
  id: string;
  /** 路线状态，true开启，false关闭 */
  status: boolean;
}) =>
  http<any, any>(
    "post",
    "/cszg/proArea/changeTheRecordingRouteStatus?" +
      Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join("&")
  );
/** 更改录制路线类型 */
export const changeTheRecordingRouteType = (params: {
  /** 录制路线id */
  id: string;
  /** 路线类型，false业务类，true辅助类 */
  type: boolean;
}) =>
  http<any, any>(
    "post",
    "/cszg/proArea/changeTheRecordingRouteType?" +
      Object.entries(params)
        .map(([key, value]) => `${key}=${value}`)
        .join("&")
  );
/** 更新车辆三方开关 */
export const vehicleInfoUpdateSwitch = (params: {
  /** 车牌号 */
  id: string;
  /** 星河推送，true开启，false关闭 */
  xinheSwitch?: boolean;
  /** 抓拍垃圾开关 */
  snapSwitch?: boolean;
}) => http<any, any>("post", "/cszg/vehicleInfo/updateSwitch", params);
/** 车辆告警 查询列表 （运维平台） /cszg/vehicleWarn/common/list */
export const getVehicleAlarmList = (params: GetVehicleAlarmListRequest) =>
  http<GetVehicleAlarmListRequest, GetVehicleAlarmListResponse>("post", "/cszg/vehicleWarn/common/list", params);

/** 更新车辆警告状态批量 （运维平台）  */
export const updateVehicleAlarmStatus = (params: { idList: string[]; description: string }) =>
  http<any, any>("post", "/cszg/vehicleWarn/common/setStatusBatch", params);

/** 获取告警详情 （运维平台） */
export const getVehicleAlarmDetail = (params: { id: string; device_id: string }) =>
  http<any, GetVehicleAlarmDetailResponse>("post", "/cszg/vehicleWarn/common/detail", params);

/** 更新告警状态 （运维平台） */
export const updateAlarmStatus = (params: { id: string; isSolved: number; description: string }) =>
  http<any, any>("post", "/cszg/vehicleWarn/common/status", params);

/** 警告模块与名称关联（运维平台） */
export const getWarnNameList = (params: { remark1: string; warnModule: string; warnName: string }) =>
  http<any, any>("post", "/cszg/warnConfig/common/warnAssociation", params);

/** 查询车辆任务详情（运维平台） */
export const getVehicleTaskDetail = (params: { deviceId: string; page: number; limit: number; workStatus: string }) =>
  http<any, GetVehicleTaskDetailResponse>("post", "/cszg/vehCalculate/common/calculateList", params);

/** 获取智慧工单（运维平台）/cszg/vehiclePart/operationMaintenance/common/getSmartWorkOrder */
export const getSmartOrderList = (params: {
  deviceId: string;
  page: number;
  limit: number;
  /**
   * 事件类型 1:垃圾 6:管线 101:无人机 18：碰撞
   */
  eventType: number[];
  /** 清扫类型0:普通清扫,1:巡扫模式 */
  cleanMode?: string;
}) =>
  http<any, GetSmartOrderListResponse>("post", "/vehiclePart/operationMaintenance/common/getSmartWorkOrder", params);
/**智慧工单-人工识别(运维平台) */
export const upDateSmartOrderDetail = (params: { id: string; recognizeResult: number; isClean: number }) =>
  http<any, any>("post", "/vehiclePart/operationMaintenance/common/recognizeSmart", params);
/** 获取红绿灯列表（运维平台） */
export const getRedLightList = (params: { deviceId: string; page: number; limit: number }) =>
  http<any, GetRedLightListResponse>("post", "/vehiclePart/operationMaintenance/common/getTrafficLight", params);
/** 查询自定义区域（运维平台） */
export const queryCustomArea = (params: { vehNo: string }) =>
  http<QueryCustomAreaResponse>(
    "get",
    `/vehiclePart/vehicle/control/common/getProAreaCustomizePointByVehNo?vehNo=${params.vehNo}`
  );

/** 红绿灯校验 */
export const updateRedLightCheck = (params: TrafficLightExamineParams) =>
  http<any, any>("post", "/vehicle/control/common/trafficLightExamine", params);

/** 维护中 (运维平台) */
export const getIncharge = (params: { vehNo: string }) =>
  http<any, any>("get", `/vehiclePart/vehicle/control/common/inCharge?vehNo=${params.vehNo}`);

/** 获取车辆-高级配置信息 （运维平台） */
export const getVehicleAdvancedConfig = (params: { deviceId: string }) =>
  http<any, GetVehicleAdvancedConfigResponse>(
    "post",
    "/vehiclePart/operationMaintenance/common/getVehConfiguration",
    params
  );

/** 设置车辆-高级配置信息 （运维平台） */
export const setVehicleAdvancedConfig = (params: SetVehicleAdvancedConfigParams) =>
  http<any, any>("post", "/vehiclePart/operationMaintenance/common/updateVehConfiguration", params);

/** 获取无人机token */
export const getUavToken = () => http<any, any>("get", "/vehiclePart/uav/common/queryLoginAndUserInfo");

/** 获取吊舱信息 */
export const getUavCabinInfo = () => http<any, any>("post", "/vehiclePart/uav/common/queryUserRightGimbal");
/** 权限内的机场及无人机 */
export const getUavAirportInfo = () => http<any, any>("get", "/vehiclePart/uav/common/queryUserRightDevice");
/** 一键起飞 */
export const takeoff = (params: { nodeId: string; airportId: string; droneId: string }) =>
  http<any, any>(
    "get",
    `/vehiclePart/uav/common/oneKeyTakeoff?nodeId=${params.nodeId}&airportId=${params.airportId}&droneId=${params.droneId}`
  );

/** 获取航线列表 */
export const getUavRouteList = () =>
  http<any, uavRouteListResponse>("get", "/vehiclePart/uav/common/queryUserRightMission");
/** 获取执行ID */
export const getUavExecuteId = (params: { droneId: string }) =>
  http<any, any>("get", `/vehiclePart/uav/common/getExecutionId?droneId=${params.droneId}`);
/** 执行任务 */
export const executeTask = (params: {
  nodeId: string;
  airportId: string;
  missionId: string;
  executionId: string;
  skipFlightCheck: boolean;
}) => http<any, any>("post", `/vehiclePart/uav/common/startMission?nodeId=${params.nodeId}`, params);

/**无人机悬停 */
export const feachUavHover = (params: { nodeId: string; droneId: string }) =>
  http<any, any>("get", `/vehiclePart/uav/common/hold?nodeId=${params.nodeId}&droneId=${params.droneId}`);
/**无人机继续 */
export const feachUavContinue = (params: { nodeId: string; droneId: string }) =>
  http<any, any>("get", `/vehiclePart/uav/common/continueMission?nodeId=${params.nodeId}&droneId=${params.droneId}`);

/**无人机返航 */
export const feachUavReturn = (params: { nodeId: string; droneId: string }) =>
  http<any, any>("get", `/vehiclePart/uav/common/returnToHome?nodeId=${params.nodeId}&droneId=${params.droneId}`);

/**红绿灯-预告信息 */
export const getVehPreviewInformation = (params: { deviceId: string }) =>
  http<any, GetRedLightListResponse>(
    "post",
    "/vehiclePart/operationMaintenance/common/getVehPreviewInformation",
    params
  );

/** 红绿灯-全车即将到达的红绿灯预告信息 */
export const getAllVehFirstPreviewInformation = (params: { deviceId: string }) =>
  http<any, GetRedLightListResponse>(
    "post",
    "/vehiclePart/operationMaintenance/common/getAllVehFirstPreviewInformation",
    params
  );

/** 红绿灯-急停操作 */
export const getRedLightStop = (params: { vehNo: string; emergencyStopCmd: string }) =>
  http<any, any>("post", "/vehicle/control/common/emergencyStop", params);

/** 获取红绿灯列表 */
export const getTrafficLightList = (params: { vehNo: string }) =>
  http<any, GetTrafficLightListResponse>("get", `/cszg/proArea/common/queryVehicleTrafficLight?vehNo=${params.vehNo}`);
/** 释放车位 */
export const releaseParkingSpace = (params: {
  /** 车位id */
  proAreaId: string;
  /** 车位号 */
  parkingNo: string;
}) =>
  http<any, any>(
    "get",
    `/vehiclePart/vehicle/cache/common/releaseStationOccupy?proAreaId=${params.proAreaId}&parkingNo=${params.parkingNo}`
  );

/** 管线/碰撞人工审核 */
export const getPipelineCollisionAudit = (params: {
  /**
   * 数据上传时间
   */
  dataTime: string;
  /**
   * 抓拍表数据id
   */
  eventId: string;
  /**
   * 0:误报，1：确认确实有管线缠绕
   */
  navigationAdvice: number;
  /**
   * 图片URL
   */
  picUrl: string;
  /**
   * 车牌号
   */
  vehNo: string;
}) => http<any, any>("post", `/vehiclePart/operationMaintenance/common/pipeLineExamine`, params);

/** 接触硬急停 */
export const getReleaseKeyEmergencyStop = (params: { vehNo: string; keyEmergencyStopClearCmd: number }) =>
  http<any, any>("post", `/vehicle/control/common/releaseKeyEmergencyStop`, params);

/** 市政-点位列表 */
export const getMunicipalFacilitiesList = (params: { areaId: string }) =>
  http<any, any>("post", `/sys/sysAreaCapturePoints/common/list`, params);
/** 市政-移除市政点位 */
export const removeMunicipalFacilities = (params: { areaId: string; id: string }) =>
  http<any, any>("post", `/sys/sysAreaCapturePoints/common/removePoints`, params);

/** 查询视频流地址 */
export const getVideoStreamAddress = (params: { vehNo: string }) =>
  http<any, { cameraId: string; flvPath: string; rtmpPath: string }[]>(
    "post",
    `/cszg/vehicleOnTime/camera/video?vehNo=${params.vehNo}`
  );

/** 查询车辆红绿灯通过时间配置 */
export const queryPassTimeLimitOfTrafficLight = (params: { vehNo: string; trafficLightId: string }) =>
  http<any, any>("post", `/cszg/proArea/common/queryPassTimeLimitOfTrafficLight`, params);
/**获取市政抓拍图片列表 */
export const getMunicipalFacilities = (params: { deviceId: string; page: number; limit: number }) =>
  http<any, GetSmartOrderListResponse[]>(
    "post",
    "/vehiclePart/operationMaintenance/common/getMunicipalFacilities",
    params
  );
