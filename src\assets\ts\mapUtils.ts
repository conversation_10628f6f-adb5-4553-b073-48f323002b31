/* eslint-disable no-loss-of-precision */
/* eslint-disable @typescript-eslint/no-loss-of-precision */

//#region 坐标转换
//定义一些常量
const x_PI = (3.14159265358979324 * 3000.0) / 180.0;
const PI = 3.1415926535897932384626;
const a = 6378245.0;
const ee = 0.00669342162296594323;

/**
 * 百度坐标系 (BD-09) 与 火星坐标系 (GCJ-02)的转换
 * 即 百度 转 谷歌、高德
 * @param bd_lon
 * @param bd_lat
 * @returns {number[]}
 */
export function BD09ToGCJ02(bd_lon: number, bd_lat: number): number[] {
  const x_pi = (3.14159265358979324 * 3000.0) / 180.0;
  const x = bd_lon - 0.0065;
  const y = bd_lat - 0.006;
  const z = Math.sqrt(x * x + y * y) - 0.00002 * Math.sin(y * x_pi);
  const theta = Math.atan2(y, x) - 0.000003 * Math.cos(x * x_pi);
  const gg_lng = z * Math.cos(theta);
  const gg_lat = z * Math.sin(theta);
  return [gg_lng, gg_lat];
}

/**
 * 火星坐标系 (GCJ-02) 与百度坐标系 (BD-09) 的转换
 * 即谷歌、高德 转 百度
 * @param lng
 * @param lat
 * @returns {number[]}
 */
export function GCJ02ToBD09(lng: number, lat: number): number[] {
  const z = Math.sqrt(lng * lng + lat * lat) + 0.00002 * Math.sin(lat * x_PI);
  const theta = Math.atan2(lat, lng) + 0.000003 * Math.cos(lng * x_PI);
  const bd_lng = z * Math.cos(theta) + 0.0065;
  const bd_lat = z * Math.sin(theta) + 0.006;
  return [bd_lng, bd_lat];
}

/**
 * WGS84转GCj02
 * @param lng
 * @param lat
 * @returns {number[]}
 */
export function WGS84ToGCJ02(lng: number, lat: number): number[] {
  if (outOfChina(lng, lat)) {
    return [lng, lat];
  } else {
    let dlat = transformlat(lng - 105.0, lat - 35.0);
    let dlng = transformlng(lng - 105.0, lat - 35.0);
    const radlat = (lat / 180.0) * PI;
    let magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
    const mglat = lat + dlat;
    const mglng = lng + dlng;
    return [mglng, mglat];
  }
}

/**
 * GCJ02 转换为 WGS84
 * @param lng
 * @param lat
 * @returns {number[]}
 */
export function GCJ02ToWGS84(lng: number, lat: number): number[] {
  if (outOfChina(lng, lat)) {
    return [lng, lat];
  } else {
    let dlat = transformlat(lng - 105.0, lat - 35.0);
    let dlng = transformlng(lng - 105.0, lat - 35.0);
    const radlat = (lat / 180.0) * PI;
    let magic = Math.sin(radlat);
    magic = 1 - ee * magic * magic;
    const sqrtmagic = Math.sqrt(magic);
    dlat = (dlat * 180.0) / (((a * (1 - ee)) / (magic * sqrtmagic)) * PI);
    dlng = (dlng * 180.0) / ((a / sqrtmagic) * Math.cos(radlat) * PI);
    const mglat = lat + dlat;
    const mglng = lng + dlng;
    return [lng * 2 - mglng, lat * 2 - mglat];
  }
}

function transformlat(lng: number, lat: number) {
  let ret = -100.0 + 2.0 * lng + 3.0 * lat + 0.2 * lat * lat + 0.1 * lng * lat + 0.2 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lat * PI) + 40.0 * Math.sin((lat / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((160.0 * Math.sin((lat / 12.0) * PI) + 320 * Math.sin((lat * PI) / 30.0)) * 2.0) / 3.0;
  return ret;
}

function transformlng(lng: number, lat: number) {
  let ret = 300.0 + lng + 2.0 * lat + 0.1 * lng * lng + 0.1 * lng * lat + 0.1 * Math.sqrt(Math.abs(lng));
  ret += ((20.0 * Math.sin(6.0 * lng * PI) + 20.0 * Math.sin(2.0 * lng * PI)) * 2.0) / 3.0;
  ret += ((20.0 * Math.sin(lng * PI) + 40.0 * Math.sin((lng / 3.0) * PI)) * 2.0) / 3.0;
  ret += ((150.0 * Math.sin((lng / 12.0) * PI) + 300.0 * Math.sin((lng / 30.0) * PI)) * 2.0) / 3.0;
  return ret;
}

/**
 * 判断是否在国内，不在国内则不做偏移
 * @param lng
 * @param lat
 * @returns {boolean}
 */
function outOfChina(lng: number, lat: number): boolean {
  return lng < 72.004 || lng > 137.8347 || lat < 0.8293 || lat > 55.8271 || false;
}
//#endregion

//#region 高精地图转换
/** 将角度转换为弧度 */
function toRadians(degrees: number): number {
  return (degrees * Math.PI) / 180;
}
/** 将弧度转换为角度 */
function toDegrees(radians: number): number {
  return (radians * 180) / Math.PI;
}
/** 定义 3D 点类 */
export class Point3D {
  x: number;
  y: number;
  z: number;
  constructor(x: number = 0, y: number = 0, z: number = 0) {
    this.x = x;
    this.y = y;
    this.z = z;
  }
}
/** 定义 LLH(WGS84) 点类（纬度、经度、高度）*/
export class PointLLH {
  lat: number;
  lon: number;
  height: number;
  constructor(lon: number = 0, lat: number = 0, height: number = 0) {
    this.lat = lat;
    this.lon = lon;
    this.height = height;
  }
}
/** 矩阵乘法 */
function matrixMultiply(A: number[][], B: number[][]): number[][] {
  const result: number[][] = [];
  for (let i = 0; i < A.length; i++) {
    result[i] = [];
    for (let j = 0; j < B[0].length; j++) {
      let sum = 0;
      for (let k = 0; k < B.length; k++) {
        sum += A[i][k] * B[k][j];
      }
      result[i][j] = sum;
    }
  }
  return result;
}
/** 矩阵转置 */
function matrixTranspose(matrix: number[][]): number[][] {
  return matrix[0].map((_, i) => matrix.map((row) => row[i]));
}

/**
 * @desc 将 LLH 坐标（WGS坐标）转换为 XYZ 坐标（笛卡尔坐标）
 * @param {PointLLH} anchorPoint 原点坐标（WGS84坐标）
 * @param {PointLLH} vehicleGlobalCoordinates 车辆坐标（WGS84坐标）
 */
export function LLHToXYZ(anchorPoint: PointLLH, vehicleGlobalCoordinates: PointLLH): Point3D {
  const toRad = (v: PointLLH): PointLLH => ({
    ...v,
    lat: toRadians(v.lat),
    lon: toRadians(v.lon),
  });

  const ap = toRad(anchorPoint);
  const vc = toRad(vehicleGlobalCoordinates);

  const f = 1 / 298.257;
  const e = Math.sqrt(2 * f - f * f);
  const a = 6378137.0;

  const N0 = a / Math.sqrt(1 - e * e * Math.sin(ap.lat) ** 2);
  const N1 = a / Math.sqrt(1 - e * e * Math.sin(vc.lat) ** 2);

  const R = [
    [-Math.sin(ap.lon), Math.cos(ap.lon), 0],
    [-Math.sin(ap.lat) * Math.cos(ap.lon), -Math.sin(ap.lat) * Math.sin(ap.lon), Math.cos(ap.lat)],
    [Math.cos(ap.lat) * Math.cos(ap.lon), Math.cos(ap.lat) * Math.sin(ap.lon), Math.sin(ap.lat)],
  ];

  const M = [
    [(N1 + vc.height) * Math.cos(vc.lat) * Math.cos(vc.lon) - (N0 + ap.height) * Math.cos(ap.lat) * Math.cos(ap.lon)],
    [(N1 + vc.height) * Math.cos(vc.lat) * Math.sin(vc.lon) - (N0 + ap.height) * Math.cos(ap.lat) * Math.sin(ap.lon)],
    [(N1 * (1 - e * e) + vc.height) * Math.sin(vc.lat) - (N0 * (1 - e * e) + ap.height) * Math.sin(ap.lat)],
  ];

  const X = matrixMultiply(R, M);

  return new Point3D(X[0][0], X[1][0], X[2][0]);
}

/**
 * @desc 将 XYZ 坐标（笛卡尔坐标）转换为 LLH 坐标 (WGS84坐标)
 * @param {PointLLH} anchorPoint 原点坐标 （WGS84坐标）
 * @param {Point3D} pointEnu xyz坐标（笛卡尔坐标）
 */
export function XYZToLLH(anchorPoint: PointLLH, pointEnu: Point3D): PointLLH {
  const ap = {
    lat: toRadians(anchorPoint.lat),
    lon: toRadians(anchorPoint.lon),
    height: anchorPoint.height,
  };

  const f = 1 / 298.257;
  const e = Math.sqrt(2 * f - f * f);
  const a = 6378137.0;
  const N1 = a / Math.sqrt(1 - e * e * Math.sin(ap.lat) ** 2);

  const R = [
    [-Math.sin(ap.lon), Math.cos(ap.lon), 0],
    [-Math.sin(ap.lat) * Math.cos(ap.lon), -Math.sin(ap.lat) * Math.sin(ap.lon), Math.cos(ap.lat)],
    [Math.cos(ap.lat) * Math.cos(ap.lon), Math.cos(ap.lat) * Math.sin(ap.lon), Math.sin(ap.lat)],
  ];

  const X = [
    [(N1 + ap.height) * Math.cos(ap.lat) * Math.cos(ap.lon)],
    [(N1 + ap.height) * Math.cos(ap.lat) * Math.sin(ap.lon)],
    [(N1 * (1 - e * e) + ap.height) * Math.sin(ap.lat)],
  ];

  const M = [[pointEnu.x], [pointEnu.y], [pointEnu.z]];

  const R_T = matrixTranspose(R);
  const temp = matrixMultiply(R_T, M);
  const M_new = temp.map((row, i) => [row[0] + X[i][0]]);

  const pointLLH = new PointLLH();
  pointLLH.lon = Math.atan2(M_new[1][0], M_new[0][0]);

  const temp1 = Math.atan(M_new[2][0] / Math.hypot(M_new[0][0], M_new[1][0]));
  let N = a / Math.sqrt(1 - e * e * Math.sin(temp1) ** 2);
  let temp2 = Math.atan((M_new[2][0] * (1 + e * e)) / Math.hypot(M_new[0][0], M_new[1][0]));
  let temp3 = 0;

  for (let i = 0; i < 100; i++) {
    N = a / Math.sqrt(1 - e * e * Math.sin(temp2) ** 2);
    temp3 = Math.atan(Math.tan(temp1) + (N * e * e * Math.sin(temp2)) / Math.hypot(M_new[0][0], M_new[1][0]));
    if (Math.abs(temp2 - temp3) < 1e-16) break;
    if (i === 99) console.error("ERROR: OUT OF ITER!");
    temp2 = temp3;
  }

  pointLLH.lat = toDegrees(temp3);
  pointLLH.lon = toDegrees(pointLLH.lon);
  pointLLH.height = Math.hypot(M_new[0][0], M_new[1][0]) / Math.cos(temp3) - N;

  return pointLLH;
}

/**
 * @desc 笛卡尔坐标批量转换为 GCJ02 坐标
 * @param {number[][]} cartesians 笛卡尔坐标 `[x, y, z]`
 * @param {number[]} origin 原点坐标 `[lng, lat, h]`
 */
export function betchXYZToGCJ02(cartesians: number[][], origin: number[]): number[][] {
  return cartesians.map(([x, y]) => {
    const lnglat = XYZToLLH(new PointLLH(origin[0], origin[1], 0), new Point3D(x, y, 0));
    return WGS84ToGCJ02(lnglat.lon, lnglat.lat);
  });
}

/**
 * @desc GCJ02 坐标批量转换为笛卡尔坐标
 * @param {number[][]} points GCJ02 坐标 `[lng, lat]`
 * @param {number[]} origin 原点坐标 `[lng, lat, h]`
 */
export function betchGCJ02ToXYZ(points: number[][], origin: number[]): number[][] {
  return points.map(([_lng, _lat]) => {
    const [lng, lat] = GCJ02ToWGS84(_lng, _lat);
    const { x, y, z } = LLHToXYZ(new PointLLH(origin[0], origin[1], 0), new PointLLH(lng, lat, 0));
    return [x, y, z];
  });
}

//test
// const clone = (e: any) => JSON.parse(JSON.stringify(e))
// const _origin = new PointLLH(103.81113998581584, 30.49613164430325, 0)
// const _point = new PointLLH(103.80854728676681, 30.49611261627401, 0)
// const point3D = clone(LLHToXYZ(clone(_origin), clone(_point)))
// const new_point = clone(XYZToLLH(clone(_origin), clone(point3D)))
// console.log({ _origin, _point, point3D, new_point })
//#endregion

/** 判断坐标数组是否存在相等 */
export function coordsIsHasEqual(coords: number[][]) {
  const sorted = coords.toSorted((a, b) => a[0] - b[0]);
  for (let i = 0; i < sorted.length - 1; i++) {
    if (coords[i][0] === coords[i + 1][0] && coords[i][1] === coords[i + 1][1]) {
      return true;
    }
  }
  return false;
}
