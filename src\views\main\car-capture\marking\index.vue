<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="手动上传"
    style="margin-top: 5vh"
    width="80%"
    @close="cancel"
  >
    <div class="marking-container">
      <div class="marking-container-left">
        <div class="marking-container-left-header">
          <el-upload
            :before-upload="handleTxtUpload"
            accept=".txt"
            show-file-list="false"
          >
            <el-button type="primary">上传txt文件</el-button>
          </el-upload>
        </div>
        <el-scrollbar
          height="69vh"
          wrap-class="marking-container-left-list"
        >
          <div
            class="marking-container-left-list-item"
            :class="{ 'is-active': currentItem?.id === item.id }"
            v-for="item in imgList"
            :key="item.id"
            @click="handleItemClick(item)"
          >
            <img
              class="item-img"
              :src="item.picUrl"
              alt=""
            />
            <div class="item-name">{{ item.id }}</div>
          </div>
        </el-scrollbar>
      </div>
      <div class="marking-container-right">
        <div class="marking-container-right-header">
          <div class="form-item">
            <label>类型</label>
            <el-select
              v-model="captureType.active"
              filterable
              placeholder="请选择类型"
              style="width: 180px"
              :disabled="!currentItem"
              @change="handleTypeChange"
            >
              <el-option
                v-for="item in carCaptureTypeOpts"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </div>
          <x-button
            :text="`上传 ${uploading ? loadPercent : ''}`"
            :loading="uploading"
            :disabled="!currentItem"
            @click="upload"
          />
        </div>
        <div
          ref="rightCanvasBox"
          class="marking-container-right-canvas"
          style="height: 70vh"
        >
          <canvas
            ref="canvasRef"
            :class="['car-capture-right-canvas', { 'is-drawing': currentItem }]"
            @mousedown="onMouseDown"
            @mousemove="onMouseMove"
            @mouseup="onMouseUp"
          />
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import axios from "axios";
import XMessage from "@/components/x-message";
import type { CameraSnapAndUploadTempResponse } from "@/services/type";
import { carCaptureTypeOpts } from "@/services/wsconfig";
import { ref, reactive, toRefs, onMounted, nextTick, watch } from "vue";
import { getLocalStorage } from "@/assets/ts/storage";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
});
const emit = defineEmits(["update:modelValue"]);
const labelPos = ref({ x: 0, y: 0 });
const imgList = ref<CameraSnapAndUploadTempResponse[]>([]);
const uploading = ref<boolean>(false);

const captureType = reactive({
  active: 1,
  options: carCaptureTypeOpts,
});
const loadPercent = ref<number>(0);
const currentItem = ref<CameraSnapAndUploadTempResponse>();
/** Canvas 相关 */
const canvasRef = ref<HTMLCanvasElement>();
let canvasCtx: CanvasRenderingContext2D;
let imgDiv: HTMLImageElement;
const drawPattern = ref<"rect">();
let dragging = false;
let dragOffset = { x: 0, y: 0 };
const isDrawing = ref<boolean>(false);
const drawData = {
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
};
const rightCanvasBox = ref<HTMLDivElement>();

onMounted(() => {});

const handleItemClick = (item: CameraSnapAndUploadTempResponse) => {
  currentItem.value = item;
  reset();
  imgDiv = new Image();
  imgDiv.crossOrigin = "anonymous";
  imgDiv.onload = () => {
    centerLabel(); // 居中
    drawAll(); // 绘制图片和标签
  };
  imgDiv.src = item.picUrl as string;
  drawPattern.value = "rect";
};

const drawAll = () => {
  drawImage(); // 你的图片绘制逻辑
  drawLabel(); // 下面实现
};
const centerLabel = () => {
  const box = rightCanvasBox.value;
  if (!box) return;
  labelPos.value.x = box.clientWidth / 2;
  labelPos.value.y = box.clientHeight / 2;
};

const isInLabelArea = (x: number, y: number) => {
  // 判断鼠标是否在标签区域
  const typeObj = captureType.options.find((opt) => opt.value === captureType.active);
  if (!typeObj) return false;
  const label = typeObj.label;
  const ctx = canvasCtx;
  ctx.font = "14px 'Microsoft YaHei', Arial";
  const textMetrics = ctx.measureText(label);
  const paddingX = 8;
  const bgWidth = textMetrics.width + paddingX * 2;
  const bgHeight = 22;
  const bgX = labelPos.value.x - bgWidth / 2;
  const bgY = labelPos.value.y - bgHeight / 2;
  return x >= bgX && x <= bgX + bgWidth && y >= bgY && y <= bgY + bgHeight;
};

const onMouseDown = (e: MouseEvent) => {
  const rect = canvasRef.value?.getBoundingClientRect()!;
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  if (isInLabelArea(x, y)) {
    dragging = true;
    dragOffset.x = x - labelPos.value.x;
    dragOffset.y = y - labelPos.value.y;
  }
};
const onMouseMove = (e: MouseEvent) => {
  if (!dragging) return;
  const rect = canvasRef.value?.getBoundingClientRect()!;
  const x = e.clientX - rect.left;
  const y = e.clientY - rect.top;
  labelPos.value.x = x - dragOffset.x;
  labelPos.value.y = y - dragOffset.y;
  drawAll();
};
function onMouseUp() {
  dragging = false;
}

const drawLabel = () => {
  const box = rightCanvasBox.value;
  if (!box || !canvasCtx) return;
  const typeObj = captureType.options.find((opt) => opt.value === captureType.active);
  if (!typeObj) return;
  const label = typeObj.label;
  const x = labelPos.value.x;
  const y = labelPos.value.y;

  canvasCtx.font = "14px 'Microsoft YaHei', Arial";
  const textMetrics = canvasCtx.measureText(label);
  const paddingX = 8;
  const paddingY = 4;
  const bgWidth = textMetrics.width + paddingX * 2;
  const bgHeight = 22;
  const bgX = x - bgWidth / 2;
  const bgY = y - bgHeight / 2;
  const radius = 0;

  canvasCtx.beginPath();
  canvasCtx.moveTo(bgX + radius, bgY);
  canvasCtx.lineTo(bgX + bgWidth - radius, bgY);
  canvasCtx.quadraticCurveTo(bgX + bgWidth, bgY, bgX + bgWidth, bgY + radius);
  canvasCtx.lineTo(bgX + bgWidth, bgY + bgHeight - radius);
  canvasCtx.quadraticCurveTo(bgX + bgWidth, bgY + bgHeight, bgX + bgWidth - radius, bgY + bgHeight);
  canvasCtx.lineTo(bgX + radius, bgY + bgHeight);
  canvasCtx.quadraticCurveTo(bgX, bgY + bgHeight, bgX, bgY + bgHeight - radius);
  canvasCtx.lineTo(bgX, bgY + radius);
  canvasCtx.quadraticCurveTo(bgX, bgY, bgX + radius, bgY);
  canvasCtx.closePath();
  canvasCtx.fillStyle = "#ef822f";
  canvasCtx.fill();

  // 绘制文字
  canvasCtx.fillStyle = "#fff";
  canvasCtx.textBaseline = "alphabetic";

  // 计算文字实际高度
  const ascent = textMetrics.actualBoundingBoxAscent || 12; // 兼容性处理
  const descent = textMetrics.actualBoundingBoxDescent || 4;
  const textHeight = ascent + descent;

  // 让文字视觉上垂直居中
  const textY = bgY + (bgHeight + textHeight) / 2 - descent;

  canvasCtx.fillText(label, bgX + paddingX, textY);
};
const handleTxtUpload = (file: File) => {
  const reader = new FileReader();
  reader.onload = (e) => {
    // 假设每行一个图片链接
    const content = e.target?.result as string;
    // 按行分割，去除空行
    imgList.value = content
      .split(/\r?\n/)
      .map((line) => line.trim())
      .filter((line) => line.length > 0)
      .map((url) => ({
        id: url.match(/\/([^\\/]+)\.(png|jpg)$/)?.[1] || "", // 用正则提取文件名作为id
        picUrl: url,
      }));

    console.log(
      {
        content,
        list: imgList.value,
      },
      "shangchuanhou"
    );
  };
  reader.readAsText(file);
  // 阻止 el-upload 自动上传
  return false;
};
const handleTypeChange = (val: number) => {
  drawAll();
};
const chioseFile = () => {
  console.log("%c chioseFile:", "color:blue;font-weight:bold;");
};
const upload = () => {
  if (!captureType.active) return XMessage("error", "请选择类型");
  if (!currentItem.value?.id) return XMessage("error", "请选择图片");
  canvasRef.value!.toBlob(
    (blob) => {
      if (blob) {
        console.log("%c blob:", "color:blue;font-weight:bold;", { blob });
        uploading.value = true;
        axios
          .post(import.meta.env.VITE_BASE_URL + "/vehicle/control/common/replaceCOSFile", blob, {
            headers: { "Content-Type": "multipart/form-data", token: getLocalStorage("token") },
            transformRequest: [
              function (file) {
                const formData = new FormData();
                formData.append("file", file);
                formData.append("picUrl", String(currentItem.value?.picUrl));
                return formData;
              },
            ],
            onUploadProgress: (progressEvent) => {
              console.log("%c onUploadProgress:", "color:blue;font-weight:bold;", progressEvent);
              loadPercent.value = progressEvent.total ? ((progressEvent.loaded / progressEvent.total) * 100) | 0 : 0;
            },
          })
          .then((res) => {
            uploading.value = false;
            reset();
            XMessage("success", "上传成功");
            currentItem.value = undefined;
          });
      }
    },
    "image/jpeg",
    0.95
  );
};

const cancel = () => {
  emit("update:modelValue", false);
  reset();
  currentItem.value = undefined;
  drawPattern.value = undefined;
  imgList.value = [];
};
const drawImage = () => {
  const box = rightCanvasBox.value;
  const canvas = canvasRef.value;
  if (!box || !canvas || !imgDiv) return;
  if (!imgDiv.complete || imgDiv.naturalWidth === 0) return; // 图片未加载好
  // 让canvas宽高等于容器
  const width = canvas.width;
  const height = canvas.height;
  // canvas.width = width;
  // canvas.height = height;

  // 让图片等比缩放居中显示在canvas内
  const imgWidth = imgDiv.width;
  const imgHeight = imgDiv.height;
  const scale = Math.min(width / imgWidth, height / imgHeight);

  const drawWidth = imgWidth * scale;
  const drawHeight = imgHeight * scale;
  const offsetX = (width - drawWidth) / 2;
  const offsetY = (height - drawHeight) / 2;

  canvasCtx.clearRect(0, 0, width, height);
  canvasCtx.drawImage(imgDiv, offsetX, offsetY, canvas.width, canvas.height);
};
const clearCanvas = () => {
  if (!canvasCtx || !canvasRef.value) return; // 防止未初始化时报错
  canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
};
/** 重置 */
const reset = () => {
  clearCanvas();
  // drawImage();
};

const resizeCanvasToBox = () => {
  // nextTick 保证 DOM 已经渲染
  nextTick(() => {
    const box = rightCanvasBox.value;
    const canvas = canvasRef.value;
    if (box && canvas) {
      const scale = box.clientHeight / 1080;
      console.log(scale, "ssssss");

      const width = 1920 * scale;
      const height = 1080 * scale;
      // 设置canvas的宽高属性（注意不是style，是属性！）
      canvas.width = width;
      canvas.height = height;
      // 如果需要撑满样式，也可以加
      // canvas.style.width = "100%";
      // canvas.style.height = "100%";
    }
  });
};
watch(
  () => props.modelValue,
  (val) => {
    if (val) {
      nextTick(() => {
        resizeCanvasToBox();
        canvasCtx = canvasRef.value!.getContext("2d")!;
        imgDiv = new Image();
        imgDiv.crossOrigin = "anonymous";
        imgDiv.src = "";
        imgDiv.onload = drawImage;
      });
    }
  }
);
</script>
<style scoped lang="scss">
.marking-container {
  width: 100%;
  height: 100%;
  display: flex;
  &-left {
    width: 220px;
    height: 100%;
    overflow: hidden;
    &-list {
      &-item {
        display: flex;
        align-items: center;
        column-gap: 5px;
        border-radius: 5px;
        cursor: pointer;
        padding: 5px;
        .item-img {
          width: 50px;
          height: 50px;
          object-fit: contain;
        }
        .item-name {
          width: 150px;
          font-size: 12px;
          color: #606266;
          text-overflow: ellipsis;
          overflow: hidden;
          white-space: nowrap;
        }
        &:hover {
          background-color: #f5f5f5;
        }
        &.is-active {
          background-color: #5964fb;

          .item-name {
            color: #fff !important;
          }
        }
      }
    }
  }
  &-right {
    flex: 1;
    &-header {
      display: flex;
      align-items: center;
      margin-bottom: 20px;
    }
  }
}
</style>
