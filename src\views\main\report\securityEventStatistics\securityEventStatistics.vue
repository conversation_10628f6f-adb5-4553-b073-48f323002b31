<template>
  <section class="security-event" ref="securityEventRef">
    <div class="security-event-top">
      <div class="top-title">{{ $t("securityEventStatistics") }}</div>
    </div>
    <div class="security-event-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.deviceNo"
          :options="formOptions.device"
          :popupContainer="securityEventRef"
          :placeholder="$t('vehNumber')"
          showSearch
          style="width: 240px; margin-right: 16px"
        />
        <DateRangePicker
          v-model:value="searchForm.opDate"
          :popupContainer="securityEventRef"
          :placeholder="[$t('startTime'), $t('endTime')]"
          style="width: 320px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.incidentType"
          :options="formOptions.incidentType"
          :popupContainer="securityEventRef"
          style="width: 140px"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:security:list')"
          @click="searchList"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="security-event-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 所属区域 -->
        <template #areaName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-area-popover"
            :container="securityEventRef"
          >
            {{ record.areaName || "--" }}
            <template #content>
              <div class="security-event-table-area-hover-popover">
                {{ record.areaName || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 所属项目 -->
        <template #projectName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-project-popover"
            :container="securityEventRef"
          >
            {{ record.projectName || "--" }}
            <template #content>
              <div class="security-event-table-project-hover-popover">
                {{ record.projectName || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 位置 -->
        <template #incidentLocation="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-address-popover"
            :container="securityEventRef"
          >
            {{ record.incidentLocation || "--" }}
            <template #content>
              <div class="security-event-table-address-hover-popover">
                {{ record.incidentLocation || "--" }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 图像 -->
        <template #image="{ record, recordIndex }">
          <img
            v-if="record.picAddress"
            :src="record.picAddress"
            alt=""
            :style="{
              height: record.picError ? '24px' : '35px',
              cursor: record.picError ? 'default' : 'pointer',
            }"
            @error="handleImgError(recordIndex)"
            @click="!record.picError && showImage(record)"
          />
        </template>
      </x-table>
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
import type { PageSizeType } from "@/components/types";
import { getSecurityEventList, getVehList } from "@/services/api";
import { DateRangePicker } from "@/components/x-date-picker";
import { useMainStore } from "@/stores/main";
import { securityEventType } from "@/assets/ts/config";
import xTable from "@/components/x-table.vue";
import xImage from "@/components/x-image.vue";
import xPopover from "@/components/x-popover.vue";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";

const $t = i18nSimpleKey("securityEventStatistics");

const securityEventRef = ref<any>();

const {
  userInfo: { permitList },
} = useMainStore();

// 数据支持
const formOptions = reactive({
  incidentType: [{ value: "", label: $t("full") }, ...securityEventType],
  device: [] as { label: string; value: string }[],
});

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "deviceNo",
      title: $t("deviceId"),
      width: "60",
    },
    {
      key: "incidentTypeText",
      title: $t("type"),
      width: "60",
    },
    {
      key: "camera",
      title: $t("camera"),
      width: "60",
    },
    {
      key: "createTime",
      title: $t("uploadTime"),
      width: "100",
    },
    {
      key: "areaName",
      title: $t("ofArea"),
      width: "120",
      slots: "areaName",
    },
    {
      key: "projectName",
      title: $t("ofProject"),
      width: "120",
      slots: "projectName",
    },
    {
      key: "incidentLocation",
      title: $t("location"),
      width: "170",
      slots: "incidentLocation",
    },
    {
      key: "image",
      title: $t("image"),
      width: "60",
      slots: "image",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

// 重置
const resetSearchForm = () => {
  searchForm.deviceNo = "";
  searchForm.opDate = ["", ""];
  searchForm.incidentType = "";
  table.pagination["current"] = 1;
  searchList();
};

// 查询
const searchForm = reactive({
  deviceNo: "",
  opDate: ["", ""],
  incidentType: "",
});
const searchList = async () => {
  table.loading = true;
  const { totalCount, list } = await getSecurityEventList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    deviceNo: searchForm.deviceNo,
    dateStart: searchForm.opDate[0] || "",
    dateEnd: searchForm.opDate[1] || "",
    incidentType: searchForm.incidentType,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            ...item,
            orderNumber: (index + 1).toString().padStart(3, "0"),
            image: ref(false),
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  // 图片错误处理重置
  table.dataSource.forEach((item) => {
    item.picAddress = "";
    item.picError = false;
  });
  searchList();
};

// 查看事件图片
const image = reactive({
  show: false,
  url: "",
});
const showImage = (info: any) => {
  image.show = true;
  image.url = info.picAddress || "";
};

// 图片容错处理
const handleImgError = (index: number) => {
  table.dataSource[index].picAddress = new URL(
    "@/assets/images/img_error.png",
    import.meta.url
  ).href;
  table.dataSource[index].picError = true;
};

(async () => {
  searchList();
  formOptions.device = (await getVehList()).map((item) => ({
    label: item!,
    value: item!,
  }));
})();
</script>

<style lang="scss">
.security-event-table-area-hover-popover,
.security-event-table-project-hover-popover,
.security-event-table-address-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
</style>
<style lang="scss" scoped>
.security-event {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-area-popover,
    .table-project-popover,
    .table-address-popover {
      @include ell;
    }
  }
}
</style>
