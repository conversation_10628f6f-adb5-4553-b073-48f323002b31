<template>
  <section
    :class="[
      'info-window',
      {
        disable: !carStatus.online,
        bottomOpera: showTaskStatus || showStopCharge,
      },
    ]"
  >
    <div
      class="info-window-loading"
      v-if="isEcar && carStatus.loading"
    >
      <img src="@/assets/images/loading.gif" />
      <span> {{ carStatus.machineStatus ? $t("closeMachine") : $t("start") }}{{ $t("in") }}... </span>
    </div>
    <span
      class="info-window-close"
      v-html="closeText"
      @click="clickHandle"
    ></span>
    <div class="info-window-container">
      <div class="top">
        <capsuleIcon
          v-show="!isOffline"
          :config="carStatus.workStatus"
          style="margin-right: 10px"
        />
        <capsuleIcon
          v-show="isOffline"
          :config="{
            text: offlineText,
            icon: offlineSvg,
            color: '#999',
          }"
          style="margin-right: 10px"
        />
        <capsuleIcon
          :config="carStatus.driveMode"
          style="margin-right: 10px"
        />
        <!-- <capsuleIcon
          v-show="carStatus.worker.text && carStatus.worker.text !== '--'"
          :config="carStatus.worker"
        /> -->
      </div>
      <div class="center">
        <div class="center-round">
          <roundPercent
            class="image"
            :percent="carStatus.speedPercent * 100"
            :offline="isOffline"
          />
          <div class="percent">
            <span>{{ carStatus.speed }}</span>
            <span>km/h</span>
          </div>
        </div>
        <div class="center-battery">
          <div class="percent">{{ carStatus.battery }}%</div>
          <div class="image">
            <batteryPercent
              :percent="Number(carStatus.battery)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
        <div class="center-water">
          <div class="percent">{{ carStatus.water }}%</div>
          <div class="image">
            <waterPercent
              :percent="Number(carStatus.water)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
        <div class="center-garbage">
          <div class="percent">{{ carStatus.garbage }}%</div>
          <div class="image">
            <garbagePercent
              :percent="Number(carStatus.garbage)"
              :offline="isOffline"
              showIcon
            />
          </div>
        </div>
      </div>
      <div class="bottom">
        <div class="bottom-left">
          <span>{{ $t("dataUploadTime") }}</span>
        </div>
        <div class="bottom-right">
          <span>{{ carStatus.updateDate }}</span>
          <span>{{ carStatus.updateTime }}</span>
        </div>
      </div>
      <div class="footer">
        <div class="footer-opera">
          <template v-if="permitList.includes('sys:vehicleOnTime:switch')">
            <windowButton
              name="map_close"
              :text="carStatus.machineStatus ? $t('closeMachine') : $t('openMachine')"
              :disabled="carStatus.loading || !dataLoaded"
              @onClick="toggleMachine"
            />
          </template>
          <template v-if="permitList.includes('sys:vehicleOnTime:task')">
            <windowButton
              name="map_task"
              :text="$t('task')"
              :disabled="!dataLoaded"
              @onClick="taskClick"
            />
          </template>
          <!-- <windowButton name="map_route" text="路线" /> -->

          <!-- 暂停按钮可删 -->
          <!-- <template
            v-if="isEcar && permitList.includes('sys:vehicleOnTime:pause')"
          >
            <windowButton
              :name="carStatus.taskStatus ? 'map_pause' : 'map_continue'"
              :text="carStatus.taskStatus ? '暂停' : '继续'"
              :disabled="pauseCarDisabled"
              @onClick="pauseCar"
            />
          </template> -->
          <template v-if="isEcar && permitList.includes('sys:vehicleOnTime:control')">
            <windowButton
              name="map_custom"
              :text="$t('control')"
              :disabled="!dataLoaded"
              @onClick="customClick"
            />
          </template>
          <template v-if="permitList.includes('sys:vehicleOnTime:camera')">
            <windowButton
              name="map_video"
              :text="$t('camera')"
              :disabled="!dataLoaded"
              @onClick="videoClick"
            />
          </template>

          <template v-if="permitList.includes('sys:vehicleOnTime:drivingData')">
            <windowButton
              name="map_client"
              :text="$t('cockpit')"
              @onClick="clientClick"
            />
          </template>
          <!-- <windowButton name="map_stop" text="急停" @onClick="stopClick" /> -->
          <template v-if="permitList.includes('sys:vehicleOnTime:vehLocusList')">
            <windowButton
              name="map_track"
              text="历史轨迹"
              @click="clickTrack"
            />
          </template>
        </div>
        <div
          v-if="showTaskStatus || showStopCharge"
          class="footer-bottom"
        >
          <div :class="['footer-bottom-status', { charge: showStopCharge }]">
            <x-icon
              v-show="showStopCharge"
              name="map_battery_icon_green"
              width="12"
              height="12"
              style="margin-right: 2px"
            />
            <!-- 协议2.0的车显示 -->
            <span v-if="carStatus.vehicleVersion">
              {{
                showTaskStatus
                  ? [6].includes(carStatus.currentTaskType)
                    ? carStatus.currentTaskTypeStr
                    : `${carStatus.currentTaskTypeStr || $t("task")}${
                        carStatus.taskWorkStatus === "pause" ? $t("pause") : $t("execute")
                      }${$t("in")}`
                  : $t("charging")
              }}
            </span>
            <span v-else>
              {{
                showTaskStatus
                  ? `${$t("task")}${carStatus.taskWorkStatus === "pause" ? $t("pause") : $t("execute")}${$t("in")}`
                  : $t("charging")
              }}
            </span>
          </div>
          <!-- 任务状态：任务规划中 -->
          <div
            v-if="
              carStatus.vehicleVersion &&
              carStatus.currentTaskType === 6 &&
              permitList.includes('sys:vehicleOnTime:pause')
            "
            class="footer-bottom-opera"
          >
            <img
              src="@/assets/images/map_task_stop.png"
              @click="taskOpera('stop')"
            />
          </div>
          <!-- 其他任务状态 -->
          <div
            v-else-if="showTaskStatus && permitList.includes('sys:vehicleOnTime:pause')"
            class="footer-bottom-opera"
          >
            <!-- 正在作业为working,作业结束为idle,作业中出现异常为error,暂停为pause -->
            <img
              v-if="carStatus.taskWorkStatus === 'pause'"
              src="@/assets/images/map_task_start.png"
              @click="taskOpera('continue')"
            />
            <img
              v-else
              src="@/assets/images/map_task_pause.png"
              @click="taskOpera('pause')"
            />
            <img
              src="@/assets/images/map_task_stop.png"
              @click="taskOpera('stop')"
            />
          </div>
          <!-- 任务状态：充电任务中 -->
          <div
            v-if="showStopCharge && permitList.includes('sys:vehicleOnTime:control')"
            class="footer-bottom-opera"
          >
            <img
              src="@/assets/images/map_task_stop.png"
              @click="stopCharge"
            />
          </div>
        </div>
      </div>
    </div>
    <distributeTask
      v-model:show="taskDrawer.show"
      :id="taskDrawer.id"
      :map="map"
      :Amap="Amap"
    />
    <distributeTask2
      v-model:show="taskDrawer2.show"
      :id="taskDrawer2.id"
      :online="carStatus.online"
    />
    <customSetting
      v-model:show="customDrawer.show"
      :id="customDrawer.id"
      :isThreeEcar="isThreeEcar"
      :isVersion2="carStatus.vehicleVersion"
    />
    <x-modal
      :visible="taskConfirm.show"
      @confirm="submitConfirm"
      @cancel="taskConfirm.show = false"
      :bodyStyle="{ padding: '32px 32px 24px 44px' }"
      :btnOption="{
        cancel: $t('close'),
        position: 'right',
        confirmLoading: taskConfirm.modeLoading,
        confirmDisabled: taskConfirm.modeLoading,
      }"
      radius="4px"
      width="424px"
      height="181px"
    >
      <div class="content">
        <div class="content-header">
          <div class="content-header-title">
            <x-icon
              class="title-icon"
              name="modal_warn"
              style="margin-right: 10px"
            />
            <span class="title-text">{{ $t("vehicleSwitchAutoMode") }}</span>
          </div>
        </div>
        <div class="content-body">{{ $t("PCheckAroundAndSafe") }}</div>
      </div>
    </x-modal>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, watch, onMounted, computed, nextTick } from "vue";
import type { PropType } from "vue";
import { useMainStore } from "@/stores/main";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import {
  workStatusMap,
  driveModeMap,
  parseSpeed,
  parseBattery,
  parseWater,
  parseGarbage,
  offlineText,
  offlineSvg,
  taskTypeMap,
} from "@/services/wsconfig";
import { delCar, openMachine, closeMachine, task, stopTask, pauseTask, continueTask } from "@/services/wsapi";
import { changeDriveMode, updateLampBlowWater, liveCarVideoInfo } from "@/services/api";
import xIcon from "@/components/x-icon.vue";
import capsuleIcon from "./capsuleIcon.vue";
import batteryPercent from "./batteryPercent.vue";
import waterPercent from "./waterPercent.vue";
import garbagePercent from "./garbagePercent.vue";
import roundPercent from "./roundPercent.vue";
import distributeTask from "./distributeTask.vue";
import distributeTask2 from "./distributeTask2.vue";
import customSetting from "./customSetting.vue";
import windowButton from "./windowButton.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import protocolCheck from "@/assets/ts/protocolcheck";
import type { Router } from "vue-router";

const $t = i18nSimpleKey("carLive");

const props = defineProps({
  router: {
    type: Object as PropType<Router>,
    required: true,
  },
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
  markerBlur: {
    type: Function,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
const {
  socket,
  userInfo,
  updateActiveCarId,
  updateActiveCarIdVideo,
  userInfo: { permitList },
} = useMainStore();

const isOffline = computed(() => !carStatus.online);
const headImgUrl = computed(() => {
  if (userInfo.imgUrl) {
    return userInfo.imgUrl;
  } else {
    return new URL("/src/assets/images/user_default_head.png", import.meta.url).href;
  }
});
const isEcar = computed(() => carStatus.manufacturer === "ecar" || carStatus.manufacturer === "PA"); // 三轮车 或 四轮车
const isThreeEcar = computed(() => carStatus?.vehType === 2); // 三轮车
const dataLoaded = ref(false);

const pauseCarDisabled = computed(
  () =>
    carStatus.driveMode.text !== $t("autoDrive") ||
    carStatus.taskWorkStatus === "idle" ||
    carStatus.taskWorkStatus === "error" ||
    !carStatus.online ||
    !dataLoaded.value
);
const showTaskStatus = computed(() => isEcar.value && !pauseCarDisabled.value);
const showStopCharge = computed(
  () =>
    carStatus.online &&
    dataLoaded.value &&
    carStatus.recharge &&
    carStatus.chargeConnectStatus === "not_connect" &&
    isThreeEcar.value &&
    !showTaskStatus.value
);

onMounted(() => {
  watch(
    () => socket.allStatus,
    (newV) => {
      if (newV) {
        const target = newV.find((v) => v.deviceId === props.id);
        if (target) {
          dataLoaded.value = true;
          // 易咖开关机loading处理
          if (isEcar.value) {
            carStatus.loading = Boolean(target.loadingMark);

            // 加载超过2分钟
            const loadingTimeOut = target.loadingTimeCurrent - target.loadingTimeStart >= 2 * 60 * 1000;
            // 在线状态切换
            const onlinStatusChange = target.online !== carStatus.online;

            if (carStatus.loading && (loadingTimeOut || onlinStatusChange)) {
              carStatus.loading = false;
            }
          }
          carStatus.online = target.online;
          carStatus.digIoSwitch = target.digIoSwitch;
          // 车辆开关机状态
          nextTick(() => {
            carStatus.machineStatus = isEcar.value ? carStatus.online : carStatus.digIoSwitch;
          });
          carStatus.workStatus = workStatusMap[target.workStatus];
          carStatus.driveMode = JSON.parse(JSON.stringify(driveModeMap[target.driveMode]));
          if (isOffline.value) {
            carStatus.driveMode.icon += "_offline";
            carStatus.driveMode.color = "#999";
          }
          carStatus.worker.text = target.worker;
          carStatus.updateDate = target.gpsDate;
          carStatus.updateTime = target.gpsTime;
          carStatus.speed = parseSpeed(target.speed);
          carStatus.speedPercent = Math.min(carStatus.speed / 10, 1);
          carStatus.battery = parseBattery(target.electric);
          carStatus.water = parseWater(target.tank);
          carStatus.garbage = parseGarbage(target.litter);
          carStatus.taskStatus = target.taskStatus;
          carStatus.taskWorkStatus = target.taskWorkStatus;
          carStatus.manufacturer = target.manufacturer;
          carStatus.recharge = target.recharge;
          carStatus.vehType = target.vehType;
          carStatus.chargeConnectStatus = target.chargeConnectStatus;
          carStatus.vehicleVersion = target.vehicleVersion;
          carStatus.currentTaskType = target.currentTaskType;
          carStatus.currentTaskTypeStr =
            taskTypeMap.find((t) => t.value === (target.currentTaskType <= 6 ? target.currentTaskType : -1))?.tip || "";
        }
      }
    },
    { immediate: true }
  );
});

const closeText = ref("&#10005");
const carStatus = reactive<Record<string, any>>({
  workStatus: {
    icon: "",
    text: "",
    color: "",
  },
  driveMode: {
    icon: "",
    text: "",
    color: "",
  },
  worker: {
    imgUrl: headImgUrl,
    text: "",
    color: "#5964FB",
  },
  online: false,
  updateDate: "",
  updateTime: "",
  speed: 0.0,
  speedPercent: 0,
  battery: 0,
  water: 0,
  garbage: 0,
  digIoSwitch: "",
  taskStatus: 0,
  taskWorkStatus: "",
  manufacturer: "",
  loading: false,
  machineStatus: false,
  recharge: false,
  vehType: 1,
  chargeConnectStatus: "not_connect",
  vehicleVersion: false, // false=1.0 true=2.0
});

const clickHandle = () => {
  closeInfoWindow();
  updateActiveCarId(null);
  !socket.enableCarIds.includes(props.id) && delCar([props.id]);
};

// 仅关闭弹窗 不断开连接
const closeInfoWindow = () => {
  props.window.close();
  // props.map.clearInfoWindow();
  props.markerBlur();
};

// 开关机
const toggleMachine = () => {
  xModal.confirm({
    title: `${$t("confirm")}${carStatus.machineStatus ? $t("close") : $t("start")}${props.id}${$t("isItOk")}`,
    closeBeforeConfirm: true,
    confirm: debounce(async () => {
      carStatus.loading = true;
      try {
        await (carStatus.machineStatus ? closeMachine : openMachine)(props.id, carStatus.manufacturer);
        Message(
          "success",
          `${props.id}${$t("vehicle")}${carStatus.machineStatus ? $t("closeMachine") : $t("openMachine")}${$t(
            "success"
          )}`
        );
        carStatus.loading = isEcar.value;
      } catch (error) {
        carStatus.loading = false;
      }
    }),
  });
};

/**
 * 多任务下发
 */
// 待机模式
const taskConfirm = reactive({
  show: false,
  modeLoading: false,
});
const submitConfirm = debounce(() => {
  taskConfirm.modeLoading = true;
  const param = {
    driveMode: 2,
    vehNo: props.id,
  };
  changeDriveMode(param)
    .then(() => {
      Message("success", $t("commandSendSuccess"));
      taskConfirm.show = false;
    })
    .finally(() => {
      taskConfirm.modeLoading = false;
    });
});
// 其他模式
const taskDrawer = reactive({
  show: false,
  id: "",
});
const taskDrawer2 = reactive({
  show: false,
  id: "",
});
const taskClick = () => {
  // 2.0 (非-易咖/PA) => 易咖/PA (切自动驾驶) => 非-易咖/PA
  if (carStatus.vehicleVersion) {
    taskDrawer2.id = props.id;
    taskDrawer2.show = true;
    closeInfoWindow();
  } else if (isEcar.value) {
    if (carStatus.online && carStatus.driveMode.text === $t("standby")) {
      taskConfirm.show = true;
    } else {
      taskDrawer.id = props.id;
      taskDrawer.show = true;
      closeInfoWindow();
    }
  } else {
    xModal.confirm({
      title: $t("confirmExecSweepTask"),
      confirm: debounce(
        () =>
          new Promise((resolve) => {
            task("falcon", props.id)
              .then(() => {
                Message("success", $t("taskSendSuccess"));
              })
              .finally(() => resolve(1));
          })
      ),
    });
  }
};
// 结束充电
const stopCharge = () => {
  xModal.confirm({
    title: $t("confirmEndCharging"),
    content: <div style="font-size:14px;color:#555;">{$t("ifConfirmEndBreakCharge")}</div>,
    confirm() {
      return updateLampBlowWater({
        deviceId: props.id,
        stopCharge: 1,
      }).then(() => {
        Message("success", $t("endChargingSuccess"));
      });
    },
  });
};

// 暂停/继续/取消
const taskOpera = (type: "stop" | "continue" | "pause") => {
  const taskTypeMap = {
    stop: {
      text: $t("cancel"),
      apiFunc: stopTask,
    },
    continue: {
      text: $t("continue"),
      apiFunc: continueTask,
    },
    pause: {
      text: $t("pause"),
      apiFunc: pauseTask,
    },
  };
  xModal.confirm({
    title: `${$t("confirmWant")}${taskTypeMap[type].text}${$t("task")}${$t("isItOk")}`,
    content: <div style="font-size:14px;color:#555;">车辆{props.id}</div>,
    confirm() {
      return taskTypeMap[type].apiFunc(props.id).then(() => {
        Message("success", `${$t("task")}${taskTypeMap[type].text}${$t("success")}`);
      });
    },
  });
};

// 自定义设置
const customDrawer = reactive({
  show: false,
  id: "",
});
const customClick = () => {
  customDrawer.id = props.id;
  customDrawer.show = true;
};

// 摄像头
const videoClick = () => {
  if (carStatus.online) {
    updateActiveCarIdVideo(props.id);
  } else {
    Message("error", "车辆不在线，无法查看实时视频");
  }
};

// 客户端
const clientClick = async () => {
  const carInfo = await liveCarVideoInfo(props.id);
  if (carInfo?.sn) {
    // let protocalUrl = "SmartGateway://open";
    let protocalUrl = `SmartClientTest:${carInfo.sn}`;
    protocolCheck(
      protocalUrl,
      () => {
        alert($t("notInstallCockpit"));
      },
      async () => {
        // await openClient(props.id);
        // console.log("检测到客户端，并拉起它");
      }
    );
  } else {
    Message("error", $t("vehicleNotEnterSN"));
  }
};
// 急停/恢复
// const stopClick = () => {
//   xModal.confirm({
//     title: `确认将${props.id}急停？`,
//     icon: "error",
//     confirm() {
//       return Promise.resolve().then(() => {
//         Message("success", `急停成功`);
//       });
//     },
//   });
// };
// 历史轨迹
const clickTrack = () => {
  props.router.push({
    name: "trackReplay",
    query: {
      id: props.id,
    },
  });
};
</script>

<style lang="scss" scoped>
.info-window {
  @include wh(368px, 260px);
  @include bis("@/assets/images/map_info_window.png");
  &.disable {
    background-image: url("@/assets/images/map_info_window_disable.png");
  }
  &.bottomOpera {
    @include wh(368px, 290px);
    background-image: url("@/assets/images/map_info_window_task.png");
  }

  position: relative;
  &-loading {
    z-index: 5;
    @include wh(354px, 230px);
    position: absolute;
    top: 10px;
    left: 6px;
    background: rgba(255, 255, 255, 0.8);
    img,
    span {
      position: absolute;
      top: 50%;
      left: 50%;
    }
    img {
      transform: translate(-50%, -70%);
      @include wh(57.6px, 64.2px);
    }
    span {
      width: max-content;
      transform: translate(-50%, 40px);
      @include sc(16px, #555) {
        font-weight: bold;
      }
    }
  }
  &-container {
    position: absolute;
    top: 20px;
    padding: 0 15px;
    width: 100%;
    .top {
      display: flex;
      @include wh(100%, 28px);
    }
    .center {
      display: flex;
      margin-top: 10px;
      &-round {
        overflow: hidden;
        position: relative;
        @include ct-f(x);
        @include wh(104px, 94px);
        .image {
          position: absolute;
          top: 5px;
        }
        .percent {
          span:nth-child(1) {
            @include ct-p(x) {
              top: 35px;
            }
            @include sc(18px, #242859) {
              font-weight: bolder;
            }
          }
          span:nth-child(2) {
            @include ct-p(x) {
              top: 57px;
            }
            @include sc(12px, #242859);
          }
        }
      }
      &-battery,
      &-water,
      &-garbage {
        flex: 1;
        .percent {
          text-align: center;
          @include sc(14px, #242859) {
            font-weight: bolder;
          }
        }
        .image {
          @include ct-f(x);
          margin-top: 5px;
        }
      }
    }
    .bottom {
      @include fj;
      height: 32px;
      line-height: 32px;
      background: #f6f6f6;
      border-radius: 4px;
      font-size: 12px;
      padding: 0 10px;
      &-left {
        color: #555;
      }
      &-right {
        color: #383838;
        span:nth-child(2) {
          padding-left: 10px;
        }
      }
    }
    .footer {
      margin-top: 5px;
      &-opera {
        display: flex;
      }
      &-bottom {
        margin-top: 6px;
        @include fj {
          align-items: center;
        }
        &-status {
          @include ct-f(y);
          @include wh(188px, 100%) {
            padding-left: 8px;
            border-radius: 4px;
            background-image: linear-gradient(to right, rgba(87, 103, 251, 0.22), rgba(87, 103, 251, 0));
            color: rgb(87, 103, 251);
          }
          &.charge {
            background-image: linear-gradient(to right, rgba(39, 212, 161, 0.17), rgba(39, 212, 161, 0));
            color: rgb(39, 212, 161);
          }
        }
        &-opera {
          height: 16px;
          display: flex;
          img {
            cursor: pointer;
            margin-right: 10px;
          }
        }
      }
    }
  }
  &-close {
    z-index: 6;
    cursor: pointer;
    display: block;
    @include ct-f;
    @include wh(16px) {
      font-size: 16px;
    }
    position: absolute;
    top: 25px;
    right: 18px;
  }
}
.content {
  .content-header {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 54px);
    @include sc(16px, #242859);
    .title-icon {
      margin-right: 10px;
    }
    .title-text {
      font-weight: bold;
    }
  }
  .content-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    padding-left: 26px;
    @include sc(14px, #383838);
  }
}
</style>
