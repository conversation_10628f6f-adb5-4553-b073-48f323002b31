<template>
  <section
    class="pro-veh-manage"
    ref="proVehRef"
  >
    <div class="pro-veh-manage-top">
      <template
        v-for="(item, index) in titleConfig.tabs"
        :key="index"
      >
        <div
          class="top-title"
          :class="{ active: titleConfig.activeKey === item.value }"
        >
          <span @click="changeTitle(item)">{{ item.lable }}</span>
          <div v-if="item.children && titleConfig.subTitle">
            <span class="active-separator">/</span>
            <span class="active-child">{{ titleConfig.subTitle }}</span>
          </div>
        </div>
      </template>
    </div>
    <template v-if="titleConfig.activeKey === 'proVehManage'">
      <div class="pro-veh-manage-middle">
        <div class="middle-left">
          <x-input
            v-model:value="searchForm.vehicleNo"
            :placeholder="$t('PEnterVehNo')"
            suffix="input_search"
            class="middle-left-input"
          />
          <x-input
            v-model:value="searchForm.userName"
            :placeholder="$t('PEnterUser')"
            suffix="input_search"
            class="middle-left-input"
          />
          <x-select
            v-model:value="searchForm.proId"
            :options="formOptions.project"
            :popupContainer="proVehRef"
            :placeholder="$t('allProjects')"
            class="middle-left-select"
          />
          <x-select
            v-model:value="searchForm.proAreaId"
            :options="formOptions.area"
            :popupContainer="proVehRef"
            :placeholder="$t('allAreas')"
            class="middle-left-select"
          />
        </div>
        <div class="middle-right">
          <x-button
            v-if="permitList.includes('sys:vehManage:list')"
            type="blue"
            :text="$t('search')"
            @click="reSearch"
            style="margin-right: 12px"
          />
          <x-button
            type="green"
            :text="$t('reset')"
            @click="resetSearchForm"
          />
        </div>
      </div>
      <div class="pro-veh-manage-bottom">
        <x-table
          :cols="table.cols"
          :dataSource="table.dataSource"
          :pagination="table.pagination"
          :loading="table.loading"
          @change="tableChange"
        >
          <!-- 排班 -->
          <template #taskType="{ record }">
            <x-icon
              :name="`status_${record.statusColor[record.taskType]}`"
              width="12"
              height="12"
            />
            <span style="margin-left: 5px">{{ record.taskTypeText }}</span>
          </template>
          <!-- 负责人 -->
          <template #userName="{ record, recordIndex }">
            <span>{{ record.userName || "--" }}</span>
            <x-popover
              v-model:visible="record.showPop"
              trigger="click"
              placement="top"
              contentType="light"
              :container="proVehRef"
              :keepShow="true"
              @visibleChange="handleClickPop($event, recordIndex)"
              class="table-project-popover"
            >
              <div>
                <x-popover
                  trigger="hover"
                  placement="top"
                >
                  <div
                    class="icon-edit"
                    @click="editManager(record)"
                  >
                    <x-icon
                      ref="editPopRef"
                      name="edit"
                      activeName="edit_active"
                      width="16"
                      height="16"
                    />
                  </div>
                  <template #content>
                    <div class="pro-veh-manage-table-project-hover-popover">
                      {{ $t("edit") }}
                    </div>
                  </template>
                </x-popover>
              </div>
              <template #content>
                <div class="pro-veh-manage-table-manager-hover-popover">
                  <div class="content-body">
                    <div class="title">{{ $t("setUser") }}</div>
                    <x-select
                      v-if="record.showPop"
                      v-model:value="managerPop.userId"
                      :options="formOptions.manager"
                      :popupContainer="proVehRef"
                      :placeholder="$t('PSelectUser')"
                      class="content"
                    />
                  </div>
                  <div class="footer-btn">
                    <x-button
                      type="white"
                      :text="$t('cancel')"
                      style="margin-right: 9px"
                      @click="cancelManagerPop(recordIndex)"
                    />
                    <x-button
                      type="blue"
                      :text="$t('confirm')"
                      :disabled="!managerPop.userId || managerPop.userId === '0'"
                      @click="confirmManagerPop(recordIndex)"
                    />
                  </div>
                </div>
              </template>
            </x-popover>
          </template>
          <!-- 项目名 -->
          <template #proName="{ record }">
            <x-popover
              trigger="hover"
              placement="bottom"
              :container="proVehRef"
            >
              {{ record.proName }}
              <template #content>
                <div class="pro-veh-manage-table-project-hover-popover">
                  {{ record.proName }}
                </div>
              </template>
            </x-popover>
          </template>
          <!-- 操作 -->
          <template #opera="{ record }">
            <div class="table-opera">
              <div class="table-opera-text">
                <span
                  v-if="permitList.includes('sys:vehManage:getVehicleSetup')"
                  @click="openSetting(record)"
                >
                  {{ $t("setting") }}
                </span>
                <span
                  v-if="permitList.includes('sys:vehManage:arrangement')"
                  @click="openSchedual(record)"
                >
                  {{ $t("schedule") }}
                </span>
                <span
                  v-if="permitList.includes('sys:vehManage:areaInfo')"
                  @click="openDetail(record)"
                >
                  {{ $t("detail") }}
                </span>
                <span
                  v-if="permitList.includes('sys:vehManage:arrangement')"
                  @click="openSchedual1_0(record)"
                >
                  {{ "排班1.0" }}
                </span>
              </div>
            </div>
          </template>
        </x-table>
        <Schedule
          v-model:show="scheduleModal.show"
          :proAreaId="scheduleModal.proAreaId"
          :vehicleId="scheduleModal.vehicleId"
          @confirm="scheduleModal.confirm"
        />
        <Schedule1_0
          v-model:show="scheduleModal1_0.show"
          :id="scheduleModal1_0.id"
          :vehicleId="scheduleModal1_0.vehicleId"
          :vehicleNo="scheduleModal1_0.vehicleNo"
          :proAreaId="scheduleModal1_0.proAreaId || ''"
          :taskType="scheduleModal1_0.taskType"
          :taskSwitch="scheduleModal1_0.taskSwitch"
          @confirm="scheduleModal1_0.confirm"
        />
        <Setting
          v-model:show="settingModal.show"
          :vehicleNo="settingModal.vehicleNo"
          :vehicleId="settingModal.vehicleId"
          :id="settingModal.id"
        />
        <Detail
          v-model:show="detailModal.show"
          :proAreaId="scheduleModal.proAreaId"
          :id="detailModal.id"
          :vehicleId="detailModal.vehicleId"
        />
      </div>
    </template>
    <template v-if="titleConfig.activeKey === 'scheduleTemplate'">
      <ScheduleTemplate
        @openDetail="openTempDetail"
        @openAdd="openTempAdd"
        @openEdit="openTempEdit"
      />
    </template>
    <template v-if="titleConfig.activeKey === 'templateDetail'">
      <TemplateDeail
        v-model:show="detailInfo.show"
        :proAreaId="detailInfo.proAreaId"
        :proName="detailInfo.proName"
        :proAreaName="detailInfo.proAreaName"
      />
    </template>
    <template v-if="titleConfig.activeKey === 'templateAdd'">
      <TemplateAdd
        v-model:show="addInfo.show"
        :proAreaId="addInfo.proAreaId"
        :proName="addInfo.proName"
        :proAreaName="addInfo.proAreaName"
        @back="changeTitle(titleConfig.tabs[1])"
      />
    </template>
    <template v-if="titleConfig.activeKey === 'templateEdit'">
      <TemplateEdit
        v-model:show="editInfo.show"
        :proAreaId="editInfo.proAreaId"
        :proName="editInfo.proName"
        :proAreaName="editInfo.proAreaName"
        @back="changeTitle(titleConfig.tabs[1])"
      />
    </template>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive, watch } from "vue";
import type { PageSizeType } from "@/components/types";
import { getProVehList, getProVehManagerList, updateProVehManager, getProjectAndArea } from "@/services/api";
import type { GetProVehListRequest } from "@/services/type";
import { useMainStore } from "@/stores/main";
import xIcon from "@/components/x-icon.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import Setting from "./components/proVehManage/setting.vue";
import Detail from "./components/proVehManage/detail.vue";
import Schedule from "./components/proVehManage/schedule.vue";
import Schedule1_0 from "./components/proVehManage/schedule1_0/setting.vue";
import ScheduleTemplate from "./components/scheduleTemplate/index.vue";
import TemplateDeail from "./components/scheduleTemplate/detail.vue";
import TemplateAdd from "./components/scheduleTemplate/add.vue";
import TemplateEdit from "./components/scheduleTemplate/edit.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

const {
  userInfo: { permitList },
} = useMainStore();

const proVehRef = ref<HTMLDivElement>();

const titleConfig = reactive({
  tabs: permitList.includes("sys:vehManage:areaScheduleTemplate")
    ? [
        {
          lable: $t("proVehManage"),
          value: "proVehManage",
        },
        {
          lable: $t("scheduleTemplate"),
          value: "scheduleTemplate",
          children: [
            {
              lable: $t("templateDetail"),
              value: "templateDetail",
            },
            {
              lable: $t("templateAdd"),
              value: "templateAdd",
            },
            {
              lable: $t("templateEdit"),
              value: "templateEdit",
            },
          ],
        },
      ]
    : [
        {
          lable: $t("proVehManage"),
          value: "proVehManage",
        },
      ],
  activeKey: "proVehManage",
  subTitle: "",
});

const changeTitle = (item: typeof titleConfig["tabs"][0]) => {
  titleConfig.activeKey = item.value;
  titleConfig.subTitle = "";
};

const detailInfo = reactive({
  show: false,
  proAreaId: "",
  proName: "",
  proAreaName: "",
});
const openTempDetail = (record: any) => {
  titleConfig.activeKey = "templateDetail";
  titleConfig.subTitle = $t("templateDetail");
  const { proAreaId, proName, proAreaName } = record;
  detailInfo.proAreaId = proAreaId;
  detailInfo.proName = proName;
  detailInfo.proAreaName = proAreaName;
  detailInfo.show = true;
};

const addInfo = reactive({
  show: false,
  proAreaId: "",
  proName: "",
  proAreaName: "",
});
const openTempAdd = (record: any) => {
  titleConfig.activeKey = "templateAdd";
  titleConfig.subTitle = $t("templateAdd");
  const { proAreaId, proName, proAreaName } = record;
  addInfo.proAreaId = proAreaId;
  addInfo.proName = proName;
  addInfo.proAreaName = proAreaName;
  addInfo.show = true;
};

const editInfo = reactive({
  show: false,
  proAreaId: "",
  proName: "",
  proAreaName: "",
});
const openTempEdit = (record: any) => {
  titleConfig.activeKey = "templateEdit";
  titleConfig.subTitle = $t("templateEdit");
  const { proAreaId, proName, proAreaName } = record;
  editInfo.proAreaId = proAreaId;
  editInfo.proName = proName;
  editInfo.proAreaName = proAreaName;
  editInfo.show = true;
};

/**
 * 表单
 */
// 搜索表单字段
const searchForm = reactive({
  vehicleNo: "",
  userName: "",
  proId: "",
  proAreaId: "",
});
// 组件数据支持
const formOptions = reactive({
  project: [] as any[],
  area: [] as any[],
  manager: [] as any[],
});

/**
 * 表格
 */
// 字段
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "vehicleNo",
      title: $t("vehicleNo"),
      width: "60",
    },
    {
      key: "taskTypeText",
      title: $t("withOrWithoutSchedule"),
      width: "60",
      slots: "taskType",
    },
    {
      key: "userName",
      title: $t("userName"),
      width: "60",
      slots: "userName",
    },
    {
      key: "areaName",
      title: $t("areaName"),
      width: "80",
    },
    {
      key: "proName",
      title: $t("projectName"),
      width: "80",
      slots: "proName",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "60",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
// 搜索
const submitSearch = async () => {
  table.loading = true;
  const { vehicleNo, userName, proAreaId, proId } = searchForm;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    proId: proAreaId ? "" : proId,
    vehicleNo,
    userName,
    proAreaId,
  } as GetProVehListRequest;
  const { totalCount, list } = await getProVehList(params);
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            statusColor: {
              0: "gray",
              1: "blue",
              2: "green",
            },
            orderId: (index + 1).toString().padStart(3, "0"),
            showPop: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
// 重新搜索
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
// 重置
const resetSearchForm = () => {
  searchForm.vehicleNo = "";
  searchForm.userName = "";
  searchForm.proId = "";
  searchForm.proAreaId = "";
  table.pagination["current"] = 1;
  submitSearch();
};
// 分页
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};

/**
 * 项目与区域级联
 */
const getProjectAndAreaList = async (proId: any) => {
  const param = proId ? [proId] : [];
  const list = (await getProjectAndArea(param)).map((item) => ({
    label: item.name,
    value: String(item.id),
  }));
  return list;
};
// 获取项目列表
(async () => {
  submitSearch();
  formOptions.project = await getProjectAndAreaList(null);
})();
// 根据项目获取区域列表
watch(
  () => searchForm.proId,
  async (newV) => {
    if (newV) {
      searchForm.proAreaId = "";
      formOptions.area = await getProjectAndAreaList(newV);
    }
  }
);

/**
 * 相关操作
 *
 */
// 设置排班
const scheduleModal = reactive({
  show: false,
  proAreaId: "",
  vehicleId: "",
  confirm: () => submitSearch(),
});
const openSchedual = (record: any) => {
  const { vehicleId, proAreaId } = record;
  scheduleModal.vehicleId = vehicleId;
  scheduleModal.proAreaId = proAreaId;
  scheduleModal.show = true;
};
/** 设置排班1.0 */
const scheduleModal1_0 = reactive({
  show: false,
  id: "",
  proAreaId: "",
  vehicleId: "",
  vehicleNo: "",
  taskType: 0,
  taskSwitch: 0,
  confirm: () => submitSearch(),
});
const openSchedual1_0 = (record: any) => {
  console.log(record);

  const { id, vehicleId, vehicleNo, proAreaId, taskType, taskSwitch } = record;
  scheduleModal1_0.id = String(id);
  scheduleModal1_0.vehicleId = vehicleId;
  scheduleModal1_0.vehicleNo = vehicleNo;
  scheduleModal1_0.proAreaId = proAreaId;
  scheduleModal1_0.taskType = taskType;
  scheduleModal1_0.taskSwitch = taskSwitch ?? 0;
  scheduleModal1_0.show = true;
};

// 查看详情
const detailModal = reactive({
  show: false,
  id: "",
  vehicleId: "",
});
const openDetail = (record: any) => {
  detailModal.id = String(record.id);
  detailModal.vehicleId = record.vehicleId;
  scheduleModal.proAreaId = record.proAreaId;
  detailModal.show = true;
};
// 设置负责人
const managerPop = reactive({
  userId: "",
  proId: "",
  id: "",
});
const editManager = async (record: any) => {
  const managerList = await getProVehManagerList({
    proId: record.proId,
  });
  formOptions.manager = managerList.map((item: any) => ({
    value: String(item.userId),
    label: item.userName,
  }));
  managerPop.userId = record.userId;
  managerPop.proId = record.proId;
  managerPop.id = record.id;
};
const cancelManagerPop = (index: any) => {
  handleClosePop(index);
  managerPop.userId = "";
  managerPop.proId = "";
  managerPop.id = "";
  formOptions.manager = [];
};
const confirmManagerPop = async (index: number) => {
  const param = {
    id: managerPop.id,
    userId: managerPop.userId,
  };
  await updateProVehManager(param);
  handleClosePop(index);
  reSearch();
};
const visibleIndex = ref();
const handleClickPop = (status: any, recordIndex: number) => {
  if (status) {
    table.dataSource = table.dataSource.map((obj, index) => {
      return {
        ...obj,
        showPop: ref(index === recordIndex),
      };
    });
  }
  visibleIndex.value = recordIndex;
};
const handleClosePop = (recordIndex: number) => {
  table.dataSource = table.dataSource.map((obj) => {
    return {
      ...obj,
      showPop: false,
    };
  });
  visibleIndex.value = recordIndex;
};
// 设置
const settingModal = reactive({
  show: false,
  vehicleNo: "",
  vehicleId: "",
  id: "",
});
const openSetting = (record: any) => {
  const { vehicleNo, vehicleId, id } = record;
  settingModal.vehicleNo = vehicleNo;
  settingModal.vehicleId = vehicleId;
  settingModal.id = id;
  settingModal.show = true;
};
</script>

<style lang="scss" scoped>
.pro-veh-manage-table-project-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
.pro-veh-manage-table-manager-hover-popover {
  padding: 15px 10px 0 10px;
  @include wh(240px, 130px);
  .content-body {
    .title {
      margin-bottom: 5px;
      @include sc(14px, #242859) {
        font-weight: 500;
      }
    }
    .content {
      width: 180px;
      margin-right: 16px;
    }
  }
  .footer-btn {
    display: flex;
    justify-content: end;
    margin-top: 15px;
  }
}
.pro-veh-manage {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include ct-f(y);
    @include wh(100%, 36px);
    line-height: 36px;
    .top-title {
      display: flex;
      margin-right: 20px;
      font-weight: bold;
      @include sc(16px, #9f9fa4);
      cursor: pointer;
      position: relative;
      &.active {
        @include sc(18px, rgb(36, 40, 89));
        &::after {
          content: "";
          position: absolute;
          bottom: -2px;
          left: 50%;
          @include wh(49px, 3px);
          transform: translateX(-50%);
          background-color: #5964fb;
        }
      }
      .active-separator {
        margin: 0 5px;
      }
      .active-child {
        @include sc(18px, rgb(36, 40, 89));
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
    .middle-left {
      &-input {
        width: 200px;
        margin-right: 16px;
      }
      &-select {
        width: 150px;
        margin-right: 16px;
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-project-popover {
      display: inline-block;
      .icon-edit {
        margin-left: 5px;
        text-align: center;
        cursor: pointer;
      }
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
