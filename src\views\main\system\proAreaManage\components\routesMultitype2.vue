<template>
  <div
    class="route-item-container"
    ref="taskContentRef"
  >
    <div
      class="structure-ponit-wrapper"
      v-if="props.version === 6"
    >
      <p class="structure-ponit-wrapper-text">请在地图上选点，点位需在途径路线附近</p>
      <el-scrollbar
        height="240px"
        class="structure-ponit-wrapper-list"
      >
        <div
          class="structure-ponit-wrapper-list-item"
          v-for="(item, index) in listModel"
          :key="index"
          @click="choosePoint(index)"
        >
          <div class="structure-ponit-wrapper-list-item-left">
            <span class="point-name">{{ item.name }}</span>
            <span class="point-longitude">经度：{{ item.longitude }}</span>
            <span class="point-latitude">纬度：{{ item.latitude }}</span>
          </div>
          <div class="structure-ponit-wrapper-list-item-right">
            <x-icon
              name="del_x"
              width="20"
              height="20"
              @click="delPoint(index)"
            />
          </div>
        </div>
      </el-scrollbar>
    </div>
    <template v-else>
      <div
        class="route-item"
        v-for="(item, index) in listModel"
        :key="index"
        @mouseover="toggleHover(index, true)"
        @mouseleave="toggleHover(index, false)"
      >
        <div class="route-item-content">
          <div class="route-item-content-left">
            {{ (index + 1).toString().padStart(2, "0") }}
          </div>
          <div class="route-item-content-center">
            <x-select
              v-if="props.version === 4"
              v-model:value="item.objType"
              :options="formOptions.types"
              :popupContainer="props.container"
              :placeholder="`请选择`"
              style="width: 80px"
              @change="objTypeChange($event, item)"
            />
            <x-select
              v-model:value="item.id"
              :options="getSelectOptions(item)"
              showPopSearch
              :popupContainer="props.container"
              :placeholder="`请选择${getPlaceholderText(item)}`"
              style="flex: 1"
              @change="selectChange($event, index)"
            />
            <!-- <x-cascader
              v-if="props.version === 1"
              v-model:value="item.sweepingTypeList"
              :options="formOptions.cascader[index]"
              :popupContainer="props.container"
              :allowClear="false"
              :autoSelectFirstChild="true"
              menuWidth="300"
              @update:value="changeCleaningType($event, index)"
              style="width: 200px"
            /> -->
            <x-select
              v-if="props.version === 4 && item.objType === 'route'"
              v-model:value="item.cleanStatus"
              :options="formOptions.cleanStatus"
              :popupContainer="props.container"
              :placeholder="`请选择`"
              style="width: 80px"
              @change="item.cleanStatus = $event"
            />
          </div>
          <div
            class="route-item-content-right"
            v-if="item.hovered"
          >
            <x-icon
              v-if="listModel.length > 1"
              class="minus-icon"
              name="minus_circle"
              width="20"
              height="20"
              @click="delRoute(index, item)"
            />
            <x-icon
              v-if="listModel.length < maxRouteLength"
              class="plus-icon"
              name="plus_circle"
              width="20"
              height="20"
              @click="addRoute(index)"
            />
          </div>
        </div>
      </div>
    </template>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref, type PropType, computed } from "vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import xCascader from "@/components/x-cascader.vue";
import { baseCleaningType, fullCleaningType } from "@/assets/ts/config";
import { treeBfsParse } from "@/assets/ts/utils";
import { templateVersionMap } from "@/assets/ts/config";
import { ITEM_RENDER_EVT } from "element-plus/es/components/virtual-list/src/defaults";

export type ListType = {
  /** 路线id/区块id */
  id: string;
  /** 路线名称/区块名称 */
  name?: string;
  vehRouteNo?: string;
  /** 清洁类型 */
  taskType?: string;
  /** 清洁类型选项数组 */
  cleanTypeArrays?: string[];
  /** 清扫类型 */
  sweepingType?: string;
  /** 清扫类型选项数组 */
  sweepingTypeList?: string[];
  /** 类型(判断地图绘制的覆盖物样式) */
  type?: "route" | "block" | "csvRoute" | "structurePoints";
  /** 对象类型  */
  objType?: "route" | "block" | "point";
  /** 排序 */
  orders?: number;

  /** 【4.0路线】是否清扫  */
  cleanStatus?: boolean;
  label: string;
  value: string;
  hovered?: boolean;
  points?: { longitude: number; latitude: number }[];
  longitude?: number;
  latitude?: number;
};

const props = defineProps({
  list: {
    type: Array as PropType<ListType[]>,
    required: true,
  },
  /** 下拉框列表 */
  options: {
    type: Array as PropType<ListType[]>,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  // 最大路线条数
  maxRouteLength: {
    type: Number,
    default: 10,
  },
  /** 模版版本: 1(1.0版本) 2(2.0版本) 3(2.0自录路线) 4(3.0版本)  5:3.0结构化路线，6:3.0结构化选点*/
  version: {
    type: Number,
    default: 1,
  },
});

const emits = defineEmits(["update:list", "delRoute", "changeRoute", "delPoint", "choosePoint"]);

const choosePoint = (index: number) => {
  emits("choosePoint", index);
};

const delPoint = (index: number) => {
  listModel.value.splice(index, 1);
  emits("delPoint", index);
  emits("update:list", listModel.value);
};

const taskContentRef = ref<HTMLDivElement>();

const listModel = ref<Omit<ListType, "label" | "value">[]>([
  {
    id: "",
    name: "",
    vehRouteNo: "",
    taskType: "",
    sweepingType: "",
    sweepingTypeList: [],
    type: undefined,
    objType: "block",
    hovered: false,
  },
]);

// 获取placeholder文本
const getPlaceholderText = (item: typeof listModel.value[0]) => {
  if (props.version === 4) {
    return item.objType === "block" ? "区块" : "路线";
  } else if (props.version === 5) {
    return "结构化路线";
  }
  return templateVersionMap[props.version];
};

/**
 *
 * 点击地图路线添加路线
 */
const addRouteByMap = (data: any) => {
  console.log(listModel.value, "listModel.value");

  const newData = {} as ListType;
  if (data.type === "route") {
    newData.id = data.id as string;
    newData.name = data.name as string;
    newData.vehRouteNo = data.vehRouteNo ?? "";
    newData.objType = "route";
    newData.type = "route";
    getTaskTypeList(data.vehRouteNo ?? "", 0);
  } else if (data.type === "block") {
    newData.id = data.id as string;
    newData.name = data?.name as string;
    newData.objType = "block";
    newData.type = "block";
  } else if (data.type === "csvRoute") {
    newData.id = data.id as string;
    newData.name = data?.name as string;
    newData.objType = "route";
    newData.type = "csvRoute";
  }
  if (!listModel.value[0].name) {
    listModel.value[0] = newData;
  } else {
    listModel.value.push(newData);
  }

  emits("update:list", listModel.value);
};

// 添加路线
const addRoute = (index: number) => {
  listModel.value.splice(index + 1, 0, {
    id: "",
    name: "",
    vehRouteNo: "",
    taskType: "",
    sweepingType: "",
    sweepingTypeList: [],
    type: undefined,
    objType: "block",
    cleanStatus: true,
    hovered: false,
  });
  emits("update:list", listModel.value);
};

// 删除路线
const delRoute = (index: number, item: any) => {
  listModel.value.splice(index, 1);
  emits("update:list", listModel.value);
  emits("delRoute", item.id);
};

// 更改路线
const selectChange = (value: string, index: number) => {
  const item = props.options.find((item) => item.value === value);
  if (item?.type === "route") {
    listModel.value[index].name = item.name as string;
    listModel.value[index].vehRouteNo = item.vehRouteNo ?? "";
    listModel.value[index].objType = "route";
    listModel.value[index].type = "route";
    getTaskTypeList(value, index);
  } else if (item?.type === "block") {
    listModel.value[index].name = item?.name as string;
    listModel.value[index].objType = "block";
    listModel.value[index].type = "block";
  } else if (item?.type === "csvRoute") {
    listModel.value[index].name = item?.name as string;
    listModel.value[index].objType = "route";
    listModel.value[index].type = "csvRoute";
  }
  emits("changeRoute");
};

const objTypeChange = (val: "route" | "block", item: Partial<ListType>) => {
  item.objType = val;
  item.cleanStatus = true;
  item.id = "";
};

const getSelectOptions = (item: Partial<ListType>) => {
  return props.options.filter((v) =>
    props.version === 1
      ? v.type === "route"
      : props.version === 2
      ? v.type === "block"
      : props.version === 3
      ? v.type === "csvRoute"
      : props.version === 4
      ? item.objType === "block"
        ? v.type === "block"
        : v.type === "csvRoute"
      : props.version === 5
      ? v.type === "route"
      : false
  );
};

const formOptions = reactive({
  cascader: [] as any[][],
  types: [
    { label: "区块", value: "block" },
    { label: "路线", value: "route" },
  ],
  cleanStatus: [
    { label: "清扫", value: true },
    { label: "不清扫", value: false },
  ],
});

treeBfsParse(fullCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

treeBfsParse(baseCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

// 根据路线获取任务类型
const getTaskTypeList = (value: string, index: number) => {
  if (value) {
    const selectedRoute: any = props.options.find((item) => item.value === value) || {};
    formOptions.cascader[index] = selectedRoute.cleanTypeArrays?.includes("edgewise")
      ? fullCleaningType
      : baseCleaningType;
  } else {
    formOptions.cascader[index] = [];
  }
};

// 更改清扫类型
const changeCleaningType = (value: string, index: number) => {
  listModel.value[index].taskType = value[0];
  listModel.value[index].sweepingType = value[1] || "";
  emits("update:list", listModel.value);
};

// 将数据更新分批处理
const updateFormRouteList = (data: ListType[]) => {
  console.log("updateFormRouteList", { data });

  if (data.length) {
    const batchSize = 10;
    let index = 0;
    const updateBatch = () => {
      const slice = data.slice(index, index + batchSize);
      listModel.value.splice(index, batchSize, ...(slice as any));
      index += batchSize;
      if (index < data.length) {
        requestAnimationFrame(updateBatch);
      }
    };
    updateBatch();
  } else if (props.version === 6) {
    listModel.value = [];
    // emits("update:list", listModel.value);
  } else {
    listModel.value = [
      {
        id: "",
        name: "",
        vehRouteNo: "",
        taskType: "",
        sweepingType: "",
        sweepingTypeList: [] as string[],
        type: "" as "block" | "route",
        objType: "block",
        hovered: false,
      },
    ];
    emits("update:list", listModel.value);
  }
};

watch(
  () => props.list,
  () => {
    props.list.forEach((item, index) => {
      getTaskTypeList(item.vehRouteNo ?? "", index);
    });
    updateFormRouteList(props.list);
  },
  { immediate: true, deep: true }
);

const toggleHover = (index: number, isHovered: boolean) => {
  listModel.value[index].hovered = isHovered;
};

defineExpose({
  addRouteByMap,
});
</script>
<style lang="scss" scoped>
.route-item-container {
  .route-item {
    padding: 10px;
    &-content {
      @include ct-f(y);
      &-left {
        @include wh(20px, 20px) {
          line-height: 20px;
          text-align: center;
        }
        @include sc(14px, #9f9fa4) {
          border: 1px solid #9f9fa4;
          border-radius: 50px;
        }
      }
      &-center {
        display: flex;
        margin-left: 10px;
        column-gap: 10px;
        width: 380px;
      }
      &-right {
        display: flex;
        margin-left: 10px;
        .minus-icon,
        .plus-icon {
          cursor: pointer;
        }
        .minus-icon {
          margin-right: 5px;
        }
      }
    }
    &:hover {
      box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease, border 0.3s ease;
    }
  }
  .structure-ponit-wrapper {
    border-radius: 4px;
    background: #f4f7fe;
    padding: 10px;

    &-text {
      @include sc(12px, #294478);
    }
    &-list {
      display: flex;
      flex-direction: column;
      margin-top: 10px;
      background-color: #fff;
      &-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 10px;
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
        &:hover {
          background: rgba(237, 242, 255, 1);
        }
        &-left {
          display: flex;
          align-items: center;
          width: 100%;
          .point-name {
            width: 60px;
            padding-left: 20px;
          }
          .point-longitude,
          .point-latitude {
            flex: 1;
          }
        }
      }
    }
  }
}
</style>
