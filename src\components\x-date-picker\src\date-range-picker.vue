<script lang="ts" setup>
import xPopover from "@/components/x-popover.vue";
import DatePanel from "./date-panel.vue";
import MonthPanel from "./month-panel.vue";
import { safeGetTime, formatDateTime } from "@/assets/ts/dateTime";
import { getYearMonth, parseDateFormat, parseDate } from "./utils";
import { reactive, ref, watch, computed, onMounted } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    rangeType?: "date" | "month";
    allowClear?: boolean;
    disabledDate?: Function;
    disabledTime?: Function;
    format?: string;
    popupContainer: HTMLElement;
    placeholder?: string[];
    showNow?: boolean;
    showTime?: boolean;
    showToday?: boolean;
    value?: string[];
    spotList?: string[];
  }>(),
  {
    rangeType: "date",
    allowClear: true,
    format: "YYYY-MM-DD",
    placeholder: () => ["开始", "结束"],
    showNow: false,
    showTime: false,
    showToday: false,
    value: () => ["", ""],
  }
);
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:value"]);

/**
 * 引用
 */
const triggerRef = ref();
const startInputRef = ref();
const endInputRef = ref();

const pickerFormat = computed(() => {
  return props.rangeType === "month" ? props.format.slice(0, 7) : props.format;
});

onMounted(() => {
  data.allMonthListRefList = document.querySelectorAll(".month-list")!;
});

/**
 * 动态值
 */
const data = reactive({
  allMonthListRefList: [],
  popoverVisible: false,
  tempValue: [...props.value].map((item) =>
    item ? formatDateTime(parseDate(item), pickerFormat.value) : item
  ),
  previewValue: "",
  year: 0,
  month: 0,
  nextYear: 0,
  nextMonth: 0,
  today: formatDateTime(new Date().getTime(), "YYYY-MM-DD"),
  todaystamp: safeGetTime(formatDateTime(new Date().getTime(), "YYYY-MM-DD")),
  focus: -1,
  touch: new Set(),
});

// 得到年月
getYearMonth({
  data,
  value: parseDate(data.tempValue[0]),
  type: props.rangeType,
});

/**
 * 恢复
 */
const recover = () => {
  data.previewValue = "";
  data.focus = -1;
  data.touch.clear();
};

/**
 * 气泡层隐藏
 */
const popoverVisibleChange = (visible: boolean) => {
  if (visible === false) {
    // 恢复临时值
    data.tempValue = [...props.value].map((item) =>
      item ? formatDateTime(parseDate(item), pickerFormat.value) : item
    );
    // 恢复
    recover();
  }
};
/**
 * 触发器被点击
 */
const onClickTrigger = () => {
  // 显示气泡层
  if (data.popoverVisible === false) {
    data.popoverVisible = true;
  }

  // 自动聚焦
  if (data.focus < 0) {
    if (document.activeElement !== startInputRef.value) {
      startInputRef.value.focus();
      data.focus = 0;
    }
  } else if (data.focus === 0) {
    if (document.activeElement !== startInputRef.value) {
      startInputRef.value.focus();
    }
  } else if (data.focus === 1) {
    if (document.activeElement !== endInputRef.value) {
      endInputRef.value.focus();
    }
  }
};
/**
 * 点击输入框
 */
const onClickInput = (focus: number) => {
  data.focus = focus;
};
/**
 * 保持层被点击   // 感觉没啥乱用，后面再删
 */
const onClickKeep = () => {
  // 如果气泡层显示，执行触发器被点击
  data.popoverVisible && triggerRef.value.click();
};
/**
 * 自动下一步
 * 自动焦点、自动确认、自动禁用等
 */
const autoNext = () => {
  // 操作2次
  if (data.touch.size === 2) {
    // 转换值
    const tranValue = data.tempValue.map((item) =>
      item ? formatDateTime(parseDate(item), pickerFormat.value) : item
    );
    // 值符合要求
    if (
      safeGetTime(parseDate(data.tempValue[0])) <
      safeGetTime(parseDate(data.tempValue[1]))
    ) {
      // 更新表单值
      emits("update:value", tranValue);
    } else {
      // 更新表单值
      emits("update:value", tranValue.reverse());
    }
    // 隐藏气泡层
    data.popoverVisible = false;
    // 恢复
    recover();
    return;
  }

  // 有开始，聚焦到结束
  if (data.touch.has(0)) {
    endInputRef.value.click();
    return;
  }

  // 有结束，聚焦到开始
  if (data.touch.has(1)) {
    startInputRef.value.click();
    return;
  }
};
/**
 * 更新临时的值
 */
const updateTempValue = ({ date, time }: any) => {
  // 保持层被点击
  onClickKeep();

  // 异常退出
  if (data.focus < 0) return;

  // 值拆分
  const [tempDate, tempTime] = parseDate(data.tempValue[data.focus])
    .split(" ")
    .filter((v: string) => v);

  // 更新对应值
  if (date && time) {
    // 日期 + 时间
    data.tempValue[data.focus] = parseDate(`${date} ${time}`);
  } else if (date) {
    // 仅日期
    const formatParse = parseDateFormat(pickerFormat.value);
    const defaultTime = formatParse.hour_minute_second
      ? " 00:00:00"
      : formatParse.hour_minute
      ? " 00:00"
      : formatParse.hour
      ? " 00"
      : "";
    data.tempValue[data.focus] = parseDate(
      tempTime ? `${date} ${tempTime}` : `${date}${defaultTime}`
    );
  } else if (time) {
    // 仅时间
    data.tempValue[data.focus] = parseDate(
      tempDate ? `${tempDate} ${time}` : `${data.today} ${time}`
    );
  }

  // 结束日期中时间未选择时 默认"23:59:59"
  if (props.showTime && data.focus === 1 && !time) {
    data.tempValue[data.focus] = parseDate(`${date} 23:59:59`);
  }

  // 开始结束
  const [start, end] = data.tempValue.map((item) => {
    if (item) {
      parseDate(item);
    }
    return item;
  });

  // 如果开始比结束晚，清空结束时间
  if (data.focus === 0 && safeGetTime(start) > safeGetTime(end)) {
    data.tempValue[1] = "";
    data.touch.delete(1);
  }

  // 如果结束比开始早，清空开始时间
  if (data.focus === 1 && safeGetTime(end) < safeGetTime(start)) {
    data.tempValue[0] = "";
    data.touch.delete(0);
  }

  // 更新临时值
  data.tempValue = [...data.tempValue].map((item) =>
    item ? formatDateTime(parseDate(item), pickerFormat.value) : item
  );

  // 不显示时间
  if (!props.showTime) {
    // 记录操作
    data.touch.add(data.focus);
    // 自动下一步
    autoNext();
  }
};
/**
 * 更新预览值
 */
const updatePreviewValue = ({ date }: any = {}) => {
  if (data.focus < 0) return;
  // 值拆分
  const tempTime = parseDate(data.tempValue[data.focus])[1]
    ?.split(" ")
    .filter((v: string) => v);

  // 预览
  if (date) {
    const pv = tempTime ? `${date} ${tempTime}` : date;
    data.previewValue = pv
      ? formatDateTime(parseDate(pv), pickerFormat.value)
      : pv;
    return;
  }

  // 退出预览
  data.previewValue = "";
};
/**
 * 确定
 */
const isConfirmBtnDisabled = computed(() => {
  return (
    (data.focus === 0 && data.tempValue[0] === "") ||
    (data.focus === 1 && data.tempValue[1] === "")
  );
});

const onClickConfirm = () => {
  if (!isConfirmBtnDisabled.value) {
    // 记录操作
    data.touch.add(data.focus);
    // 自动下一步
    autoNext();
  }
};
/**
 * 清除
 */
const onClickClear = () => {
  // 更新表单值
  emits("update:value", ["", ""]);
  // 重置临时值
  data.tempValue = ["", ""];
  data.previewValue = "";
  data.focus = 0;
  data.touch.clear();
};
/**
 * 此刻
 */
const onClickNow = () => {
  // 更新临时值
  data.tempValue[data.focus] = parseDate(
    formatDateTime(new Date().getTime(), "YYYY-MM-DD HH:mm:ss")
  );
  data.tempValue = [...data.tempValue].map((item) =>
    item ? formatDateTime(parseDate(item), pickerFormat.value) : item
  );
};
/**
 * 更新年月
 */
const changeYearMonth = (change: any) => {
  getYearMonth({ data, ...change, type: props.rangeType });
};
/**
 * 焦点变化
 */
watch(
  () => data.focus,
  () => {
    if (data.focus >= 0 && parseDate(data.tempValue[data.focus])) {
      getYearMonth({
        data,
        value: parseDate(data.tempValue[data.focus]),
        type: props.rangeType,
      });
    }
  }
);
/**
 * 值变化
 */
watch(
  () => props.value,
  () => {
    data.tempValue = [...props.value].map((item) =>
      item ? formatDateTime(parseDate(item), pickerFormat.value) : item
    );
  }
);
/**
 * 类型变化
 */
watch(
  () => props.rangeType,
  () => {
    emits("update:value", ["", ""]);
    data.tempValue = ["", ""];
    data.previewValue = "";
  }
);
</script>

<template>
  <x-popover
    trigger="focus"
    placement="leftBottom"
    contentType="none"
    v-model:visible="data.popoverVisible"
    :container="props.popupContainer"
    :triangle="false"
    :allowKeepShowElements="[...data.allMonthListRefList]"
    @visibleChange="popoverVisibleChange"
  >
    <div
      class="x-date-picker-trigger"
      :class="[{ 'x-date-picker-trigger_state-actived': data.popoverVisible }]"
      ref="triggerRef"
      @click="onClickTrigger"
    >
      <div class="x-date-picker-trigger__item">
        <input
          class="x-date-picker-trigger__input"
          :class="[
            {
              'x-date-picker-trigger__input_state-focus': data.focus === 0,
              'x-date-picker-trigger__input_state-preview':
                data.previewValue && data.focus === 0,
            },
          ]"
          type="text"
          ref="startInputRef"
          :placeholder="props.placeholder[0]"
          :value="(data.focus === 0 && data.previewValue) || data.tempValue[0]"
          @click="onClickInput(0)"
        />
      </div>
      <div class="x-date-picker-trigger__split">
        <x-icon name="calendar_to" width="8" height="2" />
      </div>
      <div class="x-date-picker-trigger__item">
        <input
          class="x-date-picker-trigger__input"
          :class="[
            {
              'x-date-picker-trigger__input_state-focus': data.focus === 1,
              'x-date-picker-trigger__input_state-preview':
                data.previewValue && data.focus === 1,
            },
          ]"
          type="text"
          ref="endInputRef"
          :placeholder="props.placeholder[1]"
          :value="(data.focus === 1 && data.previewValue) || data.tempValue[1]"
          @click="onClickInput(1)"
        />
      </div>
      <div class="x-date-picker-trigger__suffix">
        <x-icon name="calendar" width="12" height="12" />
      </div>
      <div
        class="x-date-picker-trigger__clear"
        v-if="props.allowClear"
        @click="onClickClear"
      >
        <x-icon name="input_clear" width="12" height="12" />
      </div>
    </div>
    <template #content>
      <div class="popover" @click="onClickKeep">
        <div class="popover__body">
          <template v-if="props.rangeType === 'date'">
            <DatePanel
              range
              :partial="props.showTime === false ? 'start' : undefined"
              :value="data.tempValue"
              :previewValue="data.previewValue"
              :focus="data.focus"
              :visible="data.popoverVisible"
              :year="data.year"
              :month="data.month"
              :todaystamp="data.todaystamp"
              :format="props.format"
              :popupContainer="props.popupContainer"
              :showTime="props.showTime"
              :disabledTime="props.disabledTime"
              :disabledDate="props.disabledDate"
              :touch="data.touch"
              :spotList="props.spotList"
              @updateTempValue="updateTempValue"
              @updatePreviewValue="updatePreviewValue"
              @changeYearMonth="changeYearMonth"
            >
            </DatePanel>
            <DatePanel
              v-if="props.showTime === false"
              range
              partial="end"
              :value="data.tempValue"
              :previewValue="data.previewValue"
              :focus="data.focus"
              :visible="data.popoverVisible"
              :year="data.nextYear"
              :month="data.nextMonth"
              :todaystamp="data.todaystamp"
              :format="props.format"
              :popupContainer="props.popupContainer"
              :showTime="props.showTime"
              :disabledTime="props.disabledTime"
              :disabledDate="props.disabledDate"
              :touch="data.touch"
              :spotList="props.spotList"
              @updateTempValue="updateTempValue"
              @updatePreviewValue="updatePreviewValue"
              @changeYearMonth="changeYearMonth"
            >
            </DatePanel>
          </template>
          <template v-else>
            <MonthPanel
              range
              partial="start"
              :value="data.tempValue"
              :previewValue="data.previewValue"
              :focus="data.focus"
              :visible="data.popoverVisible"
              :year="data.year - 1"
              :month="data.month"
              :format="props.formatMonth"
              :popupContainer="props.popupContainer"
              :touch="data.touch"
              @updateTempValue="updateTempValue"
              @updatePreviewValue="updatePreviewValue"
              @changeYearMonth="changeYearMonth"
            >
            </MonthPanel>
            <MonthPanel
              range
              partial="end"
              :value="data.tempValue"
              :previewValue="data.previewValue"
              :focus="data.focus"
              :visible="data.popoverVisible"
              :year="data.year"
              :month="data.nextMonth"
              :format="props.formatMonth"
              :popupContainer="props.popupContainer"
              :touch="data.touch"
              @updateTempValue="updateTempValue"
              @updatePreviewValue="updatePreviewValue"
              @changeYearMonth="changeYearMonth"
            >
            </MonthPanel>
          </template>
        </div>
        <slot name="renderExtraFooter"></slot>
        <div class="popover__foot" v-if="props.showTime">
          <template v-if="props.showNow">
            <div class="button-side" @click="onClickNow">{{ $t("now") }}</div>
          </template>
          <div
            class="button-confirm"
            :class="{ disabled: isConfirmBtnDisabled }"
            @click="onClickConfirm"
          >
            {{ $t("confirm") }}
          </div>
        </div>
      </div>
    </template>
  </x-popover>
</template>

<style scoped src="./date-picker.scss"></style>
