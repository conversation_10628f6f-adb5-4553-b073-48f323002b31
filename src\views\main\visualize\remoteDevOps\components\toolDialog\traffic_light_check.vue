<template>
  <x-drawer
    width="390px"
    :visible="props.show"
    :modal="false"
    @update:visible="closed"
  >
  <template #title>xxxxxxxxx</template>
    <div class="content">
      <!-- <x-radio-button
        type="tab"
        :gap="10"
        v-model:value="readTab.activeIndex"
        :options="FileStatusList"
      /> -->
      <div class="tabs">
        <div class="left">
          <div :class="{'left-item',active:readTab.activeIndex === item.value}" v-for="item in FileStatusList" :key="item.value">
            {{ item.label }}
          </div>

          </div>

          <div class="right">
            <div class="right-item">全部预告</div>
          </div>
        </div>
      <template v-if="readTab.activeIndex === 1">
        <div class="traffic-light-page">
          <div class="operations">
            <div
              class="operation-item"
              @dblclick="() => handleEmergencyStopCmdChange(true)"
            >
              <div class="left">
                <span class="label">急停</span>
                <span class="tip">双击打开急停</span>
              </div>
              <div class="right">
                <el-switch
                  v-model="formModel.emergencyStopCmd"
                  class="mb-2"
                  inline-prompt
                  active-text="开"
                  inactive-text="关"
                  @change="handleEmergencyStopCmdChange"
                />
              </div>
            </div>
            <div
              class="operation-item"
              @dblclick="() => handleCameraClick()"
            >
              <div class="left">
                <span class="label">摄像头</span>
                <span class="tip">双击打开</span>
              </div>
              <div class="right">
                <x-icon
                  style="cursor: pointer"
                  width="20px"
                  height="20px"
                  name="monitor-icon"
                  @click="handleCameraClick"
                />
              </div>
            </div>

            <!-- <div class="operation-item">
              <span class="label">抓拍</span>
              <x-icon
                v-if="!captureLoading"
                style="cursor: pointer"
                width="20px"
                height="20px"
                name="camera-icon"
                @click="handleCaptureClick"
              />
              <x-icon
                v-else
                style="animation: rotateCircle 1s linear infinite"
                width="20px"
                height="20px"
                name="loading"
              />
            </div> -->
          </div>

          <div class="header">
            <div class="card-info">
              <div class="device-id">{{ readerData?.device_id ?? "-" }}</div>
              <div class="text">过红绿灯校验</div>
              <div class="report-time">{{ readerData?.createdTime ?? "-" }}</div>
            </div>
            <ul class="clock-list">
              <li class="clock-item">00</li>
              <span style="font-weight: 700; font-size: 25px">:</span>
              <li class="clock-item">00</li>
              <span style="font-weight: 700; font-size: 25px">:</span>
              <li class="clock-item">{{ formattedTiming }}</li>
            </ul>
          </div>

          <div
            class="traffic-light-info"
            v-show="isNew"
          >
            <div class="item">
              <div class="left">
                <span class="label">通行方向</span>
                <span class="value">{{ showDirection(readerData.dataContent?.direction) }}</span>
              </div>
              <div class="right">
                <x-icon
                  width="117px"
                  height="88px"
                  :name="showTrafficLightColorIcon(readerData.dataContent?.direction)"
                />
              </div>
            </div>
            <div class="traffic-light-result">
              <span class="label">感知结果</span>
              <span class="value">{{ showTrafficLightColor(readerData.dataContent?.trafficLightColor) }}</span>
            </div>
          </div>
          <div class="image-container">
            <p class="title">图片</p>
            <div
              class="image-box"
              v-if="readerData.picUrl"
              @click="handleImageClick"
            >
              <!-- :src="readerData.picUrl" -->
              <el-image
                class="image"
                :src="readerData.picUrl"
              />
            </div>
          </div>
          <div
            class="record-info"
            v-if="readerData.dataContent?.status === 1"
          >
            <p class="title">记录信息</p>
            <div class="info-wrapper">
              <span class="date">{{ readerData.dataContent?.updateTime }}</span>
              <span class="status">已校验</span>
            </div>
          </div>
        </div>
      </template>
      <preview-information-page
        v-if="readTab.activeIndex === 3"
        :trafficLightData="props.trafficLightData"
        :deviceId="props.deviceId"
        @previewItemClick="handlePreviewItemClick"
      />
      <div
        class="button-container"
        v-if="[1, 2].includes(readTab.activeIndex)"
      >
        <el-button
          type="primary"
          @click="handleSubmit(1)"
          :disabled="readerData.position === 2"
          >通行</el-button
        >
        <el-button
          type="danger "
          @click="handleSubmit(0)"
          :disabled="readerData.position === 2"
          >停车</el-button
        >
        <div style="font-size: 16px; color: #9093bb; margin-top: 16px">(距离起止线10m内或过起止线可操作)</div>
      </div>
    </div>
  </x-drawer>
</template>

<script setup lang="ts">
import { ElMessage } from "element-plus";
import { debounce } from "@/assets/ts/utils";
import { getRedLightList, getRedLightStop, updateRedLightCheck } from "@/services/api";
import { GetRedLightListResponse } from "@/services/type";
import { PropType, ref, reactive, watch, computed, onUnmounted } from "vue";
import xDrawer from "@/components/x-drawer.vue";
import xRadioButton from "@/components/x-radio-button.vue";
import previewInformationPage from "./preview-information-page.vue";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  show: {
    type: Boolean,
    default: false,
  },
  trafficLightData: {
    type: Object as PropType<_AlarmMessageStatusType | any>,
    default: () => ({}),
  },
  openWith: {
    type: String as PropType<"auto" | "click">,
    default: "click",
  },
  deviceId: {
    type: String,
    default: "",
  },
});
const emits = defineEmits(["update:show", "handleCameraClick", "confirm", "previewItemClick", "handleImageClick"]);
const readTab = reactive({
  activeIndex: 1,
  statusList: [
    { label: "过红绿灯", value: 1, isHide: false },
    { label: "预告信息", value: 3, isHide: true },
  ],
});
/** 是否是新版红绿灯 */
const isNew = computed(() => {
  return props.trafficLightData?.dataContent?.trafficLightId;
});
const captureLoading = ref(false);
/**
 * 显示方向
 * @param direction 方向
 * @returns 方向
 */
const showDirection = (direction: number) => {
  switch (direction) {
    case 0:
      return "直行";
    case 1:
      return "左拐";
    case 2:
      return "右拐";
    case 3:
      return "掉头";
    default:
      return "未知";
  }
};
/**
 * 显示红绿灯颜色
 * @param color 颜色
 * @returns 颜色
 */
const showTrafficLightColor = (color: number) => {
  switch (color) {
    case 0:
      return "未知";
    case 1:
      return "红灯";
    case 2:
      return "黄灯";
    case 3:
      return "绿灯";
    case 4:
      return "黑灯";
    default:
      return "未知";
  }
};

/**
 * 显示红绿灯颜色图标
 * @param color 颜色
 * @returns 图标
 */
const showTrafficLightColorIcon = (color: number) => {
  switch (color) {
    case 0:
      return "straight-travel-icon";
    case 1:
      return "turn-left-icon";
    case 2:
      return "turn-right-icon";
    case 3:
      return "u-turn-icon";
    default:
      return "";
  }
};

const FileStatusList = computed(() => {
  return readTab.statusList.filter((item) => item.isHide);
});

const timing = ref(0);
const timer = ref<any | null>(null);
const redLightList = ref<GetRedLightListResponse[]>([]);

const formattedTiming = computed(() => {
  const number = timing.value;
  return String(number).padStart(2, "0");
});
const readerData = computed(() => {
  const { deviceId } = redLightList?.value[0] ?? {};
  if (props.openWith === "auto") {
    return { ...props.trafficLightData, createdTime: props.trafficLightData?.time };
  } else {
    return redLightList.value.length > 0
      ? { ...redLightList.value[0], device_id: deviceId, snapId: redLightList.value[0].id }
      : {};
  }
});

const formModel = reactive({
  navigationAdvice: 1,
  emergencyStopCmd: false,
});

const handleImageClick = () => {
  emits("handleImageClick");
};

const handlePreviewItemClick = (item: any) => {
  emits("previewItemClick", item);
};

/**
 * 抓拍
 */
// const handleCaptureClick = debounce(async () => {
//   try {
//     captureLoading.value = true;
//     const res = await cameraSnapAndUploadTemp({ vehNo: props.deviceId });
//     imageUrl.value = res.map((item) => item.picUrl as string);
//   } catch (error) {
//     console.error(error);
//   } finally {
//     captureLoading.value = false;
//   }
// });

const handleEmergencyStopCmdChange = async (value: boolean) => {
  try {
    await getRedLightStop({
      vehNo: props.deviceId,
      emergencyStopCmd: value ? "1" : "2",
    });
    ElMessage.success("操作成功");
  } catch (error) {
    ElMessage.error("操作失败");
    formModel.emergencyStopCmd = false;
  }
};

const handleCameraClick = () => {
  emits("handleCameraClick");
};
const handleSubmit = debounce(async (value: number) => {
  try {
    await updateRedLightCheck({
      dataTime: props.openWith === "auto" ? readerData.value.time : readerData.value.updatedTime,
      navigationAdvice: value,
      picUrl: readerData.value?.picUrl,
      vehNo: readerData.value?.device_id,
      snapId: readerData.value?.dataContent?.snapId,
      trafficLightId: readerData.value?.dataContent?.trafficLightId,
    });
    ElMessage.success("提交成功");
    emits("confirm");
  } catch (error) {
    console.log(error);
  } finally {
    emits("update:show", false);
  }
});

const closed = async () => {
  emits("update:show", false);
};

// 重置计时器的函数
const resetTimer = () => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
  timing.value = 0;
  timer.value = setInterval(() => {
    if (timing.value < 15) {
      timing.value++;
    } else {
      clearInterval(timer.value);
      timer.value = null;
      closed();
    }
  }, 1000);
};

// 监听 trafficLightData 变化
watch(
  () => props.trafficLightData,
  (newVal, oldVal) => {
    if (newVal) {
      if (oldVal && newVal.id !== oldVal.id) {
        resetTimer();
        readTab.activeIndex = 1;
      }
    }
  },
  { deep: true }
);

// 监听 show 属性变化
watch(
  () => props.show,
  (newVal) => {
    if (newVal) {
      if (props.openWith === "auto") {
        readTab.activeIndex = 1;
        resetTimer();
      } else if (props.openWith === "click") {
        readTab.activeIndex = 3;
      }
    }
  }
);

// 监听 openWith 变化
watch(
  () => props.openWith,
  (newVal) => {
    readTab.statusList = readTab.statusList.map((item) => {
      if (item.value === 1) {
        return { ...item, isHide: newVal === "auto" };
      }
      return item;
    });
  },
  { immediate: true }
);

// 组件卸载时清理计时器
onUnmounted(() => {
  if (timer.value) {
    clearInterval(timer.value);
    timer.value = null;
  }
});
</script>
<style scoped lang="scss">

.traffic-light-page {
  width: 100%;
  height: 100%;
  background-color: #fff;
  padding: 20px;
  .operations {
    display: flex;
    align-items: center;
    gap: 10px;
    .operation-item {
      display: flex;
      align-items: center;
      background: rgb(244, 247, 254);
      width: 50%;
      height: 67px;
      padding: 0 14px;
      cursor: pointer;
      user-select: none;
      .left {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 10px;
        text-align: left;
      }
      .right {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .tip {
        color: #9f9fa4;
        font-size: 12px;
      }
    }
  }

  .header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 10px;
    .card-info {
      display: flex;
      align-items: flex-start;
      flex-direction: column;
      .device-id {
        font-size: 18px;
        font-weight: 700;
        color: #383838;
      }
      .text {
        font-size: 16px;
        font-weight: 400;
        color: #242859;
      }
      .report-time {
        font-size: 14px;
        font-weight: 400;
        color: #555555;
      }
    }
    .clock-list {
      display: flex;
      align-items: center;
      gap: 1px;
      color: #242859;
      .clock-item {
        display: inline-block;
        width: 31px;
        height: 32px;
        border-radius: 4px;
        background: #242859;
        font-size: 20px;
        font-weight: 700;
        color: #ffffff;
        text-align: center;
      }
    }
  }
  .traffic-light-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    gap: 10px;
    .item {
      flex: 1;
      height: 130px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      display: flex;
      align-items: center;
      justify-content: space-around;
      .left {
        display: flex;
        align-items: center;
        flex-direction: column;
        gap: 16px;

        .label {
          font-size: 14px;
          font-weight: 400;
          color: #383838;
        }
        .value {
          font-size: 18px;
          font-weight: 700;
          color: #383838;
        }
      }
    }
    .traffic-light-result {
      width: 94px;
      height: 130px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      gap: 16px;
      .label {
        font-size: 14px;
        font-weight: 400;
        color: #383838;
      }
      .value {
        font-size: 18px;
        font-weight: 700;
        color: #383838;
      }
    }
  }
  .image-container {
    .title {
      font-size: 14px;
      font-weight: 400;
      color: #383838;
    }
    .image-box {
      display: flex;
      align-items: center;
      margin-top: 10px;
      width: 102px;
      height: 75px;
      gap: 10px;
      cursor: pointer;
      position: relative;
      .image {
        width: 100%;
        height: 100%;
        border-radius: 4px;
      }

      &::before {
        content: "";
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        width: 32px;
        height: 32px;
        background: url("@/assets/images/zoom.png") no-repeat center center;
        background-size: contain;
        opacity: 0;
        transition: opacity 0.3s ease;
        pointer-events: none;
        z-index: 1;
      }
      &:hover::before {
        opacity: 1;
      }
    }
    .image-box:hover {
      opacity: 0.7;
    }
  }
  .record-info {
    .title {
      font-size: 14px;
      font-weight: 400;
      color: #383838;
    }
    .info-wrapper {
      width: 210px;
      height: 43.25px;
      border-radius: 4px;
      background: rgb(244, 247, 254);
      padding: 11px 13px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 12px;
      font-weight: 400;
      .date {
        color: #9f9fa4;
      }
      .status {
        color: #555555;
      }
    }
  }
}
.button-container {
  background-color: #fff;
  padding: 20px;
}
</style>
