<template>
  <div class="x-radio-button">
    <ul :class="`x-radio-button-list-${props.type}`">
      <li
        v-for="(option, index) in options"
        :key="index"
        @click="disabled || option.disabled ? (e: Event) => e.preventDefault() : onClick(option.value)"
        :class="[{ 'x-radio-button-checked': props.value === option.value }, { disabled: disabled || option.disabled }]"
        :style="getItemStyle(option)"
      >
        <slot
          v-if="$slots.content"
          name="content"
          :option="option"
        ></slot>
        <template v-else>
          <span class="x-radio-button-label">{{ option.label }}</span>
        </template>
      </li>
    </ul>
  </div>
</template>

<script setup lang="ts">
import type { PropType } from "vue";
interface Option {
  label: string;
  value: any;
  disabled?: boolean; // 禁用单个单选器
  color?: string; // 添加自定义颜色属性
  [x: string]: any;
}

const props = defineProps({
  // 以配置形式设置子元素
  options: {
    type: Array<Option>,
    required: true,
  },
  // 用于设置当前选中的值
  value: {
    type: null,
    required: true,
  },
  // 禁选所有子单选器
  disabled: {
    type: Boolean,
    required: false,
  },
  // 单选器间距
  gap: {
    type: Number,
    default: 10,
    required: false,
  },
  type: {
    type: String as PropType<"default" | "tab">,
    default: "default",
  },
});

const emits = defineEmits(["update:value", "change"]);

const onClick = (value: any) => {
  if (value !== props.value) {
    emits("update:value", value);
    emits("change", value);
  }
};

// 添加获取样式的方法
const getItemStyle = (option: Option) => {
  const style: Record<string, string> = { padding: `0 ${props.gap}px` };

  // 如果当前选项被选中且有自定义颜色
  if (props.value === option.value && option.color) {
    style.color = option.color;
  }

  return style;
};
</script>

<style lang="scss" scoped>
.x-radio-button {
  user-select: none;
  // 默认样式
  &-list-default {
    @include sc(14px, #999) {
      border: 1px solid rgb(220, 220, 220);
      border-radius: 4px;
    }
    padding: 5px;
    li {
      display: inline-block;
      cursor: pointer;
    }
    li.x-radio-button-checked {
      color: #5964fb;
    }
    .disabled {
      color: #00000040;
      cursor: not-allowed !important;
    }
    :not(:last-child) {
      border-right: 1px solid #e5e5e5;
    }
  }
  // 选项卡样式
  &-list-tab {
    display: flex;
    background: #f4f7fe;
    li {
      position: relative;
      margin: 5px 10px 0px 10px;
      height: 36px;
      line-height: 36px;
      @include sc(14px, #9f9fa4);
      &:hover {
        color: #5964fb;
        cursor: pointer;
      }
      &.x-radio-button-checked {
        padding: 0 8px;
        @include sc(14px, #242859) {
          font-weight: bold;
          background: #fff;
          box-shadow: 0px -1px 3px 0px rgba(72, 111, 245, 0.14);
        }
        // 梯形两侧
        &:before,
        &:after {
          content: "";
          display: block;
          position: absolute;
          @include wh(10px, 36px);
          background: #fff;
        }
        // 梯形左侧
        &:before {
          top: 0;
          left: -5px;
          transform: skewX(-10deg);
          border-top-left-radius: 4px;
        }
        // 梯形右侧
        &:after {
          top: 0;
          right: -5px;
          transform: skewX(10deg);
          border-top-right-radius: 4px;
        }
      }
      &.disabled:hover {
        cursor: not-allowed;
      }
    }
  }
}
</style>
