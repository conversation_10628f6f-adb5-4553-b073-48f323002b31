stages:
  - deploy
variables:
  IMAGE_NAME: $CI_PROJECT_NAME/$CI_COMMIT_REF_NAME
workflow:
  rules:
    - if: $CI_COMMIT_REF_NAME != 'test' && $CI_COMMIT_REF_NAME != 'prod' && $CI_COMMIT_REF_NAME != 'mini'
      when: never
    - when: always

build_code:
  stage: deploy
  variables:
    GIT_CLEAN_FLAGS: -ffdx -e node_modules/ -e dist/ # git clean 跳过清理目录
    # GIT_CLEAN_FLAGS: none  # git clean 禁用
  cache:
    paths:
      - node_modules/
  image: node:18.16.0
  script:
    - npm i -g pnpm@9 --registry=https://registry.npmmirror.com
    - pnpm -v
    - pnpm install --registry https://registry.npmmirror.com/
    - pnpm build-${CI_COMMIT_REF_NAME}
  artifacts:
    expire_in: 3 mins
    paths:
      - dist/
  rules:
    - when: manual
    # - if: $CI_COMMIT_REF_NAME == 'test' || $CI_COMMIT_REF_NAME == 'prod'
    #   when: manual
    # - when: always
  tags:
    - alpine


.build_image: &build_image_script
  - docker build -t="$IMAGE_URL" -f ./Dockerfile .

.build_image_prod: &build_image_prod_script
  - docker build -t="$IMAGE_URL" -f ./DockerfileProd .

.deploy_image: &deploy_image_script
  - docker tag $IMAGE_URL nginx_cloud:dockerfile
  - docker save -o nginx_cloud.tar nginx_cloud:dockerfile
  - sshpass -p $PASSWORD scp -o StrictHostKeyChecking=no ./nginx_cloud.tar $USERNAME@$DEPLOY_HOST:/home/<USER>/
  - rm nginx_cloud.tar -f
  - docker rmi nginx_cloud:dockerfile -f

  - sshpass -p $PASSWORD ssh -o StrictHostKeyChecking=no $USERNAME@$DEPLOY_HOST '
    docker ps | grep nginx_cloud && docker stop nginx_cloud;
    docker ps -a | grep nginx_cloud && docker rm nginx_cloud;
    docker rmi nginx_cloud:dockerfile -f;
    docker load < nginx_cloud.tar;
    docker run -d -p 80:80 -p 443:443 -p 8443:8443 --restart=always --name nginx_cloud nginx_cloud:dockerfile;
    rm /home/<USER>/nginx_cloud.tar -f;'

deploy_code:
  stage: deploy
  needs: ["build_code"]
  script:
    - export VERSION=${CI_COMMIT_TIMESTAMP:0:10}-${CI_COMMIT_TIMESTAMP:11:2}.${CI_COMMIT_TIMESTAMP:14:2}.${CI_COMMIT_TIMESTAMP:17:2}
    - export IMAGE_URL=$HOST_HARBOR/$IMAGE_NAME-$VERSION:dockerfile

    - echo "开始构建容器服务：$IMAGE_NAME-$VERSION"

    - if [ $CI_COMMIT_REF_NAME == 'prod' ];then
    - *build_image_prod_script
    - export DEPLOY_HOST=$HOST_PROD
    - *deploy_image_script
    # - docker push $IMAGE_URL

    - elif [ $CI_COMMIT_REF_NAME == 'test' ];then
    - *build_image_script
    - export DEPLOY_HOST=$HOST_TEST
    - *deploy_image_script

    - elif [ $CI_COMMIT_REF_NAME == 'dev' ];then
    - docker ps | grep nginx_cloud && docker stop nginx_cloud
    - docker ps -a | grep nginx_cloud && docker rm nginx_cloud
    - IMAGE_IDS=$(docker images | grep "cloud-platform-frontend" | awk '{print $3}') && [ -n "$IMAGE_IDS" ] && docker rmi $IMAGE_IDS -f
    - *build_image_script
    - docker run -d -p 8080:80 --restart=always --name nginx_cloud $IMAGE_URL

    - elif [ $CI_COMMIT_REF_NAME == 'mini' ];then
    - *build_image_script
    - export DEPLOY_HOST=$HOST_TEST
    - docker save -o mini_nginx_cloud.tar $IMAGE_URL
    - sshpass -p $PASSWORD scp -o StrictHostKeyChecking=no ./mini_nginx_cloud.tar $USERNAME@$DEPLOY_HOST:/home/<USER>/
    - rm mini_nginx_cloud.tar -f

    - fi

    - echo "容器服务构建成功：$IMAGE_NAME-$VERSION"
    # - chmod a+x ./cli/ding_deploy.sh
    # - ./cli/ding_deploy.sh
  when: on_success
  tags:
    - front_shell
