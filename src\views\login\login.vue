<template>
  <section class="login">
    <div class="login-left"></div>
    <!-- <iframe
      src="https://yun.chengshizhiguang.com:8443/screen?token=586e57cf5afc557be0caec74df67e25c&proId=173447807682740220&hiddenTop=true"
      frameborder="0"
      style="
        z-index: 1000;
        width: 1200px;
        height: 100%;
        position: absolute;
        top: 0px;
        left: 300px;
      "
    ></iframe> -->
    <div class="login-right">
      <div class="login-form">
        <div class="login-form-title">
          <img
            src="@/assets/images/logo.png"
            alt=""
          />
          <span>{{ $t("cszg") }}</span>
        </div>
        <div class="login-form-content">
          <div class="content-title">{{ $t("accountLogin") }}</div>
          <x-form :model="form">
            <x-form-item>
              <x-input
                v-inputfocus
                class="content-username"
                v-model:value="form.username"
                prefix="username"
                :placeholder="$t('enterAccount')"
                @keydown.enter="isValidate && loginHandle()"
              />
            </x-form-item>
            <x-form-item>
              <x-input
                class="content-password"
                v-model:value="form.password"
                type="password"
                prefix="password"
                :placeholder="$t('enterPassword')"
                @keydown.enter="isValidate && loginHandle()"
              />
            </x-form-item>
            <x-form-item>
              <div class="content-code">
                <x-input
                  class="code-input"
                  v-model:value="form.code"
                  :maxlength="4"
                  :placeholder="$t('enterCode')"
                  @keydown.enter="isValidate && loginHandle()"
                />
                <img
                  :src="codeImageUrl"
                  alt=""
                />
                <div
                  class="code-refresh"
                  @click="_uuid = uuid()"
                >
                  <x-icon
                    class="refresh-icon"
                    name="refresh"
                  />
                  <span>{{ $t("refresh") }}</span>
                </div>
              </div>
            </x-form-item>
            <x-form-item>
              <x-button
                :text="$t('login')"
                :disabled="!isValidate"
                :class="['content-button', { disabled: !isValidate }]"
                @click="loginHandle"
              />
            </x-form-item>
          </x-form>
          <div class="content-opera">
            <x-checkbox
              :text="$t('rememberPassword')"
              v-model:checked="form.remember"
            />
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, computed } from "vue";
import { useRouter } from "vue-router";
import { uuid, debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { useMainStore } from "@/stores/main";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import xButton from "@/components/x-button.vue";
import { setLocalStorage, getLocalStorage } from "@/assets/ts/storage";
import { inputFocus } from "@/assets/ts/directives";
import Message from "@/components/x-message";
const $t = i18nSimpleKey("login");

const router = useRouter();
const vInputfocus = inputFocus;

const _uuid = ref(uuid());
const codeImageUrl = computed(() => {
  return import.meta.env.VITE_BASE_URL + "/captcha.jpg?uuid=" + _uuid.value;
});
const isValidate = computed(() => {
  return form.username && form.password && form.code && form.code.length == 4;
});

const username = getLocalStorage("username");
const password = getLocalStorage("password");
const form = reactive({
  username: username || "",
  password: password || "",
  code: "",
  remember: Boolean(username && password),
});
const { login } = useMainStore();

const loginHandle = debounce(() => {
  if (form.username && form.password) {
    login({
      userAccount: form.username,
      password: form.password,
      uuid: _uuid.value,
      captcha: form.code,
    })
      .then(() => {
        setLocalStorage("username", form.remember ? form.username : "");
        setLocalStorage("password", form.remember ? form.password : "");
        Message("success", $t("loginSuccess"));
        router.push({ name: "main" });
      })
      .catch(() => {
        form.code = "";
        _uuid.value = uuid();
      });
  }
});
</script>

<style lang="scss" scoped>
.login {
  display: flex;
  @include wh(100%);
  &-left {
    flex: 2;
    @include bis("@/assets/images/login_gif.gif");
  }
  &-right {
    @include ct-f;
    flex: 1;
    background-color: #fafcff;
    .login-form {
      @include wh(340px, 414px) {
        padding: 28px 50px 0 50px;
        border-radius: 10px;
      }
      &-title {
        animation-duration: 1s;
        animation-name: bounce;
        transform-origin: center bottom;
        @include ct-f(y);
        @include wh(210px, 30px) {
          margin-left: auto;
          margin-right: auto;
        }
        img {
          @include wh(20px);
        }
        span {
          padding-left: 8px;
          @include sc(20px, #5964fb) {
            line-height: 30px;
            font-weight: bolder;
          }
        }
      }
      &-content {
        width: 100%;
        margin-top: 30px;
        .content-title {
          color: rgb(94, 94, 94);
        }
        .content-username,
        .content-password,
        .content-code {
          margin-top: 16px;
        }
        .content-code {
          display: flex;
          @include wh(100%, 32px);
          .code-input {
            width: 112px;
          }
          img {
            @include wh(90px, 100%) {
              margin-left: 9px;
            }
          }
          .code-refresh {
            cursor: pointer;
            flex: 1;
            @include ct-f(y) {
              flex-direction: column;
            }
            height: 100%;
            .refresh-icon {
              display: block;
            }
            span {
              @include sc(10px, rgb(89, 100, 251));
            }
          }
        }
        .content-button {
          cursor: pointer;
          @include wh(100%, 36px) {
            margin-top: 45px;
            background-color: rgb(89, 100, 251);
            border-radius: 4px;
            color: #fff;
            text-align: center;
            line-height: 36px;
            &:hover {
              animation-duration: 1.3s;
              animation-name: heartBeat;
              animation-timing-function: ease-in-out;
            }
          }
          &.disabled {
            background-color: #eeeeee !important;
            &:hover {
              animation: none;
            }
          }
        }
        .content-opera {
          margin-top: 8px;
        }
      }
    }
  }
}
</style>
