<template>
  <section class="pro-user-manage" ref="projectPersonRef">
    <div class="pro-user-manage-top">
      <div class="top-title">{{ $t("proUserManage") }}</div>
    </div>
    <div class="pro-user-manage-middle">
      <div class="middle-left">
        <x-input
          v-model:value="searchForm.generalQueryName"
          :placeholder="$t('PEnterAccName')"
          suffix="input_search"
          style="width: 240px; margin-right: 16px"
        />
        <x-select
          v-model:value="searchForm.proName"
          :options="formOptions.project"
          :popupContainer="projectPersonRef"
          :placeholder="$t('allProject')"
          style="width: 320px"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:proUser:list')"
          type="blue"
          :text="$t('search')"
          @click="reSearch"
          style="margin-right: 12px"
        />
        <x-button type="green" :text="$t('reset')" @click="resetSearchForm" />
      </div>
    </div>
    <div class="pro-user-manage-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 手机号 -->
        <template #mobile="{ record }">
          {{ formatPhoneNum(record.mobile) }}
        </template>
        <!-- 项目名 -->
        <template #proName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-project-popover"
            :container="projectPersonRef"
          >
            {{ record.proName }}
            <template #content>
              <div class="pro-user-manage-table-project-hover-popover">
                {{ record.proName }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 操作 -->
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:proUser:saveVehAuth')"
                @click="openCarAuth(record)"
              >
                {{ $t("carAuth") }}
              </span>
              <span
                v-if="permitList.includes('sys:proUser:info')"
                @click="openDetail(record)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:proUser:pwdReset')"
                @click="resetPwd(record)"
                style="color: #e24562"
              >
                {{ $t("resetPwd") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <CarAuth
        v-model:show="carDrawer.show"
        :userId="carDrawer.userId"
        :id="carDrawer.id"
        @confirm="carDrawer.confirm"
      />
      <Detail v-model:show="userDetail.show" :id="userDetail.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, createVNode } from "vue";
import {
  getProUserList,
  projectList,
  resetProUserPassword,
} from "@/services/api";
import type { PageSizeType } from "@/components/types";
import { formatPhoneNum, i18nSimpleKey } from "@/assets/ts/utils";
import { useMainStore } from "@/stores/main";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xIcon from "@/components/x-icon.vue";
import Detail from "./components/detail.vue";
import CarAuth from "./components/carAuth.vue";
const $t = i18nSimpleKey("proUserManage");

const {
  userInfo: { permitList },
} = useMainStore();

const projectPersonRef = ref<any>();

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "60",
    },
    {
      key: "userName",
      title: $t("userName"),
      width: "100",
    },
    {
      key: "userAccount",
      title: $t("account"),
      width: "120",
    },
    {
      key: "mobile",
      title: $t("mobile"),
      width: "120",
      slots: "mobile",
    },
    {
      key: "proName",
      title: $t("proName"),
      width: "160",
      slots: "proName",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "200",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const searchForm = reactive({
  proName: "",
  generalQueryName: "",
});

const formOptions = reactive({
  project: [] as { label: string; value: string }[],
});

// 重新搜索
const reSearch = () => {
  table.pagination["current"] = 1;
  searchPersonList();
};

// 查询
const searchPersonList = async () => {
  table.loading = true;
  const { totalCount, list } = await getProUserList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    proName: searchForm.proName,
    generalQueryName: searchForm.generalQueryName,
  });
  table.pagination.total = totalCount || 0;
  table.dataSource = [
    ...(list
      ? list.map((item: any, index: number) => {
          return {
            id: item.id.toString(),
            orderNumber: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchPersonList();
};

// 重置
const resetSearchForm = () => {
  searchForm.proName = "";
  searchForm.generalQueryName = "";
  searchPersonList();
};

(async () => {
  searchPersonList();
  const options = (await projectList()).proNames || [];
  formOptions.project = options.map((proName: any) => ({
    label: proName!,
    value: proName!,
  }));
})();

// 查看详情
const userDetail = reactive({
  show: false,
  id: "0",
});
const openDetail = (record: any) => {
  userDetail.id = String(record.id);
  userDetail.show = true;
};

// 车辆授权
const carDrawer = reactive({
  show: false,
  userId: 0,
  id: "",
  confirm: () => searchPersonList(),
});
const openCarAuth = (record: any) => {
  carDrawer.userId = record.userId;
  carDrawer.id = record.id;
  carDrawer.show = true;
};

// 重置密码
const resetPwd = (record: any) => {
  xModal.confirm({
    title: $t("sureToResetPWD"),
    content: (
      <div>
        {$t("account")}：{record.userAccount}
      </div>
    ),
    confirm() {
      return resetProUserPassword(String(record.userId)).then((res) => {
        xModal.success({
          width: "345px",
          title: `${record.userAccount}${$t("resetSuccess")}！`,
          content: (
            <div>
              <span style="margin-right: 10px;">
                {$t("newPWD")}：{res.pwd}
              </span>
              <div
                style="cursor: pointer;display:inline-block;"
                onClick={() => copyPassword(res.pwd)}
              >
                {createVNode(xIcon, {
                  name: "copy",
                  width: "16px",
                  height: "16px",
                })}
              </div>
            </div>
          ),
        });
        searchPersonList();
      });
    },
  });
};
const copyPassword = (pwdText: string) => {
  if (window.isSecureContext && navigator.clipboard) {
    navigator.clipboard.writeText(pwdText).then(() => {
      Message("success", $t("copySuccess"));
    });
  } else {
    unsecuredCopyToClipboard(pwdText);
  }
};
const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    document.execCommand("copy");
    Message("success", $t("copySuccess"));
  } catch (err) {
    console.error("Unable to copy to clipboard", err);
  }
  document.body.removeChild(textArea);
};
</script>

<style lang="scss" scoped>
.pro-user-manage-table-project-hover-popover {
  padding: 0 6px;
  height: 32px;
  line-height: 32px;
}
.pro-user-manage {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, #242859) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
    .table-project-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          margin-right: 16px;
          color: #4277fe;
          cursor: pointer;
        }
      }
    }
  }
}
</style>
