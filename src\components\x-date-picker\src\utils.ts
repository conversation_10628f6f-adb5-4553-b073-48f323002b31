import {
  getCurrPageDays,
  safeGetTime,
  getNextYearMonth,
  getPrevYearMonth,
  formatDateTime,
} from "@/assets/ts/dateTime";

import type { DateType } from "@/assets/ts/dateTime";

/**
 * 解析时间格式化占位符
 */
export function parseDateFormat(format: string) {
  // 匹配
  const matchs = format.match(/YYYY|YY|MM|M|DD|D|HH|H|hh|h|mm|m|ss|s/g) || [];

  // 结果
  const result = {
    year: matchs.includes("YYYY") || matchs.includes("YY"),
    month:
      matchs.includes("MMMM") ||
      matchs.includes("MMM") ||
      matchs.includes("MM") ||
      matchs.includes("M"),
    day: matchs.includes("DD") || matchs.includes("D"),
    hour:
      matchs.includes("HH") ||
      matchs.includes("H") ||
      matchs.includes("hh") ||
      matchs.includes("h"),
    minute: matchs.includes("mm") || matchs.includes("m"),
    second: matchs.includes("ss") || matchs.includes("s"),
    year_month: false,
    year_month_day: false,
    hour_minute_second: false,
    hour_minute: false,
  };

  // 日不存在，分，秒也设置为不存在
  if (result.day === false) {
    result.hour = false;
    result.minute = false;
    result.second = false;
  }

  // 时不存在，分，秒也设置为不存在
  if (result.hour === false) {
    result.minute = false;
    result.second = false;
  }

  // 分不存在，秒也设置为不存在
  if (result.minute === false) {
    result.second = false;
  }

  // 年月存在
  result.year_month = result.year && result.month;

  // 年月日都存在
  result.year_month_day = result.year && result.month && result.day;

  // 时分存在
  result.hour_minute = result.hour && result.minute;

  // 时分秒存在
  result.hour_minute_second = result.hour && result.minute && result.second;

  return result;
}

/**
 * 解析日期  返回 '2024-07-26 03:04:08' | '2024-07-26' | '...'
 */
export function parseDate(date: string) {
  if (!date) return date;

  const result = {
    year: undefined as number | undefined,
    month: undefined as number | undefined,
    day: undefined as number | undefined,
    hour: undefined as number | undefined,
    minute: undefined as number | undefined,
    second: undefined as number | undefined,
  };

  // 赋值的键
  let key = "";

  for (let i = 0; i < date.length; i++) {
    const char = date[i];
    const num = parseInt(char);
    if (Number.isNaN(num)) {
      key = "";
      continue;
    } else {
      if (result.year === undefined) {
        result.year = num;
        key = "year";
        continue;
      } else if (key === "year") {
        result.year = Number(result.year + "" + num);
        if (result.year.toString().length === 4) {
          key = "";
        }
        continue;
      } else if (result.month === undefined) {
        result.month = num;
        key = "month";
        continue;
      } else if (key === "month") {
        result.month = Number(result.month + "" + num);
        key = "";
        continue;
      } else if (result.day === undefined) {
        result.day = num;
        key = "day";
        continue;
      } else if (key === "day") {
        result.day = Number(result.day + "" + num);
        key = "";
        continue;
      } else if (result.hour === undefined) {
        result.hour = num;
        key = "hour";
        continue;
      } else if (key === "hour") {
        result.hour = Number(result.hour + "" + num);
        key = "";
        continue;
      } else if (result.minute === undefined) {
        result.minute = num;
        key = "minute";
        continue;
      } else if (key === "minute") {
        result.minute = Number(result.minute + "" + num);
        key = "";
        continue;
      } else if (result.second === undefined) {
        result.second = num;
        key = "second";
        continue;
      } else if (key === "second") {
        result.second = Number(result.second + "" + num);
        key = "";
        continue;
      }
    }
  }

  let defaultFromatDate = "";

  if (result.year === undefined) {
    defaultFromatDate = "1970";
  } else {
    defaultFromatDate = result.year.toString();
  }

  if (result.month === undefined) {
    defaultFromatDate = defaultFromatDate + "-" + "01";
  } else {
    defaultFromatDate =
      defaultFromatDate + "-" + result.month.toString().padStart(2, "0");
  }

  if (result.day === undefined) {
    defaultFromatDate = defaultFromatDate + "-" + "01";
  } else {
    defaultFromatDate =
      defaultFromatDate + "-" + result.day.toString().padStart(2, "0");
  }

  if (result.hour !== undefined) {
    defaultFromatDate =
      defaultFromatDate + " " + result.hour.toString().padStart(2, "0");
  }

  if (result.minute !== undefined) {
    defaultFromatDate =
      defaultFromatDate + ":" + result.minute.toString().padStart(2, "0");
  }

  if (result.second !== undefined) {
    defaultFromatDate =
      defaultFromatDate + ":" + result.second.toString().padStart(2, "0");
  }
  return defaultFromatDate;
}

/**
 * 得到年月
 */
export function getYearMonth({ data, value, partial, year, month, type }: any) {
  if (type === "month") {
    if (value && safeGetTime(value) > 0) {
      const dateInstance = new Date(value);
      data.year = dateInstance.getFullYear();
      data.month = dateInstance.getMonth();
    } else {
      data.year = year;
      data.month = month;
    }
  } else {
    if (value && safeGetTime(value) > 0) {
      // 表单值存在
      const dateInstance = new Date(value);
      data.year = dateInstance.getFullYear();
      data.month = dateInstance.getMonth();
    } else if (partial === "start") {
      if (year && month !== undefined) {
        data.year = year;
        data.month = month;
      } else if (year) {
        data.year = year;
      } else if (month !== undefined) {
        data.month = month;
      }
      return;
    } else if (partial === "end") {
      // if (year && month !== undefined) {
      //   const [prevYear, prevMonth] = getPrevYearMonth(year, month);
      //   data.year = prevYear;
      //   data.month = prevMonth;
      // } else if (year) {
      //   const [prevYear, prevMonth] = getPrevYearMonth(year, data.month);
      //   data.year = prevYear;
      //   data.month = prevMonth;
      // } else if (month !== undefined) {
      //   const [prevYear, prevMonth] = getPrevYearMonth(data.year, month);
      //   data.year = prevYear;
      //   data.month = prevMonth;
      // }
      if (year && month !== undefined) {
        data.nextYear = year;
        data.nextMonth = month;
      } else if (year) {
        data.nextYear = year;
      } else if (month !== undefined) {
        data.nextMonth = month;
      }
      return;
    } else if (year && month !== undefined) {
      data.year = year;
      data.month = month;
    } else if (year) {
      data.year = year;
    } else if (month !== undefined) {
      data.month = month;
    } else {
      // 默认值
      const dateInstance = new Date();
      data.year = dateInstance.getFullYear();
      data.month = dateInstance.getMonth();
    }

    // 下一年月 - 仅用于范围日期
    const [nextYear, nextMonth] = getNextYearMonth(data.year, data.month);
    data.nextYear = nextYear;
    data.nextMonth = nextMonth;
  }
}

/**
 * 生成月份
 */
export const genMonths = ({
  data,
  year,
  month,
  disabledDate,
}: {
  data: any;
  year: number;
  month: number;
  disabledDate: any;
}) => {
  const currentDate = new Date();
  const currentYear = currentDate.getFullYear();
  const currentMonth = currentDate.getMonth();

  const months = [[], [], []] as MonthType[][];

  for (let i = 0; i < 12; i++) {
    const timestamp = new Date(year, i, 1).getTime();
    let type: "prev" | "curr" | "next" = "curr";

    if (year < currentYear || (year === currentYear && i < currentMonth)) {
      type = "prev";
    } else if (
      year > currentYear ||
      (year === currentYear && i > currentMonth)
    ) {
      type = "next";
    }
    const monthData: MonthType = {
      value: i,
      type: type,
      timestamp: timestamp,
    };
    if (disabledDate && typeof disabledDate === "function") {
      monthData.disabled = disabledDate(timestamp);
    }

    const index = Math.floor(i / 4);
    months[index].push(monthData);
  }

  data.months = months;
};

/**
 * 生成日期
 */
export function genDays({ data, year, month, disabledDate }: any) {
  const days = [[], [], [], [], [], []] as DateType[][];

  let i = 0;

  getCurrPageDays(year, month).forEach((tds, index) => {
    // 是否禁用
    disabledDate && (tds.disabled = disabledDate(tds));

    if ((index + 1) % 7 === 0) {
      days[i].push(tds);
      i++;
    } else {
      days[i].push(tds);
    }
  });

  data.days = days;
}

/**
 * 值转换为日期时间
 */
export function valueToDateTime({ data, value, type = "date" }: any) {
  if (value) {
    if (type === "month") {
      const [year, month] = value.split("-");
      data.date = safeGetTime(`${year}-${month}-01`);
    } else {
      const [date, time] = parseDate(value)
        .split(" ")
        .filter((v: string) => v);
      date && (data.date = safeGetTime(date));
      time && (data.time = time);
    }
  } else {
    // 情况：点击清除
    data.date = 0;
  }
}

/**
 * 值转换为范围
 */
export function valueToRange({ data, value, format, type = "date" }: any) {
  if (Array.isArray(value)) {
    data.range = value.map((item) => {
      if (type === "month") {
        const [year, month] = item.split("-");
        return safeGetTime(`${year}-${month}-01`) || 0;
      } else {
        return safeGetTime(formatDateTime(parseDate(item), format)) || 0;
      }
    });
  }
}

/**
 * 选择中范围
 */
export function valueToSelecting(props: any, data: any, type = "date") {
  if (
    !props.previewValue ||
    !Array.isArray(props.value) ||
    props.value.filter((v: string) => v).length < 1
  ) {
    if (!data.ranging.includes(0)) {
      data.ranging = [0, 0];
    }
    return;
  }

  let previewstamp;
  let startstamp = 0;
  let endstamp = 0;
  if (type === "month") {
    previewstamp = safeGetTime(`${props.previewValue}-01`);
    [startstamp, endstamp] = props.value.map((item: string) =>
      item ? safeGetTime(`${item}-01`) : 0
    );
  } else {
    previewstamp = safeGetTime(
      formatDateTime(parseDate(props.previewValue), data.dateFormat)
    );
    [startstamp, endstamp] = props.value.map((item: string) =>
      item ? safeGetTime(formatDateTime(parseDate(item), data.dateFormat)) : 0
    );
  }

  if (props.focus === 0 && previewstamp <= endstamp) {
    data.ranging = [previewstamp, endstamp];
  } else if (props.focus === 1 && previewstamp >= startstamp) {
    data.ranging = [startstamp, previewstamp];
  } else {
    data.ranging = [0, 0];
  }
}
