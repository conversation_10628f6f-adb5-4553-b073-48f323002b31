<template>
  <section
    class="car-filter"
    ref="carFilterRef"
  >
    <div class="car-filter-search">
      <div class="car-filter-search-left">
        <x-input
          v-model:value="searchModel.vehicleNo"
          placeholder="请输入车牌号"
          suffix="input_search"
          style="width: 200px"
        />
        <x-input
          v-model:value="searchModel.vin"
          placeholder="请输入VIN码"
          suffix="input_search"
          style="width: 200px"
        />
        <x-select
          v-model:value="searchModel.carStatus"
          :options="searchOptions.entList"
          :popupContainer="carFilterRef"
          placeholder="请选择车辆状态"
          style="width: 300px"
        />
        <x-input
          v-model:value="searchModel.entName"
          placeholder="请输入企业名"
          suffix="input_search"
          style="width: 200px"
        />
        <x-input
          v-model:value="searchModel.proNmae"
          placeholder="请输入项目名"
          suffix="input_search"
          style="width: 200px"
        />
        <x-input
          v-model:value="searchModel.proAreaName"
          placeholder="请输入区域名"
          suffix="input_search"
          style="width: 200px"
        />
      </div>
      <div class="car-filter-search-right">
        <x-button
          type="blue"
          :text="'搜索'"
          @click="handleSearch"
          style="margin-right: 12px"
        />
        <x-button
          type="green"
          :text="'重置'"
          @click="handleReset"
        />
      </div>
    </div>
    <div class="car-filter-table">
      <x-table
        :cols="table.cols"
        :dataSource="table.data"
        :pagination="table.pagination"
        :loading="table.loading"
        :customRow="customRowHandler"
        @change="tableChange"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { defineComponent, reactive, ref, type PropType } from "vue";
import XButton from "@/components/x-button.vue";
import XInput from "@/components/x-input.vue";
import XSelect, { type OptionsType } from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
// import XPopover from "@/components/x-popover.vue";
import type { PageSizeType } from "@/components/types";
import { useMainStore } from "@/stores/main";

const props = defineProps({
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
  show: Boolean,
  activeId: String,
});
const emit = defineEmits(["update:show", "update:activeId"]);

const socket = useMainStore().socket;
const carFilterRef = ref<HTMLElement>();

const searchModel = reactive({
  vehicleNo: "",
  vin: "",
  carStatus: "",
  entName: "",
  proNmae: "",
  proAreaName: "",
});

const searchOptions = reactive({
  entList: [] as OptionsType,
  proList: [] as OptionsType,
  proAreaList: [] as OptionsType,
});

const handleSearch = () => {
  table.pagination.current = 1;
  fetchTableData();
};
const handleReset = () => {
  searchModel.vehicleNo = "";
  searchModel.vin = "";
  searchModel.carStatus = "";
  searchModel.entName = "";
  searchModel.proNmae = "";
  searchModel.proAreaName = "";
  handleSearch();
};

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: "序号",
      width: "40",
    },
    {
      key: "vehicleNo",
      title: "车牌号",
      width: "60",
    },
    {
      key: "vin",
      title: "VIN",
      width: "200",
    },
    {
      key: "状态",
      title: "carStats",
      width: "60",
    },
    {
      key: "企业",
      title: "entName",
      width: "60",
    },
    {
      key: "proName",
      title: "项目",
      width: "80",
    },
    {
      key: "proAreaName",
      title: "区域",
      width: "80",
    },
  ],
  data: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

const fetchTableData = async () => {
  table.loading = true;
  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
  };
  table.loading = false;
  socket || props;
};
// 分页
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  fetchTableData();
};
const customRowHandler = (record: any) => {
  return {
    onClick: () => {
      emit("update:activeId", record.id);
    },
  };
};
</script>
<script lang="ts">
export default defineComponent({
  name: "car-filter",
});
</script>
<style lang="scss">
.car-filter {
  cursor: auto;
}
</style>
