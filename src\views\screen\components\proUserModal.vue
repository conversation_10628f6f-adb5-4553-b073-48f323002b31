<template>
  <x-modal
    title="项目人员"
    :visible="props.show"
    @update:visible="updateVisible"
    width="1045px"
    height="55vh"
    :bodyStyle="{ padding: '0 30px' }"
  >
    <div class="content">
      <div class="content-top">
        <template v-for="(item, index) in tabs.config" :key="index">
          <div
            class="tab"
            :class="{ active: item.value === tabs.active }"
            @click="changeTab(item.value)"
          >
            {{ item.label }}
          </div>
        </template>
      </div>
      <div class="content-bottom">
        <x-table
          :cols="table.cols"
          :dataSource="table.dataSource"
          :customRow="customRowHandler"
        >
          <template #image="{ record, recordIndex }">
            <img
              class="table-image"
              :src="record.url"
              alt=""
              :style="{
                cursor: record.picError ? 'default' : 'pointer',
              }"
              @error="handleImgError(recordIndex)"
              @click="!record.picError && showImage(record)"
            />
          </template>
          <template #status="{ record }">
            <div class="table-status">
              <span
                class="status-dot"
                :class="record.status ? 'green' : 'gray'"
              ></span>
              <span>{{ record.status ? "在线" : "离线" }}</span>
            </div>
          </template>
        </x-table>
      </div>
    </div>
  </x-modal>
  <x-image
    :visible="image.show"
    :src="image.url"
    @cancel="image.show = false"
  />
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import xModal from "./x-modal.vue";
import xTable from "./x-table.vue";
import xImage from "@/components/x-image.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  info: {
    type: Array,
    required: true,
  },
});

const tabs = reactive({
  config: [
    {
      label: "全部",
      value: "all",
    },
    {
      label: "在线",
      value: "online",
    },
    {
      label: "离线",
      value: "offline",
    },
  ],
  active: "all",
});

const table = reactive({
  cols: [
    {
      key: "orderId",
      title: "序号",
      width: "20",
    },
    {
      title: "头像",
      width: "40",
      slots: "image",
    },
    {
      key: "userName",
      title: "姓名",
      width: "40",
    },
    {
      title: "状态",
      width: "40",
      slots: "status",
    },
    {
      key: "areaName",
      title: "所在区域",
      width: "70",
    },
  ],
  dataSource: [] as any[],
});

const customRowHandler = (record: any) => {
  return {
    onClick: () => {
      emits("clickUser", record.userName);
    },
  };
};

const emits = defineEmits(["update:show", "clickUser"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

const image = reactive({
  show: false,
  url: "",
});
const showImage = (info: any) => {
  image.show = true;
  image.url = info.url || "";
};

const handleImgError = (index: number) => {
  table.dataSource[index].url = new URL(
    "@/assets/images/screen_img_error.png",
    import.meta.url
  ).href;
  table.dataSource[index].picError = true;
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      tabs.active = "all";
      updateDataSource();
    }
  }
);

const updateDataSource = () => {
  let filteredInfo = props.info;
  if (tabs.active === "online") {
    filteredInfo = props.info.filter((item: any) => item.status);
  } else if (tabs.active === "offline") {
    filteredInfo = props.info.filter((item: any) => !item.status);
  }
  table.dataSource = filteredInfo.map((item: any, index) => {
    return {
      orderId: (index + 1).toString().padStart(3, "0"),
      url: item.userPicture,
      ...item,
    };
  });
};

const changeTab = (value: string) => {
  tabs.active = value;
  updateDataSource();
};
</script>

<style lang="scss" scoped>
.content {
  padding: 0 10px;
  height: 40vh;
  overflow-y: auto;
  &-top {
    @include ct-f(y);
    @include sc(1.4vh, rgba(255, 255, 255, 0.7));
    margin-bottom: 1vh;
    .tab {
      @include ct-f;
      @include wh(90px, 3.5vh);
      cursor: pointer;
      position: relative;
      z-index: 1;
      &.active {
        color: rgb(221, 229, 250);
        &::before {
          content: "";
          position: absolute;
          top: 0;
          left: 50%;
          transform: translateX(-50%);
          @include wh(100%, 100%);
          background: linear-gradient(
            0deg,
            rgba(11, 16, 54, 0.8) 17.095%,
            rgba(19, 21, 42, 0.67) 62.989%,
            rgba(21, 35, 64, 0.66) 100%
          );
          clip-path: polygon(10% 0%, 90% 0%, 100% 100%, 0% 100%);
          border-top-left-radius: 20px;
          border-top-right-radius: 20px;
          border-top: 1px solid rgb(147, 166, 255);
          z-index: -1;
        }
        &::after {
          content: "";
          position: absolute;
          bottom: -0.1vh;
          @include wh(100%, 1vh);
          background: url("@/assets/images/screen_tab_bottom.png") no-repeat
            center center;
          background-size: cover;
          z-index: 0;
        }
      }
    }
  }
  &-bottom {
    height: calc(100% - 6vh);
    .table-image {
      @include wh(35px, 30px);
      border-radius: 100%;
    }
    .table-status {
      display: flex;
      align-items: center;
      .status-dot {
        @include wh(8px, 8px);
        border-radius: 50%;
        margin-right: 5px;
        &.green {
          background-color: rgb(30, 227, 168);
        }
        &.gray {
          background-color: rgb(161, 162, 165);
        }
      }
    }
  }
}
</style>
