<template>
  <section class="water-percent">
    <div
      v-show="props.percent > 0"
      :class="[
        'water-percent-content',
        props.offline ? 'offline' : showWaterWarn(props.percent) ? 'warn' : '',
      ]"
      :style="centerStyle"
    >
      <div class="water-percent-content-top"></div>
    </div>
    <x-icon
      v-if="showIcon"
      class="water-percent-icon"
      name="map_water_icon"
      width="12"
      height="12"
    />
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { showWaterWarn } from "@/services/wsconfig";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  offline: {
    type: Boolean,
    default: () => false,
  },
  showIcon: {
    type: Boolean,
    default: () => false,
  },
});
const centerStyle = computed(
  () =>
    `
      height: ${(props.percent / 100) * 29}px;
    `
);
</script>

<style lang="scss" scoped>
.water-percent {
  overflow: hidden;
  position: relative;
  @include wh(53px, 60px) {
    @include bis("@/assets/images/map_water.png");
  }
  &-icon {
    position: absolute;
    @include ct-p(both) {
      margin-top: 3px;
    }
  }

  &-content {
    position: absolute;
    left: 0;
    bottom: 7px;
    width: 53px;
    transition: height 0.6s ease-in-out;
    background: linear-gradient(
      90deg,
      rgba(47, 187, 149, 1),
      rgba(39, 178, 142, 1) 16.031%,
      rgba(78, 217, 176, 1) 30.534%,
      rgba(71, 210, 169, 1) 100%
    );
    &-top {
      position: absolute;
      top: -8px;
      left: 0;
      @include wh(128.2px, 9px);
      @include bis("@/assets/images/map_water_top.png");
      animation-duration: 6s;
      animation-name: water;
      animation-iteration-count: infinite;
      animation-timing-function: linear;
      @keyframes water {
        0% {
          transform: translate(0, 0);
        }
        100% {
          transform: translate(calc(53px - 100%), 0);
        }
      }
    }

    &.warn {
      background: linear-gradient(
        90deg,
        rgba(244, 21, 21, 1),
        rgba(223.13, 20.28, 20.28, 1) 18.085%,
        rgba(251.58, 90.34, 90.34, 1) 28.191%,
        rgba(253.86, 61.16, 61.16, 1) 76.064%,
        rgba(244, 21, 21, 1) 100%
      );
      .water-percent-content-top {
        @include bis("@/assets/images/map_water_top_warn.png");
      }
    }
    &.offline {
      background: linear-gradient(
        90deg,
        rgb(128, 131, 143),
        rgba(33, 35, 59, 0.74) 24.427%,
        rgb(255, 255, 255) 50.382%,
        rgb(164, 164, 164) 75.573%,
        rgb(144, 143, 151) 100%
      );
      .water-percent-content-top {
        background: none;
      }
    }
  }
}
</style>
