import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import xPagination from "@/components/x-pagination.vue";

const baseProps = {
  total: 30,
  current: 2,
  pageSize: 10
}

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(xPagination,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 total + pageSize', () => {
    const wrapper = shallowMount(xPagination,{
      props: { ...baseProps },
    });
    expect(wrapper.find('.x-pagination-total').text()).toBe(`共${baseProps.total}条`);
    expect(wrapper.vm.pageSize.value).toBe(`${baseProps.pageSize}`);
  });
});

describe('Events', () => {
  it('点击 上一页 / 下一页 / 数字按钮 后检查current', () => {
    const wrapper = shallowMount(xPagination,{
      props: { ...baseProps },
    });
    wrapper.find('.page-next').trigger('click');
    expect(wrapper.emitted()['update:current'][0][0]).toBe(3); // 点击下一页则当前页为 2+1

    wrapper.find('.page-prev').trigger('click');
    expect(wrapper.emitted()['update:current'][1][0]).toBe(1); // 点击上一页则当前页为 2-1

    wrapper.findAll('.page-num')[2].trigger('click');
    expect(wrapper.emitted()['update:current'][2][0]).toBe(3); // 点击第三个按钮则跳转至第3页
  });
})
