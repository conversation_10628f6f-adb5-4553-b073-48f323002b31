<template>
  <section
    v-show="props.visible"
    class="video-playback"
    ref="searchContainerRef"
  >
    <div class="video-playback-search">
      <div class="video-playback-search-item">
        <span>车辆：</span>
        <x-select
          v-model:value="formModel.deviceId"
          :options="formOptions.vehList"
          placeholder="请选择车辆"
          :popupContainer="searchContainerRef"
          style="width: 120px"
          show-search
        />
      </div>
      <div class="video-playback-search-item">
        <span>回放日期：</span>
        <DatePicker
          v-model:value="formModel.playerDate"
          :popupContainer="searchContainerRef"
          format="YYYY-MM-DD"
          placeholder="请选择日期"
          showToday
          :disabled-date="disabledDate"
          style="width: 150px"
        />
        <!-- <TimeRangePicker
          v-model:value="formModel.playerTime"
          :popupContainer="searchContainerRef"
          :placeholder="['开始时间', '结束时间']"
          style="width: 250px"
        /> -->
      </div>
      <div class="video-playback-search-item">
        <x-button
          text="查询"
          @click="handleSearch"
        />
      </div>
    </div>
    <div class="video-playback-container">
      <RtvsVideo
        ref="rtvsVideoRef"
        :visible="props.visible"
        :playerCount="playerCount"
        :playbackOptions="playbackOptions"
        :device-id="formModel.deviceId"
        player-type="history"
      />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { defineComponent, reactive, ref, watch } from "vue";
import RtvsVideo from "./components/rtvsVideo.vue";
import XSelect, { type OptionsType } from "@/components/x-select.vue";
import { DatePicker } from "@/components/x-date-picker";
import XMessage from "@/components/x-message";
import { getTrackVehicles, queryVehRealTimeInfoByVehNo } from "@/services/api";
import type { DateType } from "@/assets/ts/dateTime";
import XButton from "@/components/x-button.vue";

const props = defineProps({
  visible: {
    type: Boolean,
    required: true,
  },
});

const rtvsVideoRef = ref<InstanceType<typeof RtvsVideo>>();
const playerCount = ref(4);

const searchContainerRef = ref();
const formModel = reactive({
  simNumber: "",
  playerDate: new Date().toLocaleDateString().split("/").join("-"),
  deviceId: "",
  online: false,
  // playerDate: "2024-10-10",
  // deviceId: "Mark",
});
const carInfoLoading = ref<boolean>(false);
const formOptions = reactive({
  vehList: [] as OptionsType,
});

const playbackOptions = reactive<_PlaybackVideoOptions>({});

const disabledDate = (date: DateType) => {
  return date.timestamp > Date.now();
};

const getVehVideoInfo = async () => {
  carInfoLoading.value = true;
  const carInfo = await queryVehRealTimeInfoByVehNo({ id: formModel.deviceId });
  carInfoLoading.value = false;
  if (carInfo) {
    formModel.online = carInfo.online;
    if (carInfo.online) {
      formModel.simNumber = carInfo.dvrId;
      // formModel.simNumber = "18920221260"; // test
    } else {
      XMessage("error", "车辆不在线");
    }
  } else {
    formModel.simNumber = "";
    XMessage("error", "未获取到车辆信息");
  }
};

const handleSearch = async () => {
  rtvsVideoRef.value?.stopVideo();
  if (!formModel.deviceId) {
    formModel.simNumber = "";
    return XMessage("error", "请选择车辆");
  } else if (!formModel.playerDate[0] || !formModel.playerDate[1]) {
    return XMessage("error", "请选择回放日期");
  }
  await getVehVideoInfo();
  if (!formModel.online) {
    return XMessage("error", "车辆不在线");
  } else if (!formModel.simNumber) {
    return XMessage("error", "请前往车辆管理配置DVR ID");
  }
  playbackOptions.simNumber = formModel.simNumber;
  playbackOptions.playbackDate = formModel.playerDate;
  rtvsVideoRef.value!.queryAllPlayableVideoProgress();
};

const getVehList = async () => {
  const vehList = await getTrackVehicles();
  formOptions.vehList =
    vehList.map((item: any) => ({
      value: item.vehicleNo,
      label: item.vehicleNo,
    })) || [];
};
getVehList();

watch(
  () => props.visible,
  (newV) => {
    if (!newV) {
      formModel.deviceId = "";
      formModel.simNumber = "";
      formModel.online = false;
      formModel.playerDate = new Date().toLocaleDateString().split("/").join("-");
      rtvsVideoRef.value && rtvsVideoRef.value.stopVideo();
    }
  }
);
</script>
<script lang="ts">
export default defineComponent({
  name: "video-playback",
});
</script>
<style lang="scss">
.video-playback {
  background-color: #fff;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  &-search {
    display: flex;
    column-gap: 10px;
    height: 40px;
    padding: 0 20px;
    &-item {
      display: flex;
      align-items: center;
    }
  }
  &-container {
    flex: 1;
    overflow: hidden;
  }
}
</style>
