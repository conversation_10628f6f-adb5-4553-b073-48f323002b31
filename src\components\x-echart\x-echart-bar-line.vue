<template>
  <section class="x-echart-line">
    <echart-base :options="options"></echart-base>
  </section>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import { graphic } from "echarts";
import type { EChartsOption } from "echarts";
import EchartBase from "./base.vue";

const props = withDefaults(
  defineProps<{
    lineOptionType?: "default" | "test";
    title?: string;
    xLabels: string[];
    values: any[];
    vehicleData: number[];
  }>(),
  {
    title: "",
    lineOptionType: "default",
  }
);

const formatValue = (value: string, index: number) => {
  const numValue = Number(value);
  switch (index) {
    case 0:
      return `${numValue.toFixed(1)} ㎡`;
    case 1:
      return `${(numValue / 60).toFixed(1)} h`;
    default:
      return `${value} 辆`;
  }
};

const formatTooltip = (params: any) => {
  const fullLabel = props.xLabels[params[0].dataIndex];
  let tooltipContent = `<div>${fullLabel}</div>`;
  params.forEach((item: any, index: number) => {
    const color =
      typeof item.color === "object"
        ? item.color.colorStops[0].color
        : item.color;
    tooltipContent += `
      <div>
        <span style="display:inline-block;width:18px;height:2px;vertical-align:middle;background-color:${color};"></span>
        <span style="display:inline-block;width:60px;">${item.seriesName}</span>
        <span>${formatValue(item.value, index)}</span>
      </div>`;
  });
  return tooltipContent;
};

const options = computed<EChartsOption>(() => {
  const baseOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "none",
      },
      backgroundColor:
        "linear-gradient(0.00deg, rgba(11, 16, 54, 0.8) 17.095%,rgba(19, 21, 42, 0.67) 62.989%,rgba(21, 35, 64, 0.66) 100%)",
      borderColor: "rgba(221, 229, 250, 0.7)",
      borderRadius: 30,
      extraCssText: "box-shadow: 0px 0px 4px 0px rgba(245, 236, 219, 0.5);",
      textStyle: {
        color: "#fff",
        fontSize: 14,
      },
      formatter: formatTooltip,
    },
    legend: {
      right: "4%",
      icon: "rect",
      itemWidth: 18,
      itemHeight: 2,
      data: ["作业时长", "清扫面积", "车辆"],
      textStyle: {
        color: "rgba(255, 255, 255, 0.5)",
        fontSize: 12,
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
      show: false,
    },
    xAxis: [
      {
        type: "category",
        data:
          props.xLabels?.map((dateStr: any) => {
            const date = new Date(dateStr);
            return date.toLocaleDateString("en-US", {
              month: "2-digit",
              day: "2-digit",
            });
          }) || [],
        axisLabel: {
          textStyle: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 12,
          },
        },
        axisLine: {
          lineStyle: {
            color: "rgb(90, 113, 252, 0.5)",
          },
        },
        axisTick: {
          show: false,
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        axisTick: {
          show: false,
        },
        axisLabel: {
          textStyle: {
            color: "rgba(255, 255, 255, 0.5)",
            fontSize: 12,
          },
        },
        splitLine: {
          show: false,
        },
      },
    ],
  };
  return {
    default: {
      ...baseOption,
      title: {
        text: props.title,
      },
      series: [
        {
          name: "清扫面积",
          type: "line",
          smooth: true,
          showSymbol: false,
          lineStyle: {
            color: "rgb(90, 113, 252)",
            width: 4,
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(90, 113, 252, 0.75)" },
              { offset: 0.8, color: "rgba(7, 131, 250, 0)" },
            ]),
            shadowColor: "rgba(0, 0, 0, 0.1)",
          },
          itemStyle: {
            color: "rgb(90, 113, 252)",
            borderColor: "rgba(221, 220, 107, .1)",
            borderWidth: 12,
          },
          data: props.values[0],
        },
        {
          name: "作业时长",
          type: "line",
          smooth: true,
          showSymbol: false,
          lineStyle: {
            color: "rgba(0, 234, 222, 0.8)",
            width: 4,
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(11, 197, 197, 0.92)" },
              { offset: 0.4, color: "rgba(11, 197, 197, 0.21)" },
              { offset: 0.8, color: "rgba(11, 197, 197, 0)" },
            ]),
            shadowColor: "rgba(0, 0, 0, 0.1)",
          },
          itemStyle: {
            color: "rgba(0, 234, 222, 0.8)",
            borderColor: "rgba(221, 220, 107, .1)",
            borderWidth: 12,
          },
          data: props.values[1],
        },
        {
          name: "车辆",
          type: "bar",
          barWidth: "20%",
          itemStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "#9bacf5" },
              { offset: 0.8, color: "#354061" },
            ]),
            barBorderRadius: [10, 10, 0, 0],
          },
          data: props.vehicleData,
        },
      ],
    },
    test: {
      ...baseOption,
      title: {
        text: props.title,
      },
      series: [
        {
          name: "清扫面积",
          type: "line",
          smooth: true,
          showSymbol: false,
          lineStyle: {
            color: "rgb(90, 113, 252)",
            width: 4,
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(90, 113, 252, 0.75)" },
              { offset: 0.8, color: "rgba(7, 131, 250, 0)" },
            ]),
            shadowColor: "rgba(0, 0, 0, 0.1)",
          },
          itemStyle: {
            color: "rgb(90, 113, 252)",
            borderColor: "rgba(221, 220, 107, .1)",
            borderWidth: 12,
          },
          data: props.values[0],
        },
        {
          name: "作业时长",
          type: "line",
          smooth: true,
          showSymbol: false,
          lineStyle: {
            color: "rgba(0, 234, 222, 0.8)",
            width: 4,
          },
          areaStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgba(11, 197, 197, 0.92)" },
              { offset: 0.4, color: "rgba(11, 197, 197, 0.21)" },
              { offset: 0.8, color: "rgba(11, 197, 197, 0)" },
            ]),
            shadowColor: "rgba(0, 0, 0, 0.1)",
          },
          itemStyle: {
            color: "rgba(0, 234, 222, 0.8)",
            borderColor: "rgba(221, 220, 107, .1)",
            borderWidth: 12,
          },
          data: props.values[1],
        },
        {
          name: "车辆",
          type: "bar",
          barWidth: "20%",
          itemStyle: {
            color: new graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: "rgb(160, 176, 249)" },
              { offset: 1, color: "rgba(160, 176, 249, 0)" },
            ]),
            barBorderRadius: [10, 10, 0, 0],
          },
          data: props.vehicleData,
        },
      ],
    },
  }[props.lineOptionType] as EChartsOption;
});
</script>

<style lang="scss" scoped>
.x-echart-line {
  @include wh(100%);
}
</style>
