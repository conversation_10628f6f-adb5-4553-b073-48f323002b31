<template>
  <div
    class="preview-information-page"
    v-loading="loading"
  >
    <div class="tabs">
      <div
        class="tabs-item"
        :class="{ active: activeTab === 'deviceId' }"
        @click="handleTabClick('deviceId')"
      >
        {{ props.deviceId }}
      </div>
      <div
        class="tabs-item"
        :class="{ active: activeTab === 'all' }"
        @click="handleTabClick('all')"
      >
        所有车辆
      </div>
    </div>

    <div style="padding-left: 100px">
      <el-timeline>
        <el-timeline-item
          v-for="(activity, index) in activities"
          :key="activity.id"
          :hollow="index !== 0 && index !== activities.length - 1"
          placement="top"
          :class="[getLineColor(activity)]"
        >
          <div class="item">
            <div
              class="item-left"
              :class="{
                'item-left-active': activity.step === 1,
                'item-left-previous': isPrevious(activity),
              }"
            >
              {{ index + 1 }}号
            </div>
            <div
              class="item-right"
              :class="{
                'item-right-active': activity.step === 1,
                'item-right-previous': isPrevious(activity),
              }"
              @click="handleItemClick(activity)"
            >
              <p
                class="time"
                :class="{ 'time-active': activity.step === 1 }"
              >
                <span v-if="activeTab === 'all'"> {{ activity.deviceId }}</span>
                <span> {{ activity.remainingTime }}</span>
              </p>
              <div class="action-type-text">
                <div class="actionType">
                  <x-icon
                    width="15px"
                    height="15px"
                    :name="getDirectionIcon(activity.direction)"
                  />
                  <span>{{ getDirectionText(activity.direction) }}</span>
                </div>
                <span>{{ getTrafficLightType(activity.trafficLightType) }}</span>
                <span>距离{{ activity.distance }}米</span>
              </div>

              <!-- <p class="distance-eta">
                <span>{{ activity.estimatedTime }}</span>
              </p> -->
            </div>
          </div>
        </el-timeline-item>
      </el-timeline>
    </div>
    <XEmpty
      v-if="activities?.length === 0"
      description="暂无数据"
    ></XEmpty>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import xIcon from "@/components/x-icon.vue";
import { getVehPreviewInformation, getAllVehFirstPreviewInformation } from "@/services/api";
import XEmpty from "@/components/x-empty.vue";
import { usePolling } from "@/assets/ts/utils";
const props = defineProps({
  deviceId: {
    type: String,
    required: true,
  },
});

const emit = defineEmits(["previewItemClick"]);
const loading = ref(false);
const activities = ref([]);

const activeTab = ref("deviceId");

const deviceId = computed(() => {
  return activeTab.value === "deviceId" ? props.deviceId : "";
});

const handleTabClick = (tab) => {
  activeTab.value = tab;
  load();
};

const handleItemClick = (activity) => {
  emit("previewItemClick", activity);
};

// 获取方向图标
const getDirectionIcon = (direction) => {
  const icons = {
    0: "straight-arrow-icon",
    1: "left-arrow-icon",
    2: "right-arrow-icon",
    3: "u-turn-arrow-icon",
  };
  return icons[direction] || "straight-arrow-icon";
};

// 获取方向文本
const getDirectionText = (direction) => {
  const texts = {
    0: "直行",
    1: "左转",
    2: "右转",
    3: "掉头",
    4: "未知",
  };
  return texts[direction] || "未知";
};

// 获取红绿灯类型文本
const getTrafficLightType = (type) => {
  const types = {
    1: "人行道",
    2: "机动车道",
  };
  return types[type] || "未知";
};

// 获取线条颜色
const getLineColor = (activity) => {
  if (isPrevious(activity)) return "past";
  if (activity.step === 1) return "current";
  return "future";
};

// 判断是否为之前的步骤
const isPrevious = (activity) => {
  const currentIndex = activities.value.findIndex((item) => item.step === 1);
  return currentIndex > -1 && activities.value.indexOf(activity) < currentIndex;
};

const load = async () => {
  try {
    loading.value = true;
    if (activeTab.value === "deviceId") {
      const res = await getVehPreviewInformation({
        deviceId: deviceId.value,
      });
      activities.value = res ?? [];
    } else {
      const res = await getAllVehFirstPreviewInformation({
        deviceId: "",
      });
      activities.value = res ?? [];
    }
  } catch (e) {
    console.log(e);
  } finally {
    loading.value = false;
  }
};

const { startPolling, stopPolling } = usePolling(load, 5);
onMounted(() => {
  startPolling();
});
onUnmounted(() => {
  stopPolling();
});
</script>

<style scoped>
.preview-information-page {
  width: 100%;
  background-color: #fff;
  padding: 20px 0;
  box-sizing: border-box;
  .tabs {
    display: flex;
    align-items: center;
    gap: 20px;
    margin-bottom: 20px;
    margin-left: 15px;
    .tabs-item {
      position: relative;
      font-size: 14px;
      font-weight: 400;
      cursor: pointer;
      color: #9f9fa4;
      transition: color 0.3s ease, font-weight 0.3s ease;

      &::after {
        content: "";
        position: absolute;
        left: 0;
        bottom: -10px;
        width: 0;
        height: 2px;
        transform: translateX(50%);
        background-color: #242859;
        transition: width 0.3s ease;
      }

      &.active {
        color: #242859;
        font-weight: 700;
        &::after {
          width: 50%;
        }
      }
    }
  }

  .item {
    display: flex;
    align-items: flex-start;
    .item-left {
      font-size: 12px;
      font-weight: 400;
      text-align: center;
      line-height: 25px;
      width: 33px;
      height: 25px;
      border-radius: 4px;
      background: rgb(85, 85, 85);
      position: absolute;
      left: -50px;
      top: 10px;
      color: #fff;
    }
    .item-right {
      left: -60px;
      top: -0px;
    }
    .item-right {
      padding: 6px 13px;
      width: 223px;
      height: 92px;
      border-radius: 8px;
      background: rgb(244, 247, 254);
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      cursor: pointer;
      .time {
        display: flex;
        justify-content: space-between;
        font-size: 16px;
        font-weight: 700;
        color: #555555;
      }
      .action-type-text {
        display: flex;
        gap: 10px;
        font-size: 14px;
        font-weight: 400;
        color: #383838;
        .actionType {
          display: inline-block;
          /* width: 46px; */
          height: 25px;
          border-radius: 2px;
          background: rgb(36, 40, 89);
          color: #fff;
          text-align: center;
          /* line-height: 25px; */
          padding: 2px 5px;
        }
      }

      &:hover {
        background: rgba(89, 100, 251, 0.4);
      }
    }
  }
  .item-right-active {
    background-color: #f9852e33 !important;
  }
  .time-active {
    color: #f9852e !important;
  }
  .item-left-active {
    background-color: #f9852e !important;
  }

  .item-left-previous,
  .item-right-previous {
    filter: grayscale(100%);
    -webkit-filter: grayscale(100%);
    background-color: #e5e6e880 !important;
    color: #9f9fa4 !important;
  }

  :deep(.past .el-timeline-item__tail) {
    border-color: #e5e6e880 !important;
  }

  :deep(.current .el-timeline-item__tail) {
    border-color: #f9852e !important;
  }

  :deep(.future .el-timeline-item__tail) {
    border-color: #555555 !important;
  }
}
</style>
