import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import XButton from '@/components/x-button.vue';
import xIcon from "@/components/x-icon.vue";

const baseProps = { text: 'text' } 

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XButton, {
      props: { text: 'Follow' }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 loading', () => {
    const wrapper = shallowMount(XButton, {
      props: {
        loading: true,
        ...baseProps
      }
    });
    expect(wrapper.find('.x-button-loading').exists()).toBe(true);
  });
  it('正确渲染 type + text(汉字) + icon + htmlType', () => {
    const wrapper = shallowMount(XButton, {
      props: {
        type: 'green',
        text: '按钮',
        icon: 'iconName',
        htmlType: 'reset'
      }
    });
    expect(wrapper.find('.x-button.green').exists()).toBe(true);
    expect(wrapper.find('.x-button-text').text()).toBe('按钮');
    expect(wrapper.find('.x-button-icon').exists()).toBe(true);
    expect(wrapper.attributes().type).toBe('reset');
  });
  it('正确渲染 disabledIcon', () => {
    const wrapper = shallowMount(XButton, {
      props: {
        icon: 'iconName',
        disabled: true,
        disabledIcon: 'disabledIconName',
        ...baseProps
      }
    });
    expect(wrapper.findComponent(xIcon).props().name).toBe('disabledIconName');
  });
});

describe('Events Click', () => {
  it('默认状态可点击', () => {
    const wrapper = shallowMount(XButton, { props: baseProps });
    wrapper.trigger('click');
    expect(wrapper.emitted().click[0]).toBeDefined()
  });

  it('disabled时无法点击', () => {
    const wrapper = shallowMount(XButton, { props: { disabled: true, ...baseProps } });
    wrapper.trigger('click');
    expect(wrapper.emitted().click).not.toBeDefined()
  });

  it('loading时无法点击', () => {
    const wrapper = shallowMount(XButton, { props: { loading: true, ...baseProps } });
    wrapper.trigger('click');
    expect(wrapper.emitted().click).not.toBeDefined()
  });
})
