{"name": "cloud-platform-frontend", "version": "1.0.0", "private": true, "scripts": {"dev": "npm run dev-only", "build-dev": "vite build --mode development", "build-test": "vite build --mode testing", "build-prod": "vite build --mode production", "build-mini": "vite build --mode mini", "preview": "vite preview", "test:unit": "vitest", "test:unit:benchmark": "vitest bench", "test:unit:typecheck": "vitest typecheck", "test:unit:coverage": "vitest run --coverage", "test:e2e": "start-server-and-test preview :4173 'cypress run --e2e'", "test:e2e:dev": "start-server-and-test 'vite dev --port 4173' :4173 'cypress open --e2e'", "dev-only": "vite", "type-check": "vue-tsc --noEmit -p tsconfig.vitest.json --composite false", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "prepare": "husky install"}, "config": {"commitizen": {"path": "git-cz"}}, "engines": {"node": ">=18.16.0 <=20.12.2", "pnpm": ">=8.3.1 <10"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@element-plus/icons-vue": "^2.3.1", "@sentry/vue": "^7.56.0", "@vue-office/docx": "^1.3.0", "@vue-office/excel": "^1.3.0", "axios": "^1.2.1", "crypto-js": "^4.1.1", "dayjs": "^1.11.13", "default-passive-events": "^2.0.0", "echarts": "^5.4.3", "element-plus": "2.8.4", "flv.js": "^1.6.2", "jsencrypt": "^3.3.1", "normalize.css": "^8.0.1", "pinia": "^2.0.28", "protobufjs": "^7.2.3", "three": "^0.164.1", "vue": "^3.2.45", "vue-i18n": "9", "vue-router": "^4.1.6"}, "devDependencies": {"@commitlint/cli": "^17.3.0", "@commitlint/config-conventional": "^17.3.0", "@rushstack/eslint-patch": "^1.1.4", "@sentry/vite-plugin": "^2.4.0", "@types/crypto-js": "^4.1.1", "@types/jsdom": "^20.0.1", "@types/node": "^18.11.12", "@vitejs/plugin-basic-ssl": "^1.1.0", "@vitejs/plugin-vue": "^4.0.0", "@vitejs/plugin-vue-jsx": "^3.0.0", "@vitest/coverage-c8": "^0.30.1", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.2.6", "@vue/tsconfig": "^0.1.3", "@vuemap/amap-jsapi-types": "^0.0.18", "cypress": "^12.0.2", "eslint": "^8.22.0", "eslint-plugin-cypress": "^2.12.1", "eslint-plugin-vue": "^9.3.0", "husky": "^8.0.2", "jsdom": "^20.0.3", "lint-staged": "^13.1.0", "prettier": "^2.7.1", "rollup-plugin-visualizer": "^5.9.2", "sass": "~1.69.5", "start-server-and-test": "^1.15.2", "typescript": "~4.7.4", "unplugin-auto-import": "^19.1.0", "unplugin-vue-components": "^28.4.0", "vite": "^4.0.0", "vite-plugin-compression": "^0.5.1", "vitest": "^0.30.1", "vue-tsc": "^1.0.12"}}