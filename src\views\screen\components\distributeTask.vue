<template>
  <x-drawer
    :title="$t('taskSetting')"
    :visible="props.show"
    :btnOption="{
      position: 'end',
      confirm: $t('save'),
      confirmDisabled: !isValidate || isLoading,
      confirmLoading: isLoading,
    }"
    bodyPadding="0 0 32px 0"
    @update:visible="updateVisible"
    @cancel="resetDrawerForm"
    @confirm="carDrawerConfirm"
    width="580px"
  >
    <div class="task-distribution" ref="taskContentRef">
      <!-- 速度 -->
      <div class="task-distribution-top">
        <div class="task-distribution-top-title">
          <div class="left">{{ $t("speed") }}</div>
        </div>
        <div class="task-distribution-top-content">
          <template v-for="(item, index) in speedOptions.options" :key="index">
            <div
              class="speed-btn"
              :class="{ active: item.value === speedOptions.active }"
              @click="speedOptions.active = item.value"
            >
              <span style="margin-right: 5px">{{ item.label }}</span>
              <span>({{ item.value }}km/h)</span>
            </div>
          </template>
        </div>
      </div>
      <!-- 路线 -->
      <div class="task-distribution-center">
        <div class="task-distribution-center-title">
          <div class="right" @click="resetAll">
            <x-icon name="reload" />
            <span>{{ $t("reset") }}</span>
          </div>
        </div>
        <div class="task-distribution-center-content">
          <x-radio-button
            type="tab"
            :gap="15"
            v-model:value="routeTab.activeIndex"
            :options="routeTab.statusList"
            @change="changeRouteTab"
          />
          <div class="task-template-container">
            <!-- 模板路线 -->
            <template v-if="routeTab.activeIndex">
              <x-select
                v-model:value="form.template"
                :options="formOptions.template"
                :popupContainer="taskContentRef"
                @update:value="updateTemplate"
                :placeholder="$t('PSelectTemplate')"
                class="template-selector"
              />
              <div class="template-content" :style="{ height: templateHeight }">
                <div v-for="(route, i) in form.routeList" :key="i">
                  <div class="timeline">
                    <div
                      :class="[
                        'timeline-item',
                        {
                          line: i !== form.routeList.length - 1,
                        },
                      ]"
                    >
                      <div class="timeline-item-title">
                        <span class="timeline-item-title-name">
                          {{
                            getRouteName(route.vehRouteNo) ||
                            $t("routeNameMiss")
                          }}
                        </span>
                      </div>
                      <div class="timeline-item-content">
                        <span>{{
                          getTaskTypeName(route.taskType) || $t("sweepTypeMiss")
                        }}</span>
                        <span v-if="route.sweepingType"
                          >/{{ getStrengthName(route.sweepingType) }}</span
                        >
                      </div>
                    </div>
                  </div>
                </div>
              </div>
              <div class="content-fun-tfoot">
                <div
                  :class="['content-fun-tfoot-more', { open: showMore }]"
                  v-if="form.routeList?.length > 3"
                  v-on:click="showMore = !showMore"
                >
                  <span class="content-fun-tfoot-more-label">
                    {{ showMore ? $t("putAway") : $t("more") }}
                  </span>
                  <x-icon
                    :name="showMore ? 'up_circle' : 'down_circle'"
                    width="12"
                    height="12"
                  />
                </div>
              </div>
            </template>
            <!-- 自定义路线 -->
            <template v-else>
              <div class="setting-tab-items">
                <div
                  :class="[
                    'tab-item',
                    { enable: settingTab.activeIndex === index },
                  ]"
                  v-for="(item, index) in settingTab.typeList"
                  :key="index"
                  @click="changeSettingTab(index)"
                >
                  <x-icon
                    :name="`type_${item.icon}${
                      settingTab.activeIndex === index ? '_enable' : ''
                    }`"
                    width="12"
                    height="12"
                  />
                  <div class="tab-label">{{ item.label }}</div>
                </div>
              </div>
              <!-- 统一型 -->
              <template v-if="!settingTab.activeIndex">
                <routes-unified
                  :dataSource="form.routeList"
                  :routeList="formOptions.route"
                  :container="taskContentRef"
                  :maxRouteLength="999"
                  @delRoute="deleteRoute"
                  @changeRoute="addRoutes(index)"
                />
              </template>
              <template v-else>
                <!-- 多类型 -->
                <routes-multitype
                  :dataSource="form.routeList"
                  :routeList="formOptions.route"
                  :container="taskContentRef"
                  :maxRouteLength="999"
                  @delRoute="deleteRoute"
                  @changeRoute="addRoutes(index)"
                  @update:value="updateRoutes"
                />
              </template>
            </template>
            <div class="cycle-container">
              <span class="cycle-text">{{ $t("cyclesTimes") }}</span>
              <x-number-box
                :value="form.cycleNumber"
                :min="1"
                :max="99"
                @change="form.cycleNumber = $event"
              />
            </div>
          </div>
        </div>
      </div>
      <!-- 结束点 -->
      <div class="task-distribution-bottom">
        <div class="task-distribution-bottom-title">
          <div class="left">{{ $t("endPoint") }}</div>
          <div class="right" @click="resetParkingPoint">
            <x-icon name="reload" />
            <span>{{ $t("reset") }}</span>
          </div>
        </div>
        <div class="task-distribution-bottom-content">
          <x-icon name="parking_point_end" width="24" height="24" />
          <!-- 点位 -->
          <x-select
            v-model:value="form.parkingPoint"
            :options="formOptions.station"
            :popupContainer="taskContentRef"
            allowClear
            :placeholder="$t('PSelectSite')"
            style="width: 180px; margin-left: 10px"
            @update:value="updateStationId"
          />
          <!-- 清扫类型 -->
          <x-select
            v-if="showTaskType"
            v-model:value="form.taskType"
            :options="getTaskTypeList(form.parkingPoint)"
            :popupContainer="taskContentRef"
            allowClear
            :placeholder="$t('PSelectTaskType')"
            style="width: 120px; margin-left: 10px"
          />
          <!-- 车位 -->
          <x-select
            v-model:value="form.parkNo"
            :options="getParkingList(form.parkingPoint)"
            :popupContainer="taskContentRef"
            allowClear
            :placeholder="$t('PSelectParkSpace')"
            style="width: 120px; margin-left: 10px"
          />
        </div>
      </div>
    </div>
  </x-drawer>
  <x-modal
    :visible="taskConfirm.show"
    @confirm="carDrawerConfirm"
    @cancel="taskConfirm.show = false"
    :bodyStyle="{ padding: '32px 32px 24px 44px' }"
    :btnOption="{
      cancel: $t('close'),
      position: 'end',
    }"
    radius="4px"
    width="424px"
    height="181px"
  >
    <div class="content">
      <div class="content-header">
        <div class="content-header-title">
          <x-icon
            class="title-icon"
            name="modal_warn"
            style="margin-right: 10px"
          />
          <span class="title-text">{{ $t("isRunCurrentTask") }}</span>
        </div>
      </div>
      <div class="content-body">
        {{ $t("vehicleScheduleTask") }}{{ taskConfirm.remainder
        }}{{ $t("minAfterRun") }}
      </div>
    </div>
  </x-modal>
</template>
<script lang="ts" setup>
import { reactive, ref, computed, watch, createVNode, render } from "vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import {
  getVehStationList,
  getVehRouteList,
  getRouteTemplate,
} from "@/services/api";
import {
  siteCleaningType,
  speedType,
  cleaningType,
  sweepingType,
} from "@/assets/ts/config";
import { autoView } from "@/services/wsconfig";
import { task } from "@/services/wsapi";
import { useMainStore } from "@/stores/main";
import xDrawer from "@/components/x-drawer.vue";
import xSelect from "@/components/x-select.vue";
import xIcon from "@/components/x-icon.vue";
import Message from "@/components/x-message";
import xModal from "@/components/x-modal";
import routesUnified from "@/views/main/components/routesUnified.vue";
import routesMultitype from "@/views/main/components/routesMultitype.vue";
import xRadioButton from "@/components/x-radio-button.vue";
import xNumberBox from "@/components/x-number-box.vue";
import {
  polylineTemplateStyle,
  polylineHoverMarkerStyle,
} from "@/services/wsconfig";
import type { PropType } from "vue";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
const $t = i18nSimpleKey("carLive");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  map: {
    type: Object as PropType<any>,
    required: true,
  },
  Amap: {
    type: Object as PropType<any>,
    required: true,
  },
});

const { socket } = useMainStore();
const isOnline = ref(false);
watch(
  () => socket.allStatus,
  (newV) => {
    if (newV) {
      const target = newV.find((v) => v.deviceId === props.id);
      if (target) {
        isOnline.value = target.online;
      }
    }
  }
);

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const taskContentRef = ref<any>();

const form = reactive({
  routeList: [
    {
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
    },
  ],
  cycleNumber: 1, // 循环次数
  template: "", // 当前模板id
  parkingPoint: "", // 站点
  taskType: "", // 清扫类型
  parkNo: "", // 车位
});

const formOptions = reactive({
  route: [] as any[],
  station: [] as any[],
  template: [] as any[],
  taskType: siteCleaningType,
});

const speedOptions = reactive({
  options: speedType,
  active: "2.0",
});

const routeTab = reactive({
  statusList: [
    {
      value: 0,
      label: $t("customRoute"),
    },
    {
      value: 1,
      label: $t("templateRoute"),
    },
  ],
  activeIndex: 0,
});

const settingTab = reactive({
  typeList: [
    {
      value: 0,
      label: $t("uniformType"),
      icon: "unified",
    },
    {
      value: 1,
      label: $t("multiType"),
      icon: "multitype",
    },
  ],
  activeIndex: 0,
});

const changeRouteTab = (index: number) => {
  routeTab.activeIndex = index;
  if (index) {
    resetTemplate();
  } else {
    resetTaskRoutes();
  }
};

const changeSettingTab = (index: number) => {
  settingTab.activeIndex = index;
  resetTaskRoutes();
};

// 同步数据
const updateRoutes = (routeList: any) => {
  form.routeList = routeList;
};

/**
 * 地图操作
 */
const mapInfo: any = reactive({
  map: props.map || null,
  Amap: props.Amap || null,
  polylines: [],
  markers: [],
});

// 添加单路线
const addRoute = (path: any, routeName: string) => {
  // 路径折线
  const newPathLine = new mapInfo.Amap.Polyline({
    path: path,
    ...polylineTemplateStyle,
  });
  mapInfo.map.add([newPathLine]);
  mapInfo.polylines.push(newPathLine);
  // 名称标记
  let markerPosition;
  if (path.length === 2) {
    markerPosition = [
      (path[0][0] + path[1][0]) / 2,
      (path[0][1] + path[1][1]) / 2,
    ];
  } else {
    const middlePointIndex = Math.floor(path.length / 2);
    markerPosition = path[middlePointIndex];
  }
  const markerDiv = document.createElement("div");
  const _marker = new mapInfo.Amap.Marker({
    ...polylineHoverMarkerStyle,
    content: markerDiv,
    position: markerPosition,
    extData: {
      markerDiv: markerDiv,
      name: routeName,
    },
  });
  mapInfo.map.add(_marker);
  mapInfo.markers.push(_marker);
  render(
    createVNode(PolylineNameMarker, { name: routeName, type: "light" }),
    markerDiv
  );
};

// 删除单路线
const deleteRoute = (index: number) => {
  if (index < mapInfo.polylines.length) {
    mapInfo.map.remove([mapInfo.polylines[index], mapInfo.markers[index]]);
    mapInfo.polylines.splice(index, 1);
    mapInfo.markers.splice(index, 1);
  }
};

// 添加多路线
const addRoutes = () => {
  clearPolylines();
  form.routeList.forEach((route) => {
    const matchingRoute = formOptions.route.find(
      (r) => r.value === route.vehRouteNo
    );
    if (matchingRoute) {
      const path = matchingRoute.points.map((point) => [
        point.longitude,
        point.latitude,
      ]);
      if (path) {
        const routeName = getRouteName(route.vehRouteNo);
        addRoute(path, routeName);
      }
    }
  });
  mapInfo.map.setFitView(mapInfo.polylines, false, autoView);
};

// 清空路线
const clearPolylines = () => {
  const { map, polylines, markers } = mapInfo;
  map.remove(polylines);
  map.remove(markers);
  mapInfo.polylines = [];
  mapInfo.markers = [];
};

/**
 * 路线
 */
const showMore = ref(false);
const templateHeight = computed(() => {
  if (showMore.value || form.routeList.length <= 3) {
    return `${form.routeList.length * 61}px`;
  } else {
    return "183px"; // 3 * 61px
  }
});

// 选择模板
const updateTemplate = (value: any) => {
  const selectedTemplate = formOptions.template.find(
    (item) => item.value === value
  );
  form.routeList = selectedTemplate.content;
  addRoutes();
};

// 重置
const resetAll = () => {
  routeTab.activeIndex = 0;
  settingTab.activeIndex = 0;
  resetTaskRoutes();
};

// 重置路线
const resetTaskRoutes = () => {
  form.routeList = [
    {
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
    },
  ];
  clearPolylines();
  form.cycleNumber = 1;
};

// 重置模板
const resetTemplate = () => {
  const firstTemplate = formOptions.template[0];
  form.routeList = firstTemplate?.content || [];
  form.template = firstTemplate?.value || null;
  addRoutes();
  form.cycleNumber = 1;
};

// 重置站点
const resetParkingPoint = () => {
  form.parkingPoint = "";
  form.taskType = "";
  form.parkNo = "";
};

// 表单校验
const isValidate = computed(() => {
  const { routeList, parkingPoint } = form;
  if (!routeList.length) return false;
  const isValidRouteList = routeList.every(
    (item) => item.vehRouteNo && item.taskType
  );
  const isEmptyRouteList = routeList.every(
    (item) => !item.vehRouteNo && !item.taskType
  );
  const isValidStation = parkingPoint;
  const isEmptyStation = !parkingPoint;
  return (
    (isValidRouteList && isEmptyStation) ||
    (isValidStation && isEmptyRouteList) ||
    (isValidRouteList && isValidStation)
  );
});

// 定时任务
const taskConfirm = reactive({
  show: false,
  remainder: 0, // 剩余时间
});

// 提交表单
const isLoading = ref(false);
const carDrawerConfirm = debounce(() => {
  if (!isOnline.value) {
    Message("error", $t("vehicleOffTaskCannotSend"));
    return;
  }
  isLoading.value = true;
  const filteredRouteList = form.routeList.filter(
    (item) => item.vehRouteNo !== "" && item.taskType !== ""
  );
  let routePart = ""; // 路线参数拼接
  let stationPart = ""; // 结束点参数拼接
  // 任务编号#任务类型(#清扫类型)
  if (filteredRouteList.length > 0) {
    routePart = filteredRouteList
      .map(
        (item) =>
          `${item.vehRouteNo}#${item.taskType}${
            item.sweepingType ? `#${item.sweepingType}` : ""
          }`
      )
      .join(",");
  }
  // 站点编号#任务类型(#泊车位id)
  if (form.parkingPoint !== "") {
    stationPart = `${form.parkingPoint}#${form.taskType}`;
    if (form.parkNo !== "") {
      stationPart += `#${form.parkNo}`;
    }
  }
  const param =
    routePart && stationPart
      ? `${routePart}=${stationPart}`
      : routePart || stationPart;
  const speedOption = speedOptions.options.find(
    (speed) => speed.value === speedOptions.active
  );
  const speed = speedOption?.prop;
  const executeTimes = form.cycleNumber;
  new Promise(() => {
    task("ecar", props.id, speed, executeTimes, param)
      .then((res: any) => {
        // 定时任务优化
        if (res.result === 450 && !taskConfirm.show) {
          taskConfirm.show = true;
          taskConfirm.remainder = res.message;
          return;
        } else {
          taskConfirm.show = false;
        }
        Message("success", $t("taskSendSuccess"));
        emits("update:show", false);
        emits("confirm");
        resetDrawerForm();
      })
      .finally(() => {
        isLoading.value = false;
      });
  });
});

// 重置表单
const resetDrawerForm = () => {
  form.routeList = [
    {
      vehRouteNo: "",
      taskType: "",
      sweepingType: "",
    },
  ];
  form.parkingPoint = "";
  clearPolylines();
};

// 变更站点
const updateStationId = (value: string) => {
  form.taskType = "";
  form.parkNo = "";
  if (value && !form.stationType) {
    const selectedSite = formOptions.station.find(
      (item) => item.value === value
    );
    // 充电处-自动选择"充电"
    if (selectedSite.stationType === 2) {
      form.taskType = "parking_charging";
    }
    // 垃圾回收场-自动选择"倒垃圾"
    else if (selectedSite.stationType === 1) {
      form.taskType = "parking_garbage";
    }
  }
};

// 根据站点获取任务类型
const getTaskTypeList = (value: string) => {
  const selectedSite = formOptions.station.find((item) => item.value === value);
  return selectedSite ? selectedSite.cleanTypeList : [];
};

// 根据站点获取车位列表
const getParkingList = (value: string) => {
  const selectedSite = formOptions.station.find((item) => item.value === value);
  return selectedSite ? selectedSite.parkingList : [];
};

// 获取路线名称
const getRouteName = (vehRouteNo: string) => {
  return formOptions.route.find((v) => v.value === vehRouteNo)?.label;
};

// 获取任务类型名称
const getTaskTypeName = (type: string) =>
  cleaningType.find((v) => v.value === type)?.label;

// 获取档位类型名称
const getStrengthName = (type: string) =>
  sweepingType.find((v) => v.value === type)?.label;

// 结束点展示清扫类型选择框
const showTaskType = computed(() => {
  const selectedSite = formOptions.station.find(
    (item) => item.value === form.parkingPoint
  );
  return (
    selectedSite &&
    (selectedSite.stationType === 1 || selectedSite.stationType === 2)
  );
});

/**
 * 初始化
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      resetAll();
      // 路线列表
      const routeList = await getVehRouteList(props.id);
      formOptions.route = routeList.map(
        ({ gps, routeName, vehRouteNo, cleanTypeArrays }) => {
          const cleanTypeList = cleanTypeArrays.map((value) => {
            const item = cleaningType.find((type) => type.value === value);
            return {
              value: item.value,
              label: item.label,
            };
          });
          return {
            label: routeName,
            value: vehRouteNo,
            cleanTypeArrays,
            cleanTypeList: cleanTypeList,
            points: gps.map(({ longitude, latitude }) => ({
              longitude,
              latitude,
            })),
          };
        }
      );
      // 模板列表
      const templateData = [];
      const templateList = await getRouteTemplate({ vehNo: props.id });
      templateList.forEach((template) => {
        templateData.push({
          templateName: template.templateName,
          routeList: template.routeTemplateRelEntities.map((route) => ({
            vehRouteNo: route.vehRouteNo,
            routeName: route.routeName,
            taskType: route.cleanType,
          })),
        });
      });
      const formatTemplates = templateData.map(
        ({ templateName, routeList }) => ({
          label: templateName,
          value: templateName,
          content: routeList,
        })
      );
      formOptions.template = formatTemplates;
      // 站点列表
      const stationList = await getVehStationList(props.id);
      formOptions.station = stationList.map((site) => ({
        label: site.stationName,
        value: site.id,
        cleanTypeList: (site.cleanTypeList || []).map((item) => ({
          label: item.value,
          value: item.key,
        })),
        parkingList: [
          {
            value: "",
            label: $t("auto"),
          },
          ...(site.stationParkingRelEntityList || []).map((item) => ({
            label: item.parkName,
            value: item.parkNo,
          })),
        ],
        stationType: site.stationType,
      }));
      speedOptions.active = "2.0";
    }
  }
);
</script>

<style lang="scss" scoped>
.task-distribution {
  height: 100%;
  &-top,
  &-center,
  &-bottom {
    margin: 20px 25px;
    background: #fff;
    border-radius: 10px;
    &-title {
      display: flex;
      justify-content: space-between;
      .left {
        @include sc(14px, #383838) {
          font-weight: bold;
        }
      }
      .right {
        cursor: pointer;
        span {
          margin-left: 5px;
          @include sc(12px, #5964fb);
        }
      }
    }
    &-content {
      padding: 15px 0;
    }
  }
  &-top {
    padding: 15px;
    &-content {
      display: flex;
      .speed-btn {
        @include ct-f {
          margin-right: 15px;
        }
        @include wh(122px, 48px);
        @include sc(12px, #9f9fa4);
        background: #fff;
        border: 1px solid #dcdcdc;
        border-radius: 8px;
        cursor: pointer;
        &.active {
          @include sc(12px, #5964fb);
        }
      }
    }
  }
  &-center {
    position: relative;
    &-title {
      position: absolute;
      top: 10px;
      right: 10px;
    }
    &-content {
      padding: 0;
      .task-template-container {
        padding: 15px;
        .template-selector {
          :deep(.selector-label) {
            color: #5964fb;
          }
        }
        .setting-tab-items {
          @include ct-f {
            justify-content: space-around;
          }
          @include wh(158px, 36px);
          background: #f4f7fe;
          border-radius: 4px;
          .tab-item {
            @include ct-f;
            @include sc(14px, #9f9fa4);
            padding: 4px 6px;
            cursor: pointer;
            &.enable {
              background: #fff;
              border-radius: 4px;
              @include sc(14px, #5964fb);
            }
            .tab-label {
              margin-left: 2px;
            }
          }
        }
      }
      .cycle-container {
        @include ct-f(y) {
          justify-content: space-between;
          padding: 10px;
          margin-top: 15px;
        }
        .cycle-text {
          @include sc(14px, #383838) {
            font-weight: 500;
          }
        }
        background: #f4f7fe;
        border-radius: 8px;
      }
    }
  }
  &-bottom {
    padding: 15px;
    &-content {
      @include ct-f(y);
    }
  }
}
.content {
  .content-header {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 54px);
    @include sc(16px, #242859);
    .title-icon {
      margin-right: 10px;
    }
    .title-text {
      font-weight: bold;
    }
  }
  .content-body {
    display: flex;
    flex-direction: column;
    width: 100%;
    flex: 1;
    padding-left: 26px;
    @include sc(14px, #383838);
  }
}
// 预览模板
.template-content {
  overflow: hidden;
  .timeline {
    .line::before {
      @include wh(1px, 100%) {
        content: "";
        position: absolute;
        top: 19px;
        left: 10px;
        border-right: 2px solid #f3f3f3;
      }
    }
    &-item {
      position: relative;
      padding-left: 25px;
      &::after {
        @include wh(4px) {
          content: "";
          position: absolute;
          left: 8px;
          top: 20px;
          border: 3px solid rgb(89, 100, 251);
          background-color: #fff;
          border-radius: 6px;
        }
      }
      &-title {
        @include ct-f(y);
        padding-top: 15px;
        &-name {
          @include sc(14px, #555) {
            padding: 0 10px 0 5px;
          }
        }
      }
      &-content {
        position: relative;
        padding-left: 5px;
        width: 470px;
        line-height: 25px;
        @include sc(12px, #9f9fa4) {
          font-weight: 500;
        }
      }
    }
  }
}
.content-fun-tfoot {
  padding: 0 0 12px 0;
  &-more {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 21px;
    color: #9f9fa4;
    cursor: pointer;
    user-select: none;
    &-label {
      font-size: 12px;
      margin-right: 4px;
    }
  }
}
</style>
