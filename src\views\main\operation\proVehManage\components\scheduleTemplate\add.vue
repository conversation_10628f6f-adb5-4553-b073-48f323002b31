<template>
  <section
    class="template-add"
    ref="templateAddRef"
    v-if="props.show"
  >
    <div class="template-add-top">{{ props.proAreaName }}—{{ props.proName }}</div>
    <div class="template-add-middle">
      <div class="middle-left">
        <label class="required">{{ $t("templateName") }}</label>
        <x-input
          v-model:value="tempName"
          :placeholder="$t('PEnterTemplateName')"
          :maxlength="50"
          suffix="input_search"
          style="width: 400px"
          @input="repeatTempName"
        />
      </div>
      <div class="middle-right">
        <x-button
          type="paleBlue"
          icon="button_add"
          :text="$t('addTask')"
          @click="addTask"
          style="margin-right: 12px"
        />
        <x-button
          type="blue"
          :disabled="!isTaskTemplateValid"
          :text="$t('save')"
          @click="saveTemp"
        />
      </div>
    </div>
    <div class="template-add-bottom">
      <template
        v-for="(task, index) in taskList"
        :key="index"
      >
        <!-- 编辑模式 -->
        <template v-if="task.editStatus">
          <div class="task">
            <div class="task-top">
              <div class="task-top-title">
                <x-select
                  v-model:value="task.scheduleType"
                  :options="scheduleType"
                  :popupContainer="templateAddRef"
                  style="width: 400px"
                  @update:value="changeScheduleType($event, task)"
                />
                <x-popover
                  trigger="hover"
                  placement="bottom"
                  :container="templateAddRef"
                >
                  <x-icon
                    name="del_x"
                    width="16"
                    height="16"
                    class="del-icon"
                    @click="deleteTask(index)"
                  />
                  <template #content>
                    <div class="task-preview-hover-popover">
                      {{ $t("deleteTask") }}
                    </div>
                  </template>
                </x-popover>
              </div>
              <div class="task-top-content">
                <TaskEdit
                  :task="task"
                  :formOptions="formOptions"
                  :popupContainer="templateAddRef"
                />
              </div>
            </div>
            <div class="task-bottom">
              <!-- <x-button
                type="white"
                :text="$t('cancel')"
                @click="confirmTask(index)"
              /> -->
              <x-button
                type="blue"
                :text="$t('Confirm')"
                :disabled="!validateTask(task)"
                @click="confirmTask(index)"
              />
            </div>
          </div>
        </template>
        <!-- 预览模式 -->
        <template v-else>
          <TaskPreview
            :task="task"
            :showOpera="true"
            :popupContainer="templateAddRef"
            bgType="dark"
            @editTask="editTask(index)"
            @deleteTask="deleteTask(index)"
          />
        </template>
      </template>
    </div>
  </section>
</template>

<script lang="tsx" setup>
import { ref, reactive, computed } from "vue";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import { getTemplateVersionName, scheduleType } from "@/assets/ts/config";
import { addScheduleTemplate, getRouteTempListByArea, getStationListByArea, checkTempName } from "@/services/api";
import { validateTask, getRelName, type TaskItemType, createNewTask } from "../utils";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xIcon from "@/components/x-icon.vue";
import Message from "@/components/x-message";
import xPopover from "@/components/x-popover.vue";
import TaskPreview from "../taskPreview.vue";
import TaskEdit from "../taskEdit.vue";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  proAreaId: {
    type: String,
    required: true,
  },
  proName: {
    type: String,
    required: true,
  },
  proAreaName: {
    type: String,
    required: true,
  },
});
const emit = defineEmits(["back"]);

const templateAddRef = ref<any>();

const tempName = ref("");
const taskList = reactive([] as TaskItemType[]);

const formOptions = reactive({
  routeTemplate: [] as { label: string; value: string }[],
  chargingStation: [] as { label: string; value: string }[],
  garbageStation: [] as { label: string; value: string }[],
  parkingStation: [] as { label: string; value: string }[],
  wateringStation: [] as { label: string; value: string }[],
});

const isTaskTemplateValid = computed(() => {
  return (
    tempName.value &&
    taskList.length > 0 &&
    taskList.every((task) => validateTask(task)) &&
    editingTaskIdx.value === null
  );
});

const repeatTempName = debounce(async () => {
  const params = {
    tempName: tempName.value,
    proAreaId: props.proAreaId,
  };
  const result = await checkTempName(params);
  return result;
});

const changeScheduleType = (value: number, task: TaskItemType) => {
  Object.assign(task, createNewTask());
  task.scheduleType = value;
  task.relId = "0";
  task.relName = "";
  task.stationType = value === 2 ? 1 : value === 3 ? 2 : value === 5 ? 3 : -1;

  /** 1：定时任务，2：定时倒垃圾，3：定时充电，4：定时开机 5: 定时加水 6：定时关机 */
  const optionsMap: Record<number, { label: string; value: string }[]> = {
    1: formOptions.routeTemplate,
    2: formOptions.garbageStation,
    3: formOptions.chargingStation,
    4: formOptions.parkingStation,
    5: formOptions.wateringStation,
  };
};

/**
 * 操作
 */
// 添加任务
const addTask = debounce(() => {
  if (editingTaskIdx.value !== null) {
    Message("error", $t("PSaveBeforeAdd"));
    return;
  }
  taskList.push(createNewTask());
});

// 编辑状态的模板下标
const editingTaskIdx = computed(() => {
  const editingIndex = taskList.findIndex((task) => task.editStatus);
  return editingIndex !== -1 ? editingIndex : null;
});

// 保存任务
const confirmTask = debounce((index: number) => {
  const task = taskList[index];
  task.relName = getRelName(formOptions, task.relId, task.scheduleType);
  if (task.executeDays && Array.isArray(task.executeDays)) {
    task.executeDays.sort((a, b) => parseInt(a) - parseInt(b));
  }
  taskList[index].editStatus = false;
});

// 编辑任务
const editTask = (index: number) => {
  if (editingTaskIdx.value !== null) {
    Message("error", $t("PEditBeforeConfirm"));
    return;
  }
  const task = taskList[index];
  task.relName = getRelName(formOptions, task.relId, task.scheduleType);
  taskList[index].editStatus = true;
};

// 删除任务
const deleteTask = (index: number) => {
  taskList.splice(index, 1);
};

// 保存模板
const saveTemp = debounce(async () => {
  const param = {
    proAreaId: props.proAreaId,
    tempName: tempName.value,
    scheduleList: taskList.map((v) => ({
      ...v,
      stationType: v.stationType === -1 ? null : v.stationType,
      scheduleType: v.scheduleType === 4 ? v._stationType : v.scheduleType,
      _switchType: null,
    })),
  };
  await addScheduleTemplate(param);
  Message("success", $t("saveTemplateSuccess"));
  emit("back");
});
/** 获取站点列表及模版列表 */
const getOptinsDataList = async () => {
  const resArr = await Promise.all([
    getRouteTempListByArea({ proAreaId: props.proAreaId }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 1 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 2 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 3 }),
    getStationListByArea({ proAreaId: props.proAreaId, stationType: 4 }),
  ]);
  const [routeTemplateList, garbageStationList, chargingStationList, wateringStationList, parkingStationList] =
    resArr.map((v) =>
      v.map((item) => ({ ...item, label: item.relName + getTemplateVersionName(item.version), value: item.relId }))
    );
  formOptions.routeTemplate = routeTemplateList;
  formOptions.garbageStation = garbageStationList;
  formOptions.chargingStation = chargingStationList;
  formOptions.wateringStation = wateringStationList;
  formOptions.parkingStation = parkingStationList;
};
getOptinsDataList();
</script>

<style lang="scss" scoped>
.template-add {
  height: 100%;
  &-top {
    width: 100%;
    margin: 15px 0;
    @include sc(12px, #383838);
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
    label {
      @include wh(80px, 30px) {
        line-height: 30px;
      }
      @include sc(14px, rgb(85, 85, 85));
      &.required::before {
        content: "* ";
        color: red;
      }
    }
  }
  &-bottom {
    height: calc(100% - 100px);
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    .task {
      padding: 10px 15px;
      margin: 10px 0 20px 0;
      background: #f5f8fe;
      border-radius: 8px;
      &-top {
        &-title {
          @include ct-f(y) {
            justify-content: space-between;
          }
          .del-icon {
            right: 10px;
            top: 10px;
            cursor: pointer;
          }
          :deep(.x-select-normal-selector) {
            border: none;
          }
        }
        &-content {
          display: flex;
          margin-top: 10px;
          background: #fff;
          border-radius: 8px;
          padding: 10px;
        }
      }
      &-bottom {
        margin-top: 10px;
        display: flex;
        column-gap: 10px;
      }
    }
  }
  :deep(.button-confirm) {
    margin: 0 auto;
  }
  :deep(.popover__body) {
    width: 140px;
    justify-content: center;
  }
  :deep(.time-panel) {
    height: 180px;
  }
}
</style>
