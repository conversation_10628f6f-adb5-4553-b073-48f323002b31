<template>
  <section class="x-form-item">
    <div v-if="props.label" class="x-form-item-label" :style="labelStyle">
      <label :class="{ required: isRequired }">{{ props.label }}</label>
    </div>
    <div class="x-form-item-control" :style="controlStyle">
      <div :class="['x-form-item-control-slot', { 'has-error': explain }]">
        <slot></slot>
      </div>

      <div
        v-show="props.name || explain || props.extra"
        class="x-form-item-control-explain"
      >
        <Transition name="x-form-item-control-explain">
          <span v-show="explain">{{ explain }}</span>
        </Transition>
        <span v-show="props.extra && !explain" class="x-form-item-extra">{{
          props.extra
        }}</span>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, inject, computed, onMounted, watchEffect } from "vue";
import type { PropType } from "vue";
const props = defineProps({
  name: {
    type: String,
  },
  label: {
    type: String,
  },
  rules: {
    type: Array as PropType<["required" | Function, string][]>,
    default: () => [],
  },
  labelFlex: {
    type: [String, Number],
  },
  wrapperFlex: {
    type: Number,
  },
  wrapperStyle: {
    type: String,
  },
  extra: {
    type: String,
  },
});

const { formExplain, formRules, formLabelFlex, formWrapperFlex, addRule } =
  inject("formContextKey")!;

const isRequired = ref(false);
const explain = ref("");
onMounted(() => {
  if (props.name) {
    addRule(props.name, props.rules);
    isRequired.value = formRules[props.name].some((v) => v[0] === "required");
    watchEffect(() => {
      explain.value = formExplain[props.name!];
    });
  }
});

const labelStyle = computed(() => {
  let style = "";
  const _flex = props.labelFlex || formLabelFlex || 7;
  const isPx =
    typeof props.labelFlex === "string" && props.labelFlex.endsWith("px");
  style += isPx ? `width: ${props.labelFlex}` : `flex: ${_flex}`;

  return style;
});
const controlStyle = computed(() => {
  let style = "";
  if (props.wrapperStyle) {
    style += props.wrapperStyle;
  } else {
    const _flex = props.wrapperFlex || formWrapperFlex || 40;
    style += `flex: ${_flex}`;
  }
  return style;
});
</script>

<style lang="scss" scoped>
.x-form-item {
  display: flex;
  width: 100%;
  &-label {
    position: relative;
    height: 100%;
    label {
      padding-left: 8px;
      color: #5e5e5e;
      line-height: 32px;
      &.required::before {
        content: "*";
        position: absolute;
        left: 0px;
        top: 0;
        color: red;
      }
    }
  }
  &-control {
    height: 100%;
    &-slot {
      width: 100%;
      line-height: 32px;
      &.has-error {
        :deep(.x-input-content) {
          transition: border-color 0.4s ease-out;
          border-color: #e24562;
        }
      }
    }
    &-explain {
      @include wh(100%, 24px) {
        margin-bottom: 8px;
      }
      @include sc(12px, #e24562) {
        line-height: 24px;
      }
      span {
        display: block;
      }
      &-enter-active,
      &-leave-active {
        transition: all 0.4s ease-out;
      }
      &-enter-from,
      &-leave-to {
        opacity: 0;
        transform: translateX(-10px);
      }
    }
    .x-form-item-extra {
      color: #999;
    }
  }
}
</style>
