<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="处置"
    append-to-body
    width="594px"
    @close="emit('update:modelValue', false)"
  >
    <div class="alarm-dispose-dialog">
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">车牌号</label>
        <span>{{ formData.deviceId }}</span>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">告警名称</label>
        <span>{{ formData.faultContent }}</span>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">告警开始时间</label>
        <span>{{ formData.createdTime }}</span>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">告警结束时间</label>
        <span>{{ formData.endTime ?? "-" }}</span>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">告警状态</label>
        <span>{{ formData.isEnd === 0 ? "进行中" : "已结束" }}</span>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label">图片</label>
        <div class="alarm-dispose-dialog-item-img">
          <el-image
            v-if="formData.picUrl"
            style="width: 110px; height: 80px; border-radius: 5px"
            :preview-src-list="[formData.picUrl]"
            :src="formData.picUrl"
            alt="告警图片"
          />
          <span
            v-else
            style="margin-left: 10px"
            >暂无图片</span
          >
        </div>
      </div>
      <div class="alarm-dispose-dialog-item">
        <label class="alarm-dispose-dialog-item-label"><span class="required">*</span>解决方案</label>
        <el-input
          v-model="description"
          type="textarea"
          rows="4"
          maxlength="100"
          placeholder="请输入解决方案"
        />
      </div>
    </div>

    <template #footer>
      <div style="width: 100%; text-align: center">
        <div
          v-if="formData.isEnd === 0"
          style="color: #f56c6c; font-size: 14px; font-weight: 700; margin-bottom: 10px"
        >
          请先解除警情后在进行处置！
        </div>
        <el-button
          type="primary"
          @click="handleDispose"
          :disabled="formData.isEnd === 0"
          >处置</el-button
        >
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { getVehicleAlarmDetail, updateAlarmStatus } from "@/services/api";
import { ElMessage } from "element-plus";
import { ref, watch, type PropType } from "vue";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemData: {
    type: Object,
    default: () => {},
  },
  type: {
    type: String as PropType<"all" | "single">,
    default: "all",
  },
});

const emit = defineEmits(["update:modelValue", "refresh"]);

const formData = ref({} as any);
const description = ref("");
const handleDispose = async () => {
  if (!description.value) {
    ElMessage.warning("请输入解决方案");
    return;
  }
  const params = {
    id: props.itemData.id,
    isSolved: 1,
    description: description.value,
  };
  try {
    await updateAlarmStatus(params);
    ElMessage.success("处置成功");
    emit("update:modelValue", false);
    emit("refresh");
  } catch (err) {
    console.log(err);
  }
};
const load = async () => {
  try {
    const res = await getVehicleAlarmDetail({ id: props.itemData.id });
    formData.value = res;
  } catch (err) {
    console.log(err);
  }
};

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      load();
    } else {
      description.value = "";
    }
  }
);
</script>
<script lang="ts">
export default {
  name: "alarmDisposeDialog",
};
</script>
<style scoped lang="scss">
.alarm-dispose-dialog {
  padding: 20px 25px;
  .alarm-dispose-dialog-item {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    &-label {
      width: 90px;
      text-align: left;
      margin-right: 10px;
      color: #9f9fa4;
    }
    .required {
      color: #f56c6c;
    }
  }
  .alarm-dispose-dialog-item-img {
    width: 100%;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 10px;
  }
}
</style>
