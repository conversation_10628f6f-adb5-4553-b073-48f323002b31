import axios, { type AxiosRequestConfig } from "axios";
import Message from "@/components/x-message";
import { setLocalStorage, getLocalStorage } from "@/assets/ts/storage";
import { router } from "@/router/router";

const httpRequest = axios.create({
  baseURL: import.meta.env.VITE_BASE_URL,
});

httpRequest.interceptors.request.use(
  (request) => {
    if (request.url?.includes("/cszg/user-center/saveHeadImg")) {
      request.headers!["Content-Type"] = "multipart/form-data";
    }
    const token = getLocalStorage("token");
    if (token) {
      request.headers!.token = token;
    }
    return request;
  },
  (error) => {
    return Promise.reject(error);
  }
);
httpRequest.interceptors.response.use(
  (response) => {
    // console.log(
    //   "%c http requset success",
    //   "color:green",
    //   response.config.url,
    //   response
    // );
    const specialUrls: any[] = [
      "/cszg/vehCalculate/statisticsDownLoadData",
      "/cszg/vehCalculate/vehCalculateListDownLoad",
      "/cszg/proDataCalculate/proCalculateListDownload",
      "/cszg/vehDataCalculate/vehCalculateListDownload",
      "/cszg/vehicleWarn/listWarnDownload",
    ];
    const isSpecialUrl = specialUrls.includes(response.config.url);

    return isSpecialUrl ? response : response.data.result;
  },
  (error) => {
    console.log("%c http request failed", "color:red", error.config.url, error);

    const code = error.response ? error.response.data.code : undefined;
    switch (code) {
      case 99999:
        Message(
          "error",
          error.response.data.message || (error.response.data.result.code === 500 ? "网络错误" : "未知错误")
        );
        break;
      case 10000:
        Message("error", "登录超时");
        setLocalStorage("token", "");
        setTimeout(() => {
          router.push({ name: "login" });
        }, 300);
        break;
      default:
        break;
    }
    return Promise.reject(error);
  }
);

export const defaultTimeout = 600000;

export default async <Request = any, Response = any>(
  method: string,
  url: string,
  data?: Request,
  timeout?: number,
  responseType?: AxiosRequestConfig["responseType"],
  options?: AxiosRequestConfig
) => {
  return httpRequest<Request, Response>({
    method,
    url,
    data: method === "post" || method === "put" ? data : null,
    params: method === "get" || method === "delete" ? data : null,
    timeout: defaultTimeout,
    responseType,
    ...options,
  });
};

// code	  msg
// 10001	无权限
// 10101	系统未知异常
// 10102	参数异常
// 11001	用户名已存在
// 11002	手机号存在
// 11003	登录信息有误
// 11004	用户不存在
