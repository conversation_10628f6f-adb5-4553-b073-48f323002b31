<template>
  <section
    class="page-table-layout"
    ref="chargingStationRef"
  >
    <div class="page-table-layout-top">
      <div class="top-title">配件管理</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:chargingStation:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addReactive.show = true"
        />
        <Add
          v-model:show="addReactive.show"
          @confirm="addReactive.confirm"
        />
      </div>
    </div>
    <div class="page-table-layout-middle">
      <div class="middle-left">
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 320px"
          >
            <x-select
              v-model:value="searchForm.entName"
              :options="formOptions.companyOptions"
              :popupContainer="chargingStationRef"
              :placeholder="$t('PEnterEnt')"
              showSearch
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 320px"
          >
            <x-select
              v-model:value="searchForm.accessoriesType"
              :options="formOptions.accessoriesType"
              :popupContainer="chargingStationRef"
              placeholder="配件类型"
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 320px"
          >
            <x-input
              v-model:value="searchForm.serialNumber"
              placeholder="配件编号"
              suffix="input_search"
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 112px"
          >
            <x-select
              v-model:value="searchForm.isBinding"
              :options="formOptions.isBindingType"
              :popupContainer="chargingStationRef"
            />
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:chargingStation:list')"
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button
          @click="resetSearchForm"
          type="green"
          :text="$t('reset')"
        />
      </div>
    </div>
    <div class="page-table-layout-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <!-- 配件类型 -->
        <template #accessoriesType="{ record }">
          <div class="table-status">
            {{ showChargingStationName(record.accessoriesType) }}
          </div>
        </template>
        <!-- 状态 -->
        <template #statusText="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.isBinding ? 'status_blue' : 'status_gray'"
              width="12"
              height="12"
            />
            <span>{{ record.isBinding ? $t("bound") : $t("unbound") }}</span>
          </div>
        </template>
        <!-- 企业名称 -->
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            :container="chargingStationRef"
            class="table-company-popover"
          >
            {{ record.entName }}
            <template #content>
              <div class="charging-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <!-- 操作 -->
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:chargingStation:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:chargingStation:getSysChargingEntity')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:chargingStation:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editReactive.show"
        :id="editReactive.id"
        @confirm="editReactive.confirm"
      />
      <Detail
        v-model:show="detailReactive.show"
        :id="detailReactive.id"
      />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import { ref, reactive } from "vue";
import { isBindingType, accessoriesType } from "@/assets/ts/config";
import { compList, getChargingStationList, delChargingStation } from "@/services/api";
import type { GetChargingStationListRequest } from "@/services/type";
import { useMainStore } from "@/stores/main";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("chargingStationManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const chargingStationRef = ref<any>();

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  companyOptions: [] as { label: string; value: string }[],
  isBindingType: [{ value: "", label: $t("all") }, ...isBindingType],
  accessoriesType: accessoriesType,
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "40",
    },
    {
      key: "accessoriesType",
      title: "配件类型",
      width: "80",
      slots: "accessoriesType",
    },
    {
      key: "serialNumber",
      title: "配件编号",
      width: "80",
    },
    {
      key: "statusText",
      title: $t("status"),
      width: "60",
      slots: "statusText",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "80",
      slots: "entName",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "60",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  entName: "",
  serialNumber: "",
  isBinding: "",
  accessoriesType: "",
});

const showChargingStationName = (chargingStationName: number) => {
  switch (chargingStationName) {
    case 1:
      return "智能垃圾桶";
    case 2:
      return "充电加水桩";
    case 3:
      return "充电桩";
    default:
      return "-";
  }
};
/**
 * 表格-搜索
 */
const submitSearch = async () => {
  table.loading = true;

  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.entName,
    serialNumber: searchForm.serialNumber,
    isBinding: searchForm.isBinding,
    accessoriesType: searchForm.accessoriesType,
  } as GetChargingStationListRequest;

  const { totalCount, list } = await getChargingStationList(params);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.serialNumber = "";
  searchForm.isBinding = "";
  searchForm.accessoriesType = "";
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};
/**
 * 表格-首次加载
 */
(async () => {
  submitSearch();
  formOptions.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
})();

/**
 * 相关操作
 */
// 新增
const addReactive = reactive({
  show: false,
  confirm: () => submitSearch(),
});
// 编辑
const editReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openEdit = (id: string) => {
  editReactive.id = id;
  editReactive.show = true;
};
// 详情
const detailReactive = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailReactive.id = id;
  detailReactive.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("sureToDelChargingPile"),
    content: <div style="font-size:14px;color:#383838;">（{$t("willUnbindChargingPileAndArea")}）</div>,
    confirm() {
      return delChargingStation([record.id]).then(() => {
        Message("success", $t("deleteSuccess"));
        submitSearch();
      });
    },
  });
};
</script>

<style lang="scss" scoped>
.charging-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.page-table-layout {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-right {
      display: flex;
    }
    .middle-left-items {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
          padding: 6px 8px 6px 0;
        }
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
    }
  }
}
</style>
