<template>
  <x-drawer
    title="车辆排班"
    :visible="props.show"
    :btnOption="{
      position: 'center',
      confirm: '保存',
      confirmDisabled: !isValidate,
    }"
    bodyPadding="0"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    width="672px"
  >
    <div class="car-schedual" ref="schedualRef">
      <div class="tab-card">
        <div
          v-for="(item, index) in base.schedualTab.typeList"
          :key="index"
          :class="[
            'tab-card-btn',
            { active: base.schedualTab.active === index },
          ]"
          @click="switchTab(index)"
        >
          <span
            :class="[
              'tab-card-btn-radio',
              { checked: base.schedualTab.active === index },
            ]"
          ></span>
          <span class="tab-card-btn-text">{{ item }}</span>
        </div>
      </div>
      <div class="tab-wrapper">
        <div class="content-switch">
          <span class="content-switch-label">排班计划</span>
          <x-switch
            v-model:checked="base.taskSwitch"
            checkedChildren="开启"
            unCheckedChildren="关闭"
            :checked-value="1"
            :un-checked-value="0"
          />
        </div>
        <!-- 每天 -->
        <div v-show="base.schedualTab.active === 0">
          <div class="content-top">
            <task
              v-if="requestSuccess"
              :taskList="base.daySchedual.cardList"
              :stationList="formOptions.station"
              :route-template="formOptions.routeTemplate"
              :proAreaId="props.proAreaId"
              :container="schedualRef"
              @update:value="updateDayTaskList"
            />
          </div>
          <div class="content-bottom">
            <div class="content-bottom-title">排班情况</div>
            <div class="content-bottom-content">
              <timeline :timeRange="dayTimeRangeList" />
            </div>
          </div>
        </div>
        <!-- 每周 -->
        <div v-show="base.schedualTab.active === 1">
          <div class="content-top">
            <div class="tab">
              <div
                v-for="(item, index) in base.weekDayTab.typeList"
                :key="index"
                class="top-left-item"
              >
                <div
                  :class="[
                    'item-status',
                    { active: base.weekDayTab.active === index },
                  ]"
                  @click="base.weekDayTab.active = index"
                >
                  {{ item }}
                </div>
              </div>
            </div>
            <div v-for="(item, index) in base.weekSchedual" :key="index">
              <keep-alive>
                <task
                  v-if="requestSuccess && base.weekDayTab.active === index"
                  :taskList="base.weekSchedual[index].cardList"
                  :stationList="formOptions.station"
                  :route-template="formOptions.routeTemplate"
                  :proAreaId="props.proAreaId"
                  :container="schedualRef"
                  @update:value="updateWeekTaskList"
                />
              </keep-alive>
            </div>
          </div>
          <div class="content-bottom">
            <div class="content-bottom-title">排班情况</div>
            <div class="content-bottom-content">
              <timeline
                :timeRange="weekTimeRangeList[base.weekDayTab.active]"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, ref, watch, computed } from "vue";
import {
  getVehTaskInfo,
  updateVehTask,
  getVehRouteList,
  getVehStationList,
  getAreaRouteTemplate,
  updateTaskStatus,
} from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import Message from "@/components/x-message";
import xSwitch from "@/components/x-switch.vue";
import Task, { type TaskInfo } from "./task.vue";
import Timeline from "./timeline.vue";

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  vehicleId: {
    type: String,
    required: true,
  },
  vehicleNo: {
    type: String,
    required: true,
  },
  proAreaId: {
    type: String,
    required: true,
  },
  taskType: {
    type: Number,
    default: 0,
  },
  taskSwitch: {
    type: Number,
    default: 0,
  },
});

const schedualRef = ref<any>();

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    switchTab(0);
  }
};

const formOptions = reactive({
  route: [] as any[],
  station: [] as any[],
  routeTemplate: [] as any[],
});

// 获取默认任务
const getDefaultTask = () => {
  return {
    opDate: ["", ""],
    executeTimeStart: "",
    executeTimeEnd: "",
    parkPosition: 0,
    cleanType: "",
    shutDown: 0,
    templateId: 0,
  } as TaskInfo;
};

const base = reactive({
  // 排班方式选项
  schedualTab: {
    typeList: ["每天", "每周"],
    active: 0,
  },
  // 工作日选项
  weekDayTab: {
    typeList: [
      "星期一",
      "星期二",
      "星期三",
      "星期四",
      "星期五",
      "星期六",
      "星期日",
    ],
    active: 0,
  },
  // 每日任务列表
  daySchedual: {
    cardList: [getDefaultTask()],
  },
  // 每周任务列表
  weekSchedual: Array.from({ length: 7 }, () => ({
    cardList: [getDefaultTask()],
  })),
  /** 排班计划是否开启 */
  taskSwitch: 0,
});

// 切换排班方式
const switchTab = (index: number) => {
  base.schedualTab.active = index;
  if (index === 0) {
    base.daySchedual.cardList = [getDefaultTask()];
  } else if (index === 1) {
    for (let i = 0; i < 7; i++) {
      base.weekSchedual[i].cardList = [getDefaultTask()];
    }
    base.weekDayTab.active = 0;
  }
};

// 更新每日任务列表
const updateDayTaskList = (value: any) => {
  base.daySchedual.cardList = value;
};

// 更新每周任务列表
const updateWeekTaskList = (value: any) => {
  const activeDay = base.weekDayTab.active;
  base.weekSchedual[activeDay].cardList = value;
};

// 计算每日时间分段列表
const dayTimeRangeList = computed(() =>
  base.daySchedual.cardList.map((item) => item.opDate)
);

// 计算每周时间分段列表
const weekTimeRangeList = computed(() => {
  const weekRanges = [];
  for (const day of base.weekSchedual) {
    const dayRanges = day.cardList.map((item) => item.opDate);
    weekRanges.push(dayRanges);
  }
  return weekRanges;
});

// 校验表单必填项
const checkFormValidate = () => {
  const { schedualTab, daySchedual, weekSchedual } = base;
  const isTaskValid = (task: TaskInfo) =>
    task.templateId && task.opDate[0] && task.opDate[1];
  // 每日排班
  const daySchedualValid = () => {
    return daySchedual.cardList.every(isTaskValid);
  };
  // 每周排班
  const weekSchedualValid = () => {
    const filteredSchedual = weekSchedual.filter(
      (day) => !deepEqual(day.cardList, [getDefaultTask()])
    );
    let taskCount = 0;
    let validTaskCount = 0;
    for (const day of filteredSchedual) {
      for (const task of day.cardList) {
        taskCount += 1;
        if (isTaskValid(task)) {
          validTaskCount += 1;
        }
      }
    }
    return validTaskCount === taskCount && taskCount !== 0;
  };
  if (schedualTab.active === 0) {
    return daySchedualValid();
  } else if (schedualTab.active === 1) {
    return weekSchedualValid();
  }
  return false;
};

// 保存按钮禁用状态
const isValidate = ref(false);
watch(
  () => [base.daySchedual, base.weekSchedual],
  () => {
    isValidate.value = checkFormValidate();
  },
  {
    immediate: true,
    deep: true,
  }
);

// 保存排班设置
const formSubmit = async () => {
  let vehArrangement = [];
  const { schedualTab, daySchedual, weekSchedual } = base;
  if (schedualTab.active == 0) {
    // 每天
    vehArrangement = daySchedual.cardList.map((item) => {
      return {
        executeTimeStart: item.opDate[0] || "",
        executeTimeEnd: item.opDate[1] || "",
        parkPosition: item.parkPosition,
        cleanType: item.cleanType,
        shutDown: item.shutDown,
        executeDay: 0,
        templateId: item.templateId,
      };
    });
  } else {
    // 每周
    let vehArrangementW = [] as Partial<TaskInfo>[];
    weekSchedual.forEach((weekItem, weekIndex) => {
      weekItem.cardList.forEach((cardItem) => {
        const arrangementItem = {
          executeTimeStart: cardItem.opDate[0] || "",
          executeTimeEnd: cardItem.opDate[1] || "",
          parkPosition: cardItem.parkPosition,
          cleanType: cardItem.cleanType,
          shutDown: cardItem.shutDown,
          executeDay: weekIndex + 1,
          templateId: cardItem.templateId,
        };
        vehArrangementW.push(arrangementItem);
      });
    });
    vehArrangement = vehArrangementW.filter(
      (item) => item.executeTimeStart && item.executeTimeEnd && item.templateId
    );
  }
  const param = {
    id: props.id,
    vehicleId: props.vehicleId,
    taskType: base.schedualTab.active ? 2 : 1,
    vehArrangement: vehArrangement,
  };
  await updateVehTask(param);
  await updateTaskStatus({ id: props.id, taskSwitch: base.taskSwitch });
  emits("update:show", false);
  Message("success", "设置排班成功");
  emits("confirm");
};

// 查询路线列表
const requestSuccess = ref(false);
const getTaskRouteList = async () => {
  const routeList = await getVehRouteList(props.vehicleNo);
  formOptions.route = routeList.map((route) => ({
    label: route.routeName,
    value: route.vehRouteNo,
    cleanTypeList: route.cleanTypeList?.map((item) => ({
      label: item.value,
      value: item.key,
    })),
  }));
  requestSuccess.value = true;
};
// 查询站点列表
const getStationList = async () => {
  const stationList = await getVehStationList(props.vehicleNo);
  formOptions.station =
    stationList?.map((site) => ({
      value: site.id,
      label: site.stationName,
      ...site,
    })) || [];
};
/** 获取路线模板列表 */
const getRouteTemplateList = async () => {
  const result = await getAreaRouteTemplate(props.proAreaId as string);
  formOptions.routeTemplate = result
    .filter((template) => template.version === 1)
    .map((template) => ({
      label: template.templateName,
      value: template.id,
    }));
};

// 数据回显
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      base.taskSwitch = props.taskSwitch;
      await Promise.all([
        getTaskRouteList(),
        getStationList(),
        getRouteTemplateList(),
      ]);
      requestSuccess.value = true;
      base.schedualTab.active = props.taskType === 2 ? 1 : 0;
      base.weekDayTab.active = 0;
      const param = {
        vehicleId: props.vehicleId,
      };
      const taskInfo = await getVehTaskInfo(param);
      // 每日
      if (base.schedualTab.active === 0) {
        if (taskInfo.length > 0) {
          base.daySchedual.cardList = taskInfo.map((item) => {
            const {
              executeTimeStart,
              executeTimeEnd,
              parkPosition,
              cleanType,
              shutDown,
              templateId,
            } = item;
            return {
              opDate: [executeTimeStart || "", executeTimeEnd || ""],
              templateId,
              parkPosition,
              cleanType,
              shutDown,
            };
          });
        } else {
          base.daySchedual.cardList = [getDefaultTask()];
        }
        // 每周
      } else if (base.schedualTab.active === 1) {
        base.weekSchedual = base.weekSchedual.map((weekItem, index) => {
          const weekResponse = taskInfo.filter(
            (item) => item.executeDay === index + 1
          );
          if (weekResponse.length > 0) {
            return {
              cardList: weekResponse.map((weekResp) => {
                const {
                  executeTimeStart,
                  executeTimeEnd,
                  templateId,
                  parkPosition,
                  cleanType,
                  shutDown,
                } = weekResp;
                return {
                  opDate: [executeTimeStart || "", executeTimeEnd || ""],
                  templateId,
                  parkPosition,
                  cleanType,
                  shutDown,
                };
              }),
            } as any;
          } else {
            return {
              cardList: [getDefaultTask()],
            };
          }
        });
      }
    }
  }
);

// 比较两个对象是否深度相等
const deepEqual = (obj1: any, obj2: any) => {
  if (
    typeof obj1 !== "object" ||
    typeof obj2 !== "object" ||
    obj1 === null ||
    obj2 === null
  ) {
    return obj1 === obj2;
  }
  const keys1 = Object.keys(obj1);
  const keys2 = Object.keys(obj2);
  if (keys1.length !== keys2.length) {
    return false;
  }
  for (const key of keys1) {
    if (!keys2.includes(key) || !deepEqual(obj1[key], obj2[key])) {
      return false;
    }
  }
  return true;
};
</script>

<style lang="scss" scoped>
.car-schedual {
  padding: 20px;
  .tab-card {
    display: flex;
    padding: 0 5px;
    &-btn {
      position: relative;
      margin: 0 12px;
      @include wh(70px, 36px) {
        line-height: 36px;
        text-align: center;
      }
      @include sc(14px, #9f9fa4);
      border-radius: 6px 6px 0 0;
      &:before,
      &:after {
        content: "";
        display: block;
        position: absolute;
        @include wh(20px, 36px);
        transform: skewX(-10deg);
      }
      &:before {
        left: -12px;
        top: 0;
        border-top-left-radius: 4px;
      }
      &:after {
        top: 0;
        right: -12px;
        border-top-right-radius: 4px;
      }
      &.active {
        @include sc(14px, #242859) {
          font-weight: bold;
          background: #fff;
        }
        &:before,
        &:after {
          content: "";
          display: block;
          position: absolute;
          @include wh(20px, 36px);
          background: #fff;
        }
        &:before {
          transform: skewX(-10deg);
          border-top-left-radius: 4px;
          left: -12px;
          top: 0;
        }
        &:after {
          transform: skewX(10deg);
          border-top-right-radius: 4px;
          top: 0;
          right: -12px;
        }
      }
      &:hover {
        cursor: pointer;
      }
      &-radio {
        @include wh(16px) {
          position: relative;
          top: 3px;
          display: inline-block;
          background: #fff;
          border: 2px solid #d9d9d9;
          border-radius: 50%;
          transition: all 0.3s;
        }
        &:after {
          content: "";
          @include wh(16px);
          @include ct-p(both) {
            margin-top: -8px;
            margin-left: -8px;
            background-color: #5964fb;
            border-radius: 100%;
            transform: scale(0);
            opacity: 0;
            transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
          }
        }
        &.checked {
          border-color: #5964fb;
          &:after {
            transform: scale(0.5);
            opacity: 1;
          }
          &.tab-card-btn-radio {
            color: #5964fb;
          }
        }
      }
      &-text {
        margin-left: 8px;
      }
    }
  }
  .tab-wrapper {
    .content-switch {
      padding: 15px;
      background: #fff;
      border-radius: 0 10px 10px 10px;
      display: flex;
      column-gap: 20px;
      align-items: center;
      margin-bottom: 10px;
      :deep(.x-switch) {
        margin: 0;
      }
    }
    .content-top {
      padding: 5px 15px 15px 15px;
      background: #fff;
      border-radius: 10px;
      .tab {
        @include fj {
          padding: 10px 0;
        }
        @include wh(100%, 40px);

        .top-left {
          display: flex;
          &-item {
            height: 100%;
            .item-status {
              margin-right: 15px;
              @include sc(12px, #999) {
                line-height: 24px;
                text-align: center;
                font-weight: bolder;
              }
              cursor: pointer;
              &.active {
                color: #242859;
                &:after {
                  content: "";
                  display: block;
                  margin: 0 auto;
                  @include wh(25px, 2px);
                  background: #5964fb;
                }
              }
            }
          }
        }
      }
    }
    .content-bottom {
      padding: 15px;
      margin-top: 10px;
      background: #fff;
      border-radius: 10px;
      overflow-x: auto;
      margin-bottom: 46px;
      @include scrollbar(x, 4px, rgb(233, 234, 248), #a2a9fc);
      &-title {
        @include sc(14px, #555);
      }
    }
  }
}
</style>
