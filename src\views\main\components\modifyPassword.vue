<template>
  <x-modal
    :title="$t('changePassword')"
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="hideModal"
    :bodyStyle="{ padding: '20px 80px' }"
    :btnOption="{ position: 'center', confirmDisabled: !isValidate }"
    width="502px"
  >
    <div ref="contentRef">
      <x-form ref="formRef" :model="form" :rules="formRules">
        <x-form-item
          :label="$t('originalPassword')"
          name="password"
          :labelFlex="12"
        >
          <x-input
            type="password"
            v-model:value="form.password"
            :maxlength="15"
            :placeholder="$t('PEntOriPassword')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('newPassword')"
          name="newPassword"
          :labelFlex="12"
        >
          <x-input
            type="password"
            v-model:value="form.newPassword"
            :maxlength="15"
            :placeholder="$t('PEntCharNumberLetter')"
          />
        </x-form-item>
        <x-form-item
          :label="$t('confirmPassword')"
          name="checkPassword"
          :labelFlex="12"
        >
          <x-input
            type="password"
            v-model:value="form.checkPassword"
            :maxlength="15"
            :placeholder="$t('PEntNewPassword')"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import xModal from "@/components/x-modal";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import Message from "@/components/x-message";
import { useMainStore } from "@/stores/main";
import { enRSACrypt } from "@/assets/ts/encrypt";
import { includeNumberCharacter } from "@/assets/ts/validate";
import { changePwd, checkPwd, getPublicKey } from "@/services/api";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
const contentRef = ref<any>();
const formRef = ref<any>();
const form = reactive({
  password: "",
  newPassword: "",
  checkPassword: "",
});
const { logout, userInfo } = useMainStore();
// 校验原密码
const checkOriginPassword = async (value: string) => {
  const publicKey = (await getPublicKey()).publicKey;
  const result = await checkPwd({
    password: enRSACrypt(value, publicKey),
    userId: userInfo.userId.toString(),
  });
  return result?.check;
};
// 校验表单
const validateForm = async (): Promise<boolean> => {
  const { password, newPassword, checkPassword } = form;
  if ([password, newPassword, checkPassword].some((item) => item === "")) {
    return false;
  }
  if (!(await checkOriginPassword(password))) {
    return false;
  }
  if (!includeNumberCharacter(newPassword)) {
    return false;
  }
  if (checkPassword !== newPassword) {
    return false;
  }
  return true;
};
// 表单规则
const formRules = reactive({
  password: [[debounce(checkOriginPassword), $t("oriPasswordError")]],
  newPassword: [
    [includeNumberCharacter, $t("PEntCharNumberLetterAcc")],
    [
      (value: string, model: any) => value !== model.password,
      $t("cannotSamePassword"),
    ],
  ],
  checkPassword: [
    [
      (value: string, model: any) => value === model.newPassword,
      $t("notMatchPassword"),
    ],
  ],
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
// 按钮禁用状态
const isValidate = ref(false);
watch(
  () => [form.password, form.newPassword, form.checkPassword],
  async () => {
    isValidate.value = await validateForm();
  },
  {
    immediate: true,
    deep: true,
  }
);
// 提交表单
const formSubmit = async () => {
  if ((await formRef.value.asyncValidate()) && (await validateForm())) {
    const publicKey = (await getPublicKey()).publicKey;
    await changePwd({
      password: enRSACrypt(form.newPassword, publicKey),
      userId: userInfo.userId.toString(),
    });
    emits("update:show", false);
    logout().then(() => {
      Message("success", $t("passwordChangeSuccess"));
    });
  }
};
const hideModal = () => {
  formRef.value.resetFields();
  useMainStore().updatePwdModalStatus(false);
};
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      formRef.value.resetFields();
    }
  }
);
</script>
