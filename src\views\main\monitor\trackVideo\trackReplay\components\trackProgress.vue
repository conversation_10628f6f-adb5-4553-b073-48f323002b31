<template>
  <section
    class="track-progress"
    ref="contentRef"
  >
    <div class="track-progress-top">
      <div
        class="progress-slider"
        @mousedown="onHandleSliderClick"
      >
        <div class="progress-slider-rail"></div>
        <div
          class="progress-slider-track"
          :style="{
            width: `${barWidth * (base.percent / 100)}px`,
          }"
        />
        <div
          class="progress-slider-handle"
          :style="{
            top: '-4px',
            left: `${barWidth * (base.percent / 100) - 2}px`,
          }"
          @mousedown="onHandleDragStart"
        />
      </div>
    </div>
    <div class="track-progress-center">
      <p class="progress-slider-text">{{ Math.round(base.percent) }}%</p>
    </div>
    <div class="track-progress-bottom">
      <div class="progress-btns">
        <progress-selector
          class="progress-btns-item"
          v-model:value="base.speedCount"
          :options="formOptions.speedOptions"
          @update:value="speedCountChange($event)"
          :popupContainer="contentRef"
          style="width: 30px; margin-right: 10px"
        />
        <x-icon
          class="progress-btns-item"
          :class="{ disabled: props.backDisabled }"
          :name="`progress_back${props.backDisabled ? '_disabled' : ''}`"
          width="14"
          height="10"
          @click="backOff"
        />
        <x-icon
          class="progress-btns-item"
          :name="props.playStatus === 1 ? 'progress_resume' : 'progress_pause'"
          width="24"
          height="24"
          @click="togglePlay"
        />
        <x-icon
          class="progress-btns-item"
          :class="{ disabled: props.goDisabled }"
          :name="`progress_go${props.goDisabled ? '_disabled' : ''}`"
          width="14"
          height="10"
          @click="goAhead"
        />
        <x-icon
          class="progress-btns-item"
          name="progress_restart"
          width="10"
          height="10"
          @click="rePlay"
          style="margin-left: 10px"
        />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { debounce } from "@/assets/ts/utils";
import xIcon from "@/components/x-icon.vue";
import progressSelector from "./progressSelector.vue";

const props = defineProps({
  // 百分比 0-100
  value: {
    type: Number,
    default: 0,
  },
  // 播放状态 0:未开始  1:行驶中  2:暂停
  playStatus: {
    type: Number,
    default: 0,
  },
  speedCount: {
    type: Number,
    required: true,
  },
  goDisabled: {
    type: Boolean,
    default: false,
  },
  backDisabled: {
    type: Boolean,
    default: false,
  },
});

const contentRef = ref<any>();
const barWidth = ref(540);

const base = reactive({
  percent: 0,
  speedCount: 1,
  isMoving: false,
  isDragging: false,
});

watch(
  () => props.speedCount,
  (newV) => {
    base.speedCount = newV;
  }
);
watch(
  () => props.value,
  (newV) => {
    base.percent = newV;
  }
);

// 倍速选项
const formOptions = reactive({
  speedOptions: [
    {
      value: 6,
      label: "6X",
    },
    {
      value: 4,
      label: "4X",
    },
    {
      value: 2,
      label: "2X",
    },
    {
      value: 1,
      label: "1X",
    },
  ],
});

const emits = defineEmits(["changeSpeed", "togglePlay", "goAhead", "backOff", "rePlay", "changeProgress"]);

// 倍速控制
const speedCountChange = (e: number) => {
  emits("changeSpeed", e);
};
// 播放/暂停切换
const togglePlay = () => {
  emits("togglePlay", base.isMoving);
};
// 前进
const goAhead = debounce(() => {
  if (!props.goDisabled) {
    emits("goAhead");
  }
});
// 后退
const backOff = debounce(() => {
  if (!props.backDisabled) {
    emits("backOff");
  }
});
// 重新播放
const rePlay = debounce(() => {
  emits("rePlay", base.isMoving);
});

// 点击调整进度
const onHandleSliderClick = (event: MouseEvent) => {
  // @ts-ignore
  const pageZoom = Number(getComputedStyle(document.querySelector(".main")!).zoom);
  const clientX = event.clientX / pageZoom;
  const railRect = contentRef.value.getBoundingClientRect();
  const clickX = clientX - railRect.left - 15;
  const newPercent = (clickX / barWidth.value) * 100;
  updateProgress(newPercent);
};

// 拖动调整进度
const onHandleDragStart = () => {
  base.isDragging = true;
  document.addEventListener("mousemove", onHandleDrag);
  document.addEventListener("mouseup", onHandleDragEnd);
};
const onHandleDragEnd = () => {
  base.isDragging = false;
  document.removeEventListener("mousemove", onHandleDrag);
  document.removeEventListener("mouseup", onHandleDragEnd);
};
const onHandleDrag = (event: MouseEvent) => {
  if (base.isDragging) {
    // @ts-ignore
    const pageZoom = Number(getComputedStyle(document.querySelector(".main")!).zoom);
    const clientX = event.clientX / pageZoom;
    const railRect = contentRef.value.getBoundingClientRect();
    const dragX = clientX - railRect.left - 15;
    let newPercent = (dragX / barWidth.value) * 100;
    newPercent = Math.min(100, Math.max(0, newPercent));
    updateProgress(newPercent);
  }
};
const updateProgress = (newPercent: number) => {
  base.percent = newPercent;
  emits("changeProgress", newPercent);
};
</script>

<style lang="scss" scoped>
.track-progress {
  display: flex;
  flex-direction: column;
  position: absolute;
  @include ct-p(x);
  bottom: 5px;
  padding: 5px 10px;
  @include wh(570px, 83px);
  border-radius: 4px;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
  &-top {
    .progress-slider {
      position: relative;
      display: inline-block;
      width: 100%;
      cursor: pointer;
      &-rail {
        @include wh(100%, 4px);
        border-radius: 10px;
        background: rgba(196, 196, 196, 0.4);
      }
      &-track {
        position: absolute;
        top: 0;
        height: 100%;
        border-radius: 10px;
        background: #5964fb;
      }
      &-handle {
        position: absolute;
        @include wh(12px, 12px);
        border-radius: 50%;
        border: 4px solid #5964fb;
        background: #fff;
      }
    }
  }
  &-center {
    .progress-slider-text {
      @include sc(12px, #9f9fa4);
    }
  }
  &-bottom {
    @include ct-f(both);
    .progress-btns {
      @include ct-f(both);
      justify-content: space-between;
      padding: 0 150px;
      width: 100%;
      &-item {
        cursor: pointer;
        &.disabled {
          cursor: not-allowed;
        }
      }
    }
  }
}
</style>
