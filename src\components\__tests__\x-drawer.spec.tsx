import { describe, it, expect, afterEach, vi } from "vitest";
import { mount, enableAutoUnmount } from '@vue/test-utils';
import XDrawer from '@/components/x-drawer.vue';
import xButton from "@/components/x-button.vue";

const baseProps = { container: false, visible: true, title: 'titleStr' }
const btnOption = {
  cancel: 'cancelStr',
  confirm: 'confirmStr',
  position: 'end'
}
enableAutoUnmount(afterEach);

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = mount(XDrawer,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 visible: false快照', async () => {
    const wrapper = mount(XDrawer,{
      props: { visible: false, title: 'titleStr' }
    });
    expect(wrapper.html()).toMatchSnapshot();
  })
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = mount(XDrawer,{
      props: {
        ...baseProps, 
        width: '100px',
        bodyPadding: '200px',
        btnOption,
      },
    });
    expect(wrapper.find('.content-header-title').text()).toBe('titleStr');
    expect(wrapper.find('.x-drawer-content').attributes().style).toBe('width: 100px;');
    expect(wrapper.find('.content-body').attributes().style).toBe('padding: 200px;');
    expect(wrapper.find('.content-body-button').exists()).toBe(true);
    expect(wrapper.find('.content-body-button').attributes().style).toBe('justify-content: end;');
    expect(wrapper.vm.btnOption.cancel).toBe('cancelStr');
    expect(wrapper.vm.btnOption.confirm).toBe('confirmStr');
  });
});

describe('Events', () => {
  it('点击 右上角 / 取消 / 遮罩层 后检查是否关闭, 点击确认后检查是否触发confirm', () => {
    const wrapper = mount(XDrawer, {
      props: {
        ...baseProps,
        btnOption
      }
    });
    vi.useFakeTimers();
    wrapper.find('.x-drawer-mask').trigger('click');
    expect(wrapper.emitted()['update:visible'][0][0]).toBe(false);
    expect(wrapper.emitted().cancel[0]).toBeDefined();

    vi.runAllTimers();
    wrapper.find('.x-drawer-mask').trigger('click');
    expect(wrapper.emitted()['update:visible'][1][0]).toBe(false);
    expect(wrapper.emitted().cancel[1]).toBeDefined();

    vi.runAllTimers();
    const [ cancelWrapper, confirmWrapper ] = wrapper.findAllComponents(xButton)

    cancelWrapper.trigger('click');
    expect(wrapper.emitted()['update:visible'][2][0]).toBe(false);
    expect(wrapper.emitted().cancel[2]).toBeDefined();

    confirmWrapper.trigger('click');
    expect(wrapper.emitted().confirm[0]).toBeDefined();
  });
})