<template>
  <template v-if="props.show">
    <!-- Tab内面板内容 -->
    <div class="content-routes">
      <!-- <div
        v-if="/^edit$/.test(props.type)"
        class="content-routes-tip"
      >
        {{ $t("AdjustTemplateIfRouteModified") }}
      </div> -->
      <!-- <div v-if="/^add|edit$/.test(props.type)" class="content-routes-add">
        <x-button
          type="paleBlue"
          :text="$t('addRoute')"
          icon="button_add"
          @click="addRoute()"
        />
      </div> -->
      <ul class="content-routes-list">
        <li
          :class="['list-item', { enable: index === routesEnableIndex }]"
          v-for="(item, index) in routes"
          :key="index"
          @click="toggleRoute(index)"
          draggable="true"
          @dragstart="handleDragstart($event, index)"
          @dragenter="handleDragenter($event, index)"
          @dragend="handleDragend"
          @dragover="handleDragover"
        >
          <div class="list-item-left">
            <x-icon
              :name="`map_polyline${index === routesEnableIndex ? '' : '_disabled'}`"
              width="30px"
              height="30px"
            />
          </div>
          <div class="list-item-right">
            <div class="right-name">{{ item.routeName }}</div>
            <div class="right-type">{{ $t("total") }}{{ item.points.length }}{{ $t("points") }}</div>
            <!-- 隐藏删除按钮 -->
            <x-icon
              v-if="true || /^add|edit$/.test(props.type)"
              class="right-delete"
              name="del_x_blue"
              width="18px"
              height="18px"
              @click.stop="delRoute(index)"
            />
          </div>
        </li>
      </ul>
    </div>
    <!-- 底部表单区域 -->
    <Teleport :to="props.container || 'body'">
      <div
        v-show="showOperaAreaRoute"
        :class="[
          'opera-area-frame',
          {
            routes: props.type !== 'view' && showOperaAreaRoute,
            ['routes-view']: props.type === 'view' && showOperaAreaRoute,
          },
        ]"
        ref="operaAreaFrame"
      >
        <!-- 新增 -->
        <x-form
          v-if="props.type !== 'view' && showOperaAreaRoute"
          ref="routesFormRef"
          :model="routesForm"
          :rules="routeFormRules"
        >
          <x-form-item
            :label="$t('routeName')"
            name="routeName"
            labelFlex="80px"
          >
            <x-input
              v-model:value="routesForm.routeName"
              :maxlength="20"
              :placeholder="$t('PEnterRouteName')"
            />
          </x-form-item>
          <x-form-item
            :label="$t('cleanType')"
            name="cleanType"
            labelFlex="80px"
          >
            <template class="opera-area-checkbox-container">
              <div
                v-for="(item, index) in formOptions.cleaningType"
                :key="index"
                class="checkbox"
              >
                <x-checkbox
                  :text="item.label"
                  :checked="routesForm.cleanType?.includes(item.value)"
                  @update:checked="updateRouteCleanType($event, item)"
                />
              </div>
            </template>
          </x-form-item>
          <x-form-item
            :label="$t('routeNameOfCar')"
            name="carRouteName"
            labelFlex="136px"
          >
            <x-input
              :filter="(value:any) => value.replace(chineseRegexp, '')"
              v-model:value="routesForm.carRouteName"
              :maxlength="30"
              :placeholder="$t('PEnterRouteNameOfCar')"
            />
          </x-form-item>
          <div class="routes-tip">
            {{ $t("ClickMapToBegin") }},{{ $t("DragPointToAdd") }},{{ $t("ClickPointToDel") }}
          </div>
        </x-form>
        <!-- 详情 -->
        <template v-if="props.type === 'view' && showOperaAreaRoute">
          <div class="route-name-item">
            <span>{{ $t("routeName") }}：</span><span>{{ routesForm.routeName }}</span>
          </div>
          <div class="clean-type-item">
            <span>{{ $t("cleanType") }}：</span>
            <span
              v-for="(item, index) in routeCleaningTypes"
              :key="index"
              style="margin-right: 15px"
            >
              {{ item.label }}
            </span>
          </div>
          <div class="car-route-name-item">
            <span>{{ $t("routeNameOfCar") }}：</span>
            <span>{{ routesForm.carRouteName }}</span>
          </div>
        </template>
      </div>
    </Teleport>
  </template>
</template>
<script lang="tsx" setup>
import { ref, reactive, computed, createVNode, render, nextTick, watch, onMounted } from "vue";
import type { PropType } from "vue";
// import { cleaningType } from "@/assets/ts/config";
import { polylineHoverMarkerStyle, polylineMarkerStyle, polylineStyle } from "@/services/wsconfig";
import { chineseRegexp } from "@/assets/ts/regexp";
// import { isChecked } from "@/assets/ts/validate";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
// import xButton from "@/components/x-button.vue";
import xCheckbox from "@/components/x-checkbox.vue";
import PolylineNameMarker from "@/views/main/components/polylineNameMarker.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proAreaManage");

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  type: {
    type: String as PropType<"add" | "edit" | "view">,
    default: "add",
  },
  mapInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  routes: {
    type: Array as PropType<typeof routeDefaultItem[]>,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  dataLoaded: {
    type: Boolean,
    default: false,
  },
});

const formOptions = reactive({
  cleaningType: [
    {
      value: "edgewise",
      label: $t("edgewise"),
    },
  ],
});

const emits = defineEmits(["renderMarker", "toggleMarker", "togglePolyline", "togglePolygon"]);

onMounted(() => {
  const polylineEditorHandle = (event: any) => {
    routesForm.value.points = event.target.getPath().map(({ lng, lat }: any) => ({ longitude: lng, latitude: lat }));
    const newLen = routesForm.value.points.length;
    const { markerDiv, markerFocus, icon } = routesMarker.value.getExtData();
    routesMarker.value.setExtData({
      ...routesMarker.value.getExtData(),
      number: newLen,
    });
    emits("renderMarker", markerDiv, markerFocus, icon, newLen);
  };
  props.mapInfo.polylineEditor.on("addnode", polylineEditorHandle);
  props.mapInfo.polylineEditor.on("removenode", polylineEditorHandle);
  props.mapInfo.polylineEditor.on("adjust", polylineEditorHandle);
});

/**
 * Tab内面板内容
 */
const routeDefaultItem = {
  id: "0" as string | undefined,
  routeName: "路线",
  carRouteName: "",
  cleanType: [] as any[],
  points: [] as { longitude: number; latitude: number }[],
};

// 选择的路线下标
const routesEnableIndex = ref(0);

// 添加路线
const addRoute = (route?: typeof routeDefaultItem) => {
  let longitude: number = 0,
    latitude: number = 0,
    path: number[][] = [],
    newItem: typeof routeDefaultItem;
  if (route) {
    longitude = route.points[route.points.length - 1].longitude;
    latitude = route.points[route.points.length - 1].latitude;
    path = route.points.map(({ longitude, latitude }) => [longitude, latitude]);
    newItem = route;
  } else if (props.routes.length === 0 || routesFormRef.value.validate()) {
    const { lng, lat } = props.mapInfo.map.getCenter();
    longitude = lng + 0.0001;
    latitude = lat + 0.0001;
    path = [
      [longitude - 0.0002, latitude - 0.0002],
      [longitude, latitude],
    ];
    newItem = JSON.parse(JSON.stringify(routeDefaultItem));
    newItem.points = path.map(([_lng, _lat]) => ({
      longitude: _lng,
      latitude: _lat,
    }));
    newItem.routeName += props.routes.length + 1;
    newItem.carRouteName += props.routes.length + 1;
    newItem.cleanType = [];
    props.routes.push(newItem);
    routesEnableIndex.value = props.routes.length - 1;
    nextTick(() => {
      routesFormRef.value.validate();
    });
  }

  // 添加路线
  const polyline = new props.mapInfo.Amap.Polyline({
    ...polylineStyle,
    path,
  });
  polyline.on("mouseover", (event: any) => {
    const { lng, lat } = event.lnglat;
    const markerDiv = document.createElement("div");
    const _marker = new props.mapInfo.Amap.Marker({
      ...polylineHoverMarkerStyle,
      content: markerDiv,
      position: [lng, lat],
      extData: {
        markerDiv: markerDiv,
        name: newItem.routeName,
      },
    });
    props.mapInfo.map.add(_marker);
    props.mapInfo.polylineHoverMarkersMap.set(event.target, _marker);
    renderPolylineNameMarker(markerDiv, newItem.routeName);
  });
  polyline.on("mouseout", (event: any) => {
    props.mapInfo.map.remove(props.mapInfo.polylineHoverMarkersMap.get(event.target));
    props.mapInfo.polylineHoverMarkersMap.delete(event.target);
  });
  props.mapInfo.polylines.push(polyline);
  props.mapInfo.map.add([polyline]);
  // 添加路线尾部marker
  mapAddSite(
    longitude,
    latitude,
    {
      dragging: (_: any, marker: any) => {
        // 改变Polyline
        const { path } = polyline.getOptions();
        path[path.length - 1] = marker._position;
        routesForm.value.points = path.map((v: any[]) => ({
          longitude: v[0],
          latitude: v[1],
        }));
        polyline.setPath(path);
        // 改变PolylineEditor
        props.mapInfo.polylineEditor.setTarget(polyline);
        props.mapInfo.polylineEditor.open();
      },
    },
    "",
    route?.points?.length || 2
  );
  emits("togglePolyline", routesEnableIndex.value);
};

const renderPolylineNameMarker = (markerDiv: HTMLElement, name: String) => {
  render(
    createVNode(PolylineNameMarker, {
      name,
    }),
    markerDiv
  );
};

// 删除路线
const delRoute = (index: number) => {
  props.mapInfo.map.remove(props.mapInfo.polylines[index]);
  props.mapInfo.polylines.splice(index, 1);
  props.mapInfo.map.remove(props.mapInfo.polylineMarkers[index]);
  props.mapInfo.polylineMarkers.splice(index, 1);
  props.routes.splice(index, 1);
  if (routesEnableIndex.value > index) {
    routesEnableIndex.value -= 1;
    emits("togglePolyline", routesEnableIndex.value);
  } else if (routesEnableIndex.value === index) {
    routesEnableIndex.value = 0;
    emits("togglePolyline", routesEnableIndex.value);
  }
};

const toggleRoute = (index: number) => {
  if (props.type === "view") {
    routesEnableIndex.value = index;
  }
  // 当前必须通过表单验证
  else if (routesFormRef.value?.validate()) {
    routesEnableIndex.value = index;
    emits("togglePolyline", index);
  }
};

// 拖拽相关
let dragIndex: number | null = null;

const handleDragstart = (event: DragEvent, index: number) => {
  dragIndex = index;
};

const handleDragenter = (event: DragEvent, index: number) => {
  if (dragIndex !== null) {
    const dataArr = props.routes;
    const dragging = dataArr[dragIndex];
    dataArr.splice(dragIndex, 1);
    dataArr.splice(index, 0, dragging as any);
    const polylineMarker = props.mapInfo.polylineMarkers[dragIndex];
    props.mapInfo.polylineMarkers.splice(dragIndex, 1);
    props.mapInfo.polylineMarkers.splice(index, 0, polylineMarker);
    const polyline = props.mapInfo.polylines[dragIndex];
    props.mapInfo.polylines.splice(dragIndex, 1);
    props.mapInfo.polylines.splice(index, 0, polyline);
    toggleRoute(index);
    dragIndex = index;
  }
};

const handleDragover = (event: DragEvent) => {
  event.preventDefault();
};

const handleDragend = () => {
  dragIndex = null;
};

/**
 * 底部表单区域
 */
const routeFormRules = reactive({
  routeName: [["required", $t("PEnterRouteName")]],
  carRouteName: [["required", $t("PEnterRouteNameOfCar")]],
  // cleanType: [
  //   ["required", $t("PSelectCleanType") ],
  //   [isChecked, $t("PSelectCleanType") ],
  // ],
});
const routesFormRef = ref<any>();
const routesForm = computed(() => props.routes[routesEnableIndex.value]);
const routesMarker = computed(() => props.mapInfo.polylineMarkers[routesEnableIndex.value]);
const routeCleaningTypes = computed(() =>
  formOptions.cleaningType.filter((item) => routesForm.value.cleanType?.includes(item.value))
);
const updateRouteCleanType = (checked: boolean, item: any) => {
  const cleanTypeSet = new Set(routesForm.value.cleanType);
  if (checked) {
    cleanTypeSet.add(item.value);
  } else {
    cleanTypeSet.delete(item.value);
  }
  routesForm.value.cleanType = Array.from(cleanTypeSet);
};

const operaAreaFrame = ref<any>();

// 是否显示表单
const showOperaAreaRoute = computed(() => props.show && props.routes?.length > 0);

/**
 * 地图操作
 */
const mapAddSite = (
  lng: number,
  lat: number,
  eventHandles?: { [key: string]: Function },
  iconName = "map_ployline_end_enable",
  number = 2,
  drag = true
) => {
  const markerDiv = document.createElement("div");
  const newMarker = new props.mapInfo.Amap.Marker({
    ...polylineMarkerStyle,
    content: markerDiv,
    position: [lng, lat],
    cursor: drag ? "move" : "pointer",
    draggable: drag,
    extData: {
      icon: iconName,
      markerDiv: markerDiv,
      markerFocus: true,
      number,
    },
  });
  emits("renderMarker", markerDiv, true, iconName, number);
  props.mapInfo.map.add(newMarker);
  const _markers = props.mapInfo.polylineMarkers;
  _markers.push(newMarker);
  if (eventHandles) {
    Object.keys(eventHandles).forEach((eName) => {
      newMarker.on(eName, (e: any) => eventHandles[eName](e, newMarker));
    });
  }
};

watch(
  () => props.show,
  (newV) => {
    if (newV && props.type !== "view") {
      emits("toggleMarker", "none");
      emits("togglePolygon", "none");
      props.mapInfo.polygonEditor.close();
      emits("togglePolyline", routesEnableIndex.value);
      nextTick(() => {
        routesFormRef.value?.validate();
      });
    }
  },
  { immediate: true }
);

// 编辑详情页面 地图路线标记回显
watch(
  () => props.dataLoaded,
  (newV) => {
    if (newV && props.type !== "add") {
      props.routes.forEach(addRoute);
      props.mapInfo.polylineEditor.close();
      emits("toggleMarker", "none", true);
    }
  }
);
</script>

<style scoped lang="scss">
.content-routes {
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);
  overflow: hidden;
  &-tip {
    padding: 10px 0 0 20px;
    @include sc(12px, rgb(127, 131, 190));
  }
  &-add {
    height: 52px;
    width: 100%;
    padding: 10px 0 10px 14px;
  }
  &-list {
    flex: 1 0 auto;
    overflow-y: auto;
    @include wh(100%, 0);
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    padding-bottom: 56px;
    .list-item {
      cursor: pointer;
      @include fj;
      @include wh(100%, 66px);
      &-left {
        @include ct-f;
        @include wh(56px, 100%);
      }
      &-right {
        position: relative;
        height: 100%;
        flex: 1;
        .right-name {
          margin-top: 10px;
          color: #383838;
        }
        .right-type {
          margin-top: 4px;
          @include sc(12px, #555555);
        }
        .right-delete {
          display: none;
          position: absolute;
          top: 11px;
          right: 13px;
        }
      }
      &:hover {
        background-color: #f5f8ff;
        .right-delete {
          display: block;
        }
      }
      &.enable {
        background-color: #e8ecfd;
        .right-name {
          color: #5964fb;
          font-weight: bolder;
        }
      }
    }
  }
}
.opera-area-frame {
  position: absolute;
  right: 366px;
  bottom: 70px;
  padding: 14px;
  background-color: #fff;
  border-radius: 4px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  &.routes {
    @include wh(618px, 284px);
  }
  &.routes-view {
    @include wh(202px, 400px) {
      padding-left: 34px;
    }
    .task-type {
      &-item {
        display: flex;
        color: #555555;
        line-height: 40px;
      }
    }
  }
  &.routes {
    @include wh(618px, 260px);
  }
  &.routes-view {
    @include wh(400px, 162px);
    .route-name-item,
    .car-route-name-item {
      @include wh(100%, 47px);
      span {
        line-height: 47px;
      }
      span:first-child {
        color: #9f9fa4;
      }
      span:last-child {
        color: rgb(51, 51, 51);
      }
    }
    .clean-type-item {
      @include wh(100%, 47px);
      span {
        line-height: 47px;
        color: rgb(51, 51, 51);
      }
      span:first-child {
        color: #9f9fa4;
      }
    }
  }
  .routes-tip {
    @include sc(12px, rgb(160, 165, 239));
  }
}
.opera-area-checkbox-container {
  @include ct-f(y) {
    height: 30px;
  }
  .checkbox {
    margin-right: 20px;
    :deep(.x-checkbox-text) {
      @include sc(14px, #555);
    }
  }
}
</style>
