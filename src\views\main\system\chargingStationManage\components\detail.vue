<template>
  <x-drawer
    :title="$t('chargingPileDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="544px"
  >
    <div class="content">
      <div class="content-body">
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("serialNo") }}：</div>
          <div class="content-body-item-value-conatiner">
            <div class="content-body-item-value content-body-item-value_bold">
              {{ detail.info.serialNumber }}
            </div>
            <div :class="['status', { binded: detail.info.isBinding }]">
              <div class="status-inner">
                {{ detail.info.isBinding ? $t("bound") : $t("unbound") }}
              </div>
            </div>
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("chargingPileNo") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.chargingStationNo }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("ratedPower") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.ratedPower }}
            <span v-if="detail.info.ratedPower">KW</span>
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("type") }}：</div>
          <div class="content-body-item-value">
            {{ mapTypeToLabel(detail.info.type) }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("supplier") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.supplier }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("model") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.chargingStationType }}
          </div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("project") }}-{{ $t("area") }}：</div>
          <div class="content-body-item-value">{{ detail.info.proName }} - {{ detail.info.proAreaName }}</div>
        </div>
        <div class="content-body-item">
          <div class="content-body-item-label">{{ $t("enterprise") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.entName }}
          </div>
        </div>

        <div class="content-body-item">
          <div class="content-body-item-label">配件类型：</div>
          <div class="content-body-item-value">
            {{ showChargingStationName(detail.info.accessoriesType) }}
          </div>
        </div>
        <div class="content-body-item notes">
          <div class="content-body-item-label">{{ $t("notes") }}：</div>
          <div class="content-body-item-value">
            {{ detail.info.notes }}
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import { getChargingStationInfo } from "@/services/api";
import type { GetChargingStationInfoResponse } from "@/services/type";
import { chargingType } from "@/assets/ts/config";
import XDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("chargingStationManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const detail = reactive({
  info: {} as GetChargingStationInfoResponse,
});
const showChargingStationName = (chargingStationName: number) => {
  switch (chargingStationName) {
    case 1:
      return "智能垃圾桶";
    case 2:
      return "充电加水桩";
    case 3:
      return "充电桩";
    default:
      return "-";
  }
};
// 类型显示
const mapTypeToLabel = (types: string[]) => {
  return (
    types
      ?.map((type) => {
        const foundType = chargingType.find((item) => item.value === type);
        return foundType ? foundType.label : "";
      })
      .join("/") || ""
  );
};

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = (await getChargingStationInfo(props.id)) || {};
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 20px;
  border-radius: 8px;
  background: #fff;
  &-body {
    &-item {
      display: flex;
      height: 21px;
      line-height: 21px;
      font-size: 14px;
      margin-top: 20px;
      &-label {
        width: 80px;
        color: #999;
      }
      &-value {
        color: #383838;
        &_bold {
          @include sc(18px, rgb(16, 22, 55)) {
            font-weight: bold;
          }
        }
      }
      &:first-child {
        margin-top: 0;
        height: 28px;
        line-height: 28px;
        .content-body-item-value-conatiner {
          display: flex;
          justify-content: space-between;
          width: calc(100% - 80px);
        }
      }
      .status {
        @include wh(60px, 100%) {
          padding: 3px;
          border-radius: 4px;
          border: 1px solid #dcdcdc;
        }
        .status-inner {
          @include wh(100%) {
            border-radius: 4px;
            background-color: #dcdcdc;
          }
          color: #fff;
          text-align: center;
          line-height: 20px;
        }
        &.binded {
          border-color: #5964fb;
          .status-inner {
            background-color: #5964fb;
          }
        }
      }
    }
    .notes {
      height: auto;
      .content-body-item-value {
        width: calc(100% - 80px);
        white-space: pre-wrap;
        word-wrap: break-word;
      }
    }
  }
}
</style>
