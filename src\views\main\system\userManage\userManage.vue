<template>
  <section class="user-manage" ref="userManageRef">
    <div class="user-manage-top">
      <div class="top-title">{{ $t("userManage") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:user:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addDrawer.show = true"
        />
        <AddUser v-model:show="addDrawer.show" @confirm="addDrawer.confirm" />
      </div>
    </div>
    <div class="user-manage-middle">
      <div class="middle-left">
        <x-select
          v-model:value="searchForm.company"
          :options="searchForm.companyOptions"
          :popupContainer="userManageRef"
          :placeholder="$t('PEnterEnt')"
          style="width: 320px; margin-right: 16px"
          showSearch
        />
        <x-input
          v-model:value="searchForm.account"
          :placeholder="$t('PEnterAccNamePhone')"
          suffix="input_search"
          style="width: 240px"
        />
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:user:list')"
          @click="(table.pagination.current = 1) && searchUserList()"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="user-manage-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #phone="{ record }">
          {{ formatPhoneNum(record.phone) }}
        </template>
        <template #company="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="userManageRef"
          >
            {{ record.company }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.company }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #status="{ record }">
          <div class="table-status">
            <x-icon
              :name="record.status === 0 ? 'status_red' : 'status_green'"
              width="12"
              height="12"
            />
            <span>{{ record.statusText }}</span>
          </div>
        </template>
        <template #opera="{ record, recordIndex }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:user:saveUV') && !record.hasPro"
                @click="openCarAuth(record.userId)"
              >
                {{ $t("carAuth") }}
              </span>
              <span
                v-if="permitList.includes('sys:user:update')"
                @click="openEdit(record.userId)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:user:info')"
                @click="openDetail(record.userId)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-show="record.status === 0"
                style="color: #e24562"
                @click="enableUser(record)"
              >
                {{ $t("enableUser") }}
              </span>
            </div>
            <x-popover
              v-model:visible="record.opera"
              trigger="click"
              placement="bottom"
              :container="userManageRef"
              @visibleChange="handleClickPop($event, recordIndex)"
            >
              <div :class="['table-opera-more', { enable: record.opera }]">
                <span></span><span></span><span></span>
              </div>
              <template #content>
                <div class="user-manage-table-opera-click-popover">
                  <div
                    class="user-manage-table-opera-click-popover-item"
                    v-for="(item, index) in record.status === 0
                      ? [
                          ...(permitList.includes('sys:user:reset')
                            ? [$t('resetPWD')]
                            : []),
                          ...(permitList.includes('sys:user:delete')
                            ? [$t('delete')]
                            : []),
                        ]
                      : [
                          ...(permitList.includes('sys:user:reset')
                            ? [$t('resetPWD')]
                            : []),
                          ...(permitList.includes('sys:user:changeStatus')
                            ? [$t('disableUser')]
                            : []),
                          ...(permitList.includes('sys:user:delete')
                            ? [$t('delete')]
                            : []),
                        ]"
                    :key="index"
                    @click="handleOpera(item, record)"
                  >
                    {{ item }}
                  </div>
                </div>
              </template>
            </x-popover>
          </div>
        </template>
      </x-table>
      <CarAuth
        v-model:show="carDrawer.show"
        :id="carDrawer.id"
        @confirm="carDrawer.confirm"
      />
      <EditUser
        v-model:show="eidtUser.show"
        :id="eidtUser.id"
        @confirm="eidtUser.confirm"
      />
      <UserDetail v-model:show="userDetail.show" :id="userDetail.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import { ref, reactive, createVNode } from "vue";
import {
  userList,
  compList,
  delUser,
  userStatus,
  resetUserPassword,
} from "@/services/api";
import { useMainStore } from "@/stores/main";
import { formatPhoneNum, i18nSimpleKey } from "@/assets/ts/utils";
import UserDetail from "./components/userDetail.vue";
import AddUser from "./components/addUser.vue";
import CarAuth from "./components/carAuth.vue";
import EditUser from "./components/editUser.vue";
import type { PageSizeType } from "@/components/types";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import xIcon from "@/components/x-icon.vue";
const $t = i18nSimpleKey("userManage");

const {
  userInfo: { permitList },
} = useMainStore();

const userManageRef = ref<any>();
const visibleIndex = ref();

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "80",
    },
    {
      key: "account",
      title: $t("account"),
      width: "120",
    },
    {
      key: "name",
      title: $t("name"),
      width: "100",
    },
    {
      key: "phone",
      title: $t("phone"),
      width: "120",
      slots: "phone",
    },
    {
      key: "company",
      title: $t("company"),
      width: "150",
      slots: "company",
    },
    {
      key: "type",
      title: $t("type"),
      width: "100",
    },
    {
      key: "status",
      title: $t("status"),
      width: "100",
      slots: "status",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "200",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
const handleClickPop = (status, recordIndex: number) => {
  if (status) {
    table.dataSource = table.dataSource.map((obj, index) => {
      return {
        ...obj,
        opera: ref(index === recordIndex ? true : false),
      };
    });
  }
  visibleIndex.value = recordIndex;
};
// 新增
const addDrawer = reactive({
  show: false,
  confirm: () => searchUserList(),
});

// 编辑
const eidtUser = reactive({
  show: false,
  id: 0,
  confirm: () => searchUserList(),
});
const openEdit = (id: number) => {
  eidtUser.id = id;
  eidtUser.show = true;
};
// 详情
const userDetail = reactive({
  show: false,
  id: 0,
});
const openDetail = (id: number) => {
  userDetail.id = id;
  userDetail.show = true;
};

// 车辆授权
const carDrawer = reactive({
  show: false,
  id: 0,
  confirm: () => searchUserList(),
});
const openCarAuth = (id: number) => {
  carDrawer.id = id;
  carDrawer.show = true;
};

// 查询
const searchForm = reactive({
  company: "",
  companyOptions: [] as { label: string; value: string }[],
  account: "",
});
const searchUserList = async () => {
  table.loading = true;
  const { totalCount, list } = await userList({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.company,
    keyword: searchForm.account,
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderNumber: (index + 1).toString().padStart(3, "0"),
            account: item.userAccount,
            name: item.userName,
            phone: item.mobile,
            company: item.entName,
            type: item.userTypeText,
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
const resetSearchForm = () => {
  searchForm.company = "";
  searchForm.account = "";
  searchUserList();
};
(async () => {
  searchUserList();
  searchForm.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
})();
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchUserList();
};

// 解禁
const enableUser = (record: any) => {
  xModal.confirm({
    title: $t("sureToEnableAcc"),
    content: (
      <div>
        {$t("account")}：{record.account}
      </div>
    ),
    confirm() {
      return userStatus({
        userId: record.userId,
        status: 1,
      }).then(() => {
        Message("success", $t("enableSuccess"));
        searchUserList();
      });
    },
  });
};
// 删除 / 禁用 / 重置密码
const handleOpera = (type: "删除" | "禁用" | "重置密码", record: any) => {
  record.opera = false;
  ({
    删除: () => {
      xModal.confirm({
        title: $t("sureToDelAcc"),
        content: (
          <div>
            <div>
              {$t("account")}：{record.account}
            </div>
            <div style="color:rgb(249, 133, 46);font-size:12px;">
              {$t("willDisbleCarAndRole")}
            </div>
          </div>
        ),
        confirm() {
          return delUser(record.userId).then(() => {
            Message("success", $t("deleteSuccess"));
            searchUserList();
          });
        },
      });
    },
    禁用: () => {
      xModal.confirm({
        title: $t("sureToDisableAcc"),
        content: (
          <div>
            {$t("account")}：{record.account}
          </div>
        ),
        confirm() {
          return userStatus({
            userId: record.userId,
            status: 0,
          }).then(() => {
            Message("success", $t("disableSuccess"));
            searchUserList();
          });
        },
      });
    },
    重置密码: () => {
      xModal.confirm({
        title: $t("sureToResetPWD"),
        content: (
          <div>
            {$t("account")}：{record.account}
          </div>
        ),
        confirm() {
          return resetUserPassword(record.userId).then((res) => {
            xModal.success({
              width: "345px",
              title: `${record.account}${$t("resetPWDSuccess")}！`,
              content: (
                <div>
                  <span style="margin-right: 10px;">
                    {" "}
                    {$t("newPWD")}：{res.pwd}
                  </span>
                  <div
                    style="cursor: pointer;display:inline-block;"
                    onClick={() => copyPassword(res.pwd)}
                  >
                    {createVNode(xIcon, {
                      name: "copy",
                      width: "16px",
                      height: "16px",
                    })}
                  </div>
                </div>
              ),
            });
            searchUserList();
          });
        },
      });
    },
  }[type]());
};
const copyPassword = (pwdText: string) => {
  if (window.isSecureContext && navigator.clipboard) {
    navigator.clipboard.writeText(pwdText).then(() => {
      Message("success", $t("copySuccess"));
    });
  } else {
    unsecuredCopyToClipboard(pwdText);
  }
};
const unsecuredCopyToClipboard = (text: string) => {
  const textArea = document.createElement("textarea");
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.focus();
  textArea.select();
  try {
    document.execCommand("copy");
    Message("success", $t("copySuccess"));
  } catch (err) {
    console.error("Unable to copy to clipboard", err);
  }
  document.body.removeChild(textArea);
};
</script>

<style lang="scss">
.user-manage-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.user-manage-table-opera-click-popover {
  &-item {
    cursor: pointer;
    @include wh(90px, 40px);
    font-size: 13px;
    line-height: 40px;
    text-align: center;
    &:hover {
      background-color: #242859;
    }
    &:first-child {
      border-radius: 4px 4px 0 0;
    }
    &:last-child {
      border-radius: 0 0 4px 4px;
    }
  }
}
</style>
<style lang="scss" scoped>
.user-manage {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }

  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, 32px) {
      margin-top: 20px;
    }
    .middle-left,
    .middle-right {
      display: flex;
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
      // 为了让pop居中
      // margin-left: -26px;
      // padding-left: 26px;
    }
    .table-status {
      @include ct-f(y);
      span {
        margin-left: 5px;
      }
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
