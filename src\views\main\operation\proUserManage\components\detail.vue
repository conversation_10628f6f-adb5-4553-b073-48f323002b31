<template>
  <x-drawer
    :title="$t('detailInfo')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="600px"
  >
    <div class="pro-user-detail">
      <div class="user-info">
        <div class="user-info-top">
          <div class="user-info-top-name">
            {{ config.userinfo.userName }}
          </div>
          <div
            :class="[
              'user-info-top-status',
              { error: config.userinfo.status === 0 },
            ]"
          >
            <div class="status-inner">
              {{ config.userinfo.statusText }}
            </div>
          </div>
        </div>
        <div class="user-info-bottom">
          <div class="user-info-bottom-item">
            <div class="item-label">{{ $t("mobile") }}:</div>
            <div class="item-value">
              {{ formatPhoneNum(config.userinfo.mobile || "") }}
            </div>
          </div>
          <div class="user-info-bottom-item">
            <div class="item-label">{{ $t("account") }}:</div>
            <div class="item-value">{{ config.userinfo.userAccount }}</div>
          </div>
          <div class="user-info-bottom-item">
            <div class="item-label">{{ $t("proName") }}:</div>
            <div class="item-value">{{ config.userinfo.proName }}</div>
          </div>
          <div class="user-info-bottom-item">
            <div class="item-label">{{ $t("stationType") }}:</div>
            <div class="item-value">{{ config.userinfo.stationText }}</div>
          </div>
          <div class="user-info-bottom-item car-list">
            <div class="item-label">{{ $t("vehicleInCharge") }}:</div>
            <div class="item-value">
              <span
                v-for="(item, index) in config.userinfo.vehicles"
                :key="index"
              >
                {{ item.vehicleNo }}
              </span>
            </div>
          </div>
        </div>
      </div>
      <div class="user-auth">
        <div class="user-auth-top">{{ $t("authorizedVehicles") }}：</div>
        <div class="user-auth-bottom">
          <x-tree :treeData="tree" style="margin: 5px 0 0 -15px" />
        </div>
      </div>
    </div>
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, computed, watch } from "vue";
import { getProUserDetail } from "@/services/api";
import {
  resTreeToXTree,
  formatPhoneNum,
  i18nSimpleKey,
} from "@/assets/ts/utils";
import type { GetProUserDetailResponse } from "@/services/type";
import xTree from "@/components/x-tree";
import xDrawer from "@/components/x-drawer.vue";
const $t = i18nSimpleKey("proUserManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const config = reactive({
  userinfo: {} as GetProUserDetailResponse,
});

const tree = computed(() => {
  const _tree = config.userinfo.proAreaVelParams;
  return _tree ? resTreeToXTree(_tree) : [];
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      config.userinfo = await getProUserDetail(String(props.id));
    }
  }
);
</script>

<style lang="scss" scoped>
.pro-user-detail {
  display: flex;
  flex-direction: column;
  @include wh(100%);
  .user-info,
  .user-auth {
    background: #fff;
    border-radius: 8px;
    padding: 20px;
  }
  .user-info {
    &-top {
      @include fj;
      @include wh(100%, 28px);
      &-name {
        @include sc(18px, #242859) {
          font-weight: bolder;
          line-height: 28px;
        }
      }
      &-status {
        @include wh(50px, 100%) {
          padding: 3px;
          border-radius: 4px;
          border: 1px solid #28e09f;
        }
        .status-inner {
          @include wh(100%) {
            border-radius: 4px;
            background-color: #28e09f;
          }
          color: #fff;
          text-align: center;
          line-height: 20px;
        }
        &.error {
          border-color: #f4284e;
          .status-inner {
            background-color: #f4284e;
          }
        }
      }
    }
    &-bottom {
      &-item {
        @include fj;
        @include wh(100%, 21px) {
          margin-top: 20px;
        }
        .item-label {
          width: 77px;
          color: #9f9fa4;
          line-height: 21px;
        }
        .item-value {
          flex: 1;
          color: #383838;
          line-height: 21px;
        }
      }
      .car-list {
        height: auto;
        .item-value {
          display: flex;
          flex-wrap: wrap;
          margin-right: 42px;
          span {
            margin: 0 20px 10px 0;
          }
        }
      }
    }
  }
  .user-auth {
    margin-top: 20px;
    &-top {
      @include sc(16px, #9f9fa4);
      @include wh(100%, 21px) {
        line-height: 21px;
      }
    }
    &-bottom {
      width: 100%;
      height: calc(100% - 29px);
      padding: 0 10px;
    }
  }
}
</style>
