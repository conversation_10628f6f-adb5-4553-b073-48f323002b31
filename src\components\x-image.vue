<template>
  <Teleport to="body">
    <Transition name="image-preview">
      <div class="image-preview" v-show="props.visible">
        <Transition name="image-preview-mask">
          <div
            class="image-preview-mask"
            @click="handleClose"
            v-show="props.visible"
          ></div>
        </Transition>
        <Transition name="image-preview-content">
          <div v-show="props.visible" class="image-preview-content">
            <template v-if="props.src && imgLoaded">
              <img
                class="preview-image"
                :style="previewImageStyle"
                :src="props.src"
                :alt="props.alt"
                @error="props.src && handleImgError"
              />
            </template>
            <template v-else>
              <div class="preview-image error">
                <img src="@/assets/images/image_error.png" alt="image_error" />
                <span>{{ $t("loadFail") }}</span>
              </div>
            </template>
            <div
              class="close-icon"
              v-html="closeText"
              @click="handleClose"
            ></div>
          </div>
        </Transition>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed } from "vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");

const props = defineProps({
  // 展示预览弹窗
  visible: {
    type: Boolean,
    required: true,
  },
  // 图片地址
  src: {
    type: String,
    required: true,
  },
  // 图像描述
  alt: {
    type: String,
    default: "preview image",
  },
  // 图像宽度
  width: {
    type: String,
    default: "",
  },
  // 图像高度
  height: {
    type: String,
    default: "",
  },
});

const closeText = ref("&#10005");

const emits = defineEmits(["update:visible", "error", "cancel"]);

const handleClose = () => {
  emits("update:visible", false);
  emits("cancel");
};

/**
 * 容错处理
 */
const imgLoaded = ref(true);
const handleImgError = () => {
  imgLoaded.value = false;
  emits("error");
};

/**
 * 宽高处理
 */
const previewImageStyle = computed(() => {
  if (props.width && props.height) {
    // 设置宽高
    return {
      width: props.width,
      height: props.height,
    };
  } else if (props.width) {
    // 仅设置宽
    return {
      width: props.width,
      height: "auto",
    };
  } else if (props.height) {
    // 仅设置高
    return {
      width: "auto",
      height: props.height,
    };
  } else {
    // 不设宽高
    return {
      width: "auto",
      height: "auto",
      maxWidth: "800px",
      maxHeight: "600px",
    };
  }
});
</script>

<style lang="scss" scoped>
.image-preview {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  &-enter {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-mask {
    @include wh(100%) {
      background-color: rgba(4, 6, 22, 0.5);
    }
    &-enter {
      &-from {
        opacity: 0;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        opacity: 0;
      }
    }
  }
  &-content {
    &-enter {
      &-from {
        transform: scale(0.2);
        transform-origin: left top;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: scale(0.2);
        transform-origin: left top;
      }
    }
    display: flex;
    flex-direction: column;
    @include ct-p;
    .preview-image {
      border-radius: 10px;
      &.error {
        @include wh(763px, 401px);
        @include ct-f {
          flex-direction: column;
        }
        @include sc(14px, #9f9fa4);
        background: #fff;
        span {
          margin-top: -40px;
        }
      }
    }
    .close-icon {
      position: absolute;
      top: 10px;
      right: 10px;
      @include ct-f;
      @include wh(24px) {
        background-color: rgba(5, 10, 39, 0.4);
        border-radius: 50%;
      }
      @include sc(16px, #fff);
      cursor: pointer;
    }
  }
}
</style>
