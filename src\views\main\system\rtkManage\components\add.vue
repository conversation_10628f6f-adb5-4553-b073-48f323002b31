<template>
  <x-drawer
    :title="$t('addRTK')"
    :visible="props.show"
    :btnOption="{ position: 'center' }"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    @update:visible="updateVisible"
    @confirm="addSIMConfirm"
    @cancel="formRef.resetFields()"
    width="672px"
  >
    <div class="content" ref="contentRef">
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        class="content-form"
      >
        <x-form-item :label="$t('enterprise')" name="entId" labelFlex="80px">
          <x-tree-select
            v-model:value="form.entId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PEnterEnt')"
            showSearch
          />
        </x-form-item>
        <x-form-item
          :label="$t('rtkAccount')"
          name="rtkAccount"
          labelFlex="80px"
        >
          <x-input
            v-model:value="form.rtkAccount"
            :placeholder="$t('PEnterRTK')"
            :maxlength="15"
          />
        </x-form-item>
        <x-form-item
          :label="$t('rtkPassword')"
          name="rtkPassword"
          labelFlex="80px"
        >
          <x-input
            v-model:value="form.rtkPassword"
            :placeholder="$t('PEnterRTKPassword')"
            :maxlength="15"
          />
        </x-form-item>
        <x-form-item
          :label="$t('operator')"
          name="netOperator"
          labelFlex="80px"
        >
          <x-select
            v-model:value="form.netOperator"
            :options="formOptions.netOperator"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectOperator')"
            allowClear
            style="width: 240px"
          />
        </x-form-item>
        <x-form-item
          :label="$t('registerDate')"
          name="registerDate"
          labelFlex="80px"
        >
          <DatePicker
            v-model:value="form.registerDate"
            :popupContainer="contentRef"
            format="YYYY-MM-DD"
            :placeholder="$t('PSelectRegisterStartDate')"
            style="width: 240px"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive } from "vue";
import { compTree, addRTK, rtkRepeat } from "@/services/api";
import { netOperatorType } from "@/assets/ts/config";
import { resTreeToXTree, debounce } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { DatePicker } from "@/components/x-date-picker";
import XDrawer from "@/components/x-drawer.vue";
import XForm from "@/components/x-form.vue";
import XFormItem from "@/components/x-form-item.vue";
import XInput from "@/components/x-input.vue";
import XSelect from "@/components/x-select.vue";
import XTreeSelect from "@/components/x-tree-select.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("rtkManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const formRef = ref<any>();
const contentRef = ref<any>();

const form = reactive({
  entId: "",
  rtkAccount: "",
  rtkPassword: "",
  netOperator: "",
  registerDate: "",
});

const formOptions = reactive({
  company: [] as TreeItemType[],
  netOperator: netOperatorType,
});

const repeatRTK = async (value: string) => {
  const params = {
    rtkAccount: value,
  };
  const result = await rtkRepeat(params);
  return !result?.repeat;
};

const formRules = reactive({
  entId: [["required", $t("entNameRequired")]],
  rtkAccount: [
    ["required", $t("rtkAccountRequired")],
    [debounce(repeatRTK), $t("rtkAccountExist")],
  ],
  rtkPassword: [["required", $t("rtkPasswordRequired")]],
});

(async () => {
  formOptions.company = reactive(resTreeToXTree([await compTree()]));
})();

const addSIMConfirm = async () => {
  if (await formRef.value.asyncValidate()) {
    await addRTK({
      entId: form.entId,
      rtkAccount: form.rtkAccount,
      rtkPassword: form.rtkPassword,
      netOperator: form.netOperator ? form.netOperator : null,
      registerDate: form.registerDate,
    });
    Message("success", $t("createSuccess"));
    emits("update:show", false);
    emits("confirm");
    formRef.value.resetFields();
  }
};
</script>

<style lang="scss" scoped>
.content {
  &-form {
    padding: 20px 20px 0px 20px;
    margin-right: 20px;
    border-radius: 8px;
    background: #fff;
  }
}
</style>
