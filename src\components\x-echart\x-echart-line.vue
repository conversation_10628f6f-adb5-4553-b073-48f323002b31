<!-- 
<x-echart-line 
  style="width: 500px; height: 500px" 
  :xLabels="lineData.xLabels" 
  :values="lineData.values" 
/>
const lineData = {
  xLabels: ["上衣", "裤子", "鞋子", "厨具", "家具", "床上用品", "女装"],
  values: [49749, 84754, 58283, 57354, 49002, 98579, 112268],
}; 
-->

<template>
  <section class="x-echart-line">
    <echart-base :options="options"></echart-base>
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import type { EChartsOption } from "echarts";
import EchartBase from "./base.vue";

const props = withDefaults(
  defineProps<{
    lineOptionType?: "default" | "test";
    title?: string;
    xLabels: string[];
    values: any[];
  }>(),
  {
    title: "",
    lineOptionType: "default",
  }
);

const options = computed<EChartsOption>(() => {
  const baseOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "cross",
        label: {
          backgroundColor: "#6a7985",
        },
      },
    },
    legend: {},
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
  };
  return {
    default: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          data: props.xLabels,
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "分别销量",
          type: "line",
          stack: "总量",
          areaStyle: {},
          emphasis: {
            focus: "series",
          },
          data: props.values,
        },
      ],
    },
    test: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: [
        {
          type: "category",
          boundaryGap: false,
          data: props.xLabels,
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "分别销量",
          type: "line",
          stack: "总量",
          areaStyle: {},
          emphasis: {
            focus: "series",
          },
          data: props.values,
        },
      ],
    },
  }[props.lineOptionType] as EChartsOption;
});
</script>

<style lang="scss" scoped>
.x-echart-line {
  @include wh(100%);
}
</style>
