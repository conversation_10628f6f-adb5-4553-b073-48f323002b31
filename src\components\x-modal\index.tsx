import xModal from "./x-modal.vue";
import Modal from "./modal.vue";
import screenModal from "./screen-modal.vue";
import { createVNode, render, type Component } from "vue";

type xModalFnParams = Omit<InstanceType<typeof Modal>["$props"], "container">;

xModal.confirm = (props: xModalFnParams): Function => {
  const div = document.createElement("div");
  div.setAttribute("class", "Modal");
  document.body.appendChild(div);
  render(createVNode(Modal, { ...props, container: div }), div);
  return () => {
    render(null, div);
    document.body.removeChild(div);
  };
};
xModal.screenConfirm = (props: xModalFnParams): Function => {
  const div = document.createElement("div");
  div.setAttribute("class", "Modal");
  document.body.appendChild(div);
  render(createVNode(screenModal, { ...props, container: div }), div);
  return () => {
    render(null, div);
    document.body.removeChild(div);
  };
};
xModal.success = (props: xModalFnParams) => {
  const div = document.createElement("div");
  div.setAttribute("class", "Modal");
  document.body.appendChild(div);
  render(createVNode(Modal, { ...props, icon: "success", container: div }), div);
};

// @ts-ignore
export default xModal as typeof xModal & {
  /** 消息确认框 */
  confirm: (props: xModalFnParams) => Function;
  /** 大屏的消息确认框 */
  screenConfirm: (props: xModalFnParams) => Function;
  success: (props: xModalFnParams) => void;
};
