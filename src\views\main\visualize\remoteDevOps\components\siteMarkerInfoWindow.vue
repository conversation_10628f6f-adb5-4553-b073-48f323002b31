<template>
  <section
    class="site-marker"
    ref="siteMarkerInfoWindowRef"
  >
    <div class="site-marker-status">
      <span
        class="status-icon"
        :class="{
          'is-free-bg': sitStatusData.stationStatus === 0,
          'is-occupied-bg': sitStatusData.stationStatus === 1,
          'is-abnormal-bg': sitStatusData.stationStatus === 2,
        }"
      ></span>
      <span
        :class="{
          'is-free-text': sitStatusData.stationStatus === 0,
          'is-occupied-text': sitStatusData.stationStatus === 1,
          'is-abnormal-text': sitStatusData.stationStatus === 2,
        }"
        >{{ getStatetext }}</span
      >
    </div>
    <span
      class="site-marker-close"
      v-html="closeText"
      @mousedown="mousedownHandle"
    ></span>
    <div class="site-marker-top">
      <div class="panel-info">
        <div class="panel-info-image">
          <img
            :src="image.url"
            alt=""
            @click="props.stationPic && (image.show = true)"
            class="image"
          />
        </div>
        <div class="panel-info-content">
          <div class="panel-info-content-top">
            <div class="panel-info-content-top-left">
              {{ props.name || "未知" }}({{ siteConfig?.stationText || "未知" }})
            </div>
            <div
              class="panel-info-content-top-right"
              @click="moreVisible = true"
            >
              <span>更多</span>
              <x-icon
                class="more-icon"
                name="down_circle"
              />
            </div>
          </div>
          <div class="panel-info-content-center">{{ info.projectName }} - {{ info.areaName }}</div>
          <div class="panel-info-content-bottom">
            <div class="panel-info-content-bottom-icon">
              <x-icon name="locate_gray" />
            </div>
            <span class="panel-info-content-bottom-text">{{ info.address }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="site-marker-bottom">
      <x-select
        v-show="!props.distributeTask2Click"
        v-model:value="deviceId"
        :options="formOptions.deviceOptions"
        :popupContainer="props.popupContainer"
        style="width: 160px; margin-right: 5px"
      />
      <x-button
        :disabled="!deviceId || !props.distributeTask2Click"
        :text="siteConfig?.operaText || $t('opera')"
        :icon="deviceId ? `${props.type}_btn` : `${props.type}_btn_disabled`"
        :style="props.distributeTask2Click ? 'flex: 1' : ''"
        @click="toStation"
      />
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />

    <el-dialog
      v-model="moreVisible"
      title="更多详情"
      width="25%"
      append-to-body
    >
      <div class="more-detail-content">
        <div class="more-detail-content-top">
          <div class="more-detail-content-top-left">
            <span class="title">{{ props.name }}</span>
            <span class="address">{{ info.address }}</span>
          </div>
          <div class="more-detail-content-top-right">
            <span
              class="status-icon"
              :class="{
                'is-free-bg': sitStatusData.stationStatus === 0,
                'is-occupied-bg': sitStatusData.stationStatus === 1,
                'is-abnormal-bg': sitStatusData.stationStatus === 2,
              }"
            ></span>
            <span
              :class="{
                'is-free-text': sitStatusData.stationStatus === 0,
                'is-occupied-text': sitStatusData.stationStatus === 1,
                'is-abnormal-text': sitStatusData.stationStatus === 2,
              }"
              >{{ getStatetext }}</span
            >
          </div>
        </div>

        <div
          class="more-detail-content-info"
          v-if="stationRealTimeInfo.occupyInfoList.length > 0"
        >
          <template
            v-for="item in stationRealTimeInfo.occupyInfoList"
            :key="item.deviceId"
          >
            <div class="more-detail-content-info-item">
              <span class="item-label">异常原因:</span>
              <span class="item-value">{{ showWarnText(item.valetParkingStage) }}</span>
            </div>
            <div class="more-detail-content-info-item">
              <span class="item-label">上报车辆:</span>
              <span class="item-value">{{ item.deviceId }}</span>
            </div>
            <div class="more-detail-content-info-item">
              <span class="item-label">上报时间:</span>
              <span class="item-value">{{ item.updateTime }}</span>
            </div>
          </template>
        </div>
      </div>
      <template #footer>
        <div style="text-align: center">
          <el-button
            type="primary"
            @click="release"
            >释放</el-button
          >
        </div>
      </template>
    </el-dialog>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch, computed } from "vue";
import type { PropType } from "vue";
import { sitesType } from "@/assets/ts/config";
import xIcon from "@/components/x-icon.vue";
import xImage from "@/components/x-image.vue";
import xButton from "@/components/x-button.vue";
import XSelect, { type OptionsType } from "@/components/x-select.vue";
import { getStaionDetail, releaseParkingSpace } from "@/services/api";
import { closeChargingStatus } from "@/services/wsapi";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { ElMessage, ElMessageBox } from "element-plus";
import { useMainStore } from "@/stores/main";
const $t = i18nSimpleKey("mainComps");
type __ChargingType = {
  id: string;
  parkName: string;
  status: string;
  warn: boolean;
  chargingStationNo: string;
  parkNo: string;
  [x: string]: any;
};

const { socket } = useMainStore();
const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  type: {
    // 垃圾点/充电点/加水点/停车点
    type: String as PropType<"map_garbage" | "map_battery" | "map_water" | "map_park">,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  areaId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  stationPic: {
    type: String,
    required: true,
  },
  stationStatus: {
    type: Number,
  },
  stationList: {
    type: Array as PropType<__ChargingType[]>,
    required: true,
  },
  distributeTask2Click: {
    type: Function,
    default: undefined,
  },
  stationRealTimeInfo: {
    type: Object as PropType<any>,
    required: true,
  },
  parkNo: {
    type: String,
    required: true,
  },
});

interface StationStatus {
  stationStatus: number;
  parkingSpaceId: string;
}

interface SiteInformation {
  [areaId: string]: {
    [parkingSpaceId: string]: StationStatus;
  };
}

const sitStatusData = ref<StationStatus>({
  stationStatus: props.stationRealTimeInfo.stationStatus || 0,
  parkingSpaceId: props.stationRealTimeInfo.parkingSpaceId,
});

const moreVisible = ref(false);
const siteMarkerInfoWindowRef = ref<any>();

const closeText = ref("&#10005");

const siteConfig = sitesType.find((site) => site.value === props.stationStatus);

const showWarnText = computed(() => {
  return (warn: string) => {
    const warnMap = {
      CHARGING_ERROR: "充电充不上",
      RECYCLING_ERROR: "倒垃圾不成功",
      WATERFILLING_ERROR: "补水不成功",
    };
    return warnMap[warn as keyof typeof warnMap] || "未知异常";
  };
});

const getStatetext = computed(() => {
  switch (sitStatusData.value?.stationStatus) {
    case 0:
      return "空闲";
    case 1:
      return "占用";
    case 2:
      return "异常";
    default:
      return "未知";
  }
});

const release = () => {
  ElMessageBox.confirm("确定释放站点吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  }).then(async () => {
    try {
      await releaseParkingSpace({
        proAreaId: props.areaId,
        parkingNo: props.parkNo,
      });
      ElMessage.success("释放成功");
    } catch (error) {
      ElMessage.error("操作失败");
    }
  });
};

// 关闭站点信息窗体
const mousedownHandle = () => {
  props.window.close();
  closeChargingStatus();
};

/**
 * 场所图片
 */
const image = reactive({
  show: false,
  url: props.stationPic || new URL("@/assets/images/map_station_bg.png", import.meta.url).href,
});

/**
 * 基础信息
 */
const info = reactive({
  projectName: $t("unknown"),
  areaName: $t("unknown"),
  address: $t("unknown"),
});

onMounted(() => {
  // 获取附加信息
  getStationBaseInfo();
});

const getStationBaseInfo = async () => {
  const { projectName, areaName, address, vehNoList } = await getStaionDetail({
    areaId: props.areaId,
    stationId: props.id,
  });
  info.projectName = projectName as string;
  info.areaName = areaName as string;
  info.address = address as string;
  formOptions.deviceOptions = vehNoList!.map((item) => ({
    label: item,
    value: item,
  }));
  typeof props.distributeTask2Click === "function" && (deviceId.value = formOptions.deviceOptions[0].value as string);
};

/**
 * 设置任务
 */
const deviceId = ref("");

const formOptions = reactive({
  deviceOptions: [] as OptionsType,
});

const toStation = async () => {
  // 任务下发2.0
  if (typeof props.distributeTask2Click === "function") {
    props.distributeTask2Click();
    mousedownHandle();
    return;
  }
};

watch(
  () => socket.siteInformation,
  (newVal) => {
    if (newVal?.[props.areaId]?.[props.stationRealTimeInfo.parkingSpaceId]) {
      sitStatusData.value = newVal[props.areaId][props.stationRealTimeInfo.parkingSpaceId];
    }
  }
);
</script>

<style lang="scss" scoped>
.site-marker {
  overflow: hidden;
  padding: 6px 7px 7px 6px;
  @include wh(278px, 275px);
  @include bis("@/assets/images/map_site_marker_info_window.png");
  position: relative;
  &-status {
    position: absolute;
    top: 9px;
    left: 8px;
    // width: 48px;
    height: 19px;
    border-radius: 30px;
    background: rgba(255, 255, 255, 0.8);
    padding: 0 6px;
    font-size: 12px;
    display: flex;
    align-items: center;
    gap: 4px;
    .status-icon {
      display: inline-block;
      width: 9px;
      height: 9px;
      border-radius: 50%;
      background: #e5e6e8;
    }
    .is-free-bg {
      background: #27d4a1 !important;
    }
    .is-occupied-bg {
      background: #f9852e !important;
    }
    .is-abnormal-bg {
      background: #f8c939 !important;
    }
    .is-free-text {
      color: #27d4a1 !important;
    }
    .is-occupied-text {
      color: #f9852e !important;
    }
    .is-abnormal-text {
      color: #f8c939 !important;
    }
  }
  &-enter {
    &-from {
      opacity: 0.3;
      transform: translateX(3%);
    }
    &-active {
      transition: all 0.3s ease-out;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease-out;
    }
    &-to {
      opacity: 0.3;
      transform: translateX(-3%);
    }
  }
  &-close {
    z-index: 6;
    display: block;
    @include ct-f;
    @include wh(16px);
    @include sc(16px, rgb(85, 85, 85));
    position: absolute;
    top: 18px;
    right: 18px;
    cursor: pointer;
  }
  &-top {
    @include wh(100%, 205px);
    .panel-info {
      &-image {
        display: flex;
        @include wh(100%, 114px);
        .image {
          width: 100%;
          border-radius: 4px 4px 0 0;
          object-fit: cover;
        }
      }
      &-content {
        padding: 5px 10px;
        &-top {
          @include ct-f(y) {
            justify-content: space-between;
          }
          font-weight: 700;
          &-left {
            @include sc(13px, #383838);
            flex: 1;
          }
          &-right {
            cursor: pointer;
            user-select: none;
            font-size: 12px;
            color: #9f9fa4;
            .more-icon {
              margin-left: 4px;
              transform: rotate(-90deg);
            }
            // @include sc(12px, blue);
            // .detail-btn {
            //   display: flex;
            //   border: 1px solid #5964fb;
            //   font-weight: 400;
            //   border-radius: 2px;
            //   &-left {
            //     @include wh(43px, 23px);
            //     @include sc(12px, #9f9fa4);
            //     @include ct-f;
            //   }
            //   &-right {
            //     @include wh(33px, 23px);
            //     @include sc(12px, #fff);
            //     background: #5964fb;
            //     @include ct-f;
            //     cursor: pointer;
            //     &:hover {
            //       background-color: rgb(125, 134, 253);
            //     }
            //     &:active {
            //       background-color: rgb(58, 67, 203);
            //     }
            //   }
            // }
          }
        }
        &-center {
          margin-bottom: 10px;
          @include sc(12px, #9f9fa4);
        }
        &-bottom {
          display: flex;
          margin-top: 4px;
          width: 100%;
          max-height: 50px;
          overflow: hidden;
          &-icon {
            padding-right: 5px;
          }
          &-text {
            @include sc(12px, #383838);
            @include ells(2);
          }
        }
      }
    }
    .panel-detail {
      padding: 5px 10px;
      &-title {
        margin: 10px 5px;
        @include sc(14px, #333333);
      }
      &-content {
        flex-wrap: wrap;
        max-height: 153px;
        overflow-y: auto;
        @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
        @include ct-f(y);
        &-item {
          position: relative;
          @include wh(30px, 46px);
          margin: 0 5px 5px 5px;
          cursor: pointer;
          &.disabled {
            cursor: not-allowed;
          }
          &-icon {
            @include wh(30px, 46px);
          }
          &-inner {
            .inner-text {
              @include ct-p;
              width: 30px;
              text-align: center;
              @include sc(12px, #555555);
              &.active {
                color: #5964fb;
              }
            }
            .inner-image {
              @include ct-p;
              width: 18px;
              text-align: center;
            }
            .inner-warning-tip {
              position: absolute;
              top: 5px;
              width: 30px;
              text-align: center;
              @include sc(11px, #f9852ecc);
            }
          }
        }
      }
    }
  }
  &-bottom {
    padding: 5px;
    @include ct-f(y) {
      justify-content: space-between;
    }
  }
}

::v-deep(.more-detail-content-top) {
  padding: 0 11px;
  display: flex;
  justify-content: space-between;
}

::v-deep(.more-detail-content-top-left) {
  display: flex;
  flex-direction: column;
  gap: 4px;
  .title {
    font-size: 14px;
    font-weight: 700;
    color: #383838;
  }
  .address {
    font-size: 12px;
    color: #9f9fa4;
  }
}

::v-deep(.more-detail-content-top-right) {
  width: 60px;
  display: flex;
  align-items: center;
  gap: 4px;
  margin-left: 10px;
  .status-icon {
    display: inline-block;
    width: 9px;
    height: 9px;
    border-radius: 50%;
    background: #27d4a1;
  }
  .is-free-bg {
    background: #27d4a1 !important;
  }
  .is-occupied-bg {
    background: #f9852e !important;
  }
  .is-abnormal-bg {
    background: #f8c939 !important;
  }
  .is-free-text {
    color: #27d4a1 !important;
  }
  .is-occupied-text {
    color: #f9852e !important;
  }
  .is-abnormal-text {
    color: #f8c939 !important;
  }
}

::v-deep(.more-detail-content-info) {
  margin-top: 13px;
  padding: 8px 11px;
  border-radius: 8px;
  background: rgb(245, 248, 255);
  display: flex;
  flex-direction: column;
  gap: 10px;
  color: #555555;
}

::v-deep(.item-label) {
  margin-right: 10px;
}
</style>
