// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 visible: false快照 1`] = `
"<!--teleport start-->
<!--teleport end-->"
`;

exports[`Snapshot > 正确渲染 默认快照 1`] = `
"<!--teleport start-->
<transition-stub name="x-drawer" appear="false" persisted="true" css="true" data-v-0a277cea="">
  <div class="x-drawer" data-v-0a277cea="">
    <transition-stub name="x-drawer-mask" appear="false" persisted="false" css="true" data-v-0a277cea="">
      <div class="x-drawer-mask" data-v-0a277cea=""></div>
    </transition-stub>
    <transition-stub name="x-drawer-content" appear="false" persisted="false" css="true" data-v-0a277cea="">
      <div class="x-drawer-content" style="width: 800px;" data-v-0a277cea="">
        <div class="content-header" data-v-0a277cea="">
          <div class="content-header-title" data-v-0a277cea="">titleStr</div><span class="content-header-close" data-v-0a277cea="">✕</span>
        </div><!-- 跟随内容footer -->
        <!-- 底部固定footer -->
        <div class="content-body" style="" data-v-0a277cea="">
          <div class="content-body-slot" data-v-0a277cea=""></div>
        </div>
        <!--v-if-->
      </div>
    </transition-stub>
  </div>
</transition-stub>
<!--teleport end-->"
`;
