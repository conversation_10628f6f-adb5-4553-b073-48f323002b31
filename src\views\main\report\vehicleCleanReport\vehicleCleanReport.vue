<template>
  <section class="vehic-clean-report" ref="vehicCleanReportRef">
    <div class="vehic-clean-report-top">
      <div class="top-title">{{ $t("vehicleCleanReport") }}</div>
    </div>
    <div class="vehic-clean-report-middle">
      <div class="middle-left">
        <div class="middle-left-top">
          <x-select
            v-model:value="searchForm.vehicNo"
            :options="searchForm.vehicNoOptions"
            :popupContainer="vehicCleanReportRef"
            :placeholder="$t('PEntVehNo')"
            showSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.company"
            :options="searchForm.companyOptions"
            :popupContainer="vehicCleanReportRef"
            placeholder="请输入企业名"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.vin"
            :options="searchForm.vinOptions"
            :popupContainer="vehicCleanReportRef"
            placeholder="请选择VIN"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.project"
            :options="searchForm.projectOptions"
            :popupContainer="vehicCleanReportRef"
            placeholder="请选择项目"
            showPopSearch
            allowClear
          />
          <x-select
            v-model:value="searchForm.area"
            :options="searchForm.areaOptions"
            :popupContainer="vehicCleanReportRef"
            placeholder="请选择区域"
            showPopSearch
            allowClear
          />
        </div>
        <div class="middle-left-bottom">
          <div style="display: flex">
            <span style="line-height: 32px; margin-right: 16px">
              {{ $t("taskTime") }}
            </span>
            <DateRangePicker
              v-model:value="searchForm.opDate"
              :popupContainer="vehicCleanReportRef"
              :placeholder="[$t('startTime'), $t('endTime')]"
              format="YYYY-MM-DD HH:mm:ss"
              style="width: 370px"
              allowClear
              showTime
              showNow
            />
          </div>
          <div style="display: flex">
            <x-button
              @click="exportExcel"
              icon="export_blue"
              type="paleBlue"
              :text="$t('export')"
              style="margin-right: 12px"
            />
            <x-button
              @click="searchList"
              type="blue"
              :text="$t('search')"
              style="margin-right: 12px"
            />
            <x-button
              @click="resetSearchForm"
              type="green"
              :text="$t('reset')"
            />
          </div>
        </div>
      </div>
    </div>
    <div class="vehic-clean-report-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
      </x-table>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, watch } from "vue";
import {
  carCleanReport,
  getVehList,
  compList,
  getProjectAndArea,
  getVehAndVinList,
  exportCalculateList,
} from "@/services/api";
import { download, i18nSimpleKey, formatRoundNum } from "@/assets/ts/utils";
import type { PageSizeType } from "@/components/types";
import { DateRangePicker } from "@/components/x-date-picker";
import xButton from "@/components/x-button.vue";
// import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";

const $t = i18nSimpleKey("vehicleCleanReport");

const vehicCleanReportRef = ref<any>();

const table = reactive({
  cols: [
    {
      key: "orderNumber",
      title: $t("orderNumber"),
      width: "20",
    },
    {
      key: "deviceId",
      title: $t("deviceId"),
      width: "40",
      hover: true,
    },
    {
      key: "vin",
      title: "VIN",
      width: "40",
      hover: true,
    },
    {
      key: "projectName",
      title: "项目",
      width: "30",
      hover: true,
    },
    {
      key: "areaName",
      title: "区域名",
      width: "30",
      hover: true,
    },
    {
      key: "entName",
      title: "企业",
      width: "30",
      hover: true,
    },
    {
      key: "workNo",
      title: $t("workNo"),
      width: "30",
      hover: true,
    },
    {
      key: "workTime",
      title: $t("workTime"),
      width: "120",
      hover: true,
    },
    {
      key: "formatDuration",
      title: `${$t("taskDuration")}(h)`,
      width: "30",
    },
    {
      key: "formatMileage",
      title: `${$t("traveMiles")}(km)`,
      width: "40",
    },
    {
      key: "formatWorkArea",
      title: `${$t("cleanArea")}(m²)`,
      width: "40",
    },
    {
      key: "formatUsedWater",
      title: `${$t("useWater")}(L)`,
      width: "30",
    },
    {
      key: "formatUsedElectricity",
      title: `${$t("useElectric")}(kW·h)`,
      width: "40",
    },
    {
      key: "formatLitter",
      title: `${$t("garbageCleanNumber")}(L)`,
      width: "40",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});

onMounted(() => {
  searchList();
  getVehList().then((res) => {
    searchForm.vehicNoOptions = res.map((v) => ({ label: v, value: v }));
  });
  compList().then((res) => {
    searchForm.companyOptions = res.map(({ entName, entId }) => ({
      label: entName!,
      value: entId!,
    }));
  });
  getProjectAndArea([]).then((res) => {
    searchForm.projectOptions.push(
      ...res.map(({ name, id }) => ({
        label: name!,
        value: id!,
      }))
    );
  });
  watch(
    () => searchForm.project,
    (newV) => {
      if (newV) {
        getProjectAndArea([newV]).then((res) => {
          searchForm.areaOptions = res.map(({ name, id }) => ({
            label: name!,
            value: id!,
          }));
        });
      } else {
        searchForm.area = "";
        searchForm.areaOptions = [];
      }
    }
  );
  getVehAndVinList().then((res) => {
    searchForm.vinOptions.push(
      ...res.map(({ vin }) => ({
        label: vin!,
        value: vin!,
      }))
    );
  });
});

// 查询
type OptItem = { label: string; value: string | number };
const searchForm = reactive({
  vehicNo: "",
  vehicNoOptions: [] as { label: string; value: string }[],
  opDate: ["", ""],
  company: "",
  companyOptions: [] as OptItem[],
  project: "",
  projectOptions: [] as OptItem[],
  area: "",
  areaOptions: [] as OptItem[],
  vin: "",
  vinOptions: [] as OptItem[],
});
const searchList = async () => {
  table.loading = true;
  const { totalCount, list } = await carCleanReport({
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    ...(searchForm.vehicNo ? { deviceId: searchForm.vehicNo } : {}),
    ...(searchForm.company ? { entId: searchForm.company } : {}),
    ...(searchForm.project ? { proId: searchForm.project } : {}),
    ...(searchForm.area ? { areaId: searchForm.area } : {}),
    ...(searchForm.vin ? { vin: searchForm.vin } : {}),
    ...(searchForm.opDate[0] ? { workStart: searchForm.opDate[0] } : {}),
    ...(searchForm.opDate[1] ? { workEnd: searchForm.opDate[1] } : {}),
  });
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderNumber: (index + 1).toString().padStart(3, "0"),
            workTime: `${item.workStart} - ${
              item.end ? item.workEnd : $t("now")
            }`,
            formatDuration: (item.taskDuration / 60).toFixed(1),
            formatMileage: formatRoundNum(item.mileage, 1),
            formatWorkArea: formatRoundNum(item.finishedWorkArea, 1),
            formatUsedWater: formatRoundNum(item.usedWaterAmount, 1),
            formatUsedElectricity: formatRoundNum(
              item.usedElectricityAmount,
              1
            ),
            formatLitter: formatRoundNum(item.litter, 1),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
const resetSearchForm = () => {
  searchForm.vehicNo = "";
  searchForm.company = "";
  searchForm.project = "";
  searchForm.area = "";
  searchForm.vin = "";
  searchForm.opDate = ["", ""];
  searchList();
};
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  table.pagination[key] = value;
  searchList();
};

// 导出
const exportExcel = async () => {
  if (!checkDateRange()) {
    return;
  }
  const param = {
    deviceId: searchForm.vehicNo,
    workStart: searchForm.opDate[0] || "",
    workEnd: searchForm.opDate[1] || "",
    ...(searchForm.company ? { entId: searchForm.company } : {}),
    ...(searchForm.project ? { proId: searchForm.project } : {}),
    ...(searchForm.area ? { areaId: searchForm.area } : {}),
    ...(searchForm.vin ? { vin: searchForm.vin } : {}),
  };
  try {
    const response = await exportCalculateList(param);
    download(response);
    Message("success", $t("exportSuccess"));
  } catch (error) {
    console.log(error);
    Message("error", $t("exportFail"));
  }
};

// 校验导出日期范围
const checkDateRange = () => {
  const [startDate, endDate] = searchForm.opDate;
  if (startDate && endDate) {
    const diffMilliseconds =
      new Date(endDate).getTime() - new Date(startDate).getTime();
    const diffDays = diffMilliseconds / (1000 * 60 * 60 * 24);
    if (diffDays > 30 * 18) {
      Message("error", $t("cannotExportExce18Month"));
      searchForm.opDate = ["", ""];
      return false;
    }
  } else {
    Message("error", $t("PSelectCorrectTimeRange"));
    searchForm.opDate = ["", ""];
    return false;
  }
  return true;
};
</script>

<style lang="scss" scoped>
.vehic-clean-report {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }
  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px) {
      line-height: 36px;
    }
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include wh(100%, 76px) {
      margin-top: 20px;
    }
    .middle-left {
      width: 100%;
      @include fj {
        flex-direction: column;
      }
      &-top {
        @include fj;
        :deep(.x-select) {
          flex: 1;
        }
        :deep(.x-select + .x-select) {
          margin-left: 16px;
        }
      }
      &-bottom {
        @include fj;
        margin-top: 10px;
      }
    }
  }
  &-bottom {
    flex: 1;
    margin-top: 16px;
    width: 100%;
    overflow: hidden;
  }
}
</style>
