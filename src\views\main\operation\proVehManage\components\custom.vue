<template>
  <x-drawer
    :title="$t('addRoute')"
    :visible="props.show"
    :btnOption="{ position: 'center' }"
    footerType="following"
    bodyPadding="0 0 32px 0"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    width="544px"
  >
    <div class="custom-top">
      <div class="custom-top-title">{{ $t("speedSetting") }}</div>
      <div class="custom-top-content">
        <template
          v-for="(item, index) in speedType"
          :key="index"
        >
          <div
            class="speed-btn"
            :class="{ active: item.prop === data.speed }"
            @click="data.speed = item.prop"
          >
            <div class="speed-btn-left">
              <x-icon
                :name="item.prop === data.speed ? `${item.icon}_enable` : item.icon"
                width="28px"
                height="18px"
                style="margin-right: 15px"
              />
            </div>
            <div class="speed-btn-right">
              <span class="speed-label">{{ item.label }}</span>
              <span class="speed-value">{{ item.value }}km/h</span>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div class="custom-bottom">
      <!-- <div class="custom-bottom-left">
        <div class="custom-bottom-left-title">{{ $t("fanSetting") }}</div>
        <div class="custom-bottom-left-content">
          <div class="btn-bar">
            <template
              v-for="(item, index) in blowType"
              :key="index"
            >
              <div
                class="btn"
                :class="{ active: data.fan === item.value }"
                @click="data.fan = item.value"
              >
                {{ item.label }}
              </div>
            </template>
          </div>
        </div>
      </div> -->
      <div class="custom-bottom-right">
        <div class="custom-bottom-right-title">
          {{ $t("blowWaterSetting") }}
        </div>
        <div class="custom-bottom-right-content">
          <x-switch
            :checkedValue="1"
            :unCheckedValue="0"
            :unCheckedChildren="$t('off')"
            :checkedChildren="$t('on')"
            :checked="Boolean(data.blowWater)"
            @change="data.blowWater = data.blowWater === 1 ? 0 : 1"
          />
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="tsx" setup>
import { reactive, watch } from "vue";
import { speedType } from "@/assets/ts/config";
import { i18nSimpleKey } from "@/assets/ts/utils";
import xDrawer from "@/components/x-drawer.vue";
import xIcon from "@/components/x-icon.vue";
import xSwitch from "@/components/x-switch.vue";
const $t = i18nSimpleKey("proVehManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  speed: {
    type: Number,
    required: true,
  },
  fan: {
    type: Number,
    required: true,
  },
  blowWater: {
    type: Number,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const data = reactive({
  speed: 2,
  fan: 0,
  blowWater: 0,
});

const formSubmit = () => {
  emits("update:show", false);
  emits("confirm", data);
};

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      data.speed = props.speed;
      data.fan = props.fan;
      data.blowWater = props.blowWater;
    }
  }
);
</script>

<style lang="scss" scoped>
.custom {
  height: 100%;
  &-top,
  &-bottom-left,
  &-bottom-right {
    padding: 15px;
    background: #fff;
    border-radius: 10px;
  }
  &-top {
    margin: 10px;
    &-title {
      @include sc(14px, #555);
    }
    &-content {
      display: flex;
      padding-top: 15px;
      .speed-btn {
        @include ct-f {
          margin-right: 15px;
        }
        @include wh(122px, 48px);
        background: rgba(255, 255, 255, 0.15);
        border: 1px solid rgb(229, 229, 229);
        border-radius: 8px;
        cursor: pointer;
        &-right {
          display: flex;
          flex-direction: column;
          .speed-label {
            @include sc(14px, #555);
          }
          .speed-value {
            @include sc(12px, #9f9fa4);
          }
        }
        &.active {
          background: #ecefff;
          border: none;
          .speed-label,
          .speed-value {
            color: #5964fb;
          }
        }
      }
    }
  }
  &-bottom {
    margin: 0 10px;
    display: flex;
    justify-content: space-between;
    &-left,
    &-right {
      width: 100%;
      flex: 1;
      &-title {
        @include sc(14px, #555);
      }
    }
    &-left {
      margin-right: 10px;
      .btn-bar {
        @include ct-f(y) {
          justify-content: space-around;
        }
        padding: 2px;
        margin-top: 10px;
        border-radius: 4px;
        background: #f4f7fe;
        .btn {
          @include ct-f;
          @include wh(42px, 32px) {
            line-height: 32px;
          }
          @include sc(14px, #9f9fa4);
          cursor: pointer;
          &.active {
            @include sc(14px, #5964fb);
            background: #fff;
            border-radius: 4px;
          }
        }
      }
    }
    &-right {
      &-content {
        margin: 20px 0;
      }
    }
  }
}
</style>
