<template>
  <div class="main-content">
    <div class="main-content-header">
      <div class="main-content-header-input">
        <el-select
          v-model="formModel.vno"
          filterable
          remote
          reserve-keyword
          placeholder="请输入车牌/VIN/项目名进行搜索"
          clearable
          size="small"
          :remote-method="remoteSearch"
          :loading="loading"
          @change="handleSelectItem"
          popper-class="mainContent-custom-dropdown"
        >
          <el-option
            v-for="item in searchResults"
            :key="item.deviceId"
            :label="item.deviceId"
            :value="item.deviceId"
          >
            <div class="custom-option">
              <div class="option-top">
                <span
                  v-if="searchQuery && item.deviceId && item.deviceId.toLowerCase().includes(searchQuery.toLowerCase())"
                  v-html="highlightText(item.deviceId, searchQuery)"
                ></span>
                <span v-else>{{ item.deviceId || "-" }}</span>
                <span
                  v-if="searchQuery && item.vin?.toLowerCase().includes(searchQuery.toLowerCase())"
                  v-html="highlightText(item.vin, searchQuery)"
                ></span>
                <span v-else>{{ item.vin || "-" }}</span>
              </div>
              <div class="option-bottom">
                <span
                  v-if="
                    searchQuery &&
                    item.areaName &&
                    (item.proName + '/' + item.areaName).toLowerCase().includes(searchQuery.toLowerCase())
                  "
                  v-html="highlightText(item.proName + '/' + item.areaName, searchQuery)"
                ></span>
                <span v-else>{{ (item.proName || "-") + "/" + (item.areaName || "-") }}</span>
              </div>
            </div>
          </el-option>
        </el-select>
      </div>
      <div class="main-content-header-select">
        <el-select
          style="width: 98px"
          v-model="formModel.proName"
          placeholder="项目名称"
          size="small"
          clearable
          filterable
          @change="handleProChange"
        >
          <el-option
            v-for="item in projectList"
            :key="item.value"
            :label="item.label"
            :value="item.label"
            clearable
          />
        </el-select>

        <el-select
          style="width: 98px"
          v-model="formModel.areaName"
          placeholder="区域名称"
          clearable
          filterable
          size="small"
        >
          <el-option
            v-for="item in proAreaList"
            :key="item.value"
            :label="item.label"
            :value="item.label"
          />
        </el-select>
        <x-icon
          class="main-content-header-select-icon"
          width="16px"
          height="16px"
          name="progress_restart"
          @click="handleReset"
        />
      </div>
    </div>
    <x-radio-button
      type="tab"
      :gap="10"
      v-model:value="readTab.activeIndex"
      :options="readTab.statusList"
    />
    <el-scrollbar
      height="460px"
      class="list"
    >
      <div
        class="list-item"
        v-for="item in filterWarnList"
        :key="item.deviceId"
        @click="handelSelectCar(item)"
        :class="{ active: selectCar.deviceId === item.deviceId }"
      >
        <div class="list-item-left">
          <div class="list-item-left-device">
            <x-icon
              v-if="readTab.activeIndex === 'warn' && [2, 3].includes(item.warnLevel)"
              width="12px"
              height="12px"
              :name="item.warnLevel === 2 ? 'alarm_level_1' : 'alarm_level_2'"
              @click="handleReset"
            />
            <span
              class="list-item-left-device-text"
              :class="{
                'warn-level-2': item.warnLevel === 2 || item.warnHistoryLevel === 2,
                'warn-level-3': item.warnLevel === 3 || item.warnHistoryLevel === 3,
                online: readTab.activeIndex === 'online',
                offline: readTab.activeIndex === 'offline',
              }"
              >{{ item.deviceId }}</span
            >
          </div>
          <div class="list-item-left-vin">{{ item.vin }}</div>
        </div>

        <div class="list-item-right">
          <div class="list-item-right-state">
            <el-image
              v-if="item.trafficLightType && item.online && item.warnTraffic"
              :src="getTrafficLightType(item.trafficLightType)"
              style="width: 15px; height: 15px"
            ></el-image>
            <x-icon
              v-if="item.warnTraffic"
              width="20px"
              height="20px"
              name="traffic light_color"
            />
            <div
              class="list-item-right-state-time"
              v-if="readTab.activeIndex === 'offline'"
            >
              <span>{{ item.gpsDate ? item.gpsDate.slice(5) : " " }}</span>
              <span>{{ item.gpsTime }}</span>
            </div>
            <div
              v-if="readTab.activeIndex !== 'offline'"
              class="list-item-right-state-text"
              :class="[item.workStatus === 1 ? 'is-work' : 'no-work']"
            >
              {{ item.workStatus === 1 ? "作业中" : "未作业" }}
            </div>
          </div>
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <div class="list-item-right-project">{{ item.proName }}/{{ item.areaName }}</div>
            <template #content>
              <div style="padding: 8px">{{ item.proName }}/{{ item.areaName }}</div></template
            >
          </x-popover>
        </div>
      </div>

      <!-- 暂无数据展示 -->
      <XEmpty
        v-if="!filterWarnList || filterWarnList.length === 0"
        description="暂无数据"
      ></XEmpty>
    </el-scrollbar>
  </div>
</template>

<script setup lang="ts">
import { getProjectAndArea } from "@/services/api";
import { onBaseStatus } from "@/services/wsapi";
import { useMainStore } from "@/stores/main";
import { ref, reactive, toRefs, onMounted, watch, computed, provide } from "vue";
import xRadioButton from "@/components/x-radio-button.vue";
import xPopover from "@/components/x-popover.vue";
import dayjs from "dayjs";
import { composeEventHandlers } from "element-plus/es/utils";
const { socket } = useMainStore();
import XEmpty from "@/components/x-empty.vue";
const emit = defineEmits(["selectCar"]);
const selectCar = ref({
  deviceId: "",
});
/**   type === 1 车辆gps信息 */
const carGps = ref<_BaseStatusType[]>([]);
provide("carGps", carGps);
const warnList = ref<any[]>([]);
const searchResults = ref<any[]>([]);
const loading = ref(false);
const searchQuery = ref("");
const filterWarnList = computed(() => {
  // 显式引用formModel的属性，确保它们被追踪
  const { proName, areaName, vno } = toRefs(formModel);

  switch (readTab.activeIndex) {
    case "warn": {
      const baseCondition = warnList.value?.filter(
        (item) => item.warn && (item.warnLevel === 2 || item.warnLevel === 3)
      );
      let filteredList = baseCondition;
      if (proName.value || areaName.value || vno.value) {
        filteredList = baseCondition.filter(
          (item) =>
            (proName.value && item.proName === proName.value) ||
            (areaName.value && item.areaName === areaName.value) ||
            (vno.value && item.deviceId === vno.value)
        );
      }

      const historyList = filteredList
        .filter((item) => !item.warn && item.warnHistory)
        .sort((a, b) => {
          const timeA = Number(a.warnTrafficTime) || 0;
          const timeB = Number(b.warnTrafficTime) || 0;
          return timeB - timeA;
        });

      //实时（红绿灯校验>红绿灯通知提示>4类告警>3类告警）>历史4类>3类；优先按类型等级排序，类型里面按开始时间排序降序排列，最新报的展示在最前面
      const checkList = filteredList
        .filter((item) => item.warnTraffic)
        .sort((a, b) => {
          const timeA = Number(a.warnTrafficTime) || 0;
          const timeB = Number(b.warnTrafficTime) || 0;
          return timeB - timeA;
        });

      const noticeList = filteredList
        .filter((item) => item.warnTrafficForecast)
        .sort((a, b) => {
          const timeA = Number(a.warnTrafficForecastTime) || 0;
          const timeB = Number(b.warnTrafficForecastTime) || 0;
          return timeB - timeA;
        });

      const level3List = filteredList
        .filter((item) => item.warnLevel === 3)
        .sort((a, b) => {
          const timeA = Number(a.warnTime) || 0;
          const timeB = Number(b.warnTime) || 0;
          return timeB - timeA;
        });

      const level2List = filteredList
        .filter((item) => item.warnLevel === 2)
        .sort((a, b) => {
          const timeA = Number(a.warnTime) || 0;
          const timeB = Number(b.warnTime) || 0;
          return timeB - timeA;
        });

      const historyLevel3List = warnList.value
        .filter(
          (item) =>
            item.warnHistory &&
            item.warnHistoryLevel === 3 &&
            (!proName.value || item.proName === proName.value) &&
            (!areaName.value || item.areaName === areaName.value) &&
            (!vno.value || item.deviceId === vno.value)
        )
        .sort((a, b) => {
          const timeA = Number(a.warnHistoryTime) || 0;
          const timeB = Number(b.warnHistoryTime) || 0;
          return timeB - timeA;
        });

      const historyLevel2List = warnList.value
        .filter(
          (item) =>
            item.warnHistory &&
            item.warnHistoryLevel === 2 &&
            (!proName.value || item.proName === proName.value) &&
            (!areaName.value || item.areaName === areaName.value) &&
            (!vno.value || item.deviceId === vno.value)
        )
        .sort((a, b) => {
          const timeA = Number(a.warnHistoryTime) || 0;
          const timeB = Number(b.warnHistoryTime) || 0;
          return timeB - timeA;
        });

      // console.log(historyLevel3List, "historyLevel3List");
      const uniqueList = [
        ...new Set([
          ...checkList,
          ...noticeList,
          ...level3List,
          ...level2List,
          ...historyList,
          ...historyLevel3List,
          ...historyLevel2List,
        ]),
      ];
      return uniqueList;

      // return filteredList.sort((a, b) => {
      //   // 首先按workStatus排序，workStatus为1的排在前面
      //   if (a.workStatus === 1 && b.workStatus !== 1) return -1;
      //   if (a.workStatus !== 1 && b.workStatus === 1) return 1;

      //   // 其次按warnTraffic排序
      //   if (a.warnTraffic && !b.warnTraffic) return -1;
      //   if (!a.warnTraffic && b.warnTraffic) return 1;

      //   return 0;
      // });
    }
    case "online": {
      const baseCondition = warnList.value?.filter(
        (item) => item.online && item.warnHistoryLevel !== 2 && item.warnHistoryLevel !== 3
      );

      let filteredList = baseCondition;
      if (proName.value || areaName.value || vno.value) {
        filteredList = baseCondition.filter(
          (item) =>
            (proName.value && item.proName === proName.value) ||
            (areaName.value && item.areaName === areaName.value) ||
            (vno.value && item.deviceId === vno.value)
        );
      }

      // 添加排序，使warnTraffic有值的排在最前面
      return filteredList.sort((a, b) => {
        if (a.warnTraffic && !b.warnTraffic) return -1;
        if (!a.warnTraffic && b.warnTraffic) return 1;
        return 0;
      });
    }
    case "offline": {
      const filteredList = warnList.value?.filter(
        (item) =>
          !item.online &&
          (proName.value ? item.proName === proName.value : true) &&
          (areaName.value ? item.areaName === areaName.value : true) &&
          (vno.value ? item.deviceId === vno.value : true)
      );
      /**按照最新时间排序 */
      return filteredList.sort((a, b) => {
        const hasTimeA = a.gpsDate && a.gpsTime;
        const hasTimeB = b.gpsDate && b.gpsTime;
        if (!hasTimeA && !hasTimeB) {
          return 0;
        }
        if (hasTimeA && !hasTimeB) {
          return -1;
        }
        if (!hasTimeA && hasTimeB) {
          return 1;
        }
        const dateTimeStringA = `${a.gpsDate} ${a.gpsTime}`;
        const dateTimeStringB = `${b.gpsDate} ${b.gpsTime}`;
        const timestampA = dayjs(dateTimeStringA).valueOf();
        const timestampB = dayjs(dateTimeStringB).valueOf();
        return timestampB - timestampA;
      });
    }
    default:
      return [];
  }
});
const readTab = reactive({
  activeIndex: "warn",
  statusList: [
    { label: "异常", value: "warn", color: "#F41515" },
    { label: "正常", value: "online", color: "#28E09F" },
    { label: "离线", value: "offline", color: "#242859" },
  ],
});

const formModel = reactive({
  vno: "",
  proName: "",
  areaName: "",
});

const projectList = ref<any[]>([]);
const proAreaList = ref<any[]>([]);

const getTrafficLightType = (trafficLightType: string) => {
  if (trafficLightType === "CROSSWALK") {
    return new URL("@/assets/images/sidewalk-icon.gif", import.meta.url).href;
  } else if (trafficLightType === "INTERSECTION") {
    return new URL("@/assets/images/motor-vehicle-icon.gif", import.meta.url).href;
  } else {
    return new URL("@/assets/images/ukonw-icon.gif", import.meta.url).href;
  }
};

const handelSelectCar = (item: any) => {
  selectCar.value = item;
  emit("selectCar", item);
};

const handleSkipCar = (deviceId: string) => {
  const foundCar = warnList.value.find((item) => item.deviceId === deviceId);
  if (foundCar) {
    if (foundCar.warn && (foundCar.warnLevel === 2 || foundCar.warnLevel === 3)) {
      readTab.activeIndex = "warn";
    } else if (foundCar.online) {
      readTab.activeIndex = "online";
    } else {
      readTab.activeIndex = "offline";
    }
    handelSelectCar(foundCar);
  }
};
const remoteSearch = (query: string) => {
  if (query === "") {
    searchResults.value = [];
    searchQuery.value = "";
    return;
  }
  searchQuery.value = query;
  loading.value = true;
  setTimeout(() => {
    searchResults.value =
      warnList.value?.filter((item) => {
        const lowerQuery = query.toLowerCase();
        return (
          (item.deviceId && item.deviceId.toLowerCase().includes(lowerQuery)) ||
          (item.vin && item.vin.toLowerCase().includes(lowerQuery)) ||
          (item.proName && item.proName.toLowerCase().includes(lowerQuery)) ||
          (item.areaName && item.areaName.toLowerCase().includes(lowerQuery))
        );
      }) || [];

    loading.value = false;
  }, 200);
};

// 处理选择事件
const handleSelectItem = (deviceId: string) => {
  if (deviceId) {
    // 从warnList中找到匹配的设备
    const matchedDevice = warnList.value.find((item) => item.deviceId === deviceId);
    if (matchedDevice) {
      // 根据设备状态决定应该跳转到哪个tab
      if (matchedDevice.warn && (matchedDevice.warnLevel === 2 || matchedDevice.warnLevel === 3)) {
        readTab.activeIndex = "warn";
      } else if (matchedDevice.online) {
        readTab.activeIndex = "online";
      } else {
        readTab.activeIndex = "offline";
      }
    }
  }
};

const handleProChange = async (val: any) => {
  const proId = projectList.value.find((item) => item.label === val)?.value;
  proAreaList.value = await getProjectAndAreaList(proId);
  formModel.areaName = "";
  if (!val) {
    proAreaList.value = [];
  }
};

const handleReset = () => {
  formModel.proName = "";
  formModel.areaName = "";
  formModel.vno = "";
};

/**
 * 项目与区域级联
 */
const getProjectAndAreaList = async (proId: any) => {
  const param = proId ? [proId] : [];
  const list = (await getProjectAndArea(param)).map((item) => ({
    label: item.name,
    value: String(item.id),
  }));
  return list;
};

// 判断标签页的初始激活项
const determineActiveTab = () => {
  if (warnList.value?.some((item) => item.warn && (item.warnLevel === 2 || item.warnLevel === 3))) {
    return "warn";
  }
  if (warnList.value?.some((item) => item.online)) {
    return "online";
  }
  return "offline";
};

// 定义一个函数，用于选择当前活动tab中的第一个车辆
const selectFirstVehicleInActiveTab = () => {
  if (filterWarnList.value && filterWarnList.value.length > 0) {
    const firstVehicle = filterWarnList.value[0];
    selectCar.value = firstVehicle;
    emit("selectCar", firstVehicle);
  }
};

const isFirstLoad = ref(true);

onMounted(async () => {
  projectList.value = await getProjectAndAreaList(null);
  // 等待ws连接完成
  const reqWebsocketData = () => {
    setTimeout(() => {
      // ws断开的情况在ws.ts里已经处理
      // const timeoutId = setTimeout(() => {
      //   // ws已断开
      //   if (!socket.baseStatus) {
      //     reqWebsocketData();
      //   }
      // }, 1000);
      Promise.all([
        onBaseStatus(),
        // addCar(socket.activeCarId ? [...new Set([...socket.enableCarIds, socket.activeCarId])] : socket.enableCarIds),
      ]).catch(() => {
        // ws还在连接中
        // !stopReq.value && reqWebsocketData();
      });
      // .finally(() => {
      //   clearTimeout(timeoutId);
      // });
    }, 200);
  };
  reqWebsocketData();
});

watch(
  () => socket.baseStatus,
  (newV) => {
    console.debug("%c gps信息:", "color: red", newV);
    warnList.value = newV || [];
    carGps.value = newV || [];
    // 只在首次加载时自动切换标签页
    if (newV && isFirstLoad.value) {
      readTab.activeIndex = determineActiveTab();
      isFirstLoad.value = false;
      // 自动选择当前活动tab的第一个车辆
      selectFirstVehicleInActiveTab();
    }
  },
  { immediate: true }
);

// 当过滤列表变化时，如果当前选中的车辆不在列表中，则选择第一个
watch(
  () => filterWarnList.value,
  (newList) => {
    if (newList && newList.length > 0) {
      // 检查当前选中的车辆是否在列表中
      const currentVehicleInList = newList.some((item) => item.deviceId === selectCar.value.deviceId);
      if (!currentVehicleInList) {
        // selectFirstVehicleInActiveTab();
      }
    }
  }
);

// 高亮匹配文本的函数
const highlightText = (text: string, query: string): string => {
  if (!query) return text;

  const lowerText = text.toLowerCase();
  const lowerQuery = query.toLowerCase();

  if (!lowerText.includes(lowerQuery)) return text;

  const index = lowerText.indexOf(lowerQuery);
  const beforeMatch = text.substring(0, index);
  const matchedText = text.substring(index, index + query.length);
  const afterMatch = text.substring(index + query.length);

  return `${beforeMatch}<span class="highlight">${matchedText}</span>${afterMatch}`;
};

defineExpose({
  handleSkipCar,
});
</script>
<script lang="ts">
export default {
  name: "MainContent",
};
</script>
<style scoped lang="scss">
.main-content {
  width: 292px;
  height: 600px;
  position: absolute;
  right: 0px;
  top: 0px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  background: rgba(253, 254, 254, 0.96);
  overflow: hidden;
  // z-index: 777;

  :deep(.el-select) {
    .el-select-dropdown__item {
      height: auto !important;
      line-height: normal !important;
      min-height: 60px !important;
      overflow: visible !important;
      padding: 8px 12px !important;
    }

    .el-select-dropdown__wrap {
      max-height: 300px !important;
    }

    .el-popper__arrow {
      display: none !important;
    }
  }

  .main-content-header {
    padding: 10px;
    &-select {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-top: 10px;
      padding-right: 10px;
      &-icon {
        cursor: pointer;
      }
    }
  }
  .list {
    .list-item {
      width: 100%;
      // height: 67px;
      padding: 5px 22px;
      display: flex;
      align-items: center;
      justify-content: space-between;
      border-bottom: 1px solid #dcdcdc66;
      cursor: pointer;
      &-left {
        display: flex;
        align-items: flex-start;
        flex-direction: column;
        justify-content: space-between;
        &-device {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 14px;
          font-weight: 500;
          .warn-level-2 {
            color: #f9852e;
          }
          .warn-level-3 {
            color: #f41515;
          }
          .online {
            color: #28e09f !important;
          }
          .offline {
            color: #9f9fa4 !important;
          }
        }
        &-vin {
          font-size: 12px;
          color: #666666;
        }
      }
      &-right {
        display: flex;
        align-items: flex-end;
        flex-direction: column;
        justify-content: space-between;
        height: 39px;
        &-state {
          display: flex;
          align-items: center;
          gap: 5px;

          &-text {
            font-size: 12px;
            padding: 2px 5px;
            border-radius: 50%;

            &.is-work {
              background: rgba(89, 100, 251, 0.06);
              color: #5964fb;
            }
            &.no-work {
              background: rgb(244, 244, 244);
              color: #a7a7a7;
            }
          }
          &-time {
            font-size: 12px;
            color: #666666;
            display: flex;
            align-items: center;
            gap: 5px;
          }
        }
        &-project {
          font-size: 12px;
          color: #666666;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          max-width: 96px;
        }
        &-time {
          display: flex;
          align-items: center;
          gap: 5px;
          font-size: 12px;
          color: #666666;
        }
      }
      &:hover {
        background: #e8ecfd;
      }
      &.active {
        background: #e8ecfd;
      }
    }
    .list-item:last-child {
      margin-bottom: 150px;
    }
  }
}

.empty-data {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 40px 0;
  width: 100%;

  .empty-text {
    margin-top: 12px;
    font-size: 14px;
    color: #9f9fa4;
  }
}

.custom-option {
  display: flex;
  flex-direction: column;
  gap: 5px;
  width: 100%;
  min-height: 52px;
  padding: 4px 0;
  overflow: visible !important;

  .option-top {
    display: flex;
    flex-direction: column;
    font-size: 14px;
    color: #383838;
    font-weight: 500;
  }

  .option-bottom {
    color: #666666;
    font-size: 12px;
  }

  .highlight {
    color: #5964fb;
    font-weight: bold;
  }
}

:deep(.el-select-dropdown__item) {
  padding: 8px 12px;
  height: auto !important;
  line-height: normal !important;
  min-height: 60px !important;
  overflow: visible !important;

  &.selected,
  &.is-hovering {
    font-weight: normal;

    .option-top,
    .option-bottom {
      span.highlight {
        color: #5964fb;
      }
    }
  }

  &.hover,
  &.is-hovering {
    background-color: #f5f7fa;
  }
}
</style>

<style lang="scss">
.mainContent-custom-dropdown {
  .el-select-dropdown__item {
    height: auto !important;
    line-height: normal !important;
    min-height: 60px !important;
    overflow: visible !important;
    padding: 8px 12px !important;

    &:hover,
    &.is-hovering {
      background-color: #e8ecfd !important;
    }
  }

  .el-select-dropdown__wrap {
    max-height: 300px !important;
  }

  .el-popper__arrow {
    display: none !important;
  }

  // 添加高亮样式
  .highlight {
    color: #5964fb;
    font-weight: bold;
  }
}
</style>
