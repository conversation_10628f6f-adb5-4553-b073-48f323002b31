<template>
  <amap
    class="screen"
    :proId="project.proId"
    :mapCenterId="project.mapCenterId"
  >
    <div class="screen-left-bg"></div>
    <div class="screen-right-bg"></div>
    <div
      class="screen-top"
      :style="{ display: project.hiddenTop ? 'none' : 'flex' }"
    >
      <div class="screen-top-left">
        <x-timer-image
          imageName="screen_top_left_animate.png"
          :frameNum="150"
          :frameTime="24"
          :vertical="true"
        />
        <div class="screen-project">
          <x-select
            show-pop-search
            v-model:value="project.proId"
            :options="project.options"
          />
        </div>
      </div>
      <div class="screen-top-center">
        <x-timer-image
          imageName="screen_top_title_bg_animate.png"
          :frameNum="120"
          :frameTime="48"
          :vertical="true"
        >
          <div class="screen-title"></div>
        </x-timer-image>
      </div>
      <div class="screen-top-right">
        <div class="right-bg">
          <x-timer-image
            imageName="screen_top_right_animate.png"
            :frameNum="150"
            :frameTime="24"
            :vertical="true"
          />
        </div>
        <div
          class="go-back"
          @click="router.go(-1)"
        >
          退出
        </div>
        <div class="real-time">
          <span style="color: rgb(130, 181, 252)">
            {{ formatDateTime(main.time, "YYYY-MM-DD W ") }}
          </span>
          <span style="color: rgb(255, 255, 255)">
            {{ formatDateTime(main.time, "HH:mm:ss") }}
          </span>
        </div>
      </div>
    </div>
    <div class="screen-left">
      <div class="left-pro-view">
        <pro-view
          :proId="project.proId"
          @showVehModal="viewVehModal"
          @showUserModal="viewUserModal"
          @showAreaModal="viewAreaModal"
        />
      </div>
      <div class="left-car-work">
        <car-work :proId="project.proId" />
      </div>
      <div class="left-area-work">
        <area-work :proId="project.proId" />
      </div>
    </div>
    <div class="screen-work">
      <x-timer-image
        class="work-mile"
        imageName="screen_center_bg.png"
        :frameNum="99"
        :frameTime="50"
        :vertical="true"
      >
        <div class="work-mile-number">
          {{ thousandSeparator(work.mile) }}
        </div>
        <div class="work-mile-desc">作业里程(km)</div>
      </x-timer-image>
      <x-timer-image
        class="work-area"
        imageName="screen_center_bg.png"
        :frameNum="99"
        :frameTime="50"
        :vertical="true"
      >
        <div class="work-area-number">
          {{ thousandSeparator(work.area) }}
        </div>
        <div class="work-area-desc">作业面积(㎡)</div>
      </x-timer-image>
      <x-timer-image
        class="work-time"
        imageName="screen_center_bg.png"
        :frameNum="99"
        :frameTime="50"
        :vertical="true"
      >
        <div class="work-time-number">
          {{ thousandSeparator(work.time) }}
        </div>
        <div class="work-time-desc">作业时长(h)</div>
      </x-timer-image>
    </div>
    <div class="screen-right">
      <div class="right-work-view">
        <work-view :proId="project.proId" />
      </div>
      <div class="right-work-show">
        <work-show :proId="project.proId" />
      </div>
      <div class="right-alarm">
        <alarm />
      </div>
    </div>
  </amap>
  <proVehModal
    v-model:show="proVehReactive.show"
    :type="proVehReactive.type"
    @clickVeh="clickVeh"
  />
  <proUserModal
    v-model:show="proUserReactive.show"
    :info="proUserReactive.info"
    @clickUser="clickUser"
  />
  <proAreaModal
    v-model:show="proAreaReactive.show"
    :info="proAreaReactive.info"
  />
</template>
<script lang="ts" setup>
import { reactive, onMounted, onUnmounted, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import xTimerImage from "@/components/x-timer-image.vue";
import xSelect from "./components/x-select.vue";
import amap from "./components/amap.vue";
import carWork from "./components/carWork.vue";
import areaWork from "./components/areaWork.vue";
import proView from "./components/proView.vue";
import workView from "./components/workView.vue";
import workShow from "./components/workShow.vue";
import alarm from "./components/alarm.vue";
import proVehModal from "./components/proVehModal.vue";
import proUserModal from "./components/proUserModal.vue";
import proAreaModal from "./components/proAreaModal.vue";
import { thousandSeparator } from "@/assets/ts/utils";
import { animateTime } from "@/assets/ts/animate";
import { formatDateTime } from "@/assets/ts/dateTime";
import { screenProList } from "@/services/api";
import { useMainStore } from "@/stores/main";

const router = useRouter();
const route = useRoute();

const { socket } = useMainStore();
const work = reactive({
  mile: 0,
  area: 0,
  time: 0,
});
watch(
  () => socket.screenWorkView,
  (newV) => {
    work.mile = ~~newV.totalCalculate.mileage;
    work.area = ~~newV.totalCalculate.finishedWorkArea;
    work.time = ~~(newV.totalCalculate.taskDuration / 60);
  }
);
const main = reactive({
  time: new Date(),
  timer: null as null | Function,
});
const project = reactive({
  proId: String(route.query.proId || ""),
  hiddenTop: String(route.query.hiddenTop || ""),
  options: [],
  mapCenterId: "",
});
onMounted(() => {
  main.timer = animateTime(() => (main.time = new Date()), 1000);
  screenProList().then((res) => {
    !project.proId && (project.proId = res[0].proId);
    project.options = res.map(({ proName, proId }) => ({
      value: proId,
      label: proName,
    }));
  });
});
onUnmounted(() => {
  main.timer!();
});

const proVehReactive = reactive({
  show: false,
  type: "all",
});
const viewVehModal = (type: string) => {
  proVehReactive.show = true;
  proVehReactive.type = type;
};
const clickVeh = (id: string) => {
  proVehReactive.show = false;
  project.mapCenterId = id;
};
const proUserReactive = reactive({
  show: false,
  info: [],
});
const viewUserModal = (info: any) => {
  proUserReactive.show = true;
  proUserReactive.info = info;
};
const clickUser = (id: string) => {
  proUserReactive.show = false;
  project.mapCenterId = id;
};
const proAreaReactive = reactive({
  show: false,
  info: [],
});
const viewAreaModal = (info: any) => {
  proAreaReactive.show = true;
  proAreaReactive.info = info;
};
</script>

<style lang="scss" scoped>
.screen {
  // @extend %fullscreen;
  // overflow: hidden;
  @include wh(100%);
  &-top,
  &-left-bg,
  &-right-bg,
  &-left,
  &-right,
  &-work {
    z-index: var(--z-index-text);
  }
  &-top {
    @include wh(100%, 6.5vh);
  }
  &-left,
  &-right {
    position: absolute;
    top: calc(6.5vh + 10px);
    @include wh(468px, calc(100% - 6.5vh - 20px));
  }
  &-left-bg,
  &-right-bg {
    position: absolute;
    top: 0;
    @include wh(calc(468px + 25px), 100%);
    box-shadow: 0 0 60px 30px rgba(0, 0, 0, 0.25);
  }
  &-left-bg {
    left: 0;
    background: linear-gradient(to right, rgba(0, 0, 0, 0.37), rgba(0, 0, 0, 0.25));
  }
  &-right-bg {
    right: 0;
    background: linear-gradient(to left, rgba(0, 0, 0, 0.37), rgba(0, 0, 0, 0.25));
  }

  &-top {
    position: relative;
    background: linear-gradient(to bottom, rgb(6, 12, 36), rgba(6, 12, 36, 0));
    @include fj;
    &-left {
      position: relative;
      flex: 1;
      padding: 10px 0 10px 23px;
      .screen-project {
        @include ct-p(y) {
          right: 34px;
        }
        @include wh(156px, 50px) {
          margin-top: 16px;
        }
      }
    }
    &-center {
      @include wh(780px, 100%);
      :deep(.x-timer-image-content) {
        @include ct-f;
      }
      .screen-title {
        @include wh(622px, 2.6vh) {
          @include bis("@/assets/images/screen_top_title.png");
        }
      }
    }
    &-right {
      position: relative;
      flex: 1;
      display: flex;
      flex-direction: row-reverse;
      align-items: center;
      margin: 0 23px;
      .right-bg {
        position: absolute;
        left: -10px;
        top: 10px;
        @include wh(100%, 50px);
      }
      .go-back {
        position: relative;
        cursor: pointer;
        @include wh(54px, 2vh) {
          border-radius: 4px;
          background: linear-gradient(rgb(94, 147, 248), rgb(44, 110, 235));
        }
        @include sc(14px, rgb(249, 251, 254)) {
          font-weight: 500;
          text-align: center;
          line-height: 2vh;
        }
      }
      .real-time {
        position: relative;
        margin-right: 30px;
        @include sc(20px, #fff);
      }
    }
  }

  &-left .left-pro-view,
  &-left .left-car-work,
  &-left .left-area-work,
  &-right .right-work-view,
  &-right .right-work-show,
  &-right .right-alarm {
    width: 468px;
    padding-top: 1vh;
    border-radius: 8px;
    background: linear-gradient(to bottom, rgba(19, 22, 50, 0.71), rgba(32, 45, 66, 0.72));
  }
  &-left {
    left: 25px;
    @include fj {
      flex-direction: column;
    }
    .left-pro-view {
      flex: 389;
    }
    .left-car-work {
      margin-top: 16px;
      flex: 318;
    }
    .left-area-work {
      margin-top: 16px;
      flex: 245;
    }
  }
  &-work {
    @include ct-p(x) {
      top: calc(6.5vh + 10px + 70px);
    }
    @include fj;
    width: calc(24vw + 32px + 3vw);
    height: 4vw;
    .work-mile,
    .work-area,
    .work-time {
      flex: 1;
      height: 100%;
      border-radius: 8px;
      &-number {
        @include sc(1.4vw, #fff) {
          font-weight: 600;
        }
      }
      &-desc {
        @include sc(0.6vw, rgba(255, 255, 255, 0.8)) {
          font-weight: 500;
        }
      }
    }
    .work-mile {
      :deep(.x-timer-image-content) {
        padding: 0.5vw 0;
        @include fj {
          flex-direction: column;
          align-items: center;
        }
      }
    }
    .work-area {
      margin-left: 16px;
      :deep(.x-timer-image-content) {
        padding: 0.5vw 0;
        @include fj {
          flex-direction: column;
          align-items: center;
        }
      }
    }
    .work-time {
      margin-left: 16px;
      :deep(.x-timer-image-content) {
        padding: 0.5vw 0;
        @include fj {
          flex-direction: column;
          align-items: center;
        }
      }
    }
  }

  &-right {
    right: 25px;
    @include fj {
      flex-direction: column;
    }
    .right-work-view {
      flex: 280;
    }
    .right-work-show {
      margin-top: 16px;
      flex: 351;
    }
    .right-alarm {
      margin-top: 16px;
      flex: 323;
    }
  }
}
</style>
