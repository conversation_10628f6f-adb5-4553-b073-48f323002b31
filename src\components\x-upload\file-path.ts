/**
 * 从路径获取文件名、后缀、类型
 */
export function parseFilePath(url: string) {
  if (!url) return null;

  // 最后一个斜杠后的部分
  const aftLastSlashPart = url.split("/").pop();

  // hash
  const [removeHash] = aftLastSlashPart ? aftLastSlashPart.split("#") : [];

  // query
  const [removeQuery] = removeHash ? removeHash.split("?") : [];

  // 文件名、后缀
  const [name, suffix] = removeQuery.split(".");

  return {
    name,
    suffix,
    fullName: removeQuery,
    type: `application/${suffix}`,
  };
}
