<template>
  <section
    class="video-uav"
    :style="rtvsDivAttr.style"
  ></section>
</template>

<script setup lang="ts">
import { ref, reactive, toRefs, onMounted, computed } from "vue";
import { getUavCabinInfo, getUavToken, getUavAirportInfo } from "@/services/api";
const layoutOpts = {
  1: {
    width: 500,
    height: 280,
  },
  4: {
    width: 1000,
    height: 560,
  },
};

const rtvsDivAttr = computed(() => {
  //   const width = socket.activeCarIdVideo ? layoutOpts[playerCount.value].width : 0;
  //   const height = socket.activeCarIdVideo ? layoutOpts[playerCount.value].height : 0;
  const width = 500;
  const height = 280;
  return {
    style: {
      width: `${width}px`,
      height: `${height}px`,
    },
    width,
    height,
  };
});

const init = () => {
  getUavToken().then((res) => {
    console.log(res, "res1");
  });
  getUavCabinInfo().then((res) => {
    console.log(res, "res2");
  });
  getUavAirportInfo().then((res) => {
    console.log(res, "res3");
  });
};

onMounted(() => {
  init();
});
</script>

<style scoped lang="scss">
.video-uav {
  position: absolute;
  top: 0;
  left: 0;
  display: flex;
  transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
  background: #ffffff80;
  backdrop-filter: blur(4px);
}
</style>
