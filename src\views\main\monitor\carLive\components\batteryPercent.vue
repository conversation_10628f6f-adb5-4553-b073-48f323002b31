<template>
  <section
    :class="[
      'battery-percent',
      props.offline ? 'offline' : showBatteryWarn(props.percent) ? 'warn' : '',
    ]"
  >
    <img
      class="battery-percent-top"
      src="@/assets/images/map_battery_cover.png"
    />
    <div
      v-show="props.percent > 0"
      class="battery-percent-center"
      :style="centerStyle"
    >
      <div class="battery-percent-center-top"></div>
    </div>
    <div v-show="props.percent > 0" class="battery-percent-bottom"></div>
    <x-icon
      v-if="showIcon"
      class="battery-percent-icon"
      name="map_battery_icon"
      width="15"
      height="15"
    />
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import { showBatteryWarn } from "@/services/wsconfig";
import xIcon from "@/components/x-icon.vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  offline: {
    type: <PERSON>olean,
    default: () => false,
  },
  showIcon: {
    type: Boolean,
    default: () => false,
  },
});
const centerStyle = computed(
  () =>
    `
      height: ${(props.percent / 100) * 48}px;
    `
);
</script>

<style lang="scss" scoped>
.battery-percent {
  position: relative;
  @include wh(34.5px, 60px) {
    @include bis("@/assets/images/map_battery.png");
  }
  &-icon {
    position: absolute;
    @include ct-p(both) {
      margin-top: 3px;
    }
    z-index: 3;
  }
  &-top {
    z-index: 2;
    position: absolute;
    top: 1px;
    left: 0;
    @include wh(34.5px, 10px);
  }

  &-center {
    position: absolute;
    left: 0;
    bottom: 6px;
    width: 34.5px;
    transition: height 0.6s ease-in-out;
    background: linear-gradient(
      90deg,
      rgba(53, 193, 154, 1),
      rgba(38, 177, 141, 1) 19.084%,
      rgba(79, 218, 177, 1) 39.695%,
      rgba(68, 207, 166, 1) 75.043%,
      rgba(73, 212, 171, 1) 100%
    );
    &-top {
      z-index: 1;
      position: absolute;
      left: 0;
      top: -4px;
      @include wh(34.5px, 8px) {
        border-radius: 50%;
        background: linear-gradient(
          180deg,
          rgba(139.34, 225.4, 200.02, 1),
          rgba(74, 213, 172, 1) 99.237%
        );
      }
    }
  }
  &-bottom {
    position: absolute;
    left: 0;
    bottom: 2px;
    @include wh(34.5px, 8px) {
      border-radius: 50%;
      background: linear-gradient(
        90deg,
        rgba(53, 193, 154, 1),
        rgba(38, 177, 141, 1) 19.084%,
        rgba(79, 218, 177, 1) 39.695%,
        rgba(68, 207, 166, 1) 75.043%,
        rgba(73, 212, 171, 1) 100%
      );
    }
  }
  &.warn {
    .battery-percent-center {
      background: linear-gradient(
        90deg,
        rgba(244, 21, 21, 1),
        rgba(223, 20, 20, 1) 19.084%,
        rgba(252, 90, 90, 1) 39.695%,
        rgba(244, 21, 21, 1) 75.806%,
        rgba(244, 21, 21, 1) 100%
      );
      &-top {
        background: linear-gradient(
          180deg,
          rgba(242, 96, 117, 1),
          rgba(225, 29, 32, 1) 100%
        );
      }
    }
    .battery-percent-bottom {
      background: linear-gradient(
        90deg,
        rgba(244, 21, 21, 1),
        rgba(223, 20, 20, 1) 19.084%,
        rgba(252, 90, 90, 1) 39.695%,
        rgba(244, 21, 21, 1) 75.806%,
        rgba(244, 21, 21, 1) 100%
      );
    }
  }
  &.offline {
    .battery-percent-center {
      background: linear-gradient(
        90deg,
        rgb(128, 131, 143),
        rgba(33, 35, 59, 0.74) 24.427%,
        rgb(255, 255, 255) 50.382%,
        rgb(164, 164, 164) 75.573%,
        rgb(144, 143, 151) 100%
      );
      &-top {
        background: linear-gradient(
          180deg,
          rgb(235, 236, 236),
          rgb(173, 173, 173) 99.237%
        );
      }
    }
    .battery-percent-bottom {
      background: linear-gradient(
        90deg,
        rgb(128, 131, 143),
        rgba(33, 35, 59, 0.74) 24.427%,
        rgb(255, 255, 255) 50.382%,
        rgb(164, 164, 164) 75.573%,
        rgb(144, 143, 151) 100%
      );
    }
  }
}
</style>
