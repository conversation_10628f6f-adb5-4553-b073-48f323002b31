<template>
  <section class="x-select">
    <x-popover
      trigger="focus"
      :container="props.popupContainer"
      v-model:visible="config.popShow"
      @visibleChange="visibleChange"
      :placement="props.placement"
      :triangle="false"
    >
      <div
        v-if="props.showSearch"
        class="x-select-search-selector"
        ref="_bindSearchRef"
      >
        <x-input
          v-model:value="inputSearchVal"
          :placeholder="props.placeholder"
          @focus="config.popShow = true"
          suffix="input_search"
          allowClear
          :filter="filter"
          :maxlength="props.maxlength"
          :disabled="props.disabled"
          @clear="clearHandle"
        />
        <span
          :class="['selector-arrow', { 'pop-show': config.popShow }]"
          @mouseover="showClearIcon = true"
        ></span>
      </div>

      <div
        v-else
        :class="['x-select-normal-selector', { 'pop-show': config.popShow, 'is-disabled': props.disabled }]"
        ref="_bindNormalRef"
        @click="onClickTrigger"
      >
        <span :class="['selector-label', { disable: !label && !props.value }]">
          {{ (label ? label : props.value ? props.value : props.placeholder) || $t("PSelect") }}
        </span>
        <x-icon
          v-if="props.allowClear && props.value && showClearIcon"
          name="input_clear"
          @mousedown="clearHandle"
          @mouseleave="showClearIcon = false"
        />
        <span
          v-else
          :class="['selector-arrow', { 'pop-show': config.popShow }]"
          @mouseover="showClearIcon = true"
        ></span>
      </div>
      <template #content>
        <!-- 卡片：有搜索框 -->
        <div v-if="showPopSearch && !showSearch">
          <div
            v-show="props.options.length"
            class="x-select-normal-list"
            :style="{ width: `${_width}px` }"
          >
            <div class="x-select-pop-search">
              <x-input
                autofocus
                :value="popSearchValue"
                @update:value="popSearchValueUpdate"
                :placeholder="props.placeholder"
                suffix="input_search"
                allowClear
              />
            </div>
            <div
              :class="[
                'list-item',
                {
                  'list-item-check': popSearchValue === item.label,
                },
              ]"
              v-for="(item, index) in popOptionsFilter"
              :key="index"
              @click.stop="handleItemClick(item.value)"
            >
              <template v-if="String(item.label).includes(popSearchValue)">
                <template
                  v-for="(_item, _index) in item.label"
                  :key="_index"
                >
                  <template v-if="String(popSearchValue).includes(_item)">
                    <span style="color: #5964fb">{{ _item }}</span>
                  </template>
                  <template v-else>{{ _item }}</template>
                </template>
              </template>
              <template v-else>{{ item.label }}</template>
            </div>
          </div>
          <div
            v-show="!props.options.length"
            class="x-select-normal-list"
            :style="{ width: `${_width}px` }"
          >
            <div class="list-item-empty">{{ $t("noData") }}</div>
          </div>
        </div>
        <!-- 卡片：无搜索框 -->
        <div v-else>
          <div
            v-if="optionsFilter.length > 0"
            class="x-select-normal-list"
            :style="{ width: `${_width}px` }"
          >
            <div
              :class="[
                'list-item',
                {
                  'list-item-check': props.value === item.label,
                },
              ]"
              v-for="(item, index) in optionsFilter"
              :key="index"
              @click.stop="handleItemClick(item.value)"
            >
              <template v-if="String(item.label).includes(String(inputSearchVal)) && props.showSearch">
                <template
                  v-for="(_item, _index) in item.label"
                  :key="_index"
                >
                  <template v-if="String(inputSearchVal).includes(_item)">
                    <span style="color: #5964fb">{{ _item }}</span>
                  </template>
                  <template v-else>{{ _item }}</template>
                </template>
              </template>
              <template v-else>{{ item.label }}</template>
            </div>
          </div>
          <div
            v-else
            class="x-select-normal-list"
            :style="{ width: `${_width}px` }"
          >
            <div class="list-item-empty">{{ $t("noData") }}</div>
          </div>
        </div>
      </template>
    </x-popover>
  </section>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, watch } from "vue";
import type { PropType } from "vue";
import xPopover from "@/components/x-popover.vue";
import xInput from "@/components/x-input.vue";
import xIcon from "@/components/x-icon.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("xComps");
export type OptionsType<T = any> = {
  value: T;
  label: string;
  [x: string]: any;
}[];
const props = defineProps({
  value: {
    type: [String, Number, Boolean] as PropType<any>,
  },
  options: {
    type: Array as PropType<OptionsType>,
    required: true,
  },
  popupContainer: {
    type: HTMLElement as PropType<HTMLElement>,
  },
  filterOption: {
    type: Function as PropType<
      // eslint-disable-next-line no-unused-vars
      (value: string | number | undefined, options: OptionsType) => OptionsType
    >,
  },
  // 选择框支持搜索
  showSearch: {
    type: Boolean,
    default: false,
  },
  // 卡片内支持搜索
  showPopSearch: {
    type: Boolean,
    default: false,
  },
  placeholder: {
    type: String,
    default: "",
  },
  maxlength: {
    type: Number,
  },
  allowClear: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  /** 卡片弹出位置 `"rightBottom" | "bottom" | "leftBottom" | "top"`*/
  placement: {
    type: String as PropType<InstanceType<typeof xPopover>["$props"]["placement"]>,
    default: "bottom",
  },
});

const emits = defineEmits(["update:value", "change", "itemClick", "clear"]);
const label = computed(() => {
  const _item = props.options.find((v) => v.value === props.value);
  return (_item && _item.label) || "";
});
const inputSearchVal = ref<string | number>(label.value || props.value || "");
const optionsFilter = computed(() => {
  let _options = props.options;
  if (props.filterOption) _options = props.filterOption(inputSearchVal.value, _options);
  if (props.showSearch) {
    _options = _options.filter((v) => String(v.label).includes(String(inputSearchVal.value)));
  }
  return _options;
});

const popSearchValue = ref("");
const popSearchValueUpdate = (value: string) => {
  popSearchValue.value = value;
};
const visibleChange = (visible: boolean) => {
  if (!visible) popSearchValue.value = "";
};
const popOptionsFilter = computed(() => {
  let _options = props.options;
  if (props.filterOption) _options = props.filterOption(popSearchValue.value, _options);
  if (props.showPopSearch) {
    _options = _options.filter((v) => {
      return String(v.label).includes(String(popSearchValue.value));
    });
  }
  return _options;
});
const config = reactive({
  popShow: false,
});
const handleItemClick = (value: OptionsType[0]["value"]) => {
  const target = props.options.find((v) => v.value === value);
  emits("update:value", value);
  emits("change", value);
  emits("itemClick", target);
  config.popShow = false;
  showClearIcon.value = false;
  if (props.showSearch) inputSearchVal.value = target?.value ?? "";
};
const _bindNormalRef = ref<any>();
const _bindSearchRef = ref<any>();
const _width = ref(0);
watch(
  () => config.popShow,
  (newV) => {
    if (newV) {
      _width.value = (props.showSearch ? _bindSearchRef.value : _bindNormalRef.value).getBoundingClientRect().width;
    }
  }
);
/**
 * 触发器被点击
 */
const onClickTrigger = () => {
  if (props.disabled) return;
  // 显示气泡层
  if (config.popShow === false) {
    config.popShow = true;
  }
};
const filter = (v: string) => v.trim();
const showClearIcon = ref(false);
const clearHandle = () => {
  emits("update:value", "");
  emits("change", "");
  if (props.showSearch) inputSearchVal.value = "";
};
watch(
  () => props.value,
  (newV) => {
    if (props.showSearch) inputSearchVal.value = newV ?? "";
  }
);
</script>

<style lang="scss" scoped>
.x-select {
  width: 100%;
  &-search-selector {
    width: 100%;
  }
  &-normal-selector {
    cursor: pointer;
    @include fj {
      align-items: center;
    }
    color: #555;
    @include wh(100%, 32px) {
      padding: 0 9px;
      border-radius: 4px;
      background-color: #fff;
      border: 1px solid rgb(220, 220, 220);
    }
    &.pop-show {
      border-color: #5964fb;
    }
    &.is-disabled {
      opacity: 0.7;
      cursor: not-allowed;
    }
    .selector-label {
      @include ell;
      width: 0;
      flex: 1 0 auto;
      color: #555;
      &.disable {
        color: #9f9fa4;
      }
    }
    .selector-arrow {
      @include triangle(4px, 6px, rgb(220, 220, 220), bottom);
      transition: all 0.2s ease;
      &.pop-show {
        border-bottom: 6px solid #5964fb;
        border-top: none;
      }
    }
  }
  &-pop-search {
    margin-bottom: 5px;
  }
  &-normal-list {
    padding: 8px 5px;
    max-height: 256px;
    @include scrollbar(both, 4px) {
      overflow: auto;
    }
    background-color: #fff;
    box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2), 0px 4px 30px rgba(0, 0, 0, 0.1);
    .list-item {
      cursor: pointer;
      @include wh(100%, 40px) {
        height: 40px;
        padding-left: 7px;
        border-radius: 4px;
      }
      color: #383838;
      line-height: 40px;
      white-space: nowrap;
      &:hover {
        color: #5964fb;
        background-color: #d9e0fb;
      }
      @media screen and (max-width: 1280px) {
        zoom: 0.75;
      }
    }
    .list-item-empty {
      @include wh(100%, 40px) {
        height: 40px;
        padding-left: 7px;
        border-radius: 4px;
      }
      color: #9f9fa4;
      line-height: 40px;
      white-space: nowrap;
    }
    .list-item-check {
      color: #5964fb;
      background-color: #d9e0fb;
    }
  }
}
</style>
