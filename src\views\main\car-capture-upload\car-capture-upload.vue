<template>
  <section class="car-capture-upload">
    <div class="car-capture-upload-left">
      <el-upload
        v-model:file-list="fileList"
        :auto-upload="false"
        :show-file-list="false"
        :on-change="handleChange"
        :on-remove="handleRemove"
        accept=".png,.jpg"
        drag
      >
        <x-icon
          name="button_add"
          width="30"
          height="30"
        />
        <template #tip>
          <div class="el-upload__tip">支持上传png、jpg文件</div>
        </template>
      </el-upload>

      <!-- 文件信息显示区域 -->
      <div
        v-if="fileList.length > 0"
        class="file-info"
      >
        <div class="file-info-item">
          <span class="file-name">{{ fileList[0].name }}</span>
          <span class="file-size">{{ formatFileSize(fileList[0].size) }}</span>
          <x-icon
            class="file-remove"
            @click="handleRemove"
            name="input_msg_false"
            width="15"
            height="15"
          />
        </div>
      </div>
    </div>
    <div class="car-capture-upload-right">
      <div class="form-wrapper">
        <div class="form-item">
          <label>车牌号</label>
          <el-select
            v-model="fomrModel.deviceId"
            filterable
            placeholder="请选择车牌号"
            clearable
            style="width: 150px"
          >
            <el-option
              v-for="item in selectOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="form-item">
          <label>类型</label>
          <el-select
            v-model="fomrModel.type"
            filterable
            placeholder="请选择类型"
            clearable
            style="width: 180px"
          >
            <el-option
              v-for="item in carCaptureTypeOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <div class="form-item">
          <label>车辆能否自行处理</label>
          <el-select
            v-model="fomrModel.selfProcessing"
            placeholder="请选择车辆能否自行处理"
            clearable
            style="width: 150px"
          >
            <el-option
              label="是"
              :value="true"
            />
            <el-option
              label="否"
              :value="false"
            />
          </el-select>
        </div>
        <div class="form-item">
          <label>经度</label>
          <el-input
            v-model="fomrModel.longitude"
            style="width: 150px"
          />
        </div>
        <div class="form-item">
          <label>纬度</label>
          <el-input
            v-model="fomrModel.latitude"
            style="width: 150px"
          />
        </div>
        <div class="form-item">
          <el-button
            type="primary"
            :disabled="fileList.length === 0"
            @click="handleUpload"
            >上传</el-button
          >
        </div>
      </div>
      <div class="image-preview">
        <canvas
          ref="canvasRef"
          :class="['canvas', { 'is-drawing': drawPattern }]"
          :width="canvasWidth"
          :height="canvasHeight"
          @mousedown="drawStart"
          @mousemove="drawMoving"
          @mouseup="drawEnd"
          @mouseout="drawEnd"
        />
      </div>
    </div>
  </section>
</template>

<script setup lang="ts">
import { carCaptureTypeOpts } from "@/services/wsconfig";
import { getLocalStorage } from "@/assets/ts/storage";
import axios from "axios";
import { ref, onUnmounted, reactive, onMounted } from "vue";
import { ElMessage, UploadFile } from "element-plus";
import xIcon from "@/components/x-icon.vue";
import { getVehAndVinList } from "@/services/api";
import type { CameraSnapAndUploadTempResponse } from "@/services/type";
import { debounce } from "@/assets/ts/utils";
/** Canvas 相关 */
const canvasRef = ref<HTMLCanvasElement>();
let canvasCtx: CanvasRenderingContext2D | null = null;
const drawPattern = ref<"rect">();
const isDrawing = ref<boolean>(false);
const drawData = {
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
};
const canvasWidth = ref(1920);
const canvasHeight = ref(1080);
const rootRef = ref<HTMLDivElement>();
const selectedCapture = ref<CameraSnapAndUploadTempResponse>();
const fomrModel = reactive({
  deviceId: "",
  type: "",
  selfProcessing: false,
  longitude: "",
  latitude: "",
});
const fileList = ref<UploadFile[]>([]);
const previewUrl = ref<string>("");
const selectOpts = ref<any[]>([]);
const uploading = ref<boolean>(false);

const loadPercent = ref<number>(0);
// 初始化Canvas上下文
const initCanvas = () => {
  if (!canvasRef.value) {
    console.error("Canvas element not found");
    return false;
  }

  const ctx = canvasRef.value.getContext("2d");
  if (!ctx) {
    console.error("Failed to get canvas context");
    return false;
  }

  canvasCtx = ctx;
  return true;
};

const handleUpload = debounce(async () => {
  if (!fomrModel.deviceId || !fomrModel.type || !fomrModel.longitude || !fomrModel.latitude)
    return ElMessage.error("请选择车牌号、类型、经度、纬度");
  canvasRef.value!.toBlob(
    (blob) => {
      if (blob) {
        uploading.value = true;
        axios
          .post(import.meta.env.VITE_BASE_URL + "/vehicle/control/common/handlePicTemp", blob, {
            headers: { "Content-Type": "multipart/form-data", token: getLocalStorage("token") },
            transformRequest: [
              function (file) {
                const formData = new FormData();
                formData.append("file", file);
                formData.append("deviceId", fomrModel.deviceId);
                formData.append("type", String(fomrModel.type));
                formData.append("selfProcessing", String(fomrModel.selfProcessing));
                formData.append("longitude", fomrModel.longitude);
                formData.append("latitude", fomrModel.latitude);
                return formData;
              },
            ],
            onUploadProgress: (progressEvent) => {
              loadPercent.value = progressEvent.total ? ((progressEvent.loaded / progressEvent.total) * 100) | 0 : 0;
            },
          })
          .then((res) => {
            uploading.value = false;
            ElMessage.success("上传成功");
            // 清空表单数据
            fomrModel.deviceId = "";
            fomrModel.type = "";
            fomrModel.selfProcessing = false;
            fomrModel.longitude = "";
            fomrModel.latitude = "";
            fileList.value = [];
            if (previewUrl.value) {
              URL.revokeObjectURL(previewUrl.value);
              previewUrl.value = "";
            }
            // 清空canvas
            if (canvasCtx && canvasRef.value) {
              canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
            }
            // 重置canvas尺寸
            canvasWidth.value = 1920;
            canvasHeight.value = 1080;
            if (canvasRef.value) {
              canvasRef.value.width = 1920;
              canvasRef.value.height = 1080;
              canvasRef.value.style.width = "";
              canvasRef.value.style.height = "";
            }
            // 重置绘制状态
            drawPattern.value = undefined;
            isDrawing.value = false;
            drawData.startX = 0;
            drawData.startY = 0;
            drawData.endX = 0;
            drawData.endY = 0;
          });
      }
    },
    "image/jpeg",
    0.95
  );
});

const handleChange = async (file: UploadFile) => {
  if (!file.raw) return;

  try {
    await new Promise((resolve) => setTimeout(resolve, 0));

    if (!initCanvas()) {
      console.error("Failed to initialize canvas");
      return;
    }

    const img = new Image();

    await new Promise((resolve, reject) => {
      img.onload = () => {
        try {
          const targetWidth = 1920;
          const targetHeight = 1080;
          let width = img.width;
          let height = img.height;

          const scale = Math.min(targetWidth / width, targetHeight / height);
          width = width * scale;
          height = height * scale;

          canvasWidth.value = width;
          canvasHeight.value = height;

          if (!canvasRef.value) return;
          canvasRef.value.width = width;
          canvasRef.value.height = height;

          const containerHeight = rootRef.value?.clientHeight || 0;
          const styleScale = (containerHeight - 60) / height;
          canvasRef.value.style.width = width * styleScale + "px";
          canvasRef.value.style.height = height * styleScale + "px";

          if (!canvasCtx) return;
          canvasCtx.clearRect(0, 0, width, height);

          // 保存图片对象到组件实例中
          const imgObj = new Image();
          imgObj.src = img.src;
          imgObj.onload = () => {
            if (!canvasCtx || !canvasRef.value) return;
            canvasCtx.drawImage(imgObj, 0, 0, width, height);
            // 启用绘制模式
            drawPattern.value = "rect";
          };

          resolve(null);
        } catch (error) {
          console.error("绘制图片时出错:", error);
          reject(error);
        }
      };
      img.onerror = (error) => {
        console.error("图片加载失败:", error);
        reject(error);
      };

      const blobUrl = URL.createObjectURL(file.raw);
      img.src = blobUrl;
      previewUrl.value = blobUrl;
    });
  } catch (error) {
    console.error("处理图片时出错:", error);
  }
};

const drawStart = (e: MouseEvent) => {
  if (!canvasCtx || !canvasRef.value) return;
  isDrawing.value = true;

  // 计算鼠标在canvas上的实际位置
  const rect = canvasRef.value.getBoundingClientRect();
  const scaleX = canvasRef.value.width / rect.width;
  const scaleY = canvasRef.value.height / rect.height;

  drawData.startX = (e.clientX - rect.left) * scaleX;
  drawData.startY = (e.clientY - rect.top) * scaleY;

  if (drawPattern.value === "rect") {
    canvasCtx.moveTo(drawData.startX, drawData.startY);
  }
};

const drawMoving = (e: MouseEvent) => {
  if (!isDrawing.value || !canvasCtx || !canvasRef.value) return;

  try {
    const rect = canvasRef.value.getBoundingClientRect();
    const scaleX = canvasRef.value.width / rect.width;
    const scaleY = canvasRef.value.height / rect.height;

    drawData.endX = (e.clientX - rect.left) * scaleX;
    drawData.endY = (e.clientY - rect.top) * scaleY;

    // 重绘图片
    const img = new Image();
    img.onload = () => {
      if (!canvasCtx || !canvasRef.value) return;

      // 清空canvas
      canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);

      // 重绘图片
      canvasCtx.drawImage(img, 0, 0, canvasRef.value.width, canvasRef.value.height);

      // 绘制矩形
      drawRect();
    };
    img.src = previewUrl.value;
  } catch (error) {
    console.error("绘制移动时出错:", error);
  }
};

const drawEnd = (e: MouseEvent) => {
  if (!isDrawing.value || !canvasRef.value) return;

  const rect = canvasRef.value.getBoundingClientRect();
  const scaleX = canvasRef.value.width / rect.width;
  const scaleY = canvasRef.value.height / rect.height;

  drawData.endX = (e.clientX - rect.left) * scaleX;
  drawData.endY = (e.clientY - rect.top) * scaleY;

  isDrawing.value = false;
};

/** 绘制矩形 */
const drawRect = () => {
  if (!canvasCtx) return;
  canvasCtx.lineWidth = 4;
  canvasCtx.strokeStyle = "red";
  canvasCtx.beginPath();
  canvasCtx.strokeRect(
    drawData.startX,
    drawData.startY,
    drawData.endX - drawData.startX,
    drawData.endY - drawData.startY
  );
  canvasCtx.closePath();
  canvasCtx.stroke();
};

const handleRemove = () => {
  // 清空文件列表和预览
  fileList.value = [];
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
    previewUrl.value = "";
  }
  drawPattern.value = undefined;
  if (canvasCtx && canvasRef.value) {
    canvasCtx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
  }
  // 重置canvas尺寸
  canvasWidth.value = 1920;
  canvasHeight.value = 1080;
  if (canvasRef.value) {
    canvasRef.value.width = 1920;
    canvasRef.value.height = 1080;
    canvasRef.value.style.width = "";
    canvasRef.value.style.height = "";
  }
  // 重置绘制状态
  isDrawing.value = false;
  drawData.startX = 0;
  drawData.startY = 0;
  drawData.endX = 0;
  drawData.endY = 0;
};

// 格式化文件大小
const formatFileSize = (size: number | undefined) => {
  if (!size) return "0 B";
  const units = ["B", "KB", "MB", "GB"];
  let index = 0;
  while (size >= 1024 && index < units.length - 1) {
    size /= 1024;
    index++;
  }
  return `${size.toFixed(2)} ${units[index]}`;
};

const getCarList = async () => {
  const res = await getVehAndVinList();
  selectOpts.value = res.map((v) => ({
    label: v.vehicleNo,
    value: v.vehicleNo,
  }));
};
getCarList();

// 组件卸载时清理URL
onUnmounted(() => {
  if (previewUrl.value) {
    URL.revokeObjectURL(previewUrl.value);
  }
});
</script>

<style scoped lang="scss">
.car-capture-upload {
  display: flex;
  column-gap: 10px;
  padding: 10px 0 10px 10px;
  height: 100%;
  &-left {
    width: 10%;
    min-width: 200px;

    .file-info {
      margin-top: 10px;
      padding: 10px;
      border: 1px solid #eee;
      border-radius: 4px;

      .file-info-item {
        display: flex;
        align-items: center;
        gap: 10px;

        .file-name {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          font-size: 14px;
          color: #333;
        }

        .file-size {
          color: #666;
          font-size: 12px;
        }
        .file-remove {
          cursor: pointer;
        }
      }
    }
  }
  &-right {
    flex: 1;
    height: 100%;
    padding: 0 10px 0 10px;
    border-left: 1px solid #eee;
    .form-wrapper {
      display: flex;
      align-items: center;
      gap: 10px;
      .form-item {
        display: flex;
        align-items: center;
        gap: 10px;
      }
    }

    .image-preview {
      width: 100%;
      height: calc(100% - 60px);
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #fff;
      border: 1px solid #eee;
      border-radius: 4px;
      overflow: hidden;
      position: relative;
      margin-top: 10px;

      .canvas {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        object-fit: contain;
        background-color: #fff;

        &.is-drawing {
          cursor: crosshair;
        }
      }
    }
  }

  .el-upload__tip {
    color: #666;
    font-size: 12px;
    margin-top: 5px;
  }
}
</style>
