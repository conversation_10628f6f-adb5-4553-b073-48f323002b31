// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 默认快照 1`] = `
"<section class="x-pagination" data-v-b7258937=""><span class="x-pagination-total" data-v-b7258937="">共30条</span>
  <ul class="x-pagination-page" data-v-b7258937="">
    <li class="page-prev" data-v-b7258937=""><span class="page-prev-arrow" data-v-b7258937=""></span></li>
    <li class="page-num" data-v-b7258937="">1</li>
    <li class="page-num enable" data-v-b7258937="">2</li>
    <li class="page-num" data-v-b7258937="">3</li>
    <li class="page-next" data-v-b7258937=""><span class="page-next-arrow" data-v-b7258937=""></span></li>
    <li class="page-size" data-v-b7258937="">
      <x-select-stub options="[object Object],[object Object],[object Object],[object Object]" filteroption="[Function]" showsearch="false" showpopsearch="false" placeholder="" allowclear="false" disabled="false" style="width: 90px;" value="10" data-v-b7258937=""></x-select-stub>
    </li>
    <li class="page-jump" data-v-b7258937=""> 跳至 <input type="text" oninput="value=value.replace(/[^\\d]/g,'')" data-v-b7258937=""> 页 </li>
  </ul>
</section>"
`;
