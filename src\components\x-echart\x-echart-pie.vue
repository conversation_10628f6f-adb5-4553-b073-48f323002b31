<!-- 
<x-echart-pie style="width: 500px;height:500px" :pieData="pieData" />
const pieData = [
  { name: "上衣", value: 14 },
  { name: "裤子", value: 19 },
  { name: "鞋子", value: 19 },
  { name: "厨具", value: 18 },
  { name: "家具", value: 18 },
  { name: "床上用品", value: 19 },
  { name: "女装", value: 35 },
]; 
-->

<template>
  <section class="x-echart-pie">
    <echart-base :options="options"></echart-base>
  </section>
</template>

<script lang="ts" setup>
import { computed } from "vue";
import type { EChartsOption } from "echarts";
import EchartBase from "./base.vue";

const props = withDefaults(
  defineProps<{
    pieOptionType?: "default" | "test";
    pieData: { name: string; value: any }[];
  }>(),
  {
    pieOptionType: "default",
  }
);

const options = computed<EChartsOption>(() => {
  const baseOption = {
    tooltip: {
      trigger: "item",
    },
    legend: {
      orient: "horizontal",
      left: "left",
    },
  };
  return {
    default: {
      ...baseOption,
      series: [
        {
          name: "分类数据",
          type: "pie",
          radius: "50%",
          data: props.pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    },
    test: {
      ...baseOption,
      series: [
        {
          name: "分类数据",
          type: "pie",
          radius: "50%",
          data: props.pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: "rgba(0, 0, 0, 0.5)",
            },
          },
        },
      ],
    },
  }[props.pieOptionType] as EChartsOption;
});
</script>

<style lang="scss" scoped>
.x-echart-pie {
  @include wh(100%);
}
</style>
