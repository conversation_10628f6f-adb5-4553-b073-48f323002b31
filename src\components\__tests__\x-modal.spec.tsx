import { describe, it, expect, beforeEach, vi } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import xModal from "@/components/x-modal";
import { getEventListeners } from "events";

beforeEach(() => {
  vi.useFakeTimers();
  document.body.outerHTML = '';
});

const baseProps = { visible: true, title: 'titleStr' }
const btnOption = {
  cancel: 'cancelStr',
  confirm: 'confirmStr',
  position: 'end'
}

describe('Snapshot', () => {
  // x-modal.vue
  it('正确渲染 默认快照', () => {
    mount(xModal,{
      props: { ...baseProps, btnOption }
    });
    expect(document.querySelector('.x-modal')).toMatchSnapshot();
  });

  // modal.vue
  it('正确渲染 xModal.function 快照', () => {
    xModal.confirm({ title: 'confirm的标题' })
    expect(document.querySelector('.Modal')).toMatchSnapshot();
  });

});

describe('Props Render', () => {
  // x-modal.vue
  it('正确渲染 title + btnOption + width + height + bodyStyle', () => {
    mount(xModal,{
      props: { ...baseProps, btnOption, width: '400px', height: '200px', bodyStyle: { padding: '10px' } }
    });
    const contentNode = document.querySelector('.x-modal-content');
    const bodyNode = document.querySelector('.content-body');
    const btnOptionNode = contentNode.querySelector('.content-body-button');
    expect(document.querySelector('.content-header-title')?.innerHTML).toBe('titleStr');
    expect(btnOptionNode.childNodes[0].querySelector('span').innerHTML).toBe('cancelStr');
    expect(btnOptionNode.childNodes[1].querySelector('span').innerHTML).toBe('confirmStr');
    expect(btnOptionNode.style.justifyContent).toBe('end');
    expect(contentNode.style.width).toBe('400px');
    expect(contentNode.style.height).toBe('200px');
    expect(bodyNode.style.padding).toBe('10px');
  });

  // modal.vue
  it('正确渲染 props', () => {
    xModal.confirm({ 
      title: 'confirm的标题',
      icon: 'warn',
      width: '100px',
      content: <div>-content-</div>,
      confirm: Promise.resolve,
      confirmText: '111',
      cancel: Promise.resolve,
      cancelText: '222'
    })
    const buttonWrapNode = document.querySelector('.content-body-button');
    expect(document.querySelector('.title-text')?.innerHTML).toBe('confirm的标题');
    expect(document.querySelector('.title-icon')?.innerHTML.includes('#icon-modal_warn')).toBe(true);
    expect(document.querySelector('.modal-content').style.width).toBe('100px');
    expect(document.querySelector('.content-body-slot')?.innerHTML).toBe('<div>-content-</div>');
    expect(buttonWrapNode?.firstChild?.innerHTML.includes('>222<')).toBe(true);
    expect(buttonWrapNode?.lastChild?.innerHTML.includes('>111<')).toBe(true);
  });
});

describe('Events', () => {
  // x-modal.vue
  it('maskClosable: true时点击右上角X     可关闭', async () => {
    const wrapper = mount(xModal,{
      props: {  ...baseProps, maskClosable: true }
    });
    document.querySelector('.x-modal-mask').click();
    expect(wrapper.emitted()['update:visible'][0][0]).toBe(false)
  });

  it('maskClosable: false时点击右上角X    无法关闭', async () => {
    const wrapper = mount(xModal,{
      props: {  ...baseProps, maskClosable: false }
    });
    document.querySelector('.x-modal-mask').click();
    expect(wrapper.emitted()['update:visible']).toBeUndefined()
  });
  
  // modal.vue
  it('验证 confirm 和 cancel 的点击效果', async () => {
    const spyOnFunc = {
      confirmHasCall: false,
      confirmThenHasCall: false,
      cancelHasCall: false,
      cancelThenHasCall: false,
    }
    xModal.confirm({
      title: 'confirm的标题',
      confirm() {
        spyOnFunc.confirmHasCall = true;
        return new Promise((resolve)=>{
          spyOnFunc.confirmThenHasCall = true;
          resolve()
        })
      },
      cancel() {
        spyOnFunc.cancelHasCall = true;
        return new Promise((resolve)=>{
          spyOnFunc.cancelThenHasCall = true;
          resolve()
        })
      },
    })
    const buttonWrapNode = document.querySelector('.content-body-button');
    buttonWrapNode?.lastChild.click();
    expect(spyOnFunc.confirmHasCall).toBe(true)
    expect(spyOnFunc.confirmThenHasCall).toBe(true)
    buttonWrapNode?.firstChild.click();
    expect(spyOnFunc.cancelHasCall).toBe(true)
    expect(spyOnFunc.cancelThenHasCall).toBe(true)
  });

})
