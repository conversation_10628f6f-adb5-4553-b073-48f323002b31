<template>
  <section class="x-input">
    <div
      :class="['x-input-content', { focus: isFocus }]"
      @click.stop="_inputRef.focus()"
    >
      <div
        v-if="props.prefix"
        class="content-prefix"
      >
        <x-icon :name="props.prefix" />
      </div>

      <input
        :autocomplete="props.type === 'password' ? 'new-password' : 'on'"
        class="content-input"
        ref="_inputRef"
        :style="{ paddingLeft: props.prefix ? 2 : 10 + 'px' }"
        :type="props.type"
        :value="props.value"
        :placeholder="props.placeholder"
        :disabled="props.disabled"
        :autofocus="props.autofocus"
        @input="inputHandle"
        @focus="(isFocus = true) && emits('focus')"
        @blur="(isFocus = false) || emits('blur')"
        @keydown.space.prevent
        @change="inputChange"
      />

      <div
        v-if="showClear || props.suffix"
        :class="['content-suffix', { 'show-clear': showClear }]"
        @mousedown.stop="clearHandle"
      >
        <x-icon :name="showClear ? 'input_clear' : props.suffix" />
      </div>
    </div>
    <div class="x-input-message"></div>
  </section>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted, nextTick } from "vue";
import type { PropType } from "vue";

const props = defineProps({
  value: {
    type: [String, Number],
    default: "",
  },
  type: {
    type: String as PropType<"text" | "password">,
    default: "text",
  },
  /** 前缀icon名称 */
  prefix: {
    type: String,
    default: "",
  },
  /** 后缀icon名称 */
  suffix: {
    type: String,
    default: "",
  },
  placeholder: {
    type: String,
    default: "请输入",
  },
  allowClear: Boolean,
  autofocus: Boolean,
  /** 最大长度 默认无穷大 ∞ */
  maxlength: {
    type: Number,
    default: Infinity,
  },
  /** 过滤函数 */
  filter: {
    type: Function,
    default: undefined,
  },
  /** 禁用 */
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["update:value", "focus", "blur", "change", "clear"]);

const _inputRef = ref<any>();
const isFocus = ref(false);
const showClear = computed(() => props.allowClear && props.value && isFocus.value);

onMounted(() => {
  if (props.maxlength) {
    _inputRef.value.setAttribute("maxlength", props.maxlength);
  }
});
const clearHandle = () => {
  if (showClear.value) {
    emits("update:value", "");
    emits("clear");
    setTimeout(() => {
      _inputRef.value.focus();
    }, 0);
  }
};
const inputHandle = (e: Event) => {
  const value = (e.target as HTMLInputElement).value;
  // 过滤函数
  if (props.filter) {
    emits("update:value", props.filter(value));
  } else {
    emits("update:value", value);
  }
  // 完全受控
  nextTick(() => {
    _inputRef.value.value = props.value;
  });
};
const inputChange = (e: Event) => {
  const value = (e.target as HTMLInputElement).value;
  emits("change", value);
};
</script>

<style lang="scss" scoped>
.x-input {
  &-content {
    @include ct-f(y);
    @include wh(100%, 32px) {
      padding-right: 8px;
      border: 1px solid rgb(220, 220, 220);
      border-radius: 4px;
      background-color: rgba(255, 255, 255, 0.9);
    }
    transition: all 1.3s;
    &.focus {
      border-color: #5964fb;
      // animation-duration: 1.3s;
      // animation-name: heartBeat;
      // animation-timing-function: ease-in-out;
    }
    .content-prefix,
    .content-suffix {
      @include ct-f;
      @include wh(24px, 100%);
      cursor: text;
      &.show-clear {
        pointer-events: auto;
        cursor: pointer;
      }
    }

    .content-input {
      flex: 1;
      width: 100%;
      height: 100%;
      background: none;
      &:disabled {
        cursor: not-allowed;
      }
    }
  }
  &-message {
    font-size: inherit;
  }
}
</style>
