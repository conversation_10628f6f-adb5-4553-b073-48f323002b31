<template>
  <div class="all-alarm-dispose-page">
    <div class="all-alarm-dispose-page-header">
      <div class="form-box">
        <el-select
          v-model="formModel.type"
          size="small"
          style="width: 73px"
        >
          <el-option
            label="车牌号"
            value="0"
          ></el-option>
          <el-option
            label="告警名称"
            value="1"
          ></el-option>
        </el-select>
        <el-input
          v-model="formModel.name"
          :placeholder="formModel.type === '0' ? '请输入车牌号' : '请输入告警名称'"
          clearable
          style="width: 148px"
          size="small"
          @input="handleSearch"
          @keyup.enter="handleSearch"
        />
        <x-icon
          class="record-icon"
          width="16px"
          height="16px"
          name="record"
          @click="handleRecord"
        />
      </div>
    </div>

    <el-scrollbar
      min-height="250px"
      @scroll="handleScroll"
      ref="scrollbarRef"
    >
      <div class="all-alarm-dispose-page-list">
        <div
          class="all-alarm-dispose-page-list-item"
          v-for="item in alarmList"
          :key="item.id"
          @click="$emit('skip', item.deviceId)"
        >
          <div class="all-alarm-dispose-page-list-item-info">
            <span class="vno">{{ item.deviceId }}</span>
            <span class="time">{{ item.createdTime }}</span>
          </div>
          <div
            class="all-alarm-dispose-page-list-item-warn"
            :class="{
              serious: item.warnLevel === 2,
              emergency: item.warnLevel === 3,
            }"
          >
            <div class="content">
              <x-popover
                trigger="hover"
                placement="bottom"
              >
                <template #content>
                  <div style="padding: 10px">
                    <span>{{ item.faultContent }}</span>
                  </div>
                </template>
                <span class="fault-content">{{ item.faultContent }}</span>
              </x-popover>
            </div>
            <div
              class="state"
              @click.stop="handleDispose(item)"
            >
              <span>未处置</span>
              <x-icon
                class="right-icon"
                width="10px"
                height="10px"
                name="right"
              />
            </div>
          </div>
        </div>

        <XEmpty
          v-if="alarmList.length === 0"
          description="暂无数据"
        ></XEmpty>
      </div>
    </el-scrollbar>
    <alarmDispseDialog
      v-model="dialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
    <alarmCheckDialog
      v-model="checkDialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
    <alarmDisposeTabelDialog
      v-model="alarmDisposeTabelDialogVisible"
      :itemData="itemData"
      type="all"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted, watch } from "vue";
import alarmDispseDialog from "./alarm_dispose_dialog.vue";
import alarmCheckDialog from "./alarm_check_dialog.vue";
import alarmDisposeTabelDialog from "./alarm_dispose_tabel_dialog.vue";
import XEmpty from "@/components/x-empty.vue";
import { ScrollbarInstance } from "element-plus";
import { getVehicleAlarmList } from "@/services/api";
import dayjs from "dayjs";

const props = defineProps({
  deviceId: { type: String, default: "" },
});
const emit = defineEmits(["skip"]);
const dialogVisible = ref(false);
const checkDialogVisible = ref(false);
const itemData = ref({});
const alarmDisposeTabelDialogVisible = ref(false);
const alarmList = ref<any[]>([]);
const scrollbarRef = ref<ScrollbarInstance>();
let pollTimer: number | null = null;
let searchResetTimer: number | null = null;
let isPolling = true;
let hasMoreData = true;
let currentPage = 1;
let isUserScrolling = false;
let restartPollingTimer: number | null = null;
const formModel = reactive({
  type: "0",
  name: "",
});

const handleRecord = () => {
  alarmDisposeTabelDialogVisible.value = true;
};

const handleDispose = (item: any) => {
  itemData.value = item;
  item.isSolved === 0 ? (dialogVisible.value = true) : (checkDialogVisible.value = true);
};

const handleScroll = () => {
  isUserScrolling = true;
  stopPolling();

  // 如果已有定时器，先清除
  if (restartPollingTimer) {
    clearTimeout(restartPollingTimer);
    restartPollingTimer = null;
  }

  // 检查是否滚动到底部，触发加载更多
  const container = scrollbarRef.value?.wrapRef;
  if (container) {
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 滚动到底部时加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      loadMore();
    }
  }

  // 用户停止滚动后20秒重新开始轮询
  restartPollingTimer = setTimeout(() => {
    scrollbarRef.value!.setScrollTop(0);
    isUserScrolling = false;
    startPolling();
  }, 20000);
};

const startPolling = () => {
  stopPolling();
  isPolling = true;
  pollTimer = setInterval(() => {
    if (!isUserScrolling) {
      load();
    }
  }, 1000);
};

const stopPolling = () => {
  if (pollTimer) {
    clearInterval(pollTimer);
    pollTimer = null;
  }
  isPolling = false;
};

const handleSearch = () => {
  currentPage = 1;
  stopPolling();
  load();
  if (searchResetTimer) {
    clearTimeout(searchResetTimer);
  }
  searchResetTimer = setTimeout(() => {
    formModel.name = "";
    load();
    startPolling();
  }, 20000);
};

const loadMore = async () => {
  if (!hasMoreData) return;

  try {
    currentPage++;
    const params: any = {
      warnLevelList: [2, 3],
      limit: 10,
      page: currentPage,
      // isEnd: 0,
      isSolved: 0,
      mode: "3",
      deviceId: formModel.type === "0" ? formModel.name : undefined,
      faultName: formModel.type === "1" ? formModel.name : undefined,
      warnDateStart: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      warnDateEnd: dayjs().format("YYYY-MM-DD") + " 23:59:59",
      // selectTime: dayjs().format("YYYY-MM-DD HH:mm:ss"),
      faultCode: undefined,
    };
    const res = await getVehicleAlarmList(params);
    const newData = res?.list ?? [];

    if (newData.length > 0) {
      alarmList.value.push(...newData);
    } else {
      hasMoreData = false;
    }
  } catch (error) {
    console.error("加载更多数据失败:", error);
  }
};

const load = async () => {
  try {
    hasMoreData = true;
    currentPage = 1;
    const params: any = {
      warnLevelList: [2, 3],
      limit: 10,
      page: 1,
      // isEnd: 0,
      isSolved: 0,
      mode: "3",
      deviceId: formModel.type === "0" ? formModel.name : undefined,
      faultContent: formModel.type === "1" ? formModel.name : undefined,
      warnDateStart: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      warnDateEnd: dayjs().format("YYYY-MM-DD") + " 23:59:59",
      // selectTime: alarmList.value.length > 0 ? alarmList.value[0].createdTime : undefined,
      faultCode: undefined,
    };
    const res = await getVehicleAlarmList(params);
    const newData = res?.list ?? [];
    // 更新策略：
    // 1. 如果是第一次加载或者进行了筛选操作，总是更新数据
    // 2. 如果是轮询更新，则只在有新数据时才更新列表
    const isInitialLoad = alarmList.value.length === 0;
    const isFilterChanged = formModel.name;

    if (isInitialLoad || isFilterChanged || newData.length > 0) {
      alarmList.value = newData;
    }
    if (alarmList.value.length === 0) {
      hasMoreData = false;
    }

    // 搜索后20秒重置
    if (formModel.name) {
      isUserScrolling = true; // 搜索时暂停轮询
      stopPolling();

      if (searchResetTimer) clearTimeout(searchResetTimer);
      searchResetTimer = setTimeout(() => {
        formModel.name = "";
        load();
        isUserScrolling = false; // 恢复轮询
        startPolling();
      }, 20000);
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    alarmList.value = [];
  }
};

watch(
  () => props.deviceId,
  () => {
    load();
    // 设备ID变化时，重启轮询
    startPolling();
    alarmList.value = [];
  }
);
onMounted(() => {
  load();
  startPolling();
});

onUnmounted(() => {
  stopPolling();
  if (searchResetTimer) {
    clearTimeout(searchResetTimer);
  }

  if (restartPollingTimer) {
    clearTimeout(restartPollingTimer);
    restartPollingTimer = null;
  }
});
</script>

<script lang="ts">
export default {
  name: "allAlarmDisposePage",
};
</script>

<style scoped lang="scss">
.all-alarm-dispose-page {
  width: 292px;
  height: 100%;
  padding: 10px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  background: rgba(253, 254, 254, 0.96);
  // z-index: 999;
  padding-bottom: 70px;
  &-header {
    display: flex;
    flex-direction: column;
    gap: 10px;
    padding: 11px 0px;
    .form-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      .record-icon {
        cursor: pointer;
      }
    }
  }
  &-list {
    height: 280px;
    background-color: #fff;
    &-item {
      padding: 5px 10px;
      display: flex;
      align-items: center;
      flex-direction: column;
      cursor: pointer;
      gap: 2px;
      &-info {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        .vno {
          font-size: 14px;
          font-weight: 400;
          color: #383838;
        }
        .time {
          font-size: 12px;
          font-weight: 400;
          color: #9f9fa4;
        }
      }
      &-warn {
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-radius: 4px;
        padding: 3px 12px;
        .content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          gap: 5px;
          .fault-content {
            display: inline-block;
            width: 160px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            text-overflow: ellipsis;
            overflow: hidden;
            white-space: nowrap;
          }
        }
        .state {
          display: flex;
          align-items: center;
          gap: 5px;
          color: #666666;
          cursor: pointer;
        }
        &.serious {
          color: #8a1313;
          background: linear-gradient(90deg, rgba(235, 16, 16, 0.46), rgba(247, 249, 254, 0) 109.821%);
        }
        &.emergency {
          color: #f74c4c;
          background: linear-gradient(90deg, rgba(244, 21, 21, 0.16), rgba(247, 249, 254, 0) 109.821%);
        }
      }
      &:hover {
        background-color: #e8ecfd;
      }
    }
  }
}
</style>
