<template>
  <section
    class="site-marker"
    ref="siteMarkerInfoWindowRef"
  >
    <span
      class="site-marker-close"
      v-html="closeText"
      @mousedown="mousedownHandle"
    ></span>
    <div class="site-marker-top">
      <div class="panel-info">
        <div class="panel-info-image">
          <img
            :src="image.url"
            alt=""
            @click="props.stationPic && (image.show = true)"
            class="image"
          />
        </div>
        <div class="panel-info-content">
          <div class="panel-info-content-top">
            <div class="panel-info-content-top-left">
              {{ props.name || $t("unknown") }}
            </div>
            <div class="panel-info-content-top-right">
              <div>{{ siteConfig?.stationText || $t("location") }}</div>
            </div>
          </div>
          <div class="panel-info-content-center">{{ info.projectName }} - {{ info.areaName }}</div>
          <div class="panel-info-content-bottom">
            <div class="panel-info-content-bottom-icon">
              <x-icon name="locate_gray" />
            </div>
            <span class="panel-info-content-bottom-text">{{ info.address }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="site-marker-bottom">
      <x-select
        v-model:value="deviceId"
        :options="formOptions.deviceOptions"
        :popupContainer="props.popupContainer"
        style="width: 160px; margin-right: 5px"
      />
      <x-button
        :disabled="!deviceId"
        :text="siteConfig?.operaText || $t('opera')"
        :icon="deviceId ? `${props.type}_btn` : `${props.type}_btn_disabled`"
        @click="toStation"
      />
    </div>
    <x-image
      :visible="image.show"
      :src="image.url"
      @cancel="image.show = false"
    />
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted } from "vue";
import type { PropType } from "vue";
import { sitesType } from "@/assets/ts/config";
import xIcon from "@/components/x-icon.vue";
import xImage from "@/components/x-image.vue";
import xButton from "@/components/x-button.vue";
import XSelect from "@/components/x-select.vue";
import Message from "@/components/x-message";
import { task2 } from "@/services/wsapi";
import { getStaionDetail } from "@/services/api";
import { closeChargingStatus } from "@/services/wsapi";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

type __ChargingType = {
  id: string;
  parkName: string;
  status: string;
  warn: boolean;
  chargingStationNo: string;
  parkNo: string;
  [x: string]: any;
};

const props = defineProps({
  popupContainer: {
    type: HTMLElement,
  },
  window: {
    type: Object as PropType<any>,
    required: true,
  },
  type: {
    // 垃圾点/充电点/加水点/停车点
    type: String as PropType<"map_garbage" | "map_battery" | "map_water" | "map_park">,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  areaId: {
    type: String,
    required: true,
  },
  name: {
    type: String,
    required: true,
  },
  stationPic: {
    type: String,
    required: true,
  },
  stationType: {
    type: Number,
    required: true,
  },
  stationList: {
    type: Array as PropType<__ChargingType[]>,
    required: true,
  },
});

const siteMarkerInfoWindowRef = ref<any>();

const closeText = ref("&#10005");

const siteConfig = sitesType.find((site) => site.icon === props.type);

// 关闭站点信息窗体
const mousedownHandle = () => {
  props.window.close();
  closeChargingStatus();
};

/**
 * 场所图片
 */
const image = reactive({
  show: false,
  url: props.stationPic || new URL("@/assets/images/map_station_bg.png", import.meta.url).href,
});

/**
 * 基础信息
 */
const info = reactive({
  projectName: $t("unknown"),
  areaName: $t("unknown"),
  address: $t("unknown"),
});

onMounted(() => {
  getStationBaseInfo();
});

const getStationBaseInfo = async () => {
  const { projectName, areaName, address, vehNoList } = await getStaionDetail({
    areaId: props.areaId,
    stationId: props.id,
  });
  info.projectName = projectName as string;
  info.areaName = areaName as string;
  info.address = address as string;
  formOptions.deviceOptions =
    vehNoList?.map((item) => ({
      label: item,
      value: item,
    })) || [];
};

/**
 * 设置任务
 */
const deviceId = ref("");

const formOptions = reactive({
  deviceOptions: [] as any[],
});

const toStation = async () => {
  // 站点编号#任务类型(#泊车位id)
  // let param = `${props.id}#${siteConfig.paramText}`;
  // if (detailInfo.activeIndex) {
  //   const parkNo = detailInfo.chargingList[detailInfo.activeIndex].parkNo;
  //   param += `#${parkNo}`;
  // }
  // new Promise(() => {
  //   task("ecar", deviceId.value, defaultSpeed, defaultExecuteTimes, param).then(() => {
  //     Message("success", $t("taskSet"));
  //   });
  // });
  task2({
    deviceId: deviceId.value,
    executeTimes: 1,
    stationId: props.id,
  }).then(() => {
    Message("success", $t("taskSet"));
  });
};
</script>

<style lang="scss" scoped>
.site-marker {
  overflow: hidden;
  padding: 6px 7px 7px 6px;
  @include wh(278px, 275px);
  @include bis("@/assets/images/map_site_marker_info_window.png");
  &-enter {
    &-from {
      opacity: 0.3;
      transform: translateX(3%);
    }
    &-active {
      transition: all 0.3s ease-out;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease-out;
    }
    &-to {
      opacity: 0.3;
      transform: translateX(-3%);
    }
  }
  &-close {
    z-index: 6;
    display: block;
    @include ct-f;
    @include wh(16px);
    @include sc(16px, rgb(85, 85, 85));
    position: absolute;
    top: 18px;
    right: 18px;
    cursor: pointer;
  }
  &-top {
    @include wh(100%, 205px);
    .panel-info {
      &-image {
        display: flex;
        @include wh(100%, 114px);
        .image {
          width: 100%;
          border-radius: 4px 4px 0 0;
          object-fit: cover;
        }
      }
      &-content {
        padding: 5px 10px;
        &-top {
          @include ct-f(y) {
            justify-content: space-between;
          }
          font-weight: 700;
          &-left {
            @include sc(14px, #383838);
          }
          &-right {
            @include sc(12px, blue);
            .detail-btn {
              display: flex;
              border: 1px solid #5964fb;
              font-weight: 400;
              border-radius: 2px;
              &-left {
                @include wh(43px, 23px);
                @include sc(12px, #9f9fa4);
                @include ct-f;
              }
              &-right {
                @include wh(33px, 23px);
                @include sc(12px, #fff);
                background: #5964fb;
                @include ct-f;
                cursor: pointer;
                &:hover {
                  background-color: rgb(125, 134, 253);
                }
                &:active {
                  background-color: rgb(58, 67, 203);
                }
              }
            }
          }
        }
        &-center {
          margin-bottom: 10px;
          @include sc(12px, #9f9fa4);
        }
        &-bottom {
          display: flex;
          margin-top: 4px;
          width: 100%;
          max-height: 33px;
          overflow: hidden;
          &-icon {
            padding-right: 5px;
          }
          &-text {
            @include sc(12px, #383838);
            @include ells(2);
          }
        }
      }
    }
    .panel-detail {
      padding: 5px 10px;
      &-title {
        margin: 10px 5px;
        @include sc(14px, #333333);
      }
      &-content {
        flex-wrap: wrap;
        max-height: 153px;
        overflow-y: auto;
        @include scrollbar(y, 4px, rgb(233, 234, 248), #a2a9fc);
        @include ct-f(y);
        &-item {
          position: relative;
          @include wh(30px, 46px);
          margin: 0 5px 5px 5px;
          cursor: pointer;
          &.disabled {
            cursor: not-allowed;
          }
          &-icon {
            @include wh(30px, 46px);
          }
          &-inner {
            .inner-text {
              @include ct-p;
              width: 30px;
              text-align: center;
              @include sc(12px, #555555);
              &.active {
                color: #5964fb;
              }
            }
            .inner-image {
              @include ct-p;
              width: 18px;
              text-align: center;
            }
            .inner-warning-tip {
              position: absolute;
              top: 5px;
              width: 30px;
              text-align: center;
              @include sc(11px, #f9852ecc);
            }
          }
        }
      }
    }
  }
  &-bottom {
    padding: 5px;
    @include ct-f(y) {
      justify-content: space-between;
    }
  }
}
</style>
