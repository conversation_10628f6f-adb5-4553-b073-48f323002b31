<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="查看"
    append-to-body
    class="alarm-check-dialog"
    width="50%"
    @close="emit('update:modelValue', false)"
  >
    <div class="alarm-check">
      <div class="alarm-check-info">
        <div class="title">基础信息</div>
        <div class="info-grid">
          <div class="col_1">
            <div
              v-for="(item, index) in leftBasicInfo"
              :key="index"
              class="info-row"
            >
              <div
                v-for="(subItem, subIndex) in item.items"
                :key="subIndex"
                class="info-item"
              >
                <span class="label">{{ subItem.label }}:</span>
                <template v-if="subItem.type === 'text'">
                  <span
                    :class="{ 'text-wrap': subItem.key === 'faultContent' }"
                    class="value"
                    :style="{ color: subItem.color }"
                    >{{ subItem.value }}</span
                  >
                </template>
                <template v-else-if="subItem.type === 'images'">
                  <div class="img-box">
                    <el-image
                      v-for="(img, imgIndex) in subItem.images"
                      :key="imgIndex"
                      :preview-src-list="subItem.images.map((image: any) => image.picUrl)"
                      :initial-index="imgIndex"
                      class="img-item"
                      :src="img.picUrl"
                      :alt="img.alt || '图片'"
                    />
                  </div>
                </template>
              </div>
            </div>
          </div>

          <div class="col_2">
            <div
              v-for="(item, index) in rightBasicInfo"
              :key="index"
              class="info-row"
            >
              <div
                v-for="(subItem, subIndex) in item.items"
                :key="subIndex"
                class="info-item"
              >
                <span class="label">{{ subItem.label }}:</span>
                <span
                  class="value"
                  :class="{
                    bg: subItem.isbg,
                    'bg-ongoing': subItem.isbg && subItem.value === '进行中',
                    'bg-completed': subItem.isbg && subItem.value === '已完成',
                    'bg-undisposed': subItem.isbg && subItem.value === '未处置',
                    'bg-disposed': subItem.isbg && subItem.value === '已处置',
                  }"
                  >{{ subItem.value }}</span
                >
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="dispose">
        <div class="title">处置信息</div>
        <div class="dispose-grid">
          <div class="col_1">
            <div
              v-for="(item, index) in leftDisposeInfo"
              :key="index"
              class="row"
            >
              <span class="label">{{ item.label }}:</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
          <div class="col_2">
            <div
              v-for="(item, index) in rightDisposeInfo"
              :key="index"
              class="row"
            >
              <span class="label">{{ item.label }}:</span>
              <span class="value">{{ item.value }}</span>
            </div>
          </div>
        </div>
      </div>

      <div
        class="check"
        v-if="formData?.vehSnapRecordList?.length > 0 || formData?.vehicleWorkOrderList?.length > 0"
      >
        <div class="title">校验信息</div>
        <div class="check-grid">
          <div
            class="check-grid-item"
            v-for="(item, index) in checkInfo"
            :key="index"
          >
            <div
              v-for="(subItem, subIndex) in item.items"
              :key="subIndex"
              class="row"
            >
              <span class="label">{{ subItem.label }}:</span>
              <span class="value">{{ subItem.value }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </el-dialog>
</template>

<script setup lang="ts">
import { getVehicleAlarmDetail } from "@/services/api";
import { ref, reactive, toRefs, onMounted, computed, watch } from "vue";

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemData: {
    type: Object,
    default: () => ({}),
  },
});

const emit = defineEmits(["update:modelValue"]);

const formData = ref<any>({
  vehSnapRecordList: [],
  vehicleWorkOrderList: [],
});

// 基础信息 - 左侧
const leftBasicInfo = computed(() => [
  {
    items: [
      {
        label: "车牌号",
        value: formData.value?.deviceId || "-",
        type: "text",
      },
    ],
  },
  {
    items: [
      {
        label: "告警名称",
        value: formData.value?.faultContent || "-",
        type: "text",
        key: "faultContent",
      },
      {
        label: "告警等级",
        value: showwarnLevel(formData.value?.warnLevel) || "-",
        type: "text",
        color: showwarnLevelColor(formData.value?.warnLevel),
      },
    ],
  },

  {
    items: [
      {
        label: "告警开始时间",
        value: formData.value?.createdTime || "-",
        type: "text",
      },
    ],
  },
  {
    items: [
      {
        label: "所属区域",
        value: formData.value?.areaName || "-",
        type: "text",
      },
    ],
  },
  {
    items: [
      {
        label: "所属企业",
        value: formData.value?.company || "-",
        type: "text",
      },
    ],
  },
  {
    items: [
      {
        label: "图片",
        type: "images",
        images: formData.value?.vehSnapRecordList || [],
      },
    ],
  },
]);

// 基础信息 - 右侧
const rightBasicInfo = computed(() => [
  {
    items: [{ label: "VIN", value: formData.value?.vin || "-" }],
  },
  {
    items: [
      { label: "状态", value: formData.value?.isSolved === 0 ? "进行中" : "已完成", isbg: true },
      { label: "处置", value: formData.value?.isEnd === 0 ? "未处置" : "已处置", isbg: true },
    ],
  },
  {
    items: [{ label: "告警结束时间", value: formData.value?.endTime || "-" }],
  },
  {
    items: [{ label: "所属项目", value: formData.value?.project || "-" }],
  },
]);

// 处置信息 - 左侧
const leftDisposeInfo = computed(() => [
  { label: "处置内容", value: props.itemData?.description || "-" },
  { label: "提交人", value: formData.value?.records?.[0]?.userName || "-" },
]);

// 处置信息 - 右侧
const rightDisposeInfo = computed(() => [
  { label: "提交时间", value: props.itemData?.description ? props.itemData?.updatedTime : "-" },
]);

// 校验信息
const checkInfo = computed(() => {
  let resList: any = [];
  //抓拍结果
  if (formData.value?.vehSnapRecordList.length > 0) {
    resList = [
      {
        items: [
          {
            label: "感知结果",
            value: showTrafficLightColor(formData.value?.vehSnapRecordList?.[0]?.traffic_light_color),
          },
          { label: "通行方向", value: showDirection(formData.value?.vehSnapRecordList?.[0]?.direction) },
          { label: "提交人", value: formData.value?.vehSnapRecordList?.[0]?.updatedByName || "-" },
          {
            label: "提交结果",
            value: formData.value?.vehSnapRecordList?.[0]?.recognizeResultc === 1 ? "通过" : "不通过" || "-",
          },
        ],
      },
      {
        items: [{ label: "校验提交时间", value: formData.value?.vehSnapRecordList?.[0]?.createdTime || "-" }],
      },
    ];
  } else {
    resList = [
      {
        items: [
          { label: "工单类型", value: showType(formData.value?.vehicleWorkOrderList?.[0]?.eventType) },
          {
            label: "结果",
            value: formData.value?.vehicleWorkOrderList?.[0]?.recognizeResult === 1 ? "是" : "否" || "-",
          },
          { label: "扫净", value: formData.value?.vehicleWorkOrderList?.[0]?.isClean === 1 ? "是" : "否" || "-" },
          { label: "提交人", value: formData.value?.vehicleWorkOrderList?.[0]?.updatedByName || "-" },
        ],
      },
      {
        items: [{ label: "校验提交时间", value: formData.value?.vehicleWorkOrderList?.[0]?.updatedTime || "-" }],
      },
    ];
  }

  return resList;
});

const showwarnLevel = (warnLevel: number) => {
  switch (warnLevel) {
    case 0:
      return "普通";
    case 1:
      return "轻微";
    case 2:
      return "紧急";
    case 3:
      return "致命";
    default:
      return "-";
  }
};

const showwarnLevelColor = (warnLevel: number) => {
  switch (warnLevel) {
    case 0:
      return "#31a381";
    case 1:
      return "#faa564";
    case 2:
      return "#f74c4c";
    case 3:
      return "#8a1313";
    default:
      return "#000";
  }
};

const showType = (type: number) => {
  switch (type) {
    case 1:
      return "垃圾";
    case 2:
      return "坑洞";
    case 3:
      return "井盖缺失";
    case 4:
      return "新垃圾";
    case 5:
      return "已清理掉的垃圾";
    case 6:
      return "未清理掉的垃圾";
    case 7:
      return "避障";
    case 8:
      return "停障";
    default:
      return "未知";
  }
};
/**
 * 显示方向
 * @param direction 方向
 * @returns 方向
 */
const showDirection = (direction: number) => {
  switch (direction) {
    case 0:
      return "直行";
    case 1:
      return "左拐";
    case 2:
      return "右拐";
    case 3:
      return "掉头";
    default:
      return "未知";
  }
};

/**
 * 显示红绿灯颜色
 * @param color 颜色
 * @returns 颜色
 */
const showTrafficLightColor = (color: number) => {
  switch (color) {
    case 0:
      return "未知";
    case 1:
      return "红灯";
    case 2:
      return "黄灯";
    case 3:
      return "绿灯";
    case 4:
      return "黑灯";
    default:
      return "未知";
  }
};

const load = async () => {
  console.log(props.itemData, "props.itemData");
  try {
    const res = await getVehicleAlarmDetail({ id: props.itemData.id, device_id: props.itemData.deviceId });
    formData.value = res;
  } catch (err) {
    console.log(err);
  }
};

watch(
  () => props.modelValue,
  () => {
    load();
  }
);
</script>

<style scoped lang="scss">
.alarm-check {
  background-color: #f4f7fe;
  padding: 15px;
  display: flex;
  flex-direction: column;
  gap: 10px;
  &-info {
    border-radius: 8px;
    background: rgb(255, 255, 255);
    .title {
      font-size: 12px;
      font-weight: 700;
      color: #555555;
      padding: 14px 13px;
    }
    .info-grid {
      padding: 0 23px 20px;
      display: flex;
      gap: 10%;
      .col_1,
      .col_2 {
        width: 50%;
        display: flex;
        flex-direction: column;
        gap: 12px;
        .info-row {
          display: flex;
          gap: 50px;
          .info-item {
            display: flex;
            gap: 10px;
            align-items: center;
            .label {
              font-size: 14px;
              color: #9f9fa4;
              white-space: nowrap;
            }
            .value {
              white-space: nowrap;
              &.bg {
                padding: 2px 6px;
                border-radius: 4px;
                font-size: 12px;
              }
              &.bg-ongoing {
                display: inline-block;
                padding: 5px 11px;
                border-radius: 50px;
                color: #5964fb;
                background: rgba(89, 100, 251, 0.06);
                font-size: 12px;
              }
              &.bg-completed {
                display: inline-block;
                padding: 5px 11px;
                border-radius: 50px;
                background: rgba(159, 159, 164, 0.17);
                color: #4d4d4d;
                font-size: 12px;
              }
              &.bg-undisposed {
                display: inline-block;
                padding: 5px 11px;
                border-radius: 50px;
                background: rgba(159, 159, 164, 0.17);
                color: #4d4d4d;
                font-size: 12px;
              }
              &.bg-disposed {
                display: inline-block;
                padding: 5px 11px;
                border-radius: 50px;
                color: #5964fb;
                background: rgba(89, 100, 251, 0.06);
                font-size: 12px;
              }
            }
            .img-box {
              width: 100%;
              display: flex;
              align-items: center;
              flex-wrap: wrap;
              gap: 10px;
              .img-item {
                width: 110px;
                height: 81px;
                border-radius: 4px;
              }
            }
          }
        }
      }
      .col_1 {
        width: 40%;
      }
    }
  }

  .dispose {
    border-radius: 8px;
    background: rgb(255, 255, 255);

    .title {
      font-size: 12px;
      font-weight: 700;
      color: #555555;
      padding: 14px 13px;
    }
    .dispose-grid {
      display: flex;
      gap: 20%;
      .col_1,
      .col_2 {
        padding: 0 23px 20px;
        width: 30%;
        .row {
          display: flex;
          margin-top: 12px;
          gap: 10px;
          .label {
            font-size: 14px;
            color: #9f9fa4;
          }
          .value {
            font-size: 14px;
            color: #383838;
          }
        }
      }
    }
  }

  .check {
    border-radius: 8px;
    background: rgb(255, 255, 255);

    .title {
      font-size: 12px;
      font-weight: 700;
      color: #555555;
      padding: 14px 13px;
    }
    .check-grid {
      &-item {
        display: flex;
        padding: 0 23px 20px;
        .row {
          display: flex;
          margin-top: 12px;
          gap: 10px;
          flex: 1;

          .label {
            width: 120px;
            font-size: 14px;
            color: #9f9fa4;
          }
          .value {
            width: 100%;
            font-size: 14px;
            color: #383838;
          }
        }
      }
    }
  }
  .text-wrap {
    width: 100px;
    white-space: normal !important;
  }
}
</style>
<style lang="scss">
.alarm-check-dialog {
  padding: 0 !important;
  .el-dialog__header {
    padding: 10px 10px 0 10px !important;
  }
}
</style>
