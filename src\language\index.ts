import { createI18n } from "vue-i18n";
import zhC<PERSON> from "./locales/zh-cn";
import enUS from "./locales/en-us";

export default createI18n({
  legacy: false,
  locale: "zh-cn",
  fallbackLocale: "zh-cn",
  messages: {
    "zh-cn": zhCN,
    "en-us": enUS,
  },
});

export const t = (key: string) => {
  return createI18n({
    legacy: false,
    locale: "zh-cn",
    fallbackLocale: "zh-cn",
    messages: {
      "zh-cn": zhCN,
      "en-us": enUS,
    },
  }).global.t(key);
};
