<template>
  <section
    class="page-table-layout"
    ref="pageTableLayoutRef"
  >
    <div class="page-table-layout-top">
      <div class="top-title">{{ $t("projectManage") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:pro:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addReactive.show = true"
        />
        <Add
          v-model:show="addReactive.show"
          @confirm="addReactive.confirm"
        />
      </div>
    </div>
    <div class="page-table-layout-middle">
      <div class="middle-left">
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 320px"
          >
            <x-select
              v-model:value="searchForm.entName"
              :options="formOptions.companyOptions"
              :popupContainer="pageTableLayoutRef"
              :placeholder="$t('PEnterEnt')"
              showSearch
            />
          </div>
          <div class="middle-left-item">
            <x-input
              v-model:value="searchForm.proName"
              :placeholder="$t('PEnterProName')"
              suffix="input_search"
              style="width: 230px"
            />
          </div>
          <div
            class="middle-left-item"
            style="width: 156px"
          >
            <x-select
              v-model:value="searchForm.userName"
              :options="formOptions.users"
              :popupContainer="pageTableLayoutRef"
              :placeholder="$t('PEnterUserName')"
              showSearch
            />
          </div>
        </div>
        <div class="middle-left-items">
          <div
            class="middle-left-item"
            style="width: 380px; display: flex"
          >
            <div class="middle-left-item-label">{{ $t("opDate") }}</div>
            <div class="middle-left-item-value">
              <DateRangePicker
                v-model:value="searchForm.openDate"
                :popupContainer="pageTableLayoutRef"
                :placeholder="[$t('startDate'), $t('endDate')]"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:pro:list')"
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button
          @click="resetSearchForm"
          type="green"
          :text="$t('reset')"
        />
      </div>
    </div>
    <div class="page-table-layout-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.entName }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #proName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.proName }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.proName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:pro:saveRU')"
                @click="openUserAuth(record.id)"
              >
                {{ $t("staffAllocation") }}
              </span>
              <span
                v-if="permitList.includes('sys:pro:saveRV')"
                @click="openCarAuth(record.id)"
              >
                {{ $t("vehicleAllocation") }}
              </span>
              <span
                v-if="permitList.includes('sys:pro:update')"
                @click="openEdit(record.id)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:pro:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:pro:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editReactive.show"
        :id="editReactive.id"
        @confirm="editReactive.confirm"
      />
      <Detail
        v-model:show="detailReactive.show"
        :id="detailReactive.id"
      />
      <CarAuth
        v-model:show="carAuthReactive.show"
        :id="carAuthReactive.id"
        @confirm="carAuthReactive.confirm"
      />
      <UserAuth
        v-model:show="userAuthReactive.show"
        :id="userAuthReactive.id"
        @confirm="userAuthReactive.confirm"
      />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import type { ProListRequest } from "@/services/type";
import { ref, reactive, watch } from "vue";
import { compList, proList, delPro, userList } from "@/services/api";
import { useMainStore } from "@/stores/main";
import { formatDateTime } from "@/assets/ts/dateTime";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import CarAuth from "./components/carAuth.vue";
import UserAuth from "./components/userAuth.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xPopover from "@/components/x-popover.vue";
import xSelect from "@/components/x-select.vue";
import { DateRangePicker } from "@/components/x-date-picker";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("projectManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const pageTableLayoutRef = ref<any>();

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  companyOptions: [] as { label: string; value: string }[],
  users: [] as any[],
});

/**
 * 用户列表
 */
const getUserList = async () => {
  // 用户
  const users = await userList({
    page: 1,
    limit: 500,
    entName: searchForm.entName,
    station: 2, // 后台要求固定传参
  });
  formOptions.users = (users?.list || []).map((item) => ({
    value: item.userName,
    label: item.userName,
  }));
};

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "30",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "80",
      slots: "entName",
    },
    {
      key: "proName",
      title: $t("proName"),
      width: "60",
      slots: "proName",
    },
    {
      key: "proSeq",
      title: $t("proSeq"),
      width: "60",
    },
    {
      key: "userName",
      title: $t("userName"),
      width: "40",
    },
    {
      key: "openDateFormat",
      title: $t("startDate"),
      width: "40",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "100",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  entName: "",
  proName: "",
  userName: "",
  openDate: ["", ""],
});
/**
 * 表格-搜索
 */
const submitSearch = async () => {
  table.loading = true;

  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    entName: searchForm.entName,
    proName: searchForm.proName,
    userName: searchForm.userName,
  } as ProListRequest;

  const [openDateStart, openDateEnd] = searchForm.openDate;
  openDateStart && (params.openDateStart = openDateStart);
  openDateEnd && (params.openDateEnd = openDateEnd);

  const { totalCount, list } = await proList(params);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            openDateFormat: formatDateTime(item.openDate, "YYYY-MM-DD"),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.proName = "";
  searchForm.userName = "";
  searchForm.openDate = ["", ""];
  table.pagination["current"] = 1;
  submitSearch();
};

/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};
/**
 * 表格-首次加载
 */
(async () => {
  submitSearch();
  formOptions.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
  // 用户列表
  getUserList();
})();

watch(
  () => searchForm.entName,
  () => {
    // 清除选择
    searchForm.userName = "";
    // 用户列表
    getUserList();
  }
);

/**
 * 相关操作
 */
// 新增
const addReactive = reactive({
  show: false,
  confirm: () => submitSearch(),
});
// 编辑
const editReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openEdit = (id: string) => {
  editReactive.id = id;
  editReactive.show = true;
};
// 详情
const detailReactive = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailReactive.id = id;
  detailReactive.show = true;
};
// 车辆分配
const carAuthReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openCarAuth = (id: string) => {
  carAuthReactive.id = id;
  carAuthReactive.show = true;
};
// 人员分配
const userAuthReactive = reactive({
  show: false,
  id: "",
  confirm: () => submitSearch(),
});
const openUserAuth = (id: string) => {
  userAuthReactive.id = id;
  userAuthReactive.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("sureToDelProject"),
    content: <div style="font-size:14px;color:#383838;">{$t("willUnbindMemberCarAndProject")}</div>,
    confirm() {
      return delPro([record.id]).then(() => {
        Message("success", $t("deleteSuccess"));
        submitSearch();
      });
    },
  });
};
</script>

<style lang="scss">
.company-manage-table-opera-click-popover {
  &-item {
    cursor: pointer;
    @include wh(90px, 40px);
    font-size: 13px;
    line-height: 40px;
    text-align: center;
    &:hover {
      background-color: #242859;
    }
    &:first-child {
      border-radius: 4px 4px 0 0;
    }
    &:last-child {
      border-radius: 0 0 4px 4px;
    }
  }
}
</style>

<style lang="scss" scoped>
.user-manage-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.page-table-layout {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }

  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-right {
      display: flex;
    }
    .middle-left-items {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
          padding: 6px 8px 6px 0;
        }
      }
      &-value {
        flex: 1;
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
