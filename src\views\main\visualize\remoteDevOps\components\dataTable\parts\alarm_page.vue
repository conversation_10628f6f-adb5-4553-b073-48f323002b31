<template>
  <div class="alarm-page">
    <div class="alarm-page-header">
      <div class="dvice">{{ deviceId }}</div>
      <div class="form-box">
        <el-select
          v-model="formModel.type"
          placeholder="请选择"
          clearable
          multiple
          :max-collapse-tags="1"
          collapse-tags
          size="small"
          style="flex: 1"
          @change="load"
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
        <el-input
          v-model="formModel.name"
          placeholder="告警名称"
          clearable
          style="flex: 0.65"
          size="small"
          @blur="load"
          @keydown.enter="load"
        />
        <x-icon
          class="record-icon"
          width="16px"
          height="16px"
          name="record"
          @click="handleRecord"
        />
      </div>

      <div class="dateRange">
        <!-- <el-date-picker
          v-model="formModel.dateRange"
          type="daterange"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          style="width: 100%"
          value-format="YYYY-MM-DD"
          clearable
          @change="load"
        /> -->
      </div>
    </div>

    <el-scrollbar
      min-height="250px"
      ref="scrollbarRef"
      @scroll="handleScroll"
    >
      <div class="alarm-page-list">
        <div
          v-for="item in alarmList"
          :key="item.id"
          class="alarm-page-list-item"
        >
          <x-popover
            trigger="hover"
            placement="bottom"
          >
            <template #content>
              <div style="padding: 10px">
                <span>{{ item.faultContent }}</span>
              </div>
            </template>
            <div
              class="alarm-page-list-item-title"
              :class="{
                'alarm-page-list-item-title-fatal': item.warnLevel === 3,
                'alarm-page-list-item-title-emergency': item.warnLevel === 2,
                'alarm-page-list-item-title-slight': item.warnLevel === 1,
                'alarm-page-list-item-title-ordinary': item.warnLevel === 0,
              }"
            >
              {{ item.faultContent }}
            </div>
            <div class="alarm-page-list-item-content">
              <div>{{ item.createdTime }}</div>
              <div
                class="alarm-page-list-item-content-btn-box"
                @click="handleDispose(item)"
              >
                <span class="alarm-page-list-item-content-btn">未处置</span>
                <x-icon
                  class="right-icon"
                  width="10px"
                  height="10px"
                  name="right"
                />
              </div>
            </div>
          </x-popover>
        </div>
        <XEmpty
          v-if="alarmList?.length === 0"
          description="暂无数据"
        ></XEmpty>
      </div>
    </el-scrollbar>
    <alarmDispseDialog
      v-model="dialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
    <alarmCheckDialog
      v-model="checkDialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
    <alarmDisposeTabelDialog
      v-model="disposeVisible"
      :itemData="itemData"
      type="single"
      :deviceId="deviceId"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, onMounted, onUnmounted } from "vue";
import alarmDispseDialog from "./alarm_dispose_dialog.vue";
import alarmDisposeTabelDialog from "./alarm_dispose_tabel_dialog.vue";
import { getVehicleAlarmList } from "@/services/api";
import { ScrollbarInstance } from "element-plus";
import dayjs from "dayjs";
import XEmpty from "@/components/x-empty.vue";
const props = defineProps({
  deviceId: {
    type: String,
    default: "",
  },
});

const dialogVisible = ref(false);
const checkDialogVisible = ref(false);
const disposeVisible = ref(false);
const itemData = ref();
const formModel = reactive({
  type: [2, 3],
  name: "",
  // dateRange: [],
});

const options = ref([
  {
    label: "全部",
    value: 4,
  },
  {
    label: "普通告警",
    value: 0,
  },
  {
    label: "轻微告警",
    value: 1,
  },
  {
    label: "紧急告警",
    value: 2,
  },
  {
    label: "致命告警",
    value: 3,
  },
  // {
  //   label: "紧急+致命告警",
  //   value: 5,
  // },
]);

const alarmList = ref<any[]>([]);
const scrollbarRef = ref<ScrollbarInstance>();
let searchResetTimer: number | null = null;
let pollingTimer: number | null = null;
let restartPollingTimer: number | null = null;
let hasMoreData = true;
let isUserScrolling = false;

// 轮询函数，每秒获取数据
const startPolling = () => {
  stopPolling();

  pollingTimer = setInterval(() => {
    if (!isUserScrolling) {
      load();
    }
  }, 1000);
};

// 停止轮询
const stopPolling = () => {
  if (pollingTimer) {
    clearInterval(pollingTimer);
    pollingTimer = null;
  }
};

// 处理用户滚动事件
const handleScroll = () => {
  isUserScrolling = true;
  stopPolling();

  // 如果已有定时器，先清除
  if (restartPollingTimer) {
    clearTimeout(restartPollingTimer);
    restartPollingTimer = null;
  }

  // 检查是否滚动到底部，触发加载更多
  const container = scrollbarRef.value?.wrapRef;
  if (container) {
    const { scrollTop, scrollHeight, clientHeight } = container;

    // 滚动到底部时加载更多数据
    if (scrollTop + clientHeight >= scrollHeight - 10) {
      loadMore();
    }
  }

  // 用户停止滚动后20秒重新开始轮询
  restartPollingTimer = setTimeout(() => {
    scrollbarRef.value!.setScrollTop(0);
    isUserScrolling = false;
    startPolling();
  }, 20000);
};

const loadMore = async () => {
  if (!hasMoreData) return;

  try {
    const params: any = {
      warnLevelList: formModel.type.includes(4) ? [0, 1, 2, 3] : formModel.type,
      limit: 10,
      page: Math.ceil(alarmList.value.length / 10) + 1,
      // isEnd: 0,
      isSolved: 0,
      deviceId: props.deviceId,
      faultName: formModel.name,
      mode: "3",
      warnDateStart: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      warnDateEnd: dayjs().format("YYYY-MM-DD") + " 23:59:59",
    };
    const res = await getVehicleAlarmList(params);
    const newData = res?.list ?? [];

    if (newData.length > 0) {
      alarmList.value.push(...newData);
    } else {
      hasMoreData = false;
    }
  } catch (error) {
    console.error("加载更多数据失败:", error);
  }
};

const load = async () => {
  try {
    hasMoreData = true;
    const params: any = {
      warnLevelList: formModel.type.includes(4) ? [0, 1, 2, 3] : formModel.type,
      limit: 10,
      page: 1,
      isSolved: 0,
      deviceId: props.deviceId,
      faultName: formModel.name,
      mode: "3",
      warnDateStart: dayjs().format("YYYY-MM-DD") + " 00:00:00",
      warnDateEnd: dayjs().format("YYYY-MM-DD") + " 23:59:59",
    };
    const res = await getVehicleAlarmList(params);
    const newData = res?.list ?? [];

    // 更新策略：
    // 1. 如果是第一次加载或者进行了筛选操作，总是更新数据
    // 2. 如果是轮询更新，则只在有新数据时才更新列表
    const isInitialLoad = alarmList.value.length === 0;
    const isFilterChanged = formModel.name;

    if (isInitialLoad || isFilterChanged || newData.length > 0) {
      alarmList.value = newData;
    }

    if (newData.length === 0) {
      hasMoreData = false;
    }

    // 搜索后20秒重置
    if (formModel.name) {
      isUserScrolling = true; // 搜索时暂停轮询
      stopPolling();

      if (searchResetTimer) clearTimeout(searchResetTimer);
      searchResetTimer = setTimeout(() => {
        formModel.name = "";
        // formModel.type = [4]; // 修改为数组
        load();
        isUserScrolling = false; // 恢复轮询
        startPolling();
      }, 20000);
    }
  } catch (error) {
    console.error("加载数据失败:", error);
    alarmList.value = [];
  }
};

const handleDispose = (item: any) => {
  if (item.isSolved === 0) {
    dialogVisible.value = true;
  } else {
    checkDialogVisible.value = true;
  }
  itemData.value = item;
};

const handleRecord = () => {
  disposeVisible.value = true;
};

onMounted(() => {
  load();
  startPolling();
});

onUnmounted(() => {
  stopPolling();

  if (searchResetTimer) {
    clearTimeout(searchResetTimer);
    searchResetTimer = null;
  }

  if (restartPollingTimer) {
    clearTimeout(restartPollingTimer);
    restartPollingTimer = null;
  }
});

watch(
  () => props.deviceId,
  () => {
    load();
    // 设备ID变化时，重启轮询
    startPolling();
    alarmList.value = [];
  }
);

defineExpose({
  load,
});
</script>
<script lang="ts">
export default {
  name: "alarmPage",
};
</script>
<style scoped lang="scss">
.alarm-page {
  width: 285px;
  height: 100%;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  background: rgb(244, 247, 254);
  box-sizing: border-box;
  padding-bottom: 10px;
  &-header {
    display: flex;
    flex-direction: column;
    // gap: 10px;
    padding: 11px 13px 5px;
    background: linear-gradient(180deg, rgba(244, 21, 21, 0.16) -4.911%, rgba(247, 249, 254, 0) 60%);
    .dvice {
      font-size: 14px;
      font-weight: 700;
      color: #242859;
      margin-bottom: 5px;
    }

    .form-box {
      display: flex;
      align-items: center;
      justify-content: space-between;
      column-gap: 5px;

      .record-icon {
        cursor: pointer;
      }
    }
    .dateRange {
      background-color: #fff;
    }
  }
  &-list {
    padding: 5px 13px;
    padding-bottom: 80px;
    &-item {
      margin-bottom: 5px;
      &-title {
        width: 100%;
        height: 28px;
        text-align: left;
        font-size: 13px;
        border-radius: 4px;
        padding: 0 8px;
        line-height: 28px;
        color: #31a381;
        border-radius: 4px;
        background: linear-gradient(90deg, rgba(39, 212, 161, 0.22), rgba(247, 249, 254, 0) 109.821%);
        text-overflow: ellipsis;
        overflow: hidden;
        white-space: nowrap;

        &-fatal {
          color: #8a1313;
          background: linear-gradient(90deg, rgba(235, 16, 16, 0.46), rgba(247, 249, 254, 0) 109.821%);
        }
        &-emergency {
          color: #f74c4c;
          background: linear-gradient(90deg, rgba(244, 21, 21, 0.16), rgba(247, 249, 254, 0) 109.821%);
        }
        &-slight {
          color: #faa564;
          border-radius: 4px;
          background: linear-gradient(90deg, rgba(249, 133, 46, 0.13), rgba(247, 249, 254, 0) 109.821%);
        }
        &-ordinary {
          color: #31a381;
          background: linear-gradient(90deg, rgba(39, 212, 161, 0.22), rgba(247, 249, 254, 0) 109.821%);
        }
      }
      &-content {
        height: 30px;
        display: flex;
        align-items: center;
        justify-content: space-between;
        font-size: 13px;
        color: #242859;
        border-radius: 4px;
        font-size: 12px;
        color: #383838;
        background-color: #fff;
        padding: 0 8px;
        &-btn-box {
          cursor: pointer;
          &-btn {
            color: #666666;
          }
          .right-icon {
            margin-left: 3px;
          }
        }
      }
    }
  }
}
</style>
