<template>
  <section class="x-tree-select">
    <x-popover
      :trigger="props.showSearch ? 'focus' : 'click'"
      :container="props.popupContainer"
      v-model:visible="config.popShow"
      placement="bottom"
      :triangle="false"
      :gap="6"
    >
      <div
        v-if="props.showSearch"
        class="x-tree-select-search-selector"
        ref="_bindSearchRef"
      >
        <x-input
          :value="_searchValue"
          @update:value="searchValueUpdate"
          :placeholder="_placeholder"
          @focus="inputFocus"
          @blur="inputBlur"
          suffix="input_search"
          allowClear
          :filter="searchValueFilter"
          :disabled="props.disabled"
        />
        <span
          v-if="props.value && !_searchValue"
          :class="[{ transparent: valueTransp }]"
        >
          {{ _selectedTitle }}
        </span>
      </div>
      <div v-else>待开发</div>
      <template #content>
        <div
          v-show="treeDataFilter.length > 0"
          class="x-tree-select-content"
          :style="{ width: `${_width}px` }"
        >
          <x-tree
            :treeData="treeDataFilter"
            @update:selectedItems="itemUpdate"
          >
            <template #title="{ title }">
              <template
                v-for="(item, index) in title.split(new RegExp(`(?<=${_searchValue})|(?=${_searchValue})`, 'i'))"
                :key="index"
              >
                <template v-if="_searchValue.toLowerCase().includes(item.toLowerCase()) && props.showSearch">
                  <span style="color: #5964fb">{{ item }}</span>
                </template>
                <template v-else>{{ item }}</template>
              </template>
            </template>
          </x-tree>
        </div>
      </template>
    </x-popover>
  </section>
</template>
<script lang="ts" setup>
import { reactive, ref, computed, watch, watchEffect } from "vue";
import type { PropType } from "vue";
import { treeBfsParse } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xPopover from "@/components/x-popover.vue";
import xInput from "@/components/x-input.vue";
import xTree from "@/components/x-tree";

const props = defineProps({
  value: {
    type: [String, Number],
    required: true,
  },
  treeData: {
    type: Array as PropType<TreeItemType[]>,
    required: true,
  },
  popupContainer: {
    type: HTMLElement,
  },
  showSearch: {
    type: Boolean,
    default: false,
  },
  searchValue: {
    type: String,
  },
  placeholder: {
    type: String,
    default: "",
  },
  disabled: {
    type: Boolean,
    default: false,
  },
});

const emits = defineEmits(["update:value", "change", "update:searchValue"]);

const config = reactive({
  popShow: false,
});
watch(
  () => config.popShow,
  (_newV) => !_newV && searchValueUpdate("")
);

const _placeholder = computed(() => (props.value ? "" : props.placeholder));
const inputFocus = () => {
  config.popShow = true;
  valueTransp.value = true;
};
const inputBlur = () => {
  valueTransp.value = false;
};

const _searchValue = ref("");
const searchValueUpdate = (value: string) => {
  _searchValue.value = value;
  emits("update:searchValue", value);
};

const _selectedTitle = ref<number | string>("");
const valueTransp = ref(false);
const itemUpdate = (items: TreeItemType[]) => {
  config.popShow = false;
  _selectedTitle.value = items[0].title;
  searchValueUpdate("");
  emits("update:value", items[0].value);
  emits("change", items[0]);
};

const treeDataFilter = computed(() => {
  if (props.showSearch) {
    const dfs = (tree: TreeItemType[]) => {
      // 需要是动态值，否则展开不生效
      const _tree: TreeItemType[] = reactive(JSON.parse(JSON.stringify(tree)));
      const _dfs = (child: TreeItemType) => {
        if (child.children && child.children.length > 0) {
          // 有搜索关键字的时候自动展开
          if (_searchValue.value) {
            child.expanded = true;
          }
          const _children: TreeItemType[] = [];
          child.children.forEach((item) => _dfs(item) && _children.push(item));
          child.children = _children;
          return child.children.length || child.title.includes(_searchValue.value);
        } else {
          return child.title.includes(_searchValue.value);
        }
      };

      return _tree.filter((item) => _dfs(item));
    };
    return dfs(props.treeData);
  }
  return props.treeData;
});

const _bindSearchRef = ref<any>();
const _bindNormalRef = ref<any>();
const _width = ref(0);
watch(
  () => config.popShow,
  (newV) => {
    if (newV) {
      _width.value = (props.showSearch ? _bindSearchRef.value : _bindNormalRef.value).getBoundingClientRect().width;
    }
  }
);
const searchValueFilter = (v: string) => v.trim();
watchEffect(() => {
  _searchValue.value = props.searchValue || "";
  treeBfsParse(props.treeData, "children", (item: TreeItemType) => {
    if (props.value === item.value) {
      _selectedTitle.value = item.title;
    }
  });
});
</script>

<style lang="scss" scoped>
.x-tree-select {
  &-search-selector {
    position: relative;
    width: 100%;
    span {
      pointer-events: none;
      @include ct-p(y) {
        left: 10px;
      }
      &.transparent {
        color: rgba(0, 0, 0, 0.4);
      }
    }
  }
  &-content {
    overflow-y: auto;
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    max-height: 212px;
    padding: 2px 6px 6px 6px;
    background-color: #fff;
  }
}
</style>
