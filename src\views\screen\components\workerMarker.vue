<template>
  <section class="worker-marker">
    <div :class="['worker-marker-image', { disable: !props.online }]"></div>
    <div v-if="!props.focus" class="worker-marker-username">
      <div class="username-left"></div>
      {{ props.userName }}
    </div>
  </section>
</template>
<script lang="ts" setup>
const props = withDefaults(
  defineProps<{
    online: boolean;
    userName: string;
    focus: boolean;
  }>(),
  {}
);
</script>

<style lang="scss" scoped>
.worker-marker {
  position: relative;
  @include wh(2.4vw, 3vw);
  &-image {
    @include wh(100%);
    @include bis("@/assets/images/screen_woker.png");
    &.disable {
      @include bis("@/assets/images/screen_woker_disable.png");
    }
  }
  &-username {
    @include ct-p(x) {
      top: -1.6vw;
    }
    height: 1.2vw;
    padding-left: 0.4vw;
    background: linear-gradient(
      to left,
      rgba(90, 113, 252, 0),
      rgb(90, 113, 252)
    );
    border-radius: 0.2vw 0 0 0.2vw;
    @include sc(0.8vw, #fff) {
      font-weight: 700;
      line-height: 1.2vw;
      text-align: center;
    }
    .username-left {
      position: absolute;
      left: 0;
      top: 0;
      @include wh(0.2vw, 1.2vw) {
        background: linear-gradient(
          to bottom,
          rgb(158, 174, 250),
          rgb(90, 118, 252)
        );
      }
      border-radius: 0.2vw 0 0 0.2vw;
    }
  }
}
</style>
