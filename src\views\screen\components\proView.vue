<template>
  <section class="pro-view">
    <sub-title>项目概览 - 车辆</sub-title>
    <div class="pro-view-content">
      <div class="pro-view-content-car">
        <div class="pro-view-content-car-top">
          <div class="top-rate">
            <div class="top-rate-bg">
              <roundPercent
                :percent="proData.rate"
                style="width: 80px; height: 6vh"
              />
              <div class="top-rate-number">
                <span>{{ proData.rate }}</span>
                <span class="unit">%</span>
              </div>
            </div>
            <div class="top-rate-text">出勤率</div>
          </div>
          <div
            class="top-all"
            @click="handleVehClick('all')"
          >
            <div class="top-all-number">{{ proData.all }}</div>
            <div class="top-all-bg"></div>
            <div class="top-all-light"></div>
            <div class="top-all-text">全部车辆</div>
          </div>
          <div
            class="top-online"
            @click="handleVehClick('online')"
          >
            <div class="top-online-number">{{ proData.online }}</div>
            <div class="top-online-bg"></div>
            <div class="top-online-light"></div>
            <div class="top-online-text">在线车辆</div>
          </div>
          <div
            class="top-offline"
            @click="handleVehClick('offline')"
          >
            <div class="top-offline-number">{{ proData.offline }}</div>
            <div class="top-offline-bg"></div>
            <div class="top-offline-light"></div>
            <div class="top-offline-text">离线车辆</div>
          </div>
        </div>
        <div class="pro-view-content-car-bottom">
          <div
            class="bottom-work"
            @click="handleVehClick('work')"
          >
            <div class="bottom-work-left">
              <div class="bottom-work-left-img">
                <div class="bottom-work-left-img-icon screen_pro_circle_outer_green"></div>
                <div class="bottom-work-left-img-icon rotate screen_pro_circle_inner_green"></div>
              </div>
              <div class="info">
                <div class="info-top">{{ proData.work }}辆</div>
                <div class="info-bottom">作业车辆</div>
              </div>
            </div>
            <x-icon
              class="bottom-work-right"
              name="screen_pro_arrow_right"
            />
          </div>
          <div
            class="bottom-warn"
            @click="handleVehClick('warn')"
          >
            <div class="bottom-warn-left">
              <div class="bottom-warn-left-img">
                <div class="bottom-warn-left-img-icon screen_pro_circle_outer_red"></div>
                <div class="bottom-warn-left-img-icon rotate screen_pro_circle_inner_red"></div>
              </div>
              <div class="info">
                <div class="info-top">{{ proData.warn }}辆</div>
                <div class="info-bottom">告警车辆</div>
              </div>
            </div>
            <x-icon
              class="bottom-warn-right"
              name="screen_pro_arrow_right"
            />
          </div>
        </div>
      </div>
      <div class="pro-view-content-user">
        <div class="user-title">
          <span>人员</span>
        </div>
        <div class="user-content">
          <div class="info-rate">
            <div class="info-rate-bg">
              <semiRoundPercent
                :percent="userData.rate"
                style="width: 120px; height: 5.5vh"
              />
              <div class="info-rate-number">
                <span>{{ userData.rate }}</span>
                <span class="unit">%</span>
              </div>
            </div>
            <div class="info-rate-text">出勤率</div>
          </div>
          <div
            class="info-area"
            @click="handleAreaClick"
          >
            <div class="info-area-number">{{ userData.area }}个</div>
            <x-icon
              class="info-area-icon"
              name="screen_pro_area"
              width="20"
              height="20"
            />
            <div class="info-area-light"></div>
            <div class="info-area-bg"></div>
            <div class="info-area-text">运营区域</div>
          </div>
          <div
            class="info-user"
            @click="handleUserClick"
          >
            <div class="info-user-number">{{ userData.staff }}个</div>
            <x-icon
              class="info-area-icon"
              name="screen_pro_user"
              width="20"
              height="20"
            />
            <div class="info-user-light"></div>
            <div class="info-user-bg"></div>
            <div class="info-user-text">运维人员</div>
          </div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { reactive, watch } from "vue";
import xIcon from "@/components/x-icon.vue";
import subTitle from "./subTitle.vue";
import roundPercent from "./roundPercent.vue";
import semiRoundPercent from "./semiRoundPercent.vue";
import { getProUser } from "@/services/api";
import { useMainStore } from "@/stores/main";

const { socket } = useMainStore();

const props = defineProps({
  proId: {
    type: String,
    required: true,
  },
});

const proData = reactive({
  rate: 0,
  all: 0,
  online: 0,
  offline: 0,
  work: 0,
  warn: 0,
});

const userData = reactive({
  rate: 0,
  area: 0,
  areaList: [] as any,
  staff: 0,
  staffList: [] as any,
});

const getProUserInfo = async () => {
  const { workers, areas, attendanceRate } = await getProUser(props.proId);
  userData.rate = Math.round(attendanceRate) || 0;
  userData.area = areas?.length || 0;
  userData.areaList = areas;
  userData.staff = workers?.length || 0;
  userData.staffList = workers;
};

const emits = defineEmits(["showVehModal", "showUserModal", "showAreaModal"]);
const handleVehClick = (type: string) => {
  emits("showVehModal", type);
};
const handleUserClick = () => {
  emits("showUserModal", userData.staffList);
};
const handleAreaClick = () => {
  emits("showAreaModal", userData.areaList);
};

watch(
  () => props.proId,
  (newV) => {
    if (newV) {
      getProUserInfo();
    }
  }
);

watch(
  () => socket.baseStatus,
  (newV) => {
    if (newV) {
      const totalVehicles = newV.length;
      const onlineVehicles = newV.filter((vehicle) => vehicle.online).length;
      const offlineVehicles = newV.filter((vehicle) => !vehicle.online).length;
      const workingVehicles = newV.filter((vehicle) => vehicle.attendanceTimes > 0).length;
      const warnVehicles = newV.filter((vehicle) => vehicle.warnLevel !== 4).length;
      proData.rate = Math.round((workingVehicles ?? 0 / totalVehicles ?? 1) * 100);
      proData.all = totalVehicles;
      proData.online = onlineVehicles;
      proData.offline = offlineVehicles;
      proData.work = newV.filter((vehicle) => vehicle.workStatus == 1 && vehicle.online).length;
      proData.warn = warnVehicles;
    }
  }
);
</script>

<style lang="scss" scoped>
.pro-view {
  &-content {
    display: flex;
    flex-direction: column;
    padding: 0 22px 1vh 22px;
    &-car {
      &-top {
        display: flex;
        justify-content: space-between;
        .top-rate {
          @include ct-f(y) {
            flex-direction: column;
            justify-content: space-between;
          }
          &-bg {
            position: relative;
          }
          &-number {
            @include ct-p(x) {
              top: 1.2vh;
            }
            @include sc(2.2vh, rgb(255, 255, 255)) {
              font-weight: 700;
            }
            .unit {
              font-size: 1.1vh;
            }
          }
          &-text {
            @include sc(1.4vh, rgb(221, 229, 250));
          }
        }
        .top-all,
        .top-online,
        .top-offline {
          @include ct-f(y) {
            flex-direction: column;
            justify-content: space-between;
          }
          position: relative;
          cursor: pointer;
          &-number {
            @include sc(1.9vh, rgb(255, 255, 255)) {
              font-weight: 700;
            }
          }
          &-bg {
            @include wh(80px, 3.3vh);
          }
          &-light {
            position: absolute;
            bottom: 3.3vh;
            left: 50%;
            transform: translate(-50%, 0);
            @include wh(56px, 4vh);
            background-repeat: no-repeat;
            background-size: 100% 100%;
            animation: flash 1s linear infinite;
          }
          &-text {
            @include sc(1.4vh, rgb(221, 229, 250));
          }
        }
        .top-all {
          &-bg {
            @include bis("@/assets/images/screen_car_num_all_bg.png");
          }
          &-light {
            @include bis("@/assets/images/screen_car_num_all_light.png");
          }
        }
        .top-online {
          &-bg {
            @include bis("@/assets/images/screen_car_num_online_bg.png");
          }
          &-light {
            @include bis("@/assets/images/screen_car_num_online_light.png");
          }
        }
        .top-offline {
          &-bg {
            @include bis("@/assets/images/screen_car_num_offline_bg.png");
          }
          &-light {
            @include bis("@/assets/images/screen_car_num_offline_light.png");
          }
        }
      }
      &-bottom {
        display: flex;
        margin: 1vh 0;
        .bottom-work,
        .bottom-warn {
          @include ct-f {
            justify-content: space-between;
          }
          @include wh(100%, 5.5vh);
          border-radius: 8px;
          cursor: pointer;
          &-left {
            display: flex;
            padding: 5px;
            &-img {
              position: relative;
              width: 50px;
              &-icon {
                position: absolute;
                left: 50%;
                top: 50%;
                transform: translate(-50%, -50%);
                &.screen_pro_circle_outer_green {
                  @include wh(44px, 44px);
                  @include bis("@/assets/images/screen_pro_circle_outer_green.png");
                }
                &.screen_pro_circle_inner_green {
                  @include wh(37px, 37px);
                  @include bis("@/assets/images/screen_pro_circle_inner_green.png");
                }
                &.screen_pro_circle_outer_red {
                  @include wh(44px, 44px);
                  @include bis("@/assets/images/screen_pro_circle_outer_red.png");
                }
                &.screen_pro_circle_inner_red {
                  @include wh(37px, 37px);
                  @include bis("@/assets/images/screen_pro_circle_inner_red.png");
                }
                &.rotate {
                  animation: rotate-counterclockwise 2s linear infinite;
                }
              }
            }
            .info {
              &-top {
                @include sc(2vh, rgba(255, 255, 255)) {
                  font-weight: 500;
                }
              }
              &-bottom {
                @include sc(1.2vh, rgba(255, 255, 255, 0.7)) {
                  font-weight: 500;
                }
              }
            }
          }
          &-right {
            padding: 5px;
            cursor: pointer;
          }
        }
        .bottom-work {
          background: linear-gradient(0deg, rgba(39, 212, 161, 0.3), rgba(196, 196, 196, 0) 100%);
          margin-right: 10px;
        }
        .bottom-warn {
          background: linear-gradient(0deg, rgba(212, 39, 39, 0.3), rgba(196, 196, 196, 0) 100%);
        }
      }
    }
    &-user {
      display: flex;
      flex-direction: column;
      .user-title {
        position: relative;
        width: 179px;
        @include sc(1.8vh, #dde5fa) {
          font-weight: 600;
        }
        background: linear-gradient(270deg, rgba(43, 116, 255, 0), rgba(43, 116, 255, 0.3) 100%);
        &::after {
          content: "";
          position: absolute;
          top: 0;
          left: 0;
          @include wh(21px, 2.68vh) {
            @include bis("@/assets/images/screen_sub_title_left.png");
          }
        }
        span {
          padding-left: 21px;
        }
      }
      .user-content {
        @include ct-f(y) {
          justify-content: space-between;
          margin-top: -1vh;
        }
        .info-rate,
        .info-area,
        .info-user {
          height: 100%;
          @include ct-f(y) {
            flex-direction: column;
            justify-content: space-between;
          }
        }
        .info-rate {
          &-bg {
            position: relative;
            margin-top: 1.6vh;
          }
          &-number {
            position: absolute;
            bottom: 5%;
            left: 50%;
            transform: translate(-50%, -5%);
            @include sc(2.2vh, rgb(255, 255, 255)) {
              font-weight: 700;
            }
            .unit {
              font-size: 1.1vh;
            }
          }
          &-text {
            @include sc(1.4vh, rgb(221, 229, 250));
          }
        }
        .info-area,
        .info-user {
          position: relative;
          cursor: pointer;
          &-number {
            position: relative;
            top: 1vh;
            @include sc(1.9vh, rgb(255, 255, 255)) {
              font-weight: 700;
            }
          }
          &-icon {
            position: relative;
            top: 1vh;
            z-index: 999;
          }
          &-bg {
            position: relative;
            @include wh(70px, 2.5vh);
            @include bis("@/assets/images/screen_pro_user.png");
          }
          &-text {
            @include sc(1.4vh, rgb(221, 229, 250));
          }
          &-light {
            @include bis("@/assets/images/screen_pro_user_light.png");
            position: absolute;
            top: 2.4vh;
            @include wh(50px, 4vh);
            animation: flash 1s linear infinite;
          }
        }
      }
    }
  }
  @keyframes flash {
    0% {
      opacity: 1;
    }
    25% {
      opacity: 0.5;
    }
    50% {
      opacity: 1;
    }
    75% {
      opacity: 0.5;
    }
    100% {
      opacity: 1;
    }
  }
  @keyframes rotate-counterclockwise {
    from {
      transform: translate(-50%, -50%) rotate(0deg);
    }
    to {
      transform: translate(-50%, -50%) rotate(-360deg);
    }
  }
}
</style>
