// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`Snapshot > 正确渲染 默认快照 1`] = `
"<form class="x-form" data-v-212271e6="">
  <section class="x-form-item" data-v-c5b97758="">
    <div class="x-form-item-label" style="flex: 7;" data-v-c5b97758=""><label class="" data-v-c5b97758="">用户名</label></div>
    <div class="x-form-item-control" style="flex: 40;" data-v-c5b97758="">
      <div class="x-form-item-control-slot" data-v-c5b97758=""></div>
      <div class="x-form-item-control-explain" data-v-c5b97758="">
        <transition-stub name="x-form-item-control-explain" appear="false" persisted="true" css="true" data-v-c5b97758=""><span data-v-c5b97758="" style="display: none;"></span></transition-stub><span class="x-form-item-extra" data-v-c5b97758="" style="display: none;"></span>
      </div>
    </div>
  </section>
  <section class="x-form-item" data-v-c5b97758="">
    <div class="x-form-item-label" style="flex: 7;" data-v-c5b97758=""><label class="" data-v-c5b97758="">密码</label></div>
    <div class="x-form-item-control" style="flex: 40;" data-v-c5b97758="">
      <div class="x-form-item-control-slot" data-v-c5b97758=""></div>
      <div class="x-form-item-control-explain" data-v-c5b97758="">
        <transition-stub name="x-form-item-control-explain" appear="false" persisted="true" css="true" data-v-c5b97758=""><span data-v-c5b97758="" style="display: none;"></span></transition-stub><span class="x-form-item-extra" data-v-c5b97758="" style="display: none;"></span>
      </div>
    </div>
  </section>
</form>"
`;
