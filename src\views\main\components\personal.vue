<template>
  <x-modal
    :visible="props.show"
    @update:visible="updateVisible"
    :title="picking ? '更换头像' : '个人中心'"
    width="674px"
    height="471px"
  >
    <div
      v-show="picking === false"
      class="personal"
    >
      <div class="personal-left">
        <div
          class="head-img"
          :style="{ backgroundImage: headImgUrl }"
        >
          <img
            src="@/assets/images/head_bottom_icon.png"
            class="head-img-clickable-area"
            @click="picking = !picking"
          />
        </div>
        <div class="user-name">{{ userInfo.userName }}</div>
      </div>
      <div class="personal-right">
        <div class="user-info-key">
          <span>{{ $t("account") }}</span>
          <span>{{ $t("phoneNumber") }}</span>
          <span>{{ $t("positionType") }}</span>
          <span>{{ $t("ofRole") }}</span>
          <span>{{ $t("ofCompany") }}</span>
          <span class="login-info-text"> {{ $t("loginInfo") }} </span>
          <span>{{ $t("lastLogin") }}</span>
          <span>{{ $t("lastLogin") }}IP </span>
        </div>
        <div class="user-info-value">
          <span>{{ userInfo.userAccount }}</span>
          <span>{{ userInfo.mobile }}</span>
          <span>{{ userInfo.stationText }}</span>
          <span>{{ userInfo.roleName }}</span>
          <span>{{ userInfo.enterpriseTree.entName }}</span>
          <span></span>
          <span>
            {{ formatDateTime(userInfo.lastLoginTime, "YYYY-MM-DD HH:mm:ss") }}
          </span>
          <span>{{ userInfo.lastIp }}</span>
        </div>
      </div>
    </div>
    <div
      v-show="picking === true"
      class="head-img-change"
    >
      <div
        class="fixed-frame"
        :style="{ backgroundImage: fixedFrameBgUrl }"
        @click="pickImg"
        @drop="fileSelect"
        @dragover="dragOver"
        @mouseleave="mouseUp()"
        ref="fixedFrameRef"
      >
        <div
          v-show="!picked"
          class="picking-tip"
        >
          &ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;&ensp;+
          <br />{{ $t("dragImageToThis") }}
        </div>
        <div
          v-show="picked"
          ref="variableImgBoxRef"
          class="variable-image-box"
          :style="{
            width: variableImgBoxPos.width + 'px',
            height: variableImgBoxPos.height + 'px',
            left: variableImgBoxPos.left + 'px',
            top: variableImgBoxPos.top + 'px',
          }"
          @click="
            (e) => {
              e.stopPropagation();
            }
          "
          @wheel="zoom"
          @mousedown="mouseDown"
        >
          <img
            ref="imageRef"
            class="selected-image"
            draggable="false"
          />
        </div>
        <div
          class="cut-border"
          v-show="picked"
        ></div>
        <div
          class="fixed-frame-mask"
          v-show="picked"
        ></div>
      </div>

      <div
        class="bottom-menu"
        v-show="picked"
      >
        <div class="img-translate">
          <div
            class="larger"
            @click="larger"
          >
            <x-icon
              name="user_personal_larger"
              width="16px"
              height="16px"
            />
          </div>
          <div class="separate">|</div>
          <div
            class="smaller"
            @click="smaller"
          >
            <x-icon
              name="user_personal_smaller"
              width="16px"
              height="16px"
            />
          </div>
        </div>
        <div class="picked-tip">{{ $t("dragImageCutAvatar") }}</div>
        <canvas
          ref="canvasRef"
          width="150"
          height="150"
          v-show="false"
        ></canvas>
        <div
          class="head-img-save"
          @click="saveHeadImg"
        >
          {{ $t("save") }}
        </div>
      </div>

      <input
        ref="imgInputRef"
        @change="getImg"
        v-show="false"
        type="file"
        accept="image/*"
      />
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { ref, onMounted, watch, reactive } from "vue";
import xModal from "@/components/x-modal";
import { updateHeadPic } from "@/services/api";
import { setLocalStorage } from "@/assets/ts/storage";
import { useMainStore } from "@/stores/main";
import { formatDateTime } from "@/assets/ts/dateTime";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
});
const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  imgInputRef.value.value = "";
  setTimeout(() => {
    picking.value = false;
    picked.value = false;
  }, 1000);
};

const picking = ref(false); //处于更换头像窗口
const picked = ref(false); //已选择图片
const pickImg = () => {
  if (picked.value) {
    return;
  }
  imgInputRef.value.click();
};

const variableImgBoxRef = ref();
const headImgUrl = ref();
const fixedFrameBgUrl = ref();
const canvasRef = ref();
const imgInputRef = ref();
const imageRef = ref();
const fixedFrameRef = ref();
const variableImgBoxPos = reactive({
  width: 0,
  height: 0,
  left: 0,
  top: 0,
});
let ctx: CanvasRenderingContext2D;
const headPicRadius = 75;
const { userInfo } = useMainStore();

if (userInfo.imgUrl) {
  headImgUrl.value = `url(${userInfo.imgUrl})`;
}

watch(picked, (newX) => {
  if (newX === false) {
    variableImgBoxPos.top = 0;
    variableImgBoxPos.left = 0;
    fixedFrameBgUrl.value = "";
  } else {
    setTimeout(() => {
      setVariableImgBoxSize();
      fixedFrameBgUrl.value = `url(${new URL("/src/assets/images/user_personal_frame_bg.jpg", import.meta.url).href})`;
    }, 10);
  }
});

let maxMoveX: number;
let maxMoveY: number;
const setVariableImgBoxSize = () => {
  let widthRatio = fixedFrameRef.value.offsetWidth / imageRef.value.naturalWidth;
  let heightRatio = fixedFrameRef.value.offsetHeight / imageRef.value.naturalHeight;
  if (widthRatio >= heightRatio) {
    variableImgBoxPos.height = fixedFrameRef.value.offsetHeight;
    variableImgBoxPos.width = imageRef.value.naturalWidth * heightRatio;
  } else {
    variableImgBoxPos.width = fixedFrameRef.value.offsetWidth;
    variableImgBoxPos.height = imageRef.value.naturalHeight * widthRatio;
  }
  maxMoveX = variableImgBoxPos.width / 2 - headPicRadius;
  maxMoveY = variableImgBoxPos.height / 2 - headPicRadius;
};

const getImg = (e: Event) => {
  let files = (e.target as HTMLInputElement).files;
  if (files!.length > 0) {
    picked.value = true;
    imageRef.value.src = URL.createObjectURL(files![0]);
  }
};

const saveHeadImg = () => {
  let initScale = Math.max(
    imageRef.value.naturalWidth / variableImgBoxPos.width,
    imageRef.value.naturalHeight / variableImgBoxPos.height
  );

  ctx.drawImage(
    imageRef.value,
    imageRef.value.naturalWidth / 2 - headPicRadius * initScale - variableImgBoxPos.left * initScale,
    imageRef.value.naturalHeight / 2 - headPicRadius * initScale - variableImgBoxPos.top * initScale,
    headPicRadius * 2 * initScale,
    headPicRadius * 2 * initScale,
    0,
    0,
    150,
    150
  );

  canvasRef.value.toBlob(
    async (blob: Blob) => {
      const formData = new FormData();
      formData.append("file", blob, "myHead.jpg");
      const result = await updateHeadPic(userInfo.userId, formData);

      headImgUrl.value = `url(${result.uploadUrl})`;
      userInfo.imgUrl = result.uploadUrl;
      setLocalStorage("userInfo", userInfo);
      useMainStore().$state.userInfo!.imgUrl = result.uploadUrl;
      ctx.clearRect(0, 0, canvasRef.value.width, canvasRef.value.height);
      imgInputRef.value.value = "";
      picked.value = false;
      picking.value = false;
      URL.revokeObjectURL(imageRef.value.src);
    },
    "image/jpeg",
    0.9
  );
};

const dragOver = (e: DragEvent) => {
  e.stopPropagation();
  e.preventDefault();
  e.dataTransfer!.dropEffect = "copy";
};

const fileSelect = (e: DragEvent) => {
  e.stopPropagation();
  e.preventDefault();

  let files = e.dataTransfer!.files;
  if (files.length > 0) {
    let fileName = files[0].name;
    let suffix = fileName.substring(fileName.lastIndexOf(".") + 1);
    if (suffix == "jpg" || suffix == "png" || suffix == "jpeg") {
      picked.value = true;
      imageRef.value.src = URL.createObjectURL(files[0]);
    }
  }
};

const SCALE = 1.1;
const zoom = (e: WheelEvent) => {
  if (e.deltaY < 0) {
    larger();
  } else {
    smaller();
  }
};

const larger = () => {
  variableImgBoxPos.width *= SCALE;
  variableImgBoxPos.height *= SCALE;
  maxMoveX = variableImgBoxPos.width / 2 - headPicRadius;
  maxMoveY = variableImgBoxPos.height / 2 - headPicRadius;
};

const smaller = () => {
  if (
    variableImgBoxPos.left > variableImgBoxPos.width / SCALE / 2 - headPicRadius ||
    variableImgBoxPos.left < -(variableImgBoxPos.width / SCALE / 2 - headPicRadius) ||
    variableImgBoxPos.top > variableImgBoxPos.height / SCALE / 2 - headPicRadius ||
    variableImgBoxPos.top < -(variableImgBoxPos.height / SCALE / 2 - headPicRadius)
  ) {
    return;
  }
  variableImgBoxPos.width /= SCALE;
  variableImgBoxPos.height /= SCALE;
  maxMoveX = variableImgBoxPos.width / 2 - headPicRadius;
  maxMoveY = variableImgBoxPos.height / 2 - headPicRadius;
};

let mouseDownX: number;
let mouseDownY: number;
let currentTop: number = 0;
let currentLeft: number = 0;

const mouseDown = (e: MouseEvent) => {
  mouseDownX = e.clientX;
  mouseDownY = e.clientY;
  currentTop = variableImgBoxPos.top;
  currentLeft = variableImgBoxPos.left;
  variableImgBoxRef.value.style.cursor = "grabbing";
  fixedFrameRef.value.addEventListener("mousemove", mouseMove);
  fixedFrameRef.value.addEventListener("mouseup", mouseUp);
};

let transX = 0;
let transY = 0;
const mouseMove = (e: MouseEvent) => {
  transX = e.clientX - mouseDownX;
  transY = e.clientY - mouseDownY;
  variableImgBoxPos.top = Math.sign(transY + currentTop) * Math.min(Math.abs(transY + currentTop), maxMoveY);
  variableImgBoxPos.left = Math.sign(transX + currentLeft) * Math.min(Math.abs(transX + currentLeft), maxMoveX);
};

const mouseUp = () => {
  fixedFrameRef.value.removeEventListener("mousemove", mouseMove);
  fixedFrameRef.value.removeEventListener("mouseup", mouseUp);
  variableImgBoxRef.value.style.cursor = "grab";
};

onMounted(() => {
  ctx = canvasRef.value.getContext("2d");
});
</script>

<style lang="scss" scoped>
.personal {
  display: flex;
  height: 100%;
  &-left {
    width: 36%;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding-bottom: 50px;
    .head-img {
      height: 140px;
      width: 140px;
      background-image: url("@/assets/images/user_default_head.png");
      display: flex;
      justify-content: center;
      align-items: flex-end;
      border-radius: 50%;
      overflow: hidden;
      &-clickable-area {
        cursor: pointer;
      }
    }
    .user-name {
      font-size: 18px;
      margin-top: 14px;
    }
  }
  &-right {
    flex-grow: 1;
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    .user-info-key {
      height: 100%;
      width: 30%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: start;
      color: rgb(159, 159, 164);
      font-size: 14px;
      & > * {
        flex-basis: 20%;
      }
      .login-info-text {
        color: rgb(89, 100, 251);
        font-size: 12px;
        margin-top: 10px;
        margin-bottom: -10px;
      }
    }
    .user-info-value {
      height: 100%;
      width: 70%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: start;
      color: rgb(56, 56, 56);
      font-size: 14px;
      & > * {
        flex-basis: 20%;
      }
    }
  }
}

.head-img-change {
  display: flex;
  flex-direction: column;
  height: 100%;
  justify-content: center;
  align-items: center;
  .fixed-frame {
    width: 472px;
    height: 238px;
    border: 1px dashed rgb(173, 163, 163);
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    overflow: hidden;
    .picking-tip {
      color: rgb(159, 159, 164);
    }
    .variable-image-box {
      position: relative;
      cursor: grab;
      width: 100%;
      height: 100%;
      background-color: black;
      .selected-image {
        width: 100%;
        height: 100%;
        object-fit: contain;
      }
    }
    .cut-border {
      position: absolute;
      border-radius: 50%;
      border: solid 1px rgb(89, 100, 251);
      width: 150px;
      height: 150px;
      pointer-events: none;
    }
    &-mask {
      top: 74px;
      width: 472px;
      height: 238px;
      background-color: black;
      opacity: 0.4;
      position: fixed;
      pointer-events: none;
      clip-path: path(
        "M10 0L462 0C467.522 0 472 4.47705 472 10L472 228C472 233.523 467.522 238 462 238L10 238C4.47705 238 0 233.523 0 228L0 10C0 4.47705 4.47705 0 10 0ZM236 44C194.579 44 161 77.5786 161 119C161 160.421 194.579 194 236 194C277.421 194 311 160.421 311 119C311 77.5786 277.421 44 236 44Z"
      );
    }
  }

  .bottom-menu {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    justify-content: center;
    .img-translate {
      display: flex;
      justify-content: center;
      border: solid 1px rgb(220, 220, 220);
      border-radius: 20px;

      .larger {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 34px;
        width: 60px;
      }
      .separate {
        display: flex;
        justify-content: center;
        align-items: center;
        color: rgb(220, 220, 220);
      }
      .smaller {
        cursor: pointer;
        display: flex;
        justify-content: center;
        align-items: center;
        height: 34px;
        width: 60px;
      }
    }
    .picked-tip {
      margin-top: 12px;
      display: flex;
      justify-content: center;
      font-size: 14px;
      color: rgb(94, 94, 94);
    }
    .head-img-save {
      cursor: pointer;
      margin-left: 32px;
      margin-top: 12px;
      display: flex;
      justify-content: center;
      align-items: center;
      width: 60px;
      height: 32px;
      background-color: rgb(89, 100, 251);
      color: rgb(255, 255, 255);
      border-radius: 4px;
    }
  }
}
</style>
