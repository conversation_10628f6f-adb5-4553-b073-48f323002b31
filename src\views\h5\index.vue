<template>
  <section class="h5-page">
    <router-view />
  </section>
</template>

<script lang="ts" setup>
import { onMounted, onUnmounted } from "vue";

function initViewport() {
  const width = 375; // 设计稿宽度
  const scale = window.innerWidth / width;
  let meta = document.querySelector("meta[name=viewport]");
  const content = `width=${width}, init-scale=${scale}, user-scalable=no`;
  if (!meta) {
    meta = document.createElement("meta");
    meta.setAttribute("name", "viewport");
    document.head.appendChild(meta);
  }
  meta.setAttribute("content", content);
}

function removeViewport() {
  const meta = document.querySelector("meta[name=viewport]");
  if (meta) {
    const content = "width=device-width, initial-scale=1.0";
    meta.setAttribute("content", content);
  }
}

onMounted(initViewport);
onUnmounted(removeViewport);
</script>
