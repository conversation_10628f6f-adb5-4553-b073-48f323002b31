<!-- 管线和碰撞 -->
<template>
  <div
    class="pipeline_collision_page"
    ref="containerRef"
  >
    <template v-if="props.activeTab === 'pipeline'">
      <div
        class="pipeline_collision_page-item"
        v-for="item in listData"
        :key="item.id"
      >
        <div class="image_wrapper">
          <el-image
            class="image_item"
            :src="item.picUrl"
            fit="cover"
            :preview-src-list="[item.picUrl]"
            :preview-teleported="true"
          />

          <span class="image_wrapper_time">{{ item.createdTime }}</span>
        </div>
        <div class="pipeline_collision_page-item-result">
          <span class="label">人工识别结果：</span>
          <span class="pipeline_collision_page-item-result-pass">
            <template v-if="item.recognizeResult">
              <span v-if="item.recognizeResult === 1">已解除</span>
              <span
                v-else
                style="color: #f9852e"
                >未解除</span
              >
            </template>
            <template v-else>
              <span>未识别</span>
            </template>
          </span>
        </div>
        <div class="pipeline_collision_page-item-result">
          <span class="label">校验人：</span>
          <span
            class="pipeline_collision_page-item-result-pass"
            style="color: #242859"
            >{{ item.updatedUser ?? "-" }}</span
          >
        </div>
        <div class="pipeline_collision_page-item-result">
          <span class="label">时间：</span>
          <span
            class="pipeline_collision_page-item-result-pass"
            style="color: #242859"
            >{{ item.updatedTime }}</span
          >
        </div>
      </div>
      <div
        v-if="loading"
        class="loading-more"
      >
        加载中...
      </div>
      <div
        v-if="!hasMore"
        class="no-more"
      >
        没有更多数据了
      </div>
      <XEmpty
        v-if="listData.length === 0"
        description="暂无数据"
      ></XEmpty>
    </template>
    <template v-if="props.activeTab === 'collision'">
      <div
        class="pipeline_collision_page-item"
        v-for="item in mockSmartOrders"
        :key="item.id"
      >
        <div class="image_wrapper">
          <el-image
            class="image_item"
            :src="item.image"
            fit="cover"
            :preview-src-list="[item.image]"
            :preview-teleported="true"
          />
          <span class="image_wrapper_time">{{ item.time }}</span>
        </div>
        <div class="pipeline_collision_page-item-result">
          <span>地点：</span>
          <span class="pipeline_collision_page-item-result-location">{{ item.location }}</span>
        </div>
      </div>
      <XEmpty
        v-if="mockSmartOrders.length === 0"
        description="暂无数据"
      ></XEmpty>
    </template>
  </div>
</template>
<script setup lang="ts">
import { getSmartOrderList } from "@/services/api";
import XEmpty from "@/components/x-empty.vue";
import { getRedLightList, upDateRedLightDetail } from "@/services/api";
import { onMounted, ref, watch, onUnmounted, nextTick, inject } from "vue";
import type { PropType, Ref } from "vue";
import type { CarInfo } from "../../../type";
import { debounce } from "@/assets/ts/utils";
const props = defineProps({
  activeTab: {
    type: String as PropType<"pipeline" | "collision" | any>,
    default: "pipeline",
  },
});
const carInfo = inject("carInfo") as Ref<CarInfo>;
const listData = ref<any[]>([]);
const containerRef = ref<HTMLElement | null>(null);
const currentPage = ref(1);
const loading = ref(false);
const hasMore = ref(true);
const scrollLock = ref(false);

const ITEM_HEIGHT = 200; // 每个列表项的高度
const PAGE_SIZE = 10; // 每页加载的数量

const fetchRedLightList = debounce(async (page: number = 1) => {
  if (loading.value) return;

  try {
    loading.value = true;
    const res = await getSmartOrderList({
      deviceId: carInfo.value.deviceId,
      page,
      limit: PAGE_SIZE,
      eventType: props.activeTab === "pipeline" ? [6] : [18],
    });
    listData.value = page === 1 ? res.list : [...listData.value, ...res.list];
    currentPage.value = page;
  } catch (error) {
    console.log("数据加载失败", error);
  } finally {
    loading.value = false;
  }
});

const handleScroll = debounce(() => {
  if (loading.value || scrollLock.value) return;

  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  if (!scrollbar) return;

  const { scrollTop, scrollHeight, clientHeight } = scrollbar;
  const distanceToBottom = scrollHeight - scrollTop - clientHeight;
  if (distanceToBottom < ITEM_HEIGHT && hasMore.value) {
    loadMore();
  }
}, 200);

const loadMore = async () => {
  if (loading.value || !hasMore.value) return;

  try {
    loading.value = true;
    scrollLock.value = true;

    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    if (!scrollbar) return;

    const { scrollTop, scrollHeight } = scrollbar;

    const res = await getSmartOrderList({
      deviceId: carInfo.value?.deviceId || "",
      page: currentPage.value + 1,
      limit: PAGE_SIZE,
      eventType: props.activeTab === "pipeline" ? [6] : [18],
    });

    const newList = res.list || [];
    if (newList.length > 0) {
      listData.value = [...listData.value, ...newList];
      currentPage.value++;
      hasMore.value = newList.length === PAGE_SIZE;

      // 等待DOM更新后恢复滚动位置
      await nextTick();
      if (scrollbar) {
        scrollbar.scrollTop = scrollTop;
      }
    } else {
      hasMore.value = false;
    }
  } catch (error) {
    console.error("加载更多数据失败:", error);
  } finally {
    loading.value = false;
    scrollLock.value = false;
  }
};

const resetList = () => {
  listData.value = [];
  currentPage.value = 1;
  hasMore.value = true;
  loading.value = false;
  scrollLock.value = false;
};

const load = () => {
  currentPage.value = 1;
  listData.value = [];
  fetchRedLightList(1);
};

onMounted(() => {
  load();
  nextTick(() => {
    const scrollbar = document.querySelector(".el-scrollbar__wrap");
    scrollbar?.addEventListener("scroll", handleScroll);
  });
});

onUnmounted(() => {
  const scrollbar = document.querySelector(".el-scrollbar__wrap");
  scrollbar?.removeEventListener("scroll", handleScroll);
});

watch(() => props.activeTab, load);

defineExpose({
  load,
});
</script>

<script lang="ts">
export default {
  name: "PipelineCollisionPage",
};
</script>

<style scoped lang="scss">
.pipeline_collision_page {
  width: 100%;
  height: 100%;
  padding: 10px;
  padding-bottom: 100px;
  overflow-y: auto;

  .loading-more,
  .no-more {
    text-align: center;
    padding: 10px 0;
    color: #999;
    font-size: 12px;
  }

  &-item {
    width: 100%;
    height: 200px;
    padding: 10px;
    background: #fff;
    border-radius: 10px;
    margin-bottom: 10px;
    .image_wrapper {
      position: relative;
      width: 100%;
      height: 140px;
      border-radius: 4px;
      .image_item {
        border-radius: 4px;
      }
      &_time {
        position: absolute;
        bottom: 5px;
        left: 5px;
        font-size: 10px;
        color: #ffffff;
      }
    }

    &-result {
      font-size: 12px;
      color: #666666;
      &-pass {
        display: inline-block;
        text-align: right;
        width: calc(100% - 85px);
      }
      &-location {
        color: #242859;
      }
      .label {
        display: inline-block;
        width: 85px;
        text-align: left;
      }
    }
  }

  .loading-container {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #909399;

    .el-icon {
      margin-right: 8px;
      font-size: 20px;
    }
  }
}
</style>
