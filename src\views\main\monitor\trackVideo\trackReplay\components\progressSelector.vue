<template>
  <section class="progress-select">
    <x-popover
      trigger="focus"
      :container="props.popupContainer"
      v-model:visible="config.popShow"
      placement="top"
      :triangle="false"
    >
      <div
        class="progress-select-selector"
        ref="progressSelectRef"
        @click="onClickTrigger"
      >
        <span class="selector-label">
          {{ label ? label : props.placeholder || $t("PSelect") }}
        </span>
      </div>
      <template #content>
        <div
          class="progress-select-list"
          :style="{ width: `${_width}px` }"
        >
          <div
            :class="[
              'list-item',
              {
                'list-item-check': props.value === item.label,
              },
            ]"
            v-for="(item, index) in options"
            :key="index"
            @click.stop="handleItemClick(item.value)"
          >
            {{ item.label }}
          </div>
        </div>
      </template>
    </x-popover>
  </section>
</template>
<script lang="ts" setup>
import { reactive, computed, ref, watch } from "vue";
import type { PropType } from "vue";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("trackReplay");

export type OptionsType = { value: string | number; label: string }[];
const props = defineProps({
  value: {
    type: [String, Number],
    required: true,
  },
  options: {
    type: Array as PropType<OptionsType>,
    required: true,
  },
  popupContainer: {
    type: HTMLElement,
  },
  placeholder: {
    type: String,
    default: "",
  },
});

const emits = defineEmits(["update:value"]);
const label = computed(() => {
  const _item = props.options.find((v) => v.value === props.value);
  return (_item && _item.label) || "";
});

const config = reactive({
  popShow: false,
});
const handleItemClick = (value: string | number) => {
  emits("update:value", value);
  config.popShow = false;
};
const progressSelectRef = ref<any>();
const _width = ref(0);
watch(
  () => config.popShow,
  (newV) => {
    if (newV) {
      _width.value = progressSelectRef.value.getBoundingClientRect().width;
    }
  }
);
/**
 * 触发器被点击
 */
const onClickTrigger = () => {
  // 显示气泡层
  if (config.popShow === false) {
    config.popShow = true;
  }
};
</script>

<style lang="scss" scoped>
.progress-select {
  &-search-selector {
    width: 100%;
  }
  &-selector {
    cursor: pointer;
    @include fj {
      align-items: center;
      text-align: center;
    }
    width: 100%;
    border-radius: 4px;
    background-color: #fff;
    border: 1px solid #5964fb;
    .selector-label {
      @include ell;
      width: 0;
      flex: 1 0 auto;
      @include sc(12px, #5964fb);
    }
  }
  &-list {
    padding: 0 4px;
    max-height: 256px;
    @include scrollbar(both, 4px) {
      overflow: auto;
    }
    border-radius: 4px;
    background-color: #fff;
    box-shadow: 0px 0px 20px 0px rgba(155, 162, 190, 0.2), 0px 4px 30px 0px rgba(0, 0, 0, 0.1);
    .list-item {
      margin: 8px 0;
      @include wh(100%, 18px) {
        line-height: 18px;
        text-align: center;
      }
      @include sc(12px, #555);
      border-radius: 4px;
      white-space: nowrap;
      cursor: pointer;
      &:hover {
        color: #5964fb;
        border: 1px solid #5964fb;
      }
    }
    .list-item-check {
      color: #5964fb;
      border: 1px solid #5964fb;
    }
  }
}
</style>
