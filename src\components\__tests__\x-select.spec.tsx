import { describe, it, expect, afterEach, vi, beforeEach } from "vitest";
import { mount } from '@vue/test-utils';
import { nextTick } from 'vue';
import XSelect from '@/components/x-select.vue';
import XInput from '@/components/x-input.vue';

beforeEach(() => {
  document.body.outerHTML = '';
});

const baseProps = {
  value: 'value-1',
  options: [
    {
      label: 'label-1',
      value: 'value-1',
    },
    {
      label: 'label-2',
      value: 'value-2'
    },
    {
      label: 'label-3',
      value: 'value-3'
    }
  ]
}

describe('Snapshot', () => {
  it('正确渲染 外部快照', () => {
    const wrapper = mount(XSelect,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
  it('正确渲染 内部快照', () => {
    mount(XSelect,{
      props: { ...baseProps },
    });
    expect(document.querySelector('.x-popover-container')).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = mount(XSelect,{
      props: {
        ...baseProps,
        showSearch: true,
        placeholder: 'placeholder',
        maxlength: 10,
        allowClear: true,
      },
    });
    expect(wrapper.find('.x-select-search-selector')).toBeDefined();
    const inputProps = wrapper.findComponent(XInput).props()
    expect(inputProps.placeholder).toBe('placeholder');
    expect(inputProps.maxlength).toBe(10);
    expect(inputProps.allowClear).toBe(true);
  });
  it('正确渲染 showPopSearch', () => {
    const wrapper = mount(XSelect,{
      props: {
        ...baseProps,
        showSearch: false,
        showPopSearch: true
      },
    });
    expect(wrapper.find('.x-select-normal-list')).toBeDefined();
  });
});

describe('Events', () => {
  it('验证筛选功能', () => {
    const wrapper = mount(XSelect,{
      props: {
        ...baseProps,
        value: '', // 为验证filterOption
        showSearch: true,
        filterOption: (value, options)=>{
          return options.slice(0,2)
        },
      },
    });
    // 验证filterOption筛选
    expect([...document.querySelectorAll('.list-item')].map(v=>v.innerHTML)).toMatchInlineSnapshot(`
      [
        "label-1",
        "label-2",
      ]
    `);
    // 验证showSearch筛选
    wrapper.setProps({ value: 1 });
    nextTick(()=>{
      expect([...document.querySelectorAll('.list-item')].map(v=>v.innerHTML)).toMatchInlineSnapshot(`
        [
          "label-<span style="color: rgb(89, 100, 251);" data-v-a2b71f8c="">1</span>",
        ]
      `);
    })
  });
  it('验证点击选择功能', () => {
    const wrapper = mount(XSelect,{
      props: {
        ...baseProps,
      },
    });
    document.querySelectorAll('.list-item')[2].click();
    expect(wrapper.emitted()['update:value'][0][0]).toBe('value-3');
  });
});
