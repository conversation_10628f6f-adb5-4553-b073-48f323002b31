<template>
  <Transition name="modal">
    <div
      v-show="_visible"
      class="modal"
    >
      <Transition name="modal-mask">
        <div
          v-show="_visible"
          class="modal-mask"
          @click="handleCancel(true)"
        ></div>
      </Transition>
      <Transition name="modal-content">
        <div
          v-show="_visible"
          class="modal-content"
          :style="contentStyle"
        >
          <div class="content-header">
            <div class="content-header-title">
              <x-icon
                class="title-icon"
                :name="`modal_${props.icon}`"
              />
              <span class="title-text">{{ props.title }}</span>
            </div>
            <span
              class="content-header-close"
              v-html="closeText"
              @click="handleCancel(true)"
            ></span>
          </div>
          <div class="content-body">
            <div class="content-body-slot">
              <props.content v-if="props.content" />
            </div>
            <div
              v-if="props.confirm || props.cancel"
              class="content-body-button"
            >
              <x-button
                type="white"
                :text="props.cancelText"
                @click="handleCancel(false)"
                style="margin-right: 9px"
              />
              <x-button
                :type="confirmType ?? 'blue'"
                :text="props.confirmText"
                @click="handleConfirm"
              />
            </div>
          </div>
        </div>
      </Transition>
    </div>
  </Transition>
</template>
<script lang="ts" setup>
import { ref, computed, onMounted } from "vue";
import type { PropType } from "vue";
import xIcon from "@/components/x-icon.vue";
import xButton from "@/components/x-button.vue";

const props = defineProps({
  container: {
    type: HTMLElement,
    required: true,
  },
  title: {
    type: String,
    required: true,
  },
  icon: {
    type: String as PropType<"warn" | "success" | "error">,
    default: "warn",
  },
  width: {
    type: String,
    default: "424px",
  },
  content: {
    type: Object,
    required: false,
  },
  confirm: {
    type: Function,
  },
  confirmText: {
    type: String,
    default: () => "确认",
  },
  confirmType: String as PropType<InstanceType<typeof xButton>["$props"]["type"]>,
  cancel: {
    type: Function,
  },
  /** 弹窗关闭事件 */
  close: {
    type: Function,
  },
  cancelText: {
    type: String,
    default: () => "取消",
  },
  closeBeforeConfirm: {
    type: Boolean,
    default: false,
  },
});
const contentStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width}`;
  return style;
});
const closeText = ref("&#10005");
const handleClose = () => {
  _visible.value = false;
  setTimeout(() => {
    document.body.removeChild(props.container);
  }, 300);
};
/** @param isClose 是否是点击遮罩层或者关闭icon */
const handleCancel = (isClose = false) => {
  // 即： 点击遮罩层或者关闭icon，并且props.close存在时触发， 否则props.cancel存在时触发
  if (isClose && props.close) {
    props.close();
  } else if (props.cancel) {
    props.cancel();
  }
  handleClose();
};
const handleConfirm = () => {
  if (props.confirm) {
    if (props.closeBeforeConfirm) {
      handleClose();
      props.confirm();
    } else {
      props.confirm();
      handleClose();
    }
  } else {
    handleClose();
  }
};
const _visible = ref(false);
onMounted(() => {
  _visible.value = true;
});
</script>

<style lang="scss" scoped>
.modal {
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: var(--z-index-modal);
  &-enter {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-leave {
    &-active {
      transition: all 0.3s ease;
    }
  }
  &-mask {
    @include wh(100%) {
      background-color: rgba(4, 6, 22, 0.5);
    }
    &-enter {
      &-from {
        opacity: 0;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        opacity: 0;
      }
    }
  }
  &-content {
    &-enter {
      &-from {
        transform: scale(0.2);
        transform-origin: left top;
      }
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
    }
    &-leave {
      &-active {
        transition: all 0.3s cubic-bezier(0.23, 1, 0.32, 1);
      }
      &-to {
        transform: scale(0.2);
        transform-origin: left top;
      }
    }
    display: flex;
    flex-direction: column;
    @include ct-p;
    padding: 32px 32px 24px 44px;
    background-color: #fff;
    box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2), 0px 4px 30px rgba(0, 0, 0, 0.1);
    border-radius: 4px;
    .content-header {
      @include fj {
        align-items: center;
      }
      @include wh(100%, 54px);
      @include sc(16px, #242859);
      &-close {
        cursor: pointer;
      }
      .title-icon {
        margin-right: 10px;
      }
      .title-text {
        font-weight: bold;
      }
    }
    .content-body {
      display: flex;
      flex-direction: column;
      width: 100%;
      flex: 1;
      &-slot {
        width: 100%;
        flex: 1;
        padding-left: 26px;
      }
      &-button {
        display: flex;
        justify-content: end;
        @include wh(100%, 32px) {
          margin-top: 18px;
        }
      }
    }
  }
}
</style>
