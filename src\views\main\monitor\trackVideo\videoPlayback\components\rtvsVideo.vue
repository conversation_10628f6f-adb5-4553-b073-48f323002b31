<template>
  <div
    ref="playerRef"
    :class="['rtvs-video', { 'rtvs-video-history': playerType === 'history' }]"
  />
</template>

<script lang="ts" setup>
import XMessage from "@/components/x-message";
import { defineComponent, nextTick, onMounted, onUnmounted, ref, watch, type PropType } from "vue";

const props = defineProps({
  /** 因改组件不能销毁（即不可使用`v-if`），否则无法播放,因此需要个变量判断是否显示, */
  visible: {
    type: Boolean,
    default: true,
  },
  /** 分屏数量 默认：4 ,仅支持 `1 | 2 | 4 | 6 | 8 | 9 | 10 | 13 | 16`*/
  playerCount: {
    type: Number,
    default: 4,
  },
  /** 历史回放的配置参数 */
  playbackOptions: {
    type: Object as PropType<_PlaybackVideoOptions>,
    default: () => ({}),
  },
  /** 实时播放的配置参数 */
  realtimeOptions: {
    type: Object as PropType<_RealtimeVideoOptions>,
    default: () => ({}),
  },
  /** 播放器类型(history:历史；realtime:实时) 默认：realtime(实时) */
  playerType: {
    type: String as PropType<"realtime" | "history">,
    default: "history",
  },
  /** 设备id */
  deviceId: {
    type: String,
    default: "",
  },
});

/** rtvs初始化配置 后期不可改 */
const videoPlayerOptions = {
  /** 集群管理地址 */
  clusterHost: "iot.chengshizhiguang.com",
  /** 集群管理端口 */
  clusterPort: "17000",
  /** 协议版本 0: 808-2013, 1: 808-2019, 2: GB28181 */
  protocol: 0,
  /** 是否使用cdn */
  isUseCdn: false,
  /** 初始化分屏数量 */
  MaxUcNum: 4,
  /** 超时警告时间 默认4.5分钟通知 */
  timeoutWarningMsec: 270000,
  /** 超时时间 默认5分钟 */
  timeoutCloseMsec: 300000,
};

/** RTVS 实例 */
let rtvs: any;
const playerRef = ref<HTMLElement>();
const playerFull = ref<boolean>(false);
const playerType = ref<"realtime" | "history">("history");
/** 当前日期: YYYY-MM-DD */
const nowDate = new Date().toLocaleDateString().split("/").join("-");

/** 初始化 */
const rtvsInit = () => {
  // @ts-ignore
  rtvs = CvNetVideo.Init(playerRef.value, props.playerCount, {
    ...videoPlayerOptions,
    callback: rtvsInitCallback,
    events: {
      /** 超时通知 仅通知，如需关闭需用户在此事件响应中自行调用关闭接口 */
      timeoutClose: () => {
        XMessage("error", "长时间无操作，已自动关闭！");
        stopVideo();
      },
      /** 超时警告 仅通知，如需关闭需用户在此事件响应中自行调用关闭接口 */
      timeoutWarning: () => {
        XMessage("error", "视频连接超时!!!");
        stopVideo();
      },
      /** 分屏双击回调， 返回值为 true表示取消双击放大 */
      onUcDbclick: () => true,
      /** 视频停止时事件 */
      onStop: onVideoStop,
      /** 设备开始传输视频事件 */
      onDevConnect: (id: number) => {
        console.log("%c 设备开始传输视频事件", "font-weight:bold;color: blue", id);
        const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
        playBtnDiv?.classList.remove("hide");
      },
      /** 设备断开传输视频事件 */
      onDevDisconnect: (id: number) => {
        console.log("%c 设备断开传输视频事件", "font-weight:bold;color: blue", id);
        stopVideo(id);
      },
      /** Websocket通道关闭事件 */
      onWsClose: (id: number) => {
        console.log("%c Websocket通道关闭事件", "font-weight:bold;color: blue", id);
        const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
        playBtnDiv?.classList.add("hide");
      },
      /** 服务端通知事件 */
      onServerNotice: (type: "onDevConnect" | "onDevDisconnect" | "onWsClose", id: number) => {
        console.log("%c 服务端通知事件", "font-weight:bold;color: blue", type, id, rtvs.IsPlaying(id));
        const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
        rtvs.IsPlaying(id) ? playBtnDiv?.classList.add("hide") : playBtnDiv?.classList.remove("hide");
      },
      /** 服务端结束 */
      onEndByServer: (cause: number, id: number) => {
        XMessage("error", `视频${id} 播放结束，原因：${cause}`);
        stopVideo(id);
      },
    },
  });
};
/** 初始化完成后回调 */
const rtvsInitCallback = () => {
  updateCustomControls();
  setRealOrBack(props.playerType);
  const videoDivList = playerRef.value?.querySelectorAll(".video-box-body");
  videoDivList?.forEach((el: Element & { index?: number }, index) => {
    if (index < props.playerCount) {
      // 修复点击进度条无法继续播放的问题
      const playbackBarBackground = el.querySelector(".playbackbarbackgroud");
      playbackBarBackground?.addEventListener("click", () => {
        const pauseBtn = el.querySelector(".playpausebtn") as HTMLDivElement;
        pauseBtn.click();
      });

      // 修复关闭全屏时产生的视图大小与原来的视图大小不一致的问题
      const fullScreenBtn = el.querySelector(".fullscreenbtn");
      fullScreenBtn?.addEventListener("click", () => {
        if (playerFull.value) windowResize();
        playerFull.value = !playerFull.value;
      });
    }
  });
};

/** 初始化/更新 自定义内容 */
const updateCustomControls = () => {
  // 隐藏右键菜单
  // const menuDiv = playerRef.value?.querySelector(".video-menu-new");
  // menuDiv?.classList.add("hide");

  // 针对每个窗口添加控件
  const videoDivList = playerRef.value?.querySelectorAll(".video-box-body");
  videoDivList?.forEach((el: Element & { index?: number }, index) => {
    if (index < props.playerCount) {
      // 对每个视频： 创建播放按钮或更新播放按钮
      let playBtnDiv = el.querySelector(".video-play-btn");
      if (playBtnDiv) {
        // 判断是否播放状态
        rtvs.IsPlaying(el.index) ? playBtnDiv.classList.add("hide") : playBtnDiv.classList.remove("hide");
      } else {
        // 显示可播放的视频窗口
        playBtnDiv = document.createElement("div");
        playBtnDiv.setAttribute("class", "video-play-btn");
        playBtnDiv.setAttribute("id", "video-play-btn-" + el.index);
        // 播放按钮事件
        playBtnDiv.addEventListener("click", () => {
          if (playerType.value === "realtime") {
            realtimeVideo(el.index);
          } else {
            // playbackVideo(el.index);
            const firstTimeDay = playerRef.value!.querySelector(`#time-day-${index}`);
            const firstTimeline = firstTimeDay?.querySelector(`.video-time-minute.active`);
            // 主动触发双击事件
            const event = new MouseEvent("dblclick", {
              bubbles: true,
              cancelable: true,
              view: window,
            });
            firstTimeline?.dispatchEvent(event);
            const playBtnDiv = playerRef.value!.querySelector("#video-play-btn-" + el.index);
            rtvs.IsPlaying(el.index) ? playBtnDiv?.classList.add("hide") : playBtnDiv?.classList.remove("hide");
          }
        });
        el.appendChild(playBtnDiv);
      }

      // 对每个视频：添加描述或更新描述
      let descSpan = el.querySelector(".video-desc") as HTMLSpanElement;
      const descStr = `视频${el.index} @${
        (playerType.value === "realtime" ? props.realtimeOptions.simNumber : props.playbackOptions.simNumber) || "未知"
      }  ${props.deviceId}`;
      if (descSpan) {
        descSpan.innerText = descStr;
      } else {
        descSpan = document.createElement("span");
        descSpan.setAttribute("class", "video-desc");
        descSpan.setAttribute("id", "video-desc-" + el.index);
        descSpan.innerText = descStr;
        el.appendChild(descSpan);
      }
    }
  });
};

/**
 * @desc 视频停止时事件
 * @param {Number} id 表示第几个分屏 从1开始 -1表示对讲通道
 */
const onVideoStop = (id: number) => {
  console.log("%c onVideoStop:", "color:blue;font-weight:bold;", `视频${id} 播放结束`);
  if (props.playerType === "realtime") {
    const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
    playBtnDiv?.classList.remove("hide");
  }
};

/**
 * @desc 停止指定视频 默认停止全部
 * @param {Number} id 表示第几个分屏 从1开始 -1表示全部
 */
const stopVideo = (id: number = -1) => {
  rtvs.Stop(id);
  if (id === -1) {
    const playBtnDivList = playerRef.value?.querySelectorAll(".video-play-btn");
    playBtnDivList?.forEach((el) => {
      window.getComputedStyle(el).display === "none" && el.classList.remove("hide");
    });
  } else {
    const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
    playBtnDiv?.classList.remove("hide");
  }
};

/** @desc 重新设置播放控件整体所占用大小 */
const resizePlayer = (width: number, height: number) => {
  rtvs.Resize(width, height);
};

/**
 * @desc 设置播放器类型
 * @param {String} type 播放器类型 history:历史；realtime:实时
 */
const setRealOrBack = (type: "realtime" | "history") => {
  if (!rtvs) return;
  if (type === "realtime") {
    rtvs.SetRealOrBack(true);
  } else {
    rtvs.SetRealOrBack(false);
  }
};

/** 设置分屏数量,默认支持`1 | 2 | 4 | 6 | 8 | 9 | 10 | 13 | 16`; 如果无效默认选择4分屏,可结合CustomScreens自定义分屏 */
const setLayoutByScreens = (num: number) => {
  if ([1, 2, 4, 6, 8, 9, 10, 13, 16].includes(num)) {
    rtvs.LayoutByScreens(num);
  } else {
    return XMessage("error", "仅支持1 , 2 , 4 , 6 , 8 , 9 , 10 , 13 , 16分屏");
  }
};

/**
 * @desc 设置音量
 * @param {Number} volume 音量 0-100
 * @param {Number} id 表示第几个分屏
 */
const setVolume = (volume: number, id: number = 1) => {
  rtvs.SetVolume(volume, id);
};

/**
 * @desc 实时播放
 * @param {Number} videoId 表示第几个分屏
 */
const realtimeVideo = (videoId: number = 0) => {
  if (!props.realtimeOptions.simNumber) return XMessage("error", "未找到视频信息");
  const playBtnDiv = playerRef.value!.querySelector("#video-play-btn-" + videoId);
  playBtnDiv?.classList.add("hide");
  const options: _RealtimeVideoOptions = {
    simNumber: undefined,
    channelId: videoId,
    streamType: 0,
    hasAudio: false,
    videoId,
    config: {},
    callback: undefined,
    playMode: 0,
    ...props.realtimeOptions,
  };
  rtvs.StartRealTimeVideo(
    options.simNumber,
    options.channelId,
    options.streamType,
    options.hasAudio,
    options.videoId,
    options.config,
    options.callback,
    options.playMode
  );
  setVolume(0, videoId);
};

/**
 * @desc 历史回放
 * @param {Number} videoId 表示第几个分屏
 */
const playbackVideo = async (videoId: number = 1) => {
  if (!props.playbackOptions.simNumber) return XMessage("error", "未找到视频信息");
  const playBtnDiv = playerRef.value!.querySelector("#video-play-btn-" + videoId);
  playBtnDiv!.classList.add("hide");
  const options: _PlaybackVideoOptions = {
    simNumber: undefined,
    channelId: videoId,
    mediaType: 0,
    streamType: 0,
    storageType: 1,
    playbackMode: 0,
    multiple: 1,
    startTime: nowDate + " 00:00:00",
    endTime: nowDate + " 23:59:59",
    videoId,
    config: {},
    callback: undefined,
    playMode: 0,
    dataSource: 0,
    alarm: 0,
    ...props.playbackOptions,
  };

  console.log("%c playbackVideo.options:", "color:blue;font-weight:bold;", options);
  rtvs.PlaybackVideo(
    options.simNumber,
    options.channelId,
    options.mediaType,
    options.streamType,
    options.storageType,
    options.playbackMode,
    options.multiple,
    options.startTime,
    options.endTime,
    options.videoId,
    options.config,
    options.callback,
    options.playMode,
    options.dataSource
  );
};
/** @desc 处理时间 "241010125959" => "2024-10-10 12:59:59" */
// eslint-disable-next-line no-unused-vars
const customFormatDatetime = (t: string) => {
  return `20${t.slice(0, 2)}-${t.slice(2, 4)}-${t.slice(4, 6)} ${t.slice(6, 8)}:${t.slice(8, 10)}:${t.slice(10, 12)}`;
};

/**
 * @desc 查询当日录像文件列表，并渲染可播放的时间段到页面
 * @param {Number} videoId 表示第几个分屏
 */
const setBackDateTime = (videoId: number = 1) => {
  if (!props.playbackOptions.simNumber) return XMessage("error", "未找到视频信息");
  const options: _SetBackDateTimeOptions = {
    simNumber: "",
    channelId: videoId,
    playbackDate: nowDate,
    videoId,
    alarm: 0,
    mediaType: 0,
    streamType: 0,
    storageType: 1,
    config: undefined,
    dataSource: 0,
    ...props.playbackOptions,
  };
  rtvs.SetBackDateTime(
    options.simNumber,
    options.channelId,
    options.playbackDate,
    options.videoId,
    options.alarm,
    options.mediaType,
    options.streamType,
    options.storageType,
    options.config,
    options.dataSource
  );
  videoIsLoading(videoId, (isWaiting, observer) => {
    if (!isWaiting) {
      //#region 自动播放
      // const firstTimeDay = playerRef.value!.querySelector(`#time-day-${videoId - 1}`);
      // const firstTimeline = firstTimeDay?.querySelector(`.video-time-minute.active`);
      // const event = new MouseEvent("dblclick", {
      //   bubbles: true,
      //   cancelable: true,
      //   view: window,
      // });
      // firstTimeline?.dispatchEvent(event);
      //#endregion
      const playBtnDiv = playerRef.value!.querySelector("#video-play-btn-" + videoId);
      playBtnDiv?.classList.remove("hide");
    }
  });
};

/** @desc 监听视频是否在加载状态 */
const videoIsLoading = (videoId: number = 1, callback: (isWaiting: boolean, observer: MutationObserver) => void) => {
  const waitingDiv = playerRef.value!.querySelector("#video-waiting-page-" + (videoId - 1));
  const observer = new MutationObserver((mutationsList) => {
    for (const mutation of mutationsList) {
      if (mutation.type === "attributes" && mutation.attributeName === "class") {
        const isWaiting = !waitingDiv?.className.includes("hide") || false;
        callback(isWaiting, observer);
        if (!isWaiting) observer.disconnect();
      }
    }
  });
  observer.observe(waitingDiv!, { attributes: true });
};

/** @desc 是否有视频正在播放中 (只要开启不管有无画面均认为在播放，手动暂停此状态还是true) */
const playerIsPlaying = () => {
  if (playerRef.value && rtvs) {
    let isPlaying = false;
    const videoDivList = playerRef.value.querySelectorAll(".video-box-body");
    videoDivList?.forEach((el: Element & { index?: number }, index) => {
      if (index < props.playerCount && rtvs.IsPlaying(el.index)) {
        isPlaying = true;
      }
    });
    return isPlaying;
  } else {
    return false;
  }
};

/**
 * @desc 查询历史录像文件列表
 * @param {Number} videoId 表示第几个分屏
 */
const queryVideoFileList = (videoId: number = 1) =>
  new Promise<_QueryVideoFileListData>((resolve, reject) => {
    const options: _QueryVideoFileListOptions = {
      simNumber: undefined,
      channelId: videoId,
      startTime: nowDate + " 00:00:00",
      endTime: nowDate + " 23:59:59",
      alarm: 0,
      mediaType: 0,
      streamType: 0,
      storageType: 1,
      callback: resolve,
      videoId,
      config: undefined,
      dataSource: 0,
      ...props.playbackOptions,
    };
    options.startTimeStamp = dateToUnixLong(options.startTime!);
    options.endTimeStamp = dateToUnixLong(options.endTime!);
    console.log("%c queryVideoFileList.options:", "color:blue;font-weight:bold;", options);
    rtvs.QueryVideoFileList(
      options.simNumber,
      options.channelId,
      options.startTimeStamp,
      options.endTimeStamp,
      options.alarm,
      options.mediaType,
      options.streamType,
      options.storageType,
      options.callback,
      options.videoId,
      options.config,
      options.dataSource
    );
  });
/**
 * @desc 处理时间为时间戳
 * @param {String} date 时间字符串 "YYYY-MM-DD HH:mm:ss"
 */
const dateToUnixLong = (date: string) => {
  const unixLong = Date.parse(date);
  if (isNaN(unixLong)) {
    return Date.parse(date.replace(/-/g, "/")) / 1000;
  } else {
    return unixLong / 1000;
  }
};

/** @desc 获取所有可播放的视频进度及渲染到播放器上 */
const queryAllPlayableVideoProgress = () => {
  nextTick(() => {
    if (playerRef.value && rtvs) {
      const videoDivList = playerRef.value.querySelectorAll(".video-box-body");
      videoDivList?.forEach((el: Element & { index?: number }, index) => {
        if (index < props.playerCount && el.index) {
          const playBtnDiv = el.querySelector("#video-play-btn-" + el.index);
          playBtnDiv?.classList.add("hide");
          setBackDateTime(el.index);
        }
      });
    }
  });
};

onMounted(async () => {
  rtvsInit();
  watch(() => props.playerCount, setLayoutByScreens);
  watch(() => props.playbackOptions.simNumber, updateCustomControls);
  watch(() => props.realtimeOptions.simNumber, updateCustomControls);
  watch(() => props.playerType, setRealOrBack);
});

/** @desc 首次加载无法获取到播放器高度，需要等窗口变化时重新设置播放控件整体所占用大小 */
watch(
  () => props.visible,
  (newV) => {
    if (newV) {
      setRealOrBack(props.playerType);
      windowResize();
    } else {
      // 清除可播放的视频进度
      const videoTimeMinuteList = playerRef.value?.querySelectorAll(".video-time-minute.active");
      videoTimeMinuteList?.length && videoTimeMinuteList.forEach((el) => el.classList.remove("active"));
    }
  }
);

/** 窗口变化时重新设置播放控件整体所占用大小 */
const windowResize = async () => {
  const videoContainer = playerRef.value!.querySelector(".video-container") as HTMLDivElement;
  videoContainer.style.height = "auto";
  nextTick(() => {
    const { width: divWidth, height: divHeight } = window.getComputedStyle(playerRef.value!);
    const width = parseInt(divWidth);
    const height = parseInt(divHeight);
    resizePlayer(width, height);
  });
};
window.addEventListener("resize", windowResize);
onUnmounted(() => {
  window.removeEventListener("resize", windowResize);
});
defineExpose({
  rtvs,
  realtimeVideo,
  playbackVideo,
  setLayoutByScreens,
  stopVideo,
  playerIsPlaying,
  resizePlayer,
  queryVideoFileList,
  queryAllPlayableVideoProgress,
});
</script>
<script lang="ts">
export default defineComponent({
  name: "rtvs-video",
});
</script>
<style lang="scss">
.rtvs-video {
  width: 100%;
  height: 100%;
  display: flex;
  background: #ffffff80;
  backdrop-filter: blur(4px);
  .video-container {
    background-color: transparent;
    .video-select,
    .video-box-body {
      border: none !important;
    }
    .video-box-body {
      .video-body {
        background: #51708a80;
      }
    }
    .video-play-btn {
      position: absolute;
      top: calc(50% - 22px);
      left: calc(50% - 22px);
      width: 44px;
      height: 44px;
      cursor: pointer;
      background-image: url("https://szjieya-mes.oss-cn-shenzhen.aliyuncs.com/mes/web/resource/jaya/video-monitor/monitor-play.png");
      background-repeat: round;
      background-size: cover;

      &:hover {
        background-image: url("https://szjieya-mes.oss-cn-shenzhen.aliyuncs.com/mes/web/resource/jaya/video-monitor/monitor-play-active.png");
      }
    }
  }
  .video-desc {
    position: absolute;
    bottom: 5px;
    right: 10px;
    font-size: 12px;
    color: #fff;
  }
  .control-btn {
    &.stretchbtn {
      display: none;
    }
    &.fullscreenbtn {
      left: calc(100% - 25px);
    }
  }
  .video-time-cursor-text {
    height: 16px;
    left: -50%;
    transform: translate(-50%, 0);
  }
  .hide {
    display: none;
  }
  &.rtvs-video-history .video-desc {
    bottom: 30px;
  }
}
</style>
