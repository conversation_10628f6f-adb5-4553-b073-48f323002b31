<template>
  <section class="work-show">
    <sub-title>作业概览 - 展示</sub-title>
    <div class="work-show-tip">近七天</div>
    <x-echart-bar-line
      style="height: 23vh"
      :xLabels="chartData.xLabels"
      :values="[chartData.area, chartData.duration]"
      :vehicleData="chartData.vehicle"
    />
  </section>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import subTitle from "./subTitle.vue";
import xEchartBarLine from "@/components/x-echart/x-echart-bar-line.vue";
import { getWorkView } from "@/services/api";

const props = defineProps({
  proId: {
    type: String,
    required: true,
  },
});

const chartData = reactive({
  xLabels: [] as any,
  area: [] as any,
  duration: [] as any,
  vehicle: [] as any,
});

const getWorkShowInfo = async () => {
  const { vehNoSumList, taskDurationSumList, cleanAreaSumList, timeList } =
    await getWorkView(props.proId);
  chartData.xLabels = timeList;
  chartData.duration = taskDurationSumList;
  chartData.area = cleanAreaSumList;
  chartData.vehicle = vehNoSumList;
};

watch(
  () => props.proId,
  (newV) => {
    if (newV) {
      getWorkShowInfo();
    }
  }
);
</script>

<style lang="scss" scoped>
.work-show {
  @include wh(100%);
  position: relative;
  &-tip {
    width: 100px;
    position: absolute;
    right: -3vh;
    top: 1vh;
    @include sc(1.4vh, rgb(207, 219, 250));
  }
}
</style>
