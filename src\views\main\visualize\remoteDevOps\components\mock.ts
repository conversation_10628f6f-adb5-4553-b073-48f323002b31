import { ref } from "vue";
// mock 当前车辆id，确保弹窗能弹出
const deviceId = ref("mock1");

let mockIndex = 0;
let mockTimer: number | null = null;

//
const mockTrafficLightList = [
  {
    device_id: "mock1",
    messageType: 6,
    alert: true,
    picUrl: "https://dummyimage.com/600x400/ff0000/fff&text=红灯",
    // 其他你需要的字段
  },
  {
    device_id: "mock2",
    messageType: 6,
    alert: true,
    picUrl: "https://dummyimage.com/600x400/00ff00/fff&text=绿灯",
  },
  {
    device_id: "mock3",
    messageType: 6,
    alert: true,
    picUrl: "https://dummyimage.com/600x400/ffff00/000&text=黄灯",
  },
];
// 启动 mock 定时器
export const startMock = (
  trafficLightData: Ref<any>,
  iamgeUrl: Ref<string>,
  openWith: Ref<string>,
  trafficLightCheckDialogVisible: Ref<boolean>,
  isShowPreview: Ref<boolean>
) => {
  // 启动 mock 定时器
  mockTimer = window.setInterval(() => {
    const data = mockTrafficLightList[mockIndex % mockTrafficLightList.length];
    // 切换 deviceId 以模拟不同车辆
    deviceId.value = data.device_id;
    // 触发弹窗
    trafficLightData.value = data;
    iamgeUrl.value = data.picUrl;
    openWith.value = "auto";
    trafficLightCheckDialogVisible.value = true;
    isShowPreview.value = true;
    mockIndex++;
  }, 3000);
};
