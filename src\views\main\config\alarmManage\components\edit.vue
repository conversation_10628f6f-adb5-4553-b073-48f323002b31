<template>
  <x-drawer
    :title="$t('editAlarm')"
    :visible="props.show"
    :btnOption="{ position: 'center' }"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    @update:visible="updateVisible"
    @confirm="formSubmit"
    @cancel="formRef.resetFields()"
    width="880px"
  >
    <div class="content" ref="alarmEditRef">
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        class="content-form"
      >
        <div class="content-form-row">
          <x-form-item
            :label="$t('alarmName')"
            name="warnName"
            labelFlex="85px"
          >
            <x-input
              v-model:value="form.warnName"
              :placeholder="$t('PEnterAlarmName')"
              :maxlength="20"
              style="width: 240px"
            />
          </x-form-item>
          <x-form-item
            :label="'客户账号展示并接受'"
            name="isReceived"
            labelFlex="150px"
          >
            <x-switch
              :checkedValue="1"
              :unCheckedValue="0"
              :checked="Boolean(form.isReceived)"
              @change="form.isReceived = $event"
            >
              <template #checkedChildren>
                <x-icon name="switch_checked"></x-icon>
              </template>
              <template #unCheckedChildren>
                <x-icon name="switch_unChecked"></x-icon>
              </template>
            </x-switch>
          </x-form-item>
        </div>
        <div class="content-form-row">
          <x-form-item
            :label="$t('vehicleType')"
            name="vehicleType"
            labelFlex="85px"
          >
            <x-select
              v-model:value="form.vehicleType"
              :options="vehicleType"
              :popupContainer="alarmEditRef"
              :placeholder="$t('PSelect') + $t('vehicleType')"
              style="width: 240px"
            />
          </x-form-item>
          <x-form-item
            :label="$t('alarmModule')"
            name="warnModule"
            labelFlex="110px"
          >
            <x-select
              v-model:value="form.warnModule"
              :options="formOptions.warnModule"
              :popupContainer="alarmEditRef"
              :placeholder="$t('PSelect') + $t('alarmModule')"
              style="width: 240px"
            />
          </x-form-item>
        </div>
        <div class="content-form-row">
          <x-form-item
            :label="$t('alarmLevel')"
            name="warnLevel"
            labelFlex="85px"
          >
            <x-select
              v-model:value="form.warnLevel"
              :options="warnLevelType"
              :popupContainer="alarmEditRef"
              :placeholder="$t('PSelect') + $t('alarmLevel')"
              style="width: 240px"
            />
          </x-form-item>
          <x-form-item
            :label="$t('platformGlobalPopup')"
            name="popup"
            labelFlex="110px"
          >
            <x-select
              v-model:value="form.popup"
              :options="globalModalType"
              :popupContainer="alarmEditRef"
              :placeholder="$t('PSelect') + $t('popupOrNot')"
              style="width: 180px"
            />
          </x-form-item>
        </div>
        <div class="content-form-row">
          <x-form-item
            :label="$t('interfaceDocking')"
            name="interfaceDescription"
            labelFlex="85px"
          >
            <x-select
              v-model:value="form.interfaceDescription"
              :options="formOptions.interfaceDescription"
              :popupContainer="alarmEditRef"
              :placeholder="$t('PSelect') + $t('interface')"
              style="width: 240px"
            />
          </x-form-item>
        </div>
        <div class="content-form-row">
          <x-form-item
            :label="$t('alarmDescription')"
            name="description"
            labelFlex="85px"
          >
            <x-textarea
              v-model:value="form.description"
              :auto-size="{ minRows: 4, maxRows: 6 }"
              :maxlength="200"
              :placeholder="$t('PEnter')"
              style="width: 340px"
            />
          </x-form-item>
        </div>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import {
  getWarnConfigDetail,
  editWarnConfig,
  getWarnModuleAndName,
  getConfigList,
} from "@/services/api";
import {
  warnLevelType,
  globalModalType,
  vehicleType,
} from "@/assets/ts/config";
import type { WarnConfigDetailResponse } from "@/services/type";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xSelect from "@/components/x-select.vue";
import xTextarea from "@/components/x-textarea.vue";
import xSwitch from "@/components/x-switch.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmManage");

/**
 * 组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

/**
 * 引用
 */
const formRef = ref<any>();
const alarmEditRef = ref<any>();

/**
 * 组件内允许发出的事件
 */
const emits = defineEmits(["confirm", "update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
  if (bool === false) {
    formRef.value && formRef.value.resetFields();
  }
};

/**
 * 表单项
 */
const form = reactive({
  id: "",
  warnName: "",
  isReceived: 1,
  vehicleType: 0,
  warnModule: "",
  warnLevel: 0,
  popup: 0,
  interfaceDescription: "",
  description: "",
});

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  detail: {} as WarnConfigDetailResponse,
  warnModule: [] as any[],
  interfaceDescription: [] as any[],
});

/**
 * 表单校验规则
 */
const formRules = reactive({
  warnName: [["required", $t("PEnterAlarmName")]],
  isReceived: [["required", $t("PSelect") + $t("platformReceive")]],
  vehicleType: [["required", $t("PSelect") + $t("vehicleType")]],
  warnModule: [["required", $t("PSelect") + $t("alarmModule")]],
  warnLevel: [["required", $t("PSelect") + $t("alarmLevel")]],
  popup: [["required", $t("PSelect") + $t("platformGlobalPopup")]],
  interfaceDescription: [["required", $t("PSelect") + $t("interface")]],
});

/**
 * 表单提交
 */
const formSubmit = async () => {
  if (formRef.value.validate()) {
    await editWarnConfig(form);
    Message("success", $t("modifyAlarmConfigSuccess"));
    emits("update:show", false);
    emits("confirm");
    formRef.value.resetFields();
  }
};

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      // 告警模块
      const warnModuleList = await getWarnModuleAndName({
        warnModule: "all",
        warnName: "",
      });
      formOptions.warnModule =
        warnModuleList?.map((item: any) => ({
          value: item,
          label: item,
        })) || [];
      // 表单回显
      formOptions.detail = await getWarnConfigDetail({ id: props.id });
      const {
        warnName,
        isReceived,
        vehicleType,
        warnModule,
        warnLevel,
        popup,
        interfaceDescription,
        description,
      } = formOptions.detail;
      form.id = props.id;
      form.warnName = warnName;
      form.isReceived = isReceived;
      form.vehicleType = vehicleType;
      form.warnModule = warnModule;
      form.warnLevel = warnLevel;
      form.popup = popup;
      form.interfaceDescription = interfaceDescription;
      form.description = description || "";
    }
  }
);

(async () => {
  // 接口描述
  const { operConfigList } = await getConfigList({
    configList: ["warn_config"],
  });
  formOptions.interfaceDescription = operConfigList.map((item) => ({
    value: item.k,
    label: `${item.k}(${item.val})`,
  }));
})();
</script>

<style lang="scss" scoped>
.content {
  &-form {
    padding: 20px 20px 0px 20px;
    margin-right: 20px;
    border-radius: 8px;
    background: #fff;
    &-row {
      display: flex;
      flex: 1;
    }
  }
}
</style>
