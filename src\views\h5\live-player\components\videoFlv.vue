<template>
  <section
    class="video-flv"
    ref="videoContainerRef"
  >
    <video
      ref="videoElementRef"
      muted="false"
      @click="handleFullScreen"
    >
      你的浏览器太旧了，不支持HTML5视频。
    </video>
    <div
      class="video-poster"
      v-show="!controls.isPlaying"
      @click="playHandle"
    >
      <div class="video-poster-center">
        <img
          src="@/assets/images/video_camera.png"
          alt=""
        />
        <!-- <span>请点击播放视频</span> -->
      </div>
    </div>
    <!-- <div class="video-flv-desc">{{ props.position }}</div> -->
    <div class="video-flv-controls">
      <div class="video-flv-controls-left">
        <div
          class="video-flv-controls-play-btn controls-btn"
          ref="playBtnRef"
          @click="playHandle"
        >
          <XIcon
            :name="controls.isPlaying ? 'video_pause' : 'video_play'"
            width="28"
            height="28"
          />
        </div>
      </div>
      <div class="video-flv-controls-right">
        <div
          class="video-flv-controls-full-screen-btn controls-btn"
          @click="handleFullScreen"
        >
          <el-icon
            color="#fff"
            size="20"
          >
            <FullScreen />
          </el-icon>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { onMounted, onUnmounted, ref, reactive, watch } from "vue";
import flvjs from "flv.js";
// import flvjs from "flv-h265.js";
import { debounce, i18nSimpleKey } from "@/assets/ts/utils";
import XIcon from "@/components/x-icon.vue";
import { FullScreen } from "@element-plus/icons-vue";
const $t = i18nSimpleKey("carLive");

export interface videoInfoType {
  url: string;
  name: string;
  position: string;
}

const props = withDefaults(defineProps<videoInfoType>(), {});
const videoContainerRef = ref();
const videoElementRef = ref();
const playBtnRef = ref();
const flvPlayer: any = ref();
const controls = reactive({
  isPlaying: false,
});
onMounted(() => {
  initFlv();
});
onUnmounted(() => {
  destroy();
});
watch(
  () => props.url,
  () => {
    destroy();
    initFlv();
  }
);

// 实时降低延迟
const progressHandle = () => {
  const { buffered, currentTime } = flvPlayer.value;
  if (buffered.length > 0) {
    const end = buffered.end(0); // 获取当前buffered值(缓冲区末尾)
    const delta = end - currentTime; // 获取buffered与当前播放位置的差值
    // 跳帧 (延迟过大)
    if (delta > 10 || delta < 0) {
      flvPlayer.value.currentTime = end - 1;
      return;
    }
    // 追帧 (延迟较低)
    if (delta > 1) {
      videoElementRef.value.playbackRate = 1.1;
    } else {
      videoElementRef.value.playbackRate = 1;
    }
  }
};
// 更新到最新帧
const updateVideo = () => {
  const { buffered } = flvPlayer.value;
  if (buffered.length > 0) {
    let end = flvPlayer.value.buffered.end(0) - 1;
    flvPlayer.value.currentTime = end;
  }
};

const initFlv = () => {
  // https://github.com/Bilibili/flv.js/blob/HEAD/docs/api.md
  // https://zhuguibiao.github.io/flv.js/assets/264.flv
  // https://zhuguibiao.github.io/flv.js/assets/265.flv
  if (flvjs.isSupported()) {
    flvPlayer.value = flvjs.createPlayer(
      {
        type: "flv",
        isLive: true,
        hasAudio: false,
        cors: true,
        url: props.url,
      },
      {
        enableWorker: false, // 启用Worker多线程运行flv.js提升解析速度
        enableStashBuffer: false, // 是否启用IO隐藏缓冲区
        stashInitialSize: 128, // 减少首帧显示等待时长
        lazyLoad: false, // 如果有足够的数据可供播放，则中止 http 连接
        lazyLoadMaxDuration: 0, // 60 指示数据要保留多少秒lazyLoad
        lazyLoadRecoverDuration: 0, // 10 指示lazyLoad恢复时间边界（以秒为单位）
      }
    );
    flvPlayer.value.attachMediaElement(videoElementRef.value);

    videoElementRef.value.addEventListener("progress", progressHandle);
    videoElementRef.value.addEventListener("play", updateVideo);
    window.addEventListener("focus", updateVideo);

    // 错误
    flvPlayer.value.on(flvjs.Events.ERROR, (errorType: any, errorDetail: any, errorInfo: any) => {
      console.log("类型:" + JSON.stringify(errorType), "报错内容" + errorDetail, "报错信息" + errorInfo);
    });
    // 播放统计信息
    // flvPlayer.value.on(
    //   flvjs.Events.STATISTICS_INFO,
    //   (errorType: any, errorDetail: any, errorInfo: any) => {
    //     console.log(
    //       "类型:" + JSON.stringify(errorType),
    //       "报错内容" + errorDetail,
    //       "报错信息" + errorInfo
    //     );
    //   }
    // );

    playHandle().then(pause).then(playHandle);
  }
};

const playHandle = debounce(() => {
  if (controls.isPlaying) {
    pause();
  } else {
    pause();
    flvPlayer.value.load();
    return flvPlayer.value.play().then(() => {
      controls.isPlaying = true;
    });
  }
});

const pause = () => {
  flvPlayer.value.pause();
  flvPlayer.value.unload();
  controls.isPlaying = false;
};

const destroy = () => {
  if (flvPlayer.value) {
    pause();
    flvPlayer.value.detachMediaElement();
    flvPlayer.value.destroy();
    flvPlayer.value = null;
    videoElementRef.value?.removeEventListener("progress", progressHandle);
    videoElementRef.value?.removeEventListener("play", updateVideo);
    window.removeEventListener("focus", updateVideo);
  }
};

const handleFullScreen = () => {
  if (!document.fullscreenElement) {
    // 判断不同浏览器的全屏方法
    if (videoElementRef.value.requestFullscreen) {
      videoElementRef.value.requestFullscreen();
    } else if (videoElementRef.value.mozRequestFullScreen) {
      /* Firefox */
      videoElementRef.value.mozRequestFullScreen();
    } else if (videoElementRef.value.webkitRequestFullscreen) {
      /* Chrome, Safari 和 Opera */
      videoElementRef.value.webkitRequestFullscreen();
    } else if (videoElementRef.value.msRequestFullscreen) {
      /* IE/Edge */
      videoElementRef.value.msRequestFullscreen();
    }
  } else {
    const _document = document as any;
    if (_document.exitFullscreen) {
      _document.exitFullscreen();
    } else if (_document.mozCancelFullScreen) {
      /* Firefox */
      _document.mozCancelFullScreen();
    } else if (_document.webkitExitFullscreen) {
      /* Chrome, Safari 和 Opera */
      _document.webkitExitFullScreen();
    } else if (_document.msExitFullscreen) {
      /* IE/Edge */
      _document.msExitFullscreen();
    }
  }
};
</script>

<style lang="scss" scoped>
.video-flv {
  position: relative;
  @include wh(100%);
  img,
  video {
    cursor: pointer;
    @include wh(100%);
    object-fit: fill;
  }
  .video-poster {
    cursor: pointer;
    position: absolute;
    left: 0;
    top: 0;
    @include bis("@/assets/images/video_poster.png");
    @include wh(100%);
    &-center {
      user-select: none;
      @include ct-p;
      @include fj {
        flex-direction: column;
        align-items: center;
      }
      height: 84px;
      img {
        @include wh(48px, 52px);
      }
      span {
        color: #fff;
      }
    }
  }
  .video-flv-desc {
    position: absolute;
    right: 10px;
    top: 10px;
  }
  .video-flv-controls {
    position: absolute;
    bottom: 4px;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 35px;
    padding: 0 5px;
    background-color: rgba(0, 0, 0, 0.1);
    .video-flv-controls-left,
    .video-flv-controls-right {
      display: flex;
      align-items: center;
    }
    .controls-btn {
      width: 35px;
      height: 35px;
      display: flex;
      align-items: center;
      justify-content: center;
    }
  }
}
</style>
