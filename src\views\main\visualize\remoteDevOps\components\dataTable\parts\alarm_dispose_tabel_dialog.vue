<template>
  <el-dialog
    :model-value="modelValue"
    @update:model-value="emit('update:modelValue', $event)"
    title="告警处置"
    append-to-body
    width="75%"
    @close="closed"
  >
    <div class="alarm_dispose_tabel">
      <div class="form-wrapper">
        <el-form
          :model="formData"
          inline
        >
          <div class="form-wrapper-row">
            <el-form-item>
              <el-select
                v-model="formData.deviceId"
                placeholder="请输入车牌号"
                clearable
                filterable
                style="width: 240px"
              >
                <el-option
                  v-for="(item, index) in selectOpts"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-select
                v-model="formData.vin"
                placeholder="请输入VIN"
                clearable
                filterable
                style="width: 240px"
              >
                <el-option
                  v-for="(item, index) in vinList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="告警等级">
              <el-select
                v-model="formData.warnLevel"
                placeholder="告警等级"
                clearable
                style="width: 112px"
              >
                <el-option
                  label="普通告警"
                  value="0"
                ></el-option>
                <el-option
                  label="轻微告警"
                  value="1"
                ></el-option>
                <el-option
                  label="紧急告警"
                  value="2"
                ></el-option>
                <el-option
                  label="致命告警"
                  value="3"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="状态">
              <el-select
                v-model="formData.isEnd"
                placeholder="状态"
                clearable
                style="width: 112px"
              >
                <el-option
                  label="进行中"
                  :value="0"
                ></el-option>
                <el-option
                  label="已完成"
                  :value="1"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="处置">
              <el-select
                v-model="formData.isSolved"
                placeholder="处置"
                clearable
                style="width: 112px"
              >
                <el-option
                  label="未处置"
                  :value="0"
                ></el-option>
                <el-option
                  label="已处置"
                  :value="1"
                ></el-option>
              </el-select>
            </el-form-item>
          </div>
          <div
            class="form-wrapper-row"
            ref="alarmDisposeTabelRef"
          >
            <el-form-item>
              <el-select
                v-model="formData.faultName"
                placeholder="告警名称"
                clearable
                filterable
                :filter-method="filterMethod"
                style="width: 112px"
              >
                <el-option
                  v-for="(item, index) in filteredList"
                  :key="index"
                  :label="item.label"
                  :value="item.value"
                ></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="告警时间">
              <el-date-picker
                v-model="formData.dateRange"
                type="daterange"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                style="width: 100%"
                value-format="YYYY-MM-DD"
              />
            </el-form-item>

            <div class="form-wrapper-row-button">
              <x-button
                text="查询"
                @click="load"
              />
              <x-button
                type="green"
                text="重置"
                @click="reset"
              />
              <x-button
                type="paleBlue"
                text="一键处置"
                @click="handleBatchDispose"
              />
            </div>
          </div>
        </el-form>
      </div>
      <div
        class="table-wrapper"
        v-loading="table.loading"
      >
        <el-table
          :data="table.dataSource"
          :loading="table.loading"
          @selection-change="handleSelectionChange"
          @sort-change="tableChange"
          style="width: 100%"
          max-height="450px"
          class="custom-table"
        >
          <el-table-column
            type="selection"
            width="55"
            :selectable="(row) => row.isEnd !== 0 && row.isSolved === 0"
          />
          <el-table-column
            v-for="col in table.cols"
            :key="col.key"
            :prop="col.key"
            :label="col.title"
            :sortable="col.sortable"
            :width="col.width"
            :fixed="col.fixed"
          >
            <template
              #default="{ row }"
              v-if="col.key === 'warnLevel'"
            >
              <span :class="`alarm-level-${row.warnLevel}`">{{ showwarnLevel(row.warnLevel) }}</span>
            </template>
            <template
              #default="{ row }"
              v-else-if="col.key === 'isEnd'"
            >
              <div :class="`isEnd-${row.isEnd}`">{{ showisEnd(row.isEnd) }}</div>
            </template>
            <template
              #default="{ row }"
              v-else-if="col.key === 'isSolved'"
            >
              <div :class="`isSolved-${row.isSolved}`">{{ showisSolved(row.isSolved) }}</div>
            </template>
            <template
              #default="{ row }"
              v-else-if="col.key === 'opera'"
            >
              <div class="opera-wrapper">
                <el-button
                  v-if="row.isSolved === 0"
                  type="primary"
                  link
                  @click.stop="handleDispose(row)"
                >
                  处置
                </el-button>
                <el-button
                  v-else
                  type="primary"
                  link
                  @click.stop="handleCheck(row)"
                >
                  查看
                </el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <el-pagination
          v-model:current-page="table.pagination.current"
          v-model:page-size="table.pagination.pageSize"
          :total="table.pagination.total"
          :page-sizes="[10, 20, 30, 50]"
          layout="total, pager, sizes, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          class="pagination-wrapper"
          background
        />
      </div>
    </div>
    <BatchDisposeDialog
      v-model:modelValue="batchDisposeDialogVisible"
      :itemList="selectedKeys"
      @refresh="load"
    />
    <AlarmDisposeDialog
      v-model:modelValue="alarmDisposeDialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
    <AlarmCheckDialog
      v-model:modelValue="alarmCheckDialogVisible"
      :itemData="itemData"
      @refresh="load"
    />
  </el-dialog>
</template>

<script setup lang="ts">
import type { PageSizeType } from "@/components/types";
import { getVehAndVinList, getVehicleAlarmList, getWarnNameList } from "@/services/api";
import { ref, reactive, type PropType, watch, onMounted } from "vue";
import BatchDisposeDialog from "./Batch_dispose_dialog.vue";
import AlarmDisposeDialog from "./alarm_dispose_dialog.vue";
import AlarmCheckDialog from "./alarm_check_dialog.vue";
import { ElMessage } from "element-plus";
import { debounce } from "@/assets/ts/utils";
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false,
  },
  itemData: {
    type: Object,
    default: () => {},
  },
  type: {
    type: String as PropType<"all" | "single">,
    default: "all",
  },
  deviceId: {
    type: String,
    default: "",
  },
});
// 用于存储过滤后的结果
const filteredList = ref<any[]>([]);
const batchDisposeDialogVisible = ref(false);
const alarmDisposeDialogVisible = ref(false);
const alarmCheckDialogVisible = ref(false);
const warnNameList = ref<{ label: string; value: string }[]>([]);
const itemData = ref();
const selectOpts = ref<{ label: string; value: string }[]>([]);
const formData = reactive({
  deviceId: "",
  vin: "",
  warnLevel: "",
  isEnd: "",
  isSolved: 0,
  faultName: "",
  dateRange: [],
});
const vinList = ref<{ label: string; value: string }[]>([]);
const alarmDisposeTabelRef = ref();
const emit = defineEmits(["update:modelValue"]);
const table = reactive({
  cols: [
    {
      key: "deviceId",
      title: "车牌号",
      width: "120px",
    },
    {
      key: "vin",
      title: "VIN",
      width: "180px",
    },
    {
      key: "faultContent",
      title: "告警名称",
      width: "200px",
    },
    {
      key: "faultCode",
      title: "故障码",
      width: "100px",
    },
    {
      key: "warnLevel",
      title: "告警等级",
      width: "100px",
      slots: "warnLevel",
    },
    {
      key: "isEnd",
      title: "状态",
      width: "100px",
      slots: "isEnd",
    },
    {
      key: "isSolved",
      title: "处置",
      width: "80px",
      slots: "isSolved",
    },
    {
      key: "createdTime",
      title: "告警开始时间",
      width: "180px",
    },
    {
      key: "endTime",
      title: "告警结束时间",
      width: "180px",
    },
    {
      key: "opera",
      title: "操作",
      slots: "opera",
      fixed: "right",
    },
  ] as any[],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
const showisEnd = (isEnd: number) => {
  return isEnd === 0 ? "进行中" : "已完成";
};
const showisSolved = (isSolved: number) => {
  return isSolved === 0 ? "未处置" : "已处置";
};
const showwarnLevel = (warnLevel: number) => {
  if (warnLevel === 0) {
    return "普通";
  } else if (warnLevel === 1) {
    return "轻微";
  } else if (warnLevel === 2) {
    return "紧急";
  } else if (warnLevel === 3) {
    return "致命";
  } else {
    return "-";
  }
};

const closed = () => {
  emit("update:modelValue", false);
  reset();
};

const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  load();
};

const reset = () => {
  formData.deviceId = "";
  formData.vin = "";
  formData.warnLevel = "";
  formData.isEnd = "";
  formData.isSolved = "";
  formData.faultName = "";
  formData.dateRange = [];
  table.pagination.current = 1;
  table.pagination.pageSize = 10;
  load();
};

const handleBatchDispose = () => {
  if (selectedKeys.value.length === 0) {
    ElMessage.warning("请选择要处置的告警");
    return;
  }
  batchDisposeDialogVisible.value = true;
};

const load = async () => {
  table.loading = true;
  let data;
  if (props.type === "all") {
    data = {
      warnLevelList: [2, 3],
      isEnd: 0,
      mode: "1",
    };
  } else {
    data = {
      warnLevelList: [0, 1, 2, 3],
      isEnd: 0,
      mode: "1",
    };
  }

  const params: any = {
    ...data,
    page: table.pagination.current,
    limit: table.pagination.pageSize,
    ...formData,
    // warnDateStart: formData.dateRange[0] ? `${formData.dateRange[0]} 00:00:00` : "",
    // warnDateEnd: formData.dateRange[1] ? `${formData.dateRange[1]} 23:59:59` : "",
    dateRange: undefined,
    warnLevelList: formData.warnLevel ? [formData.warnLevel] : data.warnLevelList,
  };

  try {
    const res = await getVehicleAlarmList(params);
    if (res) {
      table.dataSource = res.list || [];
      table.pagination.total = Number(res.totalCount) || 0;
      table.pagination.current = Number(res.currPage) || 1;
      table.pagination.pageSize = Number(res.pageSize) || 10;
    } else {
      table.dataSource = [];
      table.pagination.total = 0;
      table.pagination.current = 1;
      table.pagination.pageSize = 10;
    }
  } catch (err) {
    table.dataSource = [];
    table.pagination.total = 0;
    table.pagination.current = 1;
    table.pagination.pageSize = 10;
  } finally {
    table.loading = false;
  }
};

const handleCheck = (row: any) => {
  alarmCheckDialogVisible.value = true;
  itemData.value = row;
};
const handleDispose = (row: any) => {
  alarmDisposeDialogVisible.value = true;
  itemData.value = row;
};

// 选中的行键
const selectedKeys = ref<number[]>([]);

// 处理选择变化
const handleSelectionChange = (keys: number[], rows: any[]) => {
  selectedKeys.value = Array.isArray(keys) ? keys : [];
};

// 防抖的过滤方法
const debouncedFilter = debounce((query: string, list: any[]) => {
  if (!query) {
    return list;
  }
  const searchText = query.toLowerCase();
  return list.filter((item) => item.label.toLowerCase().includes(searchText));
}, 300); // 300ms 的防抖时间

const filterMethod = async (query: string) => {
  if (!query) {
    filteredList.value = warnNameList.value;
    return filteredList.value;
  }

  // 使用防抖函数进行过滤
  const result = await debouncedFilter(query, warnNameList.value);
  filteredList.value = result;
  return filteredList.value;
};

const fetchAlarmNameList = async () => {
  const res = await getWarnNameList({
    remark1: "",
    warnModule: "",
    warnName: "all",
  });

  const formatModuleList =
    res?.map((item: any) => ({
      value: item.replace(/\t|\n/g, ""),
      label: item.replace(/\t|\n/g, ""),
    })) || [];
  warnNameList.value = formatModuleList;
  console.log(warnNameList.value, "warnNameList");
};
const getCarList = async () => {
  const res = await getVehAndVinList();
  selectOpts.value = res.map((v) => ({
    label: v.vehicleNo,
    value: v.vehicleNo,
  }));
  vinList.value = res.map((v) => ({
    label: v.vin,
    value: v.vehicleNo,
  }));
};

watch(
  () => props.modelValue,
  (newVal) => {
    if (newVal) {
      if (props.type === "single") {
        formData.deviceId = props.deviceId;
      }
      load();
      fetchAlarmNameList();
      getCarList();
    }
  }
);

const handleSizeChange = (val: PageSizeType) => {
  table.pagination.pageSize = val;
  load();
};

const handleCurrentChange = (val: number) => {
  table.pagination.current = val;
  load();
};

// 在组件挂载时初始化过滤列表
onMounted(() => {
  filteredList.value = warnNameList.value;
});

defineExpose({
  load,
});
</script>

<style scoped lang="scss">
.alarm_dispose_tabel {
  padding: 25px 27px;
  display: flex;
  flex-direction: column;
  height: 100%;

  .form-wrapper-row {
    display: flex;
    text-align: center;
    &-button {
      width: 54%;
      display: flex;
      gap: 10px;
      justify-content: flex-end;
    }
  }

  .table-wrapper {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-height: 0;

    :deep(.custom-table) {
      .el-table__body tr:hover > td {
        background-color: #ecf2ff !important;
      }
    }

    .pagination-wrapper {
      margin-top: 16px;
      display: flex;
      justify-content: flex-end;

      :deep(.el-pagination) {
        .btn-prev,
        .btn-next,
        .el-pager li {
          background-color: #fff;
          border: 1px solid #dcdfe6;
          color: #606266;

          &:hover {
            color: #409eff;
          }

          &.is-active {
            background-color: #409eff;
            color: #fff;
            border-color: #409eff;
          }
        }

        .el-pager li:not(.is-active) {
          background-color: #fff;
          color: #606266;
          border: 1px solid #dcdfe6;
        }
      }
    }
  }

  .opera-wrapper {
    display: flex;
    gap: 10px;
  }
  .alarm-level-0 {
    color: #28e09f;
  }
  .alarm-level-1 {
    color: #faa565;
  }
  .alarm-level-2 {
    color: #f85959;
  }
  .alarm-level-3 {
    color: #962525;
  }
  .isEnd-0 {
    border-radius: 14px;
    background: rgba(89, 100, 251, 0.06);
    padding: 3px 12px;
    color: #5964fb;
  }
  .isEnd-1 {
    width: 100%;
    border-radius: 14px;
    background: rgba(40, 224, 159, 0.06);
    padding: 3px 12px;
    color: #28e09f;
  }
  .isSolved-0 {
    color: #555555;
  }
  .isSolved-1 {
    color: #999999;
  }
  :deep(.el-pagination > .is-first) {
    margin-right: 10px;
  }
}
</style>
