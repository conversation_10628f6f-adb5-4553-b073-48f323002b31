<script lang="ts" setup>
import { nextTick, onMounted, reactive, ref, watch } from "vue";
import { genTimeColumns, valueToTime, mergeHourMinuteSecond } from "./utils";
import { animate } from "@/assets/ts/animate";

import type { timeItem } from "./type";

/**
 * 编译器宏-组件外允许传入的属性
 * 注释的表示未实现
 */
const props = withDefaults(
  defineProps<{
    title?: string;
    partial?: string;
    value?: string;
    format?: string;
    disabledTime?: Function;
    hourStep?: number;
    minuteStep?: number;
    secondStep?: number;
    hideDisabledOptions?: boolean;
    visible?: boolean;
  }>(),
  {
    format: "HH:mm:ss",
    hourStep: 1,
    minuteStep: 1,
    secondStep: 1,
    hideDisabledOptions: false,
  }
);

/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["updateTempValue"]);

/**
 * 引用
 */
const hourRef = ref();
const minuteRef = ref();
const secondRef = ref();

/**
 * 动态值
 */
const data = reactive({
  time: valueToTime(props.value, props.format),
  hours: [] as timeItem[],
  minutes: [] as timeItem[],
  seconds: [] as timeItem[],
});

// 生成时间列
genTimeColumns({
  data,
  value: props.value,
  partial: props.partial,
  format: props.format,
  disabledTime: props.disabledTime,
  hourStep: props.hourStep,
  minuteStep: props.minuteStep,
  secondStep: props.secondStep,
  hideDisabledOptions: props.hideDisabledOptions,
});

// 自动滚动
const autoScrollTop = (duration = 200) => {
  if (hourRef.value && data.time.hour !== undefined) {
    const index = data.hours.findIndex((item) => item.value === data.time.hour);
    animate(hourRef.value, { scrollTop: index * 36 }, duration);
  }
  if (minuteRef.value && data.time.minute !== undefined) {
    const index = data.minutes.findIndex(
      (item) => item.value === data.time.minute
    );
    animate(minuteRef.value, { scrollTop: index * 36 }, duration);
  }
  if (secondRef.value && data.time.second !== undefined) {
    const index = data.seconds.findIndex(
      (item) => item.value === data.time.second
    );
    animate(secondRef.value, { scrollTop: index * 36 }, duration);
  }
};

// 点击项
const onClickItem = (type: string, item: timeItem) => {
  // 禁用项无效
  if (item.disabled) return;
  // 更新选中
  data.time[type] = item.value;
  // 发出事件
  emits(
    "updateTempValue",
    mergeHourMinuteSecond(data.time, props.format),
    props.partial
  );
  // 自动滚动
  nextTick(() => autoScrollTop());
};

watch(
  () => props.value,
  () => {
    // 更新时间
    data.time = valueToTime(props.value, props.format);
    // 自动滚动
    nextTick(() => autoScrollTop());
  }
);

/**
 * 组件挂载后
 */
onMounted(() => {
  watch(
    () => props.visible,
    () => {
      if (props.visible === true) {
        // 更新时间
        data.time = valueToTime(props.value, props.format);
        // 自动滚动
        nextTick(() => autoScrollTop());
      } else if (props.visible === false) {
        // 重置时间
        data.time = {};
      }
    }
  );
});
</script>

<template>
  <div>
    <div class="time-head" v-if="props.title">{{ props.title }}</div>
    <div class="time-panel">
      <div class="time-panel__column">
        <div class="time-column" ref="hourRef">
          <template v-for="(item, i) in data.hours" :key="i">
            <div
              class="time-column__item"
              :class="[{ 'time-column__item_state-disabled': item.disabled }]"
              @click.stop="onClickItem('hour', item)"
            >
              <div
                class="time-item"
                :class="[
                  { 'time-item_state-selected': item.value === data.time.hour },
                ]"
              >
                {{ item.label }}
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="time-panel__column" v-if="data.minutes.length > 0">
        <div class="time-column" ref="minuteRef">
          <template v-for="(item, i) in data.minutes" :key="i">
            <div
              class="time-column__item"
              :class="[{ 'time-column__item_state-disabled': item.disabled }]"
              @click.stop="onClickItem('minute', item)"
            >
              <div
                class="time-item"
                :class="[
                  {
                    'time-item_state-selected': item.value === data.time.minute,
                  },
                ]"
              >
                {{ item.label }}
              </div>
            </div>
          </template>
        </div>
      </div>

      <div class="time-panel__column" v-if="data.seconds.length > 0">
        <div class="time-column" ref="secondRef">
          <template v-for="(item, i) in data.seconds" :key="i">
            <div
              class="time-column__item"
              :class="[{ 'time-column__item_state-disabled': item.disabled }]"
              @click.stop="onClickItem('second', item)"
            >
              <div
                class="time-item"
                :class="[
                  {
                    'time-item_state-selected': item.value === data.time.second,
                  },
                ]"
              >
                {{ item.label }}
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss" scoped>
.time-head {
  height: 41px;
  border-bottom: 1px solid #e5e6eb;
  line-height: 40px;
  text-align: center;
  color: #383838;
}
.time-panel {
  display: flex;
  padding: 0 9px;

  &__column {
    width: 40px;
    overflow: hidden;
  }
}
.time-column {
  width: 80px;
  height: 180px;
  overflow-y: scroll;
  overscroll-behavior: contain;
  -ms-scroll-chaining: contain;
  cursor: pointer;

  &__item {
    padding: 6px 0;
    height: 36px;
    width: 40px;

    .time-item {
      font-size: 12px;
      border-radius: 4px;
      text-align: center;
      line-height: 24px;
      margin: 0 2px;
      background: transparent;
      transition: background 0.2s;

      &_state-selected {
        font-weight: 700;
        background: #e5edff;
        color: #5964fb;
      }
    }

    &_state-disabled .time-item {
      color: #c9cdd4;
    }

    &:not(&_state-disabled):hover .time-item:not(.time-item_state-selected) {
      background: rgba(0, 0, 0, 0.04);
    }
  }
}
</style>
