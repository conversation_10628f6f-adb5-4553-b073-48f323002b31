<template>
  <x-drawer
    :visible="props.show"
    @update:visible="updateVisible"
    @confirm="modalConfirm"
    @cancel="formRef.resetFields()"
    :btnOption="btnOption"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    :title="$t('editInfo')"
    width="674px"
  >
    <div class="edit-user-model" ref="xModelContentRef">
      <x-form ref="formRef" :model="form" :rules="formRules">
        <x-form-item :label="$t('name')" name="name">
          <x-input
            v-model:value="form.name"
            :maxlength="20"
            :placeholder="$t('PEnterUserName')"
          />
        </x-form-item>
        <x-form-item :label="$t('phone')" name="phone">
          <x-input
            v-model:value="form.phone"
            :maxlength="11"
            :filter="mobileFilter"
            :placeholder="$t('PEnterPhone')"
          />
        </x-form-item>
        <x-form-item :label="$t('entName')" name="company">
          <span style="padding-left: 10px">{{ form.company }}</span>
        </x-form-item>
        <x-form-item :label="$t('loginAcc')" name="username">
          <span style="padding-left: 10px"> {{ form.username }}</span>
        </x-form-item>
        <x-form-item :label="$t('stationType')" name="jobType">
          <span style="padding-left: 10px"> {{ form.jobTypeName }}</span>
        </x-form-item>
        <x-form-item :label="$t('role')" name="role">
          <x-tree-select
            v-model:value="form.role"
            :treeData="formOption.role"
            :popupContainer="xModelContentRef"
            :disabled="form.userTypeText === '主账号'"
            :placeholder="$t('PEnterRoleName')"
            showSearch
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>
<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { isPhoneNumber } from "@/assets/ts/validate";
import { JobType } from "@/assets/ts/config";
import {
  getUserDetail,
  userSearchRole,
  editUser,
  mobileRepeat,
} from "@/services/api";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import xDrawer from "@/components/x-drawer.vue";
import xForm from "@/components/x-form.vue";
import xFormItem from "@/components/x-form-item.vue";
import xInput from "@/components/x-input.vue";
import xTreeSelect from "@/components/x-tree-select.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("userManage");
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
});
const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);
const btnOption = reactive({
  position: "center" as "center",
});
const xModelContentRef = ref<any>();
const form = reactive({
  name: "",
  phone: "",
  company: "",
  username: "",
  jobType: 0,
  jobTypeName: "",
  role: 0,
  userTypeText: "",
});
const formOption = reactive({
  jobType: JobType,
  role: [] as TreeItemType[],
});
const repeatPhoneNumber = async (value: string) => {
  const result = await mobileRepeat({
    mobile: value,
    userId: props.id.toString(),
  });
  return !result?.repeat;
};
const formRules = reactive({
  name: [["required", $t("PEnterUserName")]],
  phone: [
    ["required", $t("PEnterPhone")],
    [isPhoneNumber, $t("phoneIncorrect")],
    [repeatPhoneNumber, $t("phoneExist")],
  ],
  company: [["required", $t("PEnterCompany")]],
  username: [["required", $t("PSetLoginAcc")]],
  jobType: [["required", $t("PSelectStationType")]],
  role: [["required", $t("PEnterRoleName")]],
});
const mobileFilter = (v: string) => v.replace(/\D/g, "");
const _entId = ref(0); // 后端要求传
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const {
        userName,
        mobile,
        entName,
        userAccount,
        station,
        entId,
        roleList,
        userTypeText,
      } = await getUserDetail(props.id);
      form.name = userName;
      form.phone = mobile;
      form.company = entName;
      form.username = userAccount;
      form.jobType = station;
      form.jobTypeName =
        JobType.find((job) => job.value === station)?.label || "";
      form.userTypeText = userTypeText;
      _entId.value = entId;
      const res = await userSearchRole({ entId });
      formOption.role = res.roleList.map((item) => ({
        title: item.roleName,
        value: item.roleId,
      }));
      form.role =
        roleList.length > 0 && res.roleList.length > 0 ? roleList[0].roleId : 0;
    }
  }
);

const formRef = ref<any>();
const modalConfirm = async () => {
  if (formRef.value.validate()) {
    await editUser({
      entId: _entId.value,
      userId: props.id.toString(),
      userName: form.name,
      mobile: form.phone,
      station: form.jobType,
      roleIdList: [form.role],
    });
    emits("update:show", false);
    Message("success", $t("saveSuccess"));
    emits("confirm");
  }
};
</script>

<style lang="scss" scoped>
.edit-user-model {
  padding: 20px 20px 0px 20px;
  margin-right: 20px;
  border-radius: 8px;
  background: #fff;
}
</style>
