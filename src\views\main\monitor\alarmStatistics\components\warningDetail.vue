<template>
  <x-drawer
    :title="$t('faultDetail')"
    width="672px"
    :visible="props.show"
    @update:visible="updateVisible"
  >
    <div class="content">
      <div class="content-warning">
        <div class="content-warning-title">
          <x-title>{{ $t("faultInfo") }}</x-title>
        </div>
        <div class="content-warning-body">
          <div class="content-warning-body-item">
            <div class="label" style="margin-right: 12px">
              {{ $t("faultName") }}
            </div>
            <div class="value">
              <span class="strong">
                {{ config.warningDetail.faultContent }}
              </span>
              <div
                :class="[
                  'status',
                  { statusDisabled: config.warningDetail.isSolved === 1 },
                ]"
              >
                <span>{{
                  config.warningDetail.isSolved === 0
                    ? $t("unsolved")
                    : $t("solved")
                }}</span>
              </div>
            </div>
          </div>
          <div class="content-warning-body-item">
            <div class="label">{{ $t("faultLevel") }}：</div>
            <div
              v-for="(item, index) in filteredWarningType"
              :key="index"
              style="margin-left: 10px"
            >
              <span :style="{ color: item.color }">{{
                config.warningDetail.warnLevelStr
              }}</span>
            </div>
          </div>
          <div class="content-warning-body-item wholeline">
            <div class="label">{{ $t("faultTime") }}：</div>
            <div class="value">
              {{ config.warningDetail.createdTime }} -
              {{ config.warningDetail.endTime }}
            </div>
          </div>
        </div>
      </div>
      <div class="content-car" style="margin: 10px 0">
        <div class="content-car-title">
          <x-title>{{ $t("deviceInfo") }}</x-title>
        </div>
        <div class="content-car-body">
          <div class="content-car-body-item">
            <div class="label">{{ $t("deviceNumber") }}：</div>
            <div class="value">
              {{ config.warningDetail.deviceId }}
            </div>
          </div>
          <div class="content-car-body-item">
            <div class="label">{{ $t("deviceType") }}：</div>
            <div class="value">
              {{ config.warningDetail.carType }}
            </div>
          </div>

          <div class="content-car-body-item">
            <div class="label">VIN：</div>
            <div class="value">
              {{ config.warningDetail.vin }}
            </div>
          </div>
          <div class="content-car-body-item">
            <div class="label">⻋辆型号：</div>
            <div class="value">
              {{ config.warningDetail.moduleName }}
            </div>
          </div>
          <div class="content-car-body-item">
            <div class="label">项⽬区域：</div>
            <div class="value">
              {{ config.warningDetail.areaName }}
            </div>
          </div>

          <div class="content-car-body-item">
            <div class="label">{{ $t("ofProject") }}：</div>
            <div class="value">
              {{ config.warningDetail.project }}
            </div>
          </div>
          <div class="content-car-body-item">
            <div class="label">{{ $t("ofEnterprise") }}：</div>
            <div class="value">
              {{ config.warningDetail.company }}
            </div>
          </div>
        </div>
      </div>
      <div class="content-log">
        <div class="content-log-title">
          <x-title>{{ $t("recordInfo") }}</x-title>
        </div>
        <div class="content-log-body">
          <div
            v-for="(item, index) in config.warningDetail.records"
            :key="index"
          >
            <div class="timeline">
              <div
                :class="[
                  'timeline-item',
                  { line: index !== config.warningDetail.records.length - 1 },
                ]"
              >
                <div class="timeline-item-title">
                  <span class="timeline-item-title-name">
                    {{ item.userName }}
                  </span>
                  <span class="timeline-item-title-date">
                    {{ item.updateTime }}
                  </span>
                  <div
                    :class="['status', { statusDisabled: item.isSolved === 1 }]"
                  >
                    <span>{{
                      item.isSolved === 0 ? $t("unsolved") : $t("solved")
                    }}</span>
                  </div>
                </div>
                <div class="timeline-item-content">
                  {{ item.description }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script setup lang="ts">
import { reactive, watch, computed } from "vue";
import { getWarnDetail } from "@/services/api";
import type { WarnDetailResponse } from "@/services/type";
import { warnLevelDefType } from "@/assets/ts/config";
import xTitle from "@/components/x-title.vue";
import xDrawer from "@/components/x-drawer.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("alarmStatistics");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const config = reactive({
  warningDetail: {} as WarnDetailResponse,
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      config.warningDetail = await getWarnDetail({ id: props.id });
    }
  }
);

const emits = defineEmits(["update:show"]);

const updateVisible = (bool: boolean) => emits("update:show", bool);

const filteredWarningType = computed(() =>
  warnLevelDefType.filter(
    (item) => item.value === config.warningDetail.warnLevel
  )
);
</script>

<style lang="scss" scoped>
.content {
  &-warning,
  &-car,
  &-log {
    border-radius: 8px;
    background: #ffffff;
    &-title {
      @include sc(14px, #5e5e5e) {
        margin-left: -10px;
      }
    }
  }
  &-warning,
  &-car {
    padding: 10px 20px 20px 20px;
    &-body {
      display: flex;
      flex-wrap: wrap;
      &-item {
        display: flex;
        flex-basis: 50%;
        padding: 5px 0;
        &.wholeline {
          flex-basis: 100%;
        }
        .label {
          @include sc(14px, #9f9fa4);
        }
        .value {
          display: flex;
          @include sc(14px, #383838) {
            margin-left: 10px;
            flex: 1;
          }
        }
        .strong {
          @include sc(16px, #242859) {
            font-weight: 700;
            margin-right: 5px;
          }
        }
      }
    }
  }
  &-log {
    padding: 10px 0 20px 20px;
    &-body {
      .timeline {
        .line::before {
          @include wh(1px, 100%) {
            content: "";
            position: absolute;
            top: 19px;
            left: 10px;
            border-right: 2px solid #f3f3f3;
          }
        }
        &-item {
          position: relative;
          padding-left: 25px;
          &::after {
            @include wh(4px) {
              content: "";
              position: absolute;
              left: 8px;
              top: 20px;
              border: 3px solid rgb(89, 100, 251);
              background-color: #fff;
              border-radius: 6px;
            }
          }
          &-title {
            @include ct-f(y);
            padding: 10px 0;
            &-name {
              @include sc(14px, #383838) {
                padding: 0 10px 0 5px;
              }
            }
            &-date {
              @include sc(12px, #9f9fa4) {
                margin-right: 5px;
              }
            }
          }
          &-content {
            @include sc(14px, #5e5e5e) {
              position: relative;
              width: 470px;
              padding: 10px;
              line-height: 25px;
              border-radius: 4px;
              background-color: #f4f7fe;
            }
            &::after {
              position: absolute;
              top: -12px;
              left: 10px;
              content: "";
              border-bottom: 26px solid #f4f7fe;
              border-left: 10px solid transparent;
              border-right: 15px solid transparent;
            }
          }
        }
      }
    }
  }
  .status {
    @include ct-f(both);
    @include wh(58px, 28px) {
      border-radius: 50px;
    }
    @include sc(12px, #5964fb) {
      background: rgba(89, 100, 251, 0.06);
    }
    &.statusDisabled {
      background: #f0f0f0;
      color: #c9c3c3;
    }
  }
}
</style>
