<template>
  <x-modal
    width="900px"
    height="520px"
    :visible="Boolean(socket.alarmStatusPop)"
    :bodyStyle="bodyStyle"
    @cancel="closeModal"
  >
    <div class="alarm-modal">
      <div class="alarm-modal-top">
        <div class="alarm-modal-top-left">
          <x-icon
            name="alarm_warn"
            width="300"
            height="300"
          ></x-icon>
        </div>
        <div class="alarm-modal-top-middle">
          <p>
            {{ $t("realTimeAlarms") }}
            <span class="warn-num">{{ detail.warnNum }}{{ $t("individual") }}</span>
          </p>
          <p>{{ $t("dataUploadTime") }}：{{ detail.updateDate }}</p>
          <p>{{ detail.deviceId }}</p>
        </div>
        <div class="alarm-modal-top-right">
          <div
            class="warn-button"
            @click="toAlarmStatistics"
          >
            <span>{{ $t("alarmStatistics") }}</span>
          </div>
        </div>
      </div>
      <div class="alarm-modal-bottom">
        <div class="alarm-modal-bottom-waterfall">
          <div
            v-if="loading"
            class="alarm-modal-bottom-loading"
          >
            <img
              src="@/assets/images/loading.gif"
              alt=""
            />
          </div>
          <div
            class="warn-system"
            v-for="(item, index) in detail.warnList"
            :key="index"
          >
            <div class="warn-system-title">
              <div
                class="warn-system-title-left"
                :style="{ background: getBgColor(item) }"
              >
                <x-icon
                  name="system_vehicle_control"
                  width="25"
                  height="25"
                ></x-icon>
              </div>
              <div class="warn-system-title-right">
                <p>{{ item.label }}</p>
                <p>
                  {{ item.list.length || 0 }}
                  <span>个</span>
                </p>
              </div>
            </div>
            <div class="warn-system-content">
              <div
                class="warn-system-content-item"
                v-for="(warn, i) in item.list"
                :key="i"
              >
                <x-icon
                  :name="`map_warn_${warn.warnLevel}`"
                  width="15"
                  height="15"
                ></x-icon>
                <span>{{ warn.warnName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import xModal from "@/components/x-modal";
import xIcon from "@/components/x-icon.vue";
import Message from "@/components/x-message";
import { reactive, watch, ref, onMounted } from "vue";
import { useMainStore } from "@/stores/main";
import { useRouter } from "vue-router";
import { addCar, delCar } from "@/services/wsapi";
import { warnLevelDefType } from "@/assets/ts/config";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

const router = useRouter();

const {
  userInfo: { permitList },
  socket,
} = useMainStore();

const bodyStyle = reactive({
  padding: 0,
  "background-color": "#F4F7FE",
});

const detail = reactive({
  warnNum: 0,
  updateDate: "",
  deviceId: socket.alarmStatusPop,
  warnList: [] as any[],
});

onMounted(() => {
  addCar([socket.alarmStatusPop as string]);
});

const loading = ref(true);

watch(
  () => socket.allStatus,
  (newV) => {
    if (newV) {
      loading.value = true;
      const target = newV.find((v) => v.deviceId === socket.alarmStatusPop);
      if (target) {
        detail.warnNum = target.warnNum;
        detail.warnList = [];
        if (target.updateDate && target.updateTime) {
          detail.updateDate = `${target.updateDate.replace(/-/g, "/")} ${target.updateTime}`;
        }
        for (const key in target.faultCacheMap) {
          const obj = target.faultCacheMap[key];
          const { faultModule, faultContent, warnLevel } = obj;
          const systemIndex = detail.warnList.findIndex((system) => system.value === faultModule);
          if (systemIndex !== -1) {
            detail.warnList[systemIndex].list.push({
              warnName: faultContent,
              warnLevel: warnLevel,
            });
          } else {
            detail.warnList.push({
              label: faultModule,
              value: faultModule,
              list: [
                {
                  warnName: faultContent,
                  warnLevel: warnLevel,
                },
              ],
            });
          }
        }
        loading.value = false;
      }
    }
  }
);

const closeModal = () => {
  !socket.enableCarIds.includes(socket.alarmStatusPop as string) && delCar([socket.alarmStatusPop as string]);
  socket.alarmStatusPop = false;
};

const toAlarmStatistics = () => {
  let isPermit = permitList.some((item: any) => item.startsWith("sys:vehicleWarn"));
  if (isPermit) {
    router.push({
      name: "alarmStatistics",
      query: {
        deviceId: socket.alarmStatusPop as string,
      },
    });
  } else {
    Message("error", $t("noPermission"));
  }
};

// 模块主体颜色
const getBgColor = (item: any) => {
  if (!item.list || item.list.length === 0) return "";
  const warnLevels = item.list.map((warn: any) => warn.warnLevel);
  const highestWarnLevel = Math.max(...warnLevels);
  const highestWarn = warnLevelDefType.find((item) => item.value === highestWarnLevel);
  return highestWarn ? highestWarn.color : "";
};
</script>

<style lang="scss" scoped>
.alarm-modal {
  &-top {
    @include ct-f(y);
    @include wh(100%, 220px) {
      background-image: linear-gradient(180deg, rgba(243.62, 21.04, 21.04, 50%), rgba(246, 127, 89, 0) 100%);
      border-radius: 10px 10px 0 0;
    }
    &-left {
      margin-top: auto;
    }
    &-middle {
      margin: 40px 0 0 20px;
      p:nth-child(1) {
        @include sc(18px, #f41515) {
          font-weight: 700;
        }
        .warn-num {
          margin-left: 10px;
          font-size: 24px;
        }
      }
      p:nth-child(2) {
        padding: 5px 0;
        @include sc(13px, #383838);
      }
      p:nth-child(3) {
        @include sc(18px, #383838) {
          font-weight: 700;
        }
      }
    }
    &-right {
      margin: 0 30px 0 auto;
      .warn-button {
        @include wh(96px, 48px) {
          line-height: 48px;
          text-align: center;
        }
        @include sc(16px, #e24562);
        border-radius: 4px;
        background: #fff;
        cursor: pointer;
      }
    }
  }
  &-bottom {
    @include scrollbar(y, 4px) {
      overflow-y: auto;
      max-height: 300px;
    }
    &-loading {
      @include ct-p;
      img {
        @include wh(50px);
      }
    }
    &-waterfall {
      column-count: 3;
      gap: 0;
      counter-reset: count;
      width: 100%;
      margin: 0 auto;
      padding: 0 20px 20px;
      box-sizing: border-box;
    }
    .warn-system {
      break-inside: avoid;
      margin: 0 5px 5px 0;
      padding: 15px;
      background: #fff;
      border-radius: 4px;
      box-sizing: border-box;
      &-title {
        @include ct-f(y);
        padding-bottom: 10px;
        &-left {
          @include ct-f(both);
          @include wh(36px) {
            border-radius: 50%;
          }
        }
        &-right {
          margin-left: 10px;
          p:nth-child(1) {
            @include sc(13px, #5e5e5e) {
              margin-bottom: 5px;
            }
          }
          p:nth-child(2) {
            @include sc(14px, #383838) {
              font-weight: 700;
            }
            span {
              margin-left: 5px;
              font-size: 12px;
              font-weight: 400;
            }
          }
        }
      }
      &-content {
        display: flex;
        flex-wrap: wrap;
        &-item {
          margin: 5px;
          padding: 5px 10px;
          border: 1px solid rgb(220, 220, 220);
          border-radius: 4px;
          span {
            @include sc(14px, #5e5e5e) {
              margin-left: 5px;
            }
          }
        }
      }
    }
  }
}
</style>
