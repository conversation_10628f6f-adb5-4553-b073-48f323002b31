<template>
  <div class="task-status-part">
    <div class="task-status-part-tabs">
      <div
        class="task-status-part-tabs-item"
        :class="{ active: activeTab === item.value }"
        v-for="item in taskStatus"
        :key="item.value"
        @click="handleTabClick(item.value)"
      >
        <div class="task-status-part-tabs-item-label">{{ item.label }}</div>
      </div>
    </div>

    <div
      class="table-wrapper"
      ref="tableContainerRef"
    >
      <el-table
        :data="table.dataSource"
        style="width: 100%"
        height="100%"
        v-loading="table.loading"
      >
        <el-table-column
          prop="workNo"
          label="任务编号"
          width="180"
        />
        <el-table-column
          prop="workStatus"
          label="任务状态"
          width="85"
        >
          <template #default="{ row }">
            <span :class="row.workStatus === 0 ? 'progress' : 'complete'">{{
              row.workStatus === 0 ? "进行中" : "已完成"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="tempName"
          label="任务模板"
          width="130"
        >
          <template #default="{ row }">
            <span>{{ row.tempName ? row.tempName : "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="taskWorkType"
          label="任务类型"
          width="120"
        >
          <template #default="{ row }">
            <span>{{ showTaskWorkType(row.taskWorkType) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="taskCompletionPercentage"
          label="主任务进度"
          width="100"
        >
          <template #default="{ row }">
            <span>{{ formatRoundNum(row.taskCompletionPercentage, 0) * 100 ?? 0 }}%</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="workStart"
          label="任务开始时间"
          width="180"
        />
        <el-table-column
          prop="workEnd"
          label="任务结束时间"
          width="180"
        />
        <el-table-column
          prop="taskDuration"
          label="已耗时"
          width="120"
        >
          <template #default="{ row }">
            <span>{{ formatDuration(row.taskDuration) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          prop="userName"
          label="操作人"
          width="120"
        />
      </el-table>
    </div>
  </div>
</template>

<script setup lang="ts">
import { getVehicleTaskDetail } from "@/services/api";
import { ref, reactive, onMounted, nextTick } from "vue";
import { formatRoundNum } from "@/assets/ts/utils";
const props = defineProps({
  deviceId: {
    type: String,
    default: "",
  },
});

const tableContainerRef = ref<HTMLElement>();
const activeTab = ref<string>("0");
const taskStatus = ref([
  /** 后端说展示不需要 */
  // { label: "全部", value: 0 },
  { label: "进行中", value: "0" },
  { label: "已完成", value: "1" },
]);

const table = reactive({
  dataSource: [] as any[],
  loading: false,
  page: 1,
  limit: 10,
  hasMore: true,
});

const showTaskWorkType = (value: number) => {
  switch (value) {
    case 0:
      return "任务";
    case 1:
      return "垃圾点";
    case 2:
      return "充电点";
    case 3:
      return "加水点";
    case 4:
      return "停车点";
    case 5:
      return "补给点";
    default:
      return "-";
  }
};

// 将分钟转换为小时，并添加单位h
const formatDuration = (minutes: number) => {
  if (minutes === undefined || minutes === null) return "0h";

  // 转换为小时，保留1位小数
  const hours = (minutes / 60).toFixed(1);
  return `${hours}h`;
};

const handleTabClick = (value: string) => {
  activeTab.value = value;
  table.page = 1;
  table.dataSource = [];
  table.hasMore = true;
  loadData();
};

const handleScroll = () => {
  if (table.loading || !table.hasMore) return;

  const el = tableContainerRef.value?.querySelector(".el-scrollbar__wrap") as HTMLElement;
  if (el) {
    const distanceToBottom = el.scrollHeight - el.scrollTop - el.clientHeight;
    if (distanceToBottom < 10) {
      table.page++;
      loadData();
    }
  }
};

const loadData = async () => {
  if (table.loading || !table.hasMore) return;

  table.loading = true;

  try {
    const res = await getVehicleTaskDetail({
      deviceId: props.deviceId,
      page: table.page,
      limit: table.limit,
      workStatus: activeTab.value,
    });

    const newData = res.list || [];

    table.dataSource.push(...newData);

    // 判断是否还有更多数据
    if (newData.length < table.limit) {
      table.hasMore = false;
    }
  } catch (error) {
    console.error(error);
    table.hasMore = false;
  } finally {
    table.loading = false;
  }
};

onMounted(async () => {
  await loadData();
  nextTick(() => {
    const el = tableContainerRef.value?.querySelector(".el-scrollbar__wrap") as HTMLElement;
    if (el) {
      el.addEventListener("scroll", handleScroll);
    }
  });
});
</script>

<script lang="ts">
export default {
  name: "taskStatusPart",
};
</script>

<style scoped lang="scss">
.task-status-part {
  width: 100%;
  height: 100%;
  background-color: #fff;

  &-tabs {
    display: flex;
    align-items: center;
    color: #9f9fa4;
    padding: 10px 15px;
    cursor: pointer;
    gap: 20px;
    user-select: none;

    &-item {
      position: relative;

      &:hover {
        color: #242859;
        font-weight: 700;

        &::after {
          content: "";
          display: block;
          width: 19px;
          height: 2px;
          background: #242859;
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
      &.active {
        color: #242859;
        font-weight: 700;
        &::after {
          content: "";
          display: block;
          width: 19px;
          height: 2px;
          background: #242859;
          position: absolute;
          bottom: -10px;
          left: 50%;
          transform: translateX(-50%);
        }
      }
    }
  }

  .progress {
    display: inline-block;
    padding: 5px 11px;
    border-radius: 50px;
    color: #5964fb;
    background: rgba(89, 100, 251, 0.06);
    font-size: 12px;
    white-space: nowrap;
  }
  .complete {
    display: inline-block;
    padding: 5px 11px;
    border-radius: 50px;
    background: rgba(159, 159, 164, 0.17);
    color: #4d4d4d;
    font-size: 12px;
    white-space: nowrap;
  }

  .table-wrapper {
    height: calc(100% - 40px);
    overflow: hidden;

    :deep(.el-table) {
      height: 100%;

      .el-table__body-wrapper {
        overflow-y: auto;
      }
    }
  }
}
</style>
