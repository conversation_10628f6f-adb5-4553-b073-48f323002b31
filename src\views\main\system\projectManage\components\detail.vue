<template>
  <x-drawer
    :title="$t('projectDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="680px"
  >
    <div class="content">
      <div class="content-info-panel">
        <div class="content-info-panel-head">
          <div class="content-info-panel-title">
            {{ detail.info.proName }}
          </div>
          <div class="content-info-panel-seq">
            {{ detail.info.proSeq }}
          </div>
          <div class="content-info-panel-date">
            {{ detail.info.openDate || $t("incorrectOpDateOutput") }}
          </div>
        </div>
        <div class="content-info-panel-subtitle">
          <div
            class="content-info-panel-subtitle-item"
            style="font-weight: 600"
          >
            {{ detail.info.userName }}
          </div>
          <div class="content-info-panel-subtitle-item">
            {{ detail.info.userPhone || $t("incorrectMobileOutput") }}
          </div>
        </div>
        <div class="content-info-panel-body">
          <div
            class="content-info-panel-body-item"
            v-for="(item, index) in bottomInfo"
            :key="index"
          >
            <div
              class="content-info-panel-body-item-label"
              v-if="item.label"
            >
              {{ item.label }}
            </div>
            <div class="content-info-panel-body-item-value">
              {{ item.value }}
            </div>
          </div>
        </div>
      </div>
      <div class="content-fun">
        <div class="content-fun-title">{{ $t("staffAllocation") }}（{{ detail.info.relUserList?.length }}）</div>
        <div
          class="content-fun-tbody"
          :style="{
            height: detail.open.relUser ? `${41 * Math.ceil(detail.info.relUserList?.length / 6)}px` : `${41 * 3}px`,
          }"
        >
          <div
            class="content-fun-tbody-th"
            style="width: 97px"
            v-for="item in detail.info.relUserList || []"
            :key="item.userId"
          >
            {{ item.userName }}
          </div>
        </div>
        <div class="content-fun-tfoot">
          <div
            :class="['content-fun-tfoot-more', { open: detail.open.relUser }]"
            v-if="detail.info.relUserList?.length > 18"
            v-on:click="detail.open.relUser = !detail.open.relUser"
          >
            <span class="content-fun-tfoot-more-label">
              {{ detail.open.relUser ? $t("collapse") : $t("more") }}
            </span>
            <x-icon
              :name="detail.open.relUser ? 'up_circle' : 'down_circle'"
              width="12"
              height="12"
            />
            <!-- <span class="content-fun-tfoot-more-circle">
              <i class="content-fun-tfoot-more-circle-arrow"></i>
            </span> -->
          </div>
        </div>
      </div>
      <div class="content-fun">
        <div class="content-fun-title">{{ $t("vehicleAllocation") }}（{{ detail.info.relVehList?.length || 0 }}）</div>
        <div
          class="content-fun-tbody"
          :style="{
            height: detail.open.relVeh ? `${41 * Math.ceil(detail.info.relVehList?.length / 4)}px` : `${41 * 3}px`,
          }"
        >
          <div
            class="content-fun-tbody-th"
            style="width: 25%"
            v-for="item in detail.info.relVehList || []"
            :key="item.id"
          >
            {{ item.vehicleNo }}
          </div>
        </div>
        <div class="content-fun-tfoot">
          <div
            class="content-fun-tfoot-more"
            v-if="detail.info.relVehList?.length > 18"
            v-on:click="detail.open.relVeh = !detail.open.relVeh"
          >
            <span class="content-fun-tfoot-more-label">
              {{ detail.open.relVeh ? $t("collapse") : $t("more") }}
            </span>
            <x-icon
              :name="detail.open.relVeh ? 'up_circle' : 'down_circle'"
              width="12"
              height="12"
            />
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import type { GetProDetailResponse } from "@/services/type";
import { reactive, watch, computed } from "vue";
import { getProDetail } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import xIcon from "@/components/x-icon.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("projectManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 详情数据
 */
const detail = reactive({
  info: {} as GetProDetailResponse,
  open: {
    relUser: false,
    relVeh: false,
  },
});

/**
 * 依赖详情数据的计算属性
 */
const bottomInfo = computed(() => [
  {
    label: "",
    value: detail.info.entName,
  },
]);

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.info = await getProDetail(props.id);

      // detail.info.relUserList = relUserList;
      // detail.info.relVehList = relVehList;
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  &-info-panel,
  &-fun {
    border-radius: 8px;
  }
  &-info-panel {
    background: rgb(255, 255, 255);
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    padding: 12px 20px;
    &-head {
      display: flex;
      align-items: center;
      justify-content: space-between;
    }
    &-title {
      @include sc(16px, #242859) {
        font-weight: 700;
      }
    }
    &-date {
      @include sc(14px, #5e5e5e);
    }
    &-subtitle {
      @include fj(flex-start) {
        margin-top: 10px;
      }
      @include sc(14px, #383838);
      &-item::after {
        content: "|";
        color: #e5e5e5;
        padding: 0 12px;
      }
      &-item:last-child::after {
        display: none;
      }
    }
    &-body {
      padding-top: 10px;
      &-item {
        &-label {
          @include sc(14px, #9f9fa4) {
            width: 5em;
          }
        }
        &-value {
          @include sc(14px, #5e5e5e) {
            flex: 1;
            overflow: hidden;
          }
        }
      }
    }
  }
  &-fun {
    background: #fff;
    margin-top: 16px;
    &-title {
      @include sc(14px, #5e5e5e) {
        padding: 16px 20px 4px 20px;
      }
    }
    &-tbody {
      overflow-y: hidden;
      transition: height 0.3s;

      &-th {
        @include sc(14px, #383838) {
          display: inline-block;
          vertical-align: top;
          padding: 10px 0;
          text-align: center;
        }
      }
    }
    &-tfoot {
      padding: 0 0 12px 0;
      &-more {
        display: flex;
        align-items: center;
        justify-content: center;
        height: 21px;
        color: #9f9fa4;
        cursor: pointer;
        user-select: none;

        // &:hover {
        //   color: #5964fb;
        // }

        &-label {
          font-size: 12px;
          margin-right: 4px;
        }
        // &-circle {
        //   width: 13px;
        //   height: 13px;
        //   border: 1px solid currentcolor;
        //   display: inline-block;
        //   vertical-align: top;
        //   border-radius: 50%;
        //   position: relative;

        //   &-arrow {
        //     position: absolute;
        //     left: 3px;
        //     top: 7px;
        //     transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1),
        //       top 0.3s cubic-bezier(0.645, 0.045, 0.355, 1), opacity 0.3s;

        //     &::after,
        //     &::before {
        //       position: absolute;
        //       width: 5px;
        //       height: 1px;
        //       background-color: currentcolor;
        //       border-radius: 2px;
        //       transition: transform 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
        //       content: "";
        //     }
        //     &::before {
        //       transform: rotate(-45deg) translateX(2px);
        //     }
        //     &::after {
        //       transform: rotate(45deg) translateX(-2px);
        //     }
        //   }
        // }
        // &.open {
        //   .content-fun-tfoot-more-circle-arrow {
        //     top: 4px;
        //   }
        //   .content-fun-tfoot-more-circle-arrow::before {
        //     transform: rotate(45deg) translateX(2px);
        //   }
        //   .content-fun-tfoot-more-circle-arrow::after {
        //     transform: rotate(-45deg) translateX(-2px);
        //   }
        // }
      }
    }
  }
}
</style>
