<template>
  <section class="threejs">
    <div id="container" ref="threeDom">
      <div style="position: absolute; left: 0; top: 0">
        {{ position }}
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { onMounted, reactive, ref } from "vue";
import * as THREE from "three";
// 控制器control / 加载器loader / 后期处理效果post-processing effect / ... —— 必须从examples/jsm子目录下导入
import { OrbitControls } from "three/examples/jsm/controls/OrbitControls.js";
import { GLTFLoader } from "three/examples/jsm/loaders/GLTFLoader";
import { DRACOLoader } from "three/addons/loaders/DRACOLoader.js";
// import { RoomEnvironment } from "three/addons/environments/RoomEnvironment.js";

onMounted(() => {
  initThree();
  render();
  // 根据浏览器窗口自适应
  window.onresize = () => {
    camera.aspect = window.innerWidth / window.innerHeight;
    camera.updateProjectionMatrix();
    renderer.setSize(window.innerWidth, window.innerHeight);
  };
});
const position = reactive({
  x: 0,
  y: 0,
  z: 0,
});

const threeDom = ref<HTMLElement>();
let renderer: THREE.WebGLRenderer;
let camera: THREE.PerspectiveCamera;
let controls: OrbitControls;
let scene: THREE.Scene;
let mesh: THREE.Mesh;
let roadMesh: THREE.Mesh;
let houseMesh: THREE.Mesh;
let cameraCurve: THREE.CatmullRomCurve3;
const initThree = () => {
  // ----------------------------------------------------------------渲染器 - WebGL渲染器
  renderer = new THREE.WebGLRenderer({
    // antialias: true // 是否执行抗锯齿
  });
  renderer.setPixelRatio(window.devicePixelRatio); // 设置设备像素比，用于避免HiDPI设备上绘图模糊
  renderer.setSize(window.innerWidth, window.innerHeight, true); // canvas大小，低分辨率可设置false
  renderer.autoClear = false; // 是否在渲染每一帧之前自动清除其输出
  threeDom.value?.appendChild(renderer.domElement);

  // ----------------------------------------------------------------摄像机 - 透视相机
  camera = new THREE.PerspectiveCamera(
    45, // 摄像机 视锥体 垂直视野角度0-180   视角越广 看的越多 模型越小
    window.innerWidth / window.innerHeight, // 摄像机 视锥体 长宽比              设置浏览器长宽，避免变形
    1, // 摄像机 视锥体 近截面              不渲染 比近端面近的部分
    9999999 // 摄像机 视锥体 远截面           不渲染 比远端面远的部分
  );
  // 摄像机三维坐标坐标
  camera.position.set(-300, 275, 558);

  // ----------------------------------------------------------------轨道控制器 (可以使得相机围绕目标进行轨道运动)
  controls = new OrbitControls(camera, renderer.domElement);
  // controls.zoomSpeed = 1;         // 缩放速度
  // controls.rotateSpeed = 1;       // 旋转速度
  // controls.panSpeed = 1;          // 位移速度
  controls.target.set(0, 0, 0); // 相机聚焦点  会覆盖camera.lookAt
  // controls.enableZoom = false;       // 是否开启缩放 (默认开启)
  // controls.enableRotate = false;     // 是否开启旋转 (默认开启)
  // controls.enablePan = false;        // 是否开启平移 (默认开启)
  // controls.enableDamping = true;     // 是否开启阻尼 (默认关闭，开启后控制器具有重量感)
  // controls.dispose()
  controls.update();

  // ----------------------------------------------------------------场景
  scene = new THREE.Scene();
  scene.background = new THREE.Color(0xbfe3dd);
  // const pmremGenerator = new THREE.PMREMGenerator(renderer);
  // scene.environment = pmremGenerator.fromScene(
  //   new RoomEnvironment(renderer),
  //   0.04
  // ).texture;
  // scene.add()  默认添加到(0,0,0)

  // ----------------------------------------------------------------模型
  const axesHelper = new THREE.AxesHelper(1000); // 辅助对象 - 坐标轴
  scene.add(axesHelper);

  // 使用指定的点创建一条平滑的三维样条曲线当做相机运动路径
  cameraCurve = new THREE.CatmullRomCurve3(
    [
      new THREE.Vector3(-600, -80, 160),
      new THREE.Vector3(-300, -20, 80),
      new THREE.Vector3(300, -20, 80),
      new THREE.Vector3(300, 80, 0),
      new THREE.Vector3(300, 40, -600),
      new THREE.Vector3(-600, -80, -400),
    ],
    true
  );
  // 参考路径上取100个点，每个点上添加蓝色小球
  const pathPoints = cameraCurve.getPoints(1000);

  // 引入三维模型(glb或者gltf格式)
  const dracoLoader = new DRACOLoader();
  const loader = new GLTFLoader();
  dracoLoader.setDecoderPath("/gltf/");
  loader.setDRACOLoader(dracoLoader);
  loader.load(`/models/roads.glb`, (glb) => {
    roadMesh = glb.scene;
    roadMesh.scale.set(10, 10, 10);
    roadMesh.position.set(-2000, -400, -2000);
    scene.add(roadMesh);
  });
  loader.load(`/models/LittlestTokyo.glb`, (glb) => {
    houseMesh = glb.scene;
    houseMesh.scale.set(0.6, 0.6, 0.6);
    houseMesh.position.set(0, 80, 0);
    scene.add(houseMesh);
  });
  loader.load(`/models/car.glb`, (glb) => {
    mesh = glb.scene.children[0].children[0];
    // mesh.position.set(pathPoints[333].x, pathPoints[333].y, pathPoints[333].z);
    mesh.scale.set(0.06, 0.06, 0.06);
    scene.add(mesh);
  });
  //绘制一条路径参考线
  const geometry = new THREE.BufferGeometry().setFromPoints(pathPoints);
  const material = new THREE.LineBasicMaterial({
    color: 0x000000,
    linewidth: 1,
  });
  const curveObject = new THREE.Line(geometry, material);
  scene.add(curveObject);

  // ----------------------------------------------------------------光源
  // 环境光
  const ambientLight = new THREE.AmbientLight(0xffffff); // 创建环境光
  scene.add(ambientLight); // 将环境光添加到场景
  const directionLight = new THREE.DirectionalLight(0xffffff);
  directionLight.position.set(-20, 30, 40);
  directionLight.intensity = 1.5;
  scene.add(directionLight);
};
let shperePathIndex = 0;
const render = () => {
  //参考路径的索引在0-1000中往复减少
  if (shperePathIndex === 1000) {
    shperePathIndex = -1;
  }
  shperePathIndex += 1;
  // 设置绿色球的位置为参考路径上当前点的位置
  if (mesh) {
    //取相参考径上当前点的坐标
    const sphereCurveIndex = shperePathIndex / 1000; //取值0~1
    const tmpSpherePosition = cameraCurve.getPointAt(sphereCurveIndex);
    mesh.position.set(
      tmpSpherePosition.x,
      tmpSpherePosition.y,
      tmpSpherePosition.z
    );
    // 当前点在线条上的位置
    mesh.position.copy(tmpSpherePosition);
    // 返回一个点t在曲线上位置向量的法线向量
    const tangent = cameraCurve.getTangentAt(sphereCurveIndex);
    // 位置向量和切线向量相加即为所需朝向的点向量
    const lookAtVec = tangent.add(tmpSpherePosition);
    mesh.lookAt(lookAtVec);

    // 镜头追踪 - 镜头方向
    const lookAtOffset = new THREE.Vector3(0, 0, 100); // 创建一个新的位置向量，为模型的朝向加上一个偏移量
    lookAtOffset.applyQuaternion(mesh.quaternion); // 将偏移量转换到模型的世界坐标系中
    camera.lookAt(mesh.position.clone().add(lookAtOffset)); // 设置相机的朝向为模型的朝向加上偏移量

    // 镜头追踪 - 镜头位置
    const offset = new THREE.Vector3(0, 80, -250); // 相机的位置偏移量
    offset.applyQuaternion(mesh.quaternion); // 将偏移量转换到模型的世界坐标系中
    camera.position.copy(mesh.position).add(offset); // 设置相机的位置为模型的位置加上偏移量
  }

  position.x = camera.position.x;
  position.y = camera.position.y;
  position.z = camera.position.z;
  renderer.render(scene, camera); // 渲染循环(场景+摄像机)
  requestAnimationFrame(render);
};
</script>

<style lang="scss" scoped>
.threejs {
  @include wh(100%);
  #container {
    @include wh(100%) {
      position: relative;
    }
  }
}
</style>
