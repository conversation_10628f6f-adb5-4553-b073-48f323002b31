<template>
  <section class="track-video">
    <div class="track-video-tabs">
      <div
        :class="['track-video-tabs-item', { 'is-active': item.value === tabs.active }]"
        v-for="item in tabs.options"
        :key="item.value"
        @click="handleTabItem(item)"
      >
        <span>{{ item.label }}</span>
      </div>
    </div>
    <div class="track-video-content">
      <TrackReplay
        :id="query.id"
        v-if="tabs.active === 'track_replay'"
      />
      <VideoPlayback :visible="tabs.active === 'video_playback'" />
    </div>
  </section>
</template>

<script lang="ts" setup>
import { defineComponent, reactive } from "vue";
import TrackReplay from "./trackReplay/trackReplay.vue";
import { useRoute } from "vue-router";
import VideoPlayback from "./videoPlayback/videoPlayback.vue";
const route = useRoute();
const query = reactive({
  id: (route.query.id || "") as string,
});

const tabs = reactive({
  active: "track_replay",
  options: [
    { label: "轨迹回放", value: "track_replay" },
    { label: "历史视频回放", value: "video_playback" },
  ],
});
const handleTabItem = (tab: typeof tabs.options[0]) => {
  tabs.active = tab.value;
};
</script>
<script lang="ts">
export default defineComponent({
  name: "track-video",
});
</script>
<style lang="scss">
.track-video {
  display: flex;
  flex-direction: column;
  height: 100%;
  &-tabs {
    @include wh(100%, 40px);
    display: flex;
    column-gap: 20px;
    padding: 0 20px;
    box-sizing: border-box;
    font-weight: bold;
    &-item {
      @include sc(16px, #9f9fa4);
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      position: relative;
      &.is-active {
        @include sc(18px, rgb(36, 40, 89));
        &::after {
          content: "";
          position: absolute;
          bottom: 0px;
          left: 50%;
          @include wh(49px, 3px);
          transform: translateX(-50%);
          background-color: #5964fb;
        }
      }
    }
  }
  &-content {
    flex: 1;
    padding-top: 10px;
  }
}
</style>
