<template>
  <section class="car-capture">
    <div
      class="car-capture-left"
      ref="rootRef"
    >
      <x-select
        v-model:value="selectModel"
        :popupContainer="rootRef"
        :options="selectOpts"
        showSearch
        placeholder="请选择/输入车牌号"
        @change="selectChange"
      />
      <div
        class="car-capture-list"
        v-xloading="loading"
      >
        <div
          :class="['car-capture-list-item', { 'is-active': selectedCapture?.id === item.id }]"
          v-for="item in captureList"
          :key="item.id"
          @click="handleCaptureItem(item)"
        >
          <img
            class="car-capture-list-item-img"
            :src="item.picUrl"
          />
          <div class="car-capture-list-item-info">
            <span>{{ item.picUrl ? item.picUrl.match(/\/([^\/]+)\.(png|jpg)$/)?.at(1) : "" }}</span>
          </div>
        </div>
      </div>
    </div>
    <div class="car-capture-right">
      <div class="car-capture-right-btns">
        <div class="form-item">
          <label>类型</label>
          <el-select
            v-model="captureType.active"
            filterable
            placeholder="请选择类型"
            style="width: 180px"
            :disabled="!selectedCapture"
            @change="handleTypeChange"
          >
            <el-option
              v-for="item in carCaptureTypeOpts"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </div>
        <x-button
          :text="`上传 ${uploading ? loadPercent : ''}`"
          :loading="uploading"
          :disabled="!selectedCapture"
          @click="upload"
        />

        <x-button
          text="手动上传"
          @click="handupload"
        />
      </div>
      <canvas
        ref="canvasRef"
        :class="['car-capture-right-canvas', { 'is-drawing': selectedCapture }]"
        @mousedown="drawStart"
        @mousemove="drawMoving"
        @mouseup="drawEnd"
        @mouseout="drawEnd"
      />
    </div>
    <MarkingPage v-model="markingVisible" />
  </section>
</template>

<script lang="ts" setup>
import { carCaptureTypeOpts } from "@/services/wsconfig";
import { getLocalStorage } from "@/assets/ts/storage";
import XButton from "@/components/x-button.vue";
import XMessage from "@/components/x-message";
import XRadioButton from "@/components/x-radio-button.vue";
import XSelect, { type OptionsType } from "@/components/x-select.vue";
import { cameraSnapAndUploadTemp, getVehAndVinList } from "@/services/api";
import type { CameraSnapAndUploadTempResponse } from "@/services/type";
import axios from "axios";
import { defineComponent, onMounted, reactive, ref } from "vue";
import MarkingPage from "./marking/index.vue";
/** 请求中断器 */
let abortController: AbortController | undefined;
const rootRef = ref<HTMLDivElement>();
const selectOpts = ref<OptionsType>([]);
const selectModel = ref<string>();
const loading = ref<boolean>(false);
const captureList = ref<CameraSnapAndUploadTempResponse[]>([]);
const selectedCapture = ref<CameraSnapAndUploadTempResponse>();
const captureType = reactive({
  active: 1,
  options: carCaptureTypeOpts,
});
const markingVisible = ref<boolean>(false);
const handupload = () => {
  markingVisible.value = true;
};

const selectChange = (val: string) => {
  console.log("%c selectChange:", "color:blue;font-weight:bold;", val);
  if (abortController) {
    abortController.abort();
    loading.value = false;
  }
  getCaptrueList();
};

const getCarList = async () => {
  const res = await getVehAndVinList();
  selectOpts.value = res.map((v) => ({
    label: v.vehicleNo,
    value: v.vehicleNo,
  }));
};
getCarList();

const handleTypeChange = (val: number) => {
  clearCanvas();
  drawImage();
  if (drawData.startX !== drawData.endX && drawData.startY !== drawData.endY) {
    drawRect();
  }
};

const getCaptrueList = async () => {
  if (!selectModel.value) return;
  if (abortController) {
    abortController.abort();
    loading.value = false;
  }
  abortController = new AbortController();
  loading.value = true;
  try {
    const res = await cameraSnapAndUploadTemp({ vehNo: selectModel.value }, { signal: abortController?.signal });
    captureList.value = res;
  } finally {
    loading.value = false;
  }
};

const handleCaptureItem = (item: typeof captureList.value[0]) => {
  selectedCapture.value = item;
  reset();
  imgDiv.src = item.picUrl as string;
  drawPattern.value = "rect";
};

/** Canvas 相关 */
const canvasRef = ref<HTMLCanvasElement>();
let canvasCtx: CanvasRenderingContext2D;
let imgDiv: HTMLImageElement;
const drawPattern = ref<"rect">();
const isDrawing = ref<boolean>(false);
const drawData = {
  startX: 0,
  startY: 0,
  endX: 0,
  endY: 0,
};
onMounted(() => {
  const scale = (rootRef.value!.clientHeight - 60) / 1080;
  canvasRef.value!.style.width = 1920 * scale + "px";
  canvasRef.value!.style.height = 1080 * scale + "px";

  canvasCtx = canvasRef.value!.getContext("2d")!;
  imgDiv = new Image();
  imgDiv.crossOrigin = "anonymous";
  imgDiv.src = "";
  imgDiv.onload = drawImage;
});

const drawImage = () => {
  const scale = (rootRef.value!.clientHeight - 60) / imgDiv.height;
  canvasRef.value!.width = imgDiv.width * scale;
  canvasRef.value!.height = imgDiv.height * scale;
  canvasCtx.drawImage(imgDiv, 0, 0, canvasRef.value!.width, canvasRef.value!.height);
};

const drawStart = (e: MouseEvent) => {
  isDrawing.value = true;
  drawData.startX = e.pageX - canvasRef.value!.offsetLeft;
  drawData.startY = e.pageY - canvasRef.value!.offsetTop;
  if (drawPattern.value === "rect") {
    canvasCtx.moveTo(drawData.startX, drawData.startY);
  }
};
const drawMoving = (e: MouseEvent) => {
  if (!isDrawing.value) return false;
  if (drawPattern.value === "rect") {
    drawData.endX = e.pageX - canvasRef.value!.offsetLeft;
    drawData.endY = e.pageY - canvasRef.value!.offsetTop;
    clearCanvas();
    drawImage();
    drawRect();
  }
};
const drawEnd = (e: MouseEvent) => {
  if (!isDrawing.value) return false;
  isDrawing.value = false;
  if (drawPattern.value === "rect") {
    drawData.endX = e.pageX - canvasRef.value!.offsetLeft;
    drawData.endY = e.pageY - canvasRef.value!.offsetTop;
    // drawRect();
    isDrawing.value = false;
  }
};
/** 绘制矩形 */
const drawRect = (data?: typeof drawData) => {
  canvasCtx.lineWidth = 4;
  canvasCtx.strokeStyle = "#ef822f";
  canvasCtx.beginPath();
  let startX: number, startY: number, endX: number, endY: number;

  if (data) {
    startX = data.startX;
    startY = data.startY;
    endX = data.endX;
    endY = data.endY;
  } else {
    startX = drawData.startX;
    startY = drawData.startY;
    endX = drawData.endX;
    endY = drawData.endY;
  }
  const rectX = Math.min(startX, endX);
  const rectY = Math.min(startY, endY);
  const rectWidth = Math.abs(endX - startX);
  const rectHeight = Math.abs(endY - startY);

  canvasCtx.strokeRect(rectX, rectY, rectWidth, rectHeight);
  canvasCtx.closePath();
  canvasCtx.stroke();

  const typeObj = captureType.options.find((opt) => opt.value === captureType.active);
  if (typeObj) {
    const label = typeObj.label;
    const paddingX = 8;
    const paddingY = 4;
    const minFontSize = 12;
    let fontSize = rectHeight - paddingY * 2;
    canvasCtx.font = `${fontSize}px 'Microsoft YaHei', Arial`;
    let textMetrics = canvasCtx.measureText(label);
    let textWidth = textMetrics.width;

    while ((textWidth > rectWidth - paddingX * 2 || fontSize > rectHeight - paddingY * 2) && fontSize > minFontSize) {
      fontSize -= 1;
      canvasCtx.font = `${fontSize}px 'Microsoft YaHei', Arial`;
      textMetrics = canvasCtx.measureText(label);
      textWidth = textMetrics.width;
    }
    if (fontSize < minFontSize) {
      fontSize = minFontSize;
      canvasCtx.font = `${fontSize}px 'Microsoft YaHei', Arial`;
      textMetrics = canvasCtx.measureText(label);
      textWidth = textMetrics.width;
    }
    const textX = rectX + paddingX;
    const textY = rectY - 4;

    // 标签背景
    const labelBgWidth = textWidth + paddingX * 2;
    const labelBgHeight = fontSize + paddingY * 2;
    const labelBgX = rectX;
    const labelBgY = rectY - labelBgHeight - 4;
    const radius = 0;
    canvasCtx.beginPath();
    canvasCtx.moveTo(labelBgX + radius, labelBgY);
    canvasCtx.lineTo(labelBgX + labelBgWidth - radius, labelBgY);
    canvasCtx.quadraticCurveTo(labelBgX + labelBgWidth, labelBgY, labelBgX + labelBgWidth, labelBgY + radius);
    canvasCtx.lineTo(labelBgX + labelBgWidth, labelBgY + labelBgHeight - radius);
    canvasCtx.quadraticCurveTo(
      labelBgX + labelBgWidth,
      labelBgY + labelBgHeight,
      labelBgX + labelBgWidth - radius,
      labelBgY + labelBgHeight
    );
    canvasCtx.lineTo(labelBgX + radius, labelBgY + labelBgHeight);
    canvasCtx.quadraticCurveTo(labelBgX, labelBgY + labelBgHeight, labelBgX, labelBgY + labelBgHeight - radius);
    canvasCtx.lineTo(labelBgX, labelBgY + radius);
    canvasCtx.quadraticCurveTo(labelBgX, labelBgY, labelBgX + radius, labelBgY);
    canvasCtx.closePath();
    canvasCtx.fillStyle = "#ef822f";
    canvasCtx.fill();
    canvasCtx.fillStyle = "#fff";
    const ascent = textMetrics.actualBoundingBoxAscent || fontSize * 0.8;
    const descent = textMetrics.actualBoundingBoxDescent || fontSize * 0.2;
    const textHeight = ascent + descent;
    const textBaseY = labelBgY + (labelBgHeight + textHeight) / 2 - descent;
    canvasCtx.fillText(label, labelBgX + paddingX, textBaseY);
  }
};
const clearCanvas = () => {
  canvasCtx.clearRect(0, 0, canvasRef.value!.width, canvasRef.value!.height);
};

const loadPercent = ref<number>(0);
const uploading = ref<boolean>(false);
const upload = () => {
  if (!captureType.active) return XMessage("error", "请选择类型");
  if (!selectedCapture.value?.id) return XMessage("error", "请选择图片");
  canvasRef.value!.toBlob(
    (blob) => {
      if (blob) {
        console.log("%c blob:", "color:blue;font-weight:bold;", { blob });
        uploading.value = true;
        axios
          .post(import.meta.env.VITE_BASE_URL + "/vehicle/control/common/handlePicTemp", blob, {
            headers: { "Content-Type": "multipart/form-data", token: getLocalStorage("token") },
            transformRequest: [
              function (file) {
                const formData = new FormData();
                formData.append("file", file);
                formData.append("id", String(selectedCapture.value?.id));
                formData.append("type", String(captureType.active));
                return formData;
              },
            ],
            onUploadProgress: (progressEvent) => {
              console.log("%c onUploadProgress:", "color:blue;font-weight:bold;", progressEvent);
              loadPercent.value = progressEvent.total ? ((progressEvent.loaded / progressEvent.total) * 100) | 0 : 0;
            },
          })
          .then((res) => {
            uploading.value = false;
            reset();
            XMessage("success", "上传成功");
          });
      }
    },
    "image/jpeg",
    0.95
  );
};

/** 重置 */
const reset = () => {
  clearCanvas();
  drawImage();
};
</script>
<script lang="ts">
export default defineComponent({
  name: "car-capture",
});
</script>
<style lang="scss">
.car-capture {
  display: flex;
  column-gap: 10px;
  padding: 10px 0 10px 10px;
  height: 100%;
  .form-item {
    display: flex;
    align-items: center;
    gap: 10px;
  }
  &-left {
    width: 10%;
    min-width: 200px;
  }
  &-list {
    position: relative;
    min-height: 150px;
    padding-top: 10px;
    &-item {
      display: flex;
      align-items: center;
      column-gap: 5px;
      border-radius: 5px;
      cursor: pointer;
      &-img {
        width: 50px;
        height: 50px;
        object-fit: contain;
      }
      &:hover {
        background-color: #f5f5f5;
      }
      &.is-active {
        background-color: #5964fb;
        color: #fff;
      }
    }
  }
  &-right {
    flex: 1;
    height: 100%;
    padding: 0 10px 0 10px;
    border-left: 1px solid #eee;
    &-btns {
      display: flex;
      column-gap: 20px;
      padding-bottom: 5px;
    }
    &-canvas {
      border: 1px solid #eee;
      &.is-drawing {
        cursor: pointer;
      }
    }
  }
}
</style>
