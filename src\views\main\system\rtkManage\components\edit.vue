<template>
  <x-drawer
    :title="$t('editRTK')"
    :visible="props.show"
    :btnOption="{ position: 'center' }"
    footerType="following"
    bodyPadding="20px 0 20px 20px"
    @update:visible="updateVisible"
    @confirm="editRTKConfirm"
    width="672px"
  >
    <div class="content" ref="contentRef">
      <x-form
        ref="formRef"
        :model="form"
        :rules="formRules"
        class="content-form"
      >
        <x-form-item :label="$t('enterprise')" name="entId" labelFlex="80px">
          <x-tree-select
            v-model:value="form.entId"
            :treeData="formOptions.company"
            :popupContainer="contentRef"
            :placeholder="$t('PEnterEnt')"
            showSearch
          />
        </x-form-item>
        <x-form-item
          :label="$t('rtkAccount')"
          name="rtkAccount"
          labelFlex="80px"
        >
          <x-input
            v-model:value="form.rtkAccount"
            :placeholder="$t('PEnterRTK')"
            :maxlength="15"
          />
        </x-form-item>
        <x-form-item
          :label="$t('rtkPassword')"
          name="rtkPassword"
          labelFlex="80px"
        >
          <x-input
            v-model:value="form.rtkPassword"
            :placeholder="$t('PEnterRTKPassword')"
            :maxlength="15"
          />
        </x-form-item>
        <x-form-item
          :label="$t('operator')"
          name="netOperator"
          labelFlex="80px"
        >
          <x-select
            v-model:value="form.netOperator"
            :options="formOptions.netOperator"
            :popupContainer="contentRef"
            :placeholder="$t('PSelectOperator')"
            allowClear
            style="width: 240px"
          />
        </x-form-item>
        <x-form-item
          :label="$t('registerDate')"
          name="registerDate"
          labelFlex="80px"
        >
          <DatePicker
            v-model:value="form.registerDate"
            :popupContainer="contentRef"
            format="YYYY-MM-DD"
            :placeholder="$t('PSelectRegisterStartDate')"
            style="width: 240px"
          />
        </x-form-item>
      </x-form>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { ref, reactive, watch } from "vue";
import { compTree, getRTKDetail, editRTK, rtkRepeat } from "@/services/api";
import { netOperatorType } from "@/assets/ts/config";
import { resTreeToXTree, debounce } from "@/assets/ts/utils";
import type { TreeItemType } from "@/components/x-tree/tree.vue";
import { formatDateTime } from "@/assets/ts/dateTime";
import { DatePicker } from "@/components/x-date-picker";
import XDrawer from "@/components/x-drawer.vue";
import XForm from "@/components/x-form.vue";
import XFormItem from "@/components/x-form-item.vue";
import XInput from "@/components/x-input.vue";
import XSelect from "@/components/x-select.vue";
import XTreeSelect from "@/components/x-tree-select.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("rtkManage");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const formRef = ref<any>();
const contentRef = ref<any>();

const form = reactive({
  id: "",
  entId: "",
  rtkAccount: "",
  rtkPassword: "",
  netOperator: "",
  registerDate: "",
});

const formOptions = reactive({
  company: [] as TreeItemType[],
  netOperator: netOperatorType,
});

const repeatRTK = async (value: string) => {
  const params = {
    id: form.id,
    rtkAccount: value,
  };
  const result = await rtkRepeat(params);
  return !result?.repeat;
};

const formRules = reactive({
  entId: [["required", $t("entNameRequired")]],
  rtkAccount: [
    ["required", $t("rtkAccountRequired")],
    [debounce(repeatRTK), $t("rtkAccountExist")],
  ],
  rtkPassword: [["required", $t("rtkPasswordRequired")]],
});

watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      const { id, entId, rtkAccount, rtkPassword, netOperator, registerDate } =
        await getRTKDetail(props.id);
      form.id = id;
      form.entId = Number(entId);
      form.rtkAccount = rtkAccount;
      form.rtkPassword = rtkPassword;
      form.netOperator = netOperator || "";
      form.registerDate = registerDate
        ? formatDateTime(registerDate, "YYYY-MM-DD")
        : "";
      formOptions.company = reactive(resTreeToXTree([await compTree()]));
    }
  }
);

const editRTKConfirm = async () => {
  if (await formRef.value.asyncValidate()) {
    await editRTK({
      id: form.id,
      entId: Number(form.entId),
      rtkAccount: form.rtkAccount,
      rtkPassword: form.rtkPassword,
      netOperator: form.netOperator ? form.netOperator : null,
      registerDate: form.registerDate,
    });
    Message("success", $t("editSuccess"));
    emits("update:show", false);
    emits("confirm");
  }
};
</script>

<style lang="scss" scoped>
.content {
  &-form {
    padding: 20px 20px 0px 20px;
    margin-right: 20px;
    border-radius: 8px;
    background: #fff;
  }
}
</style>
