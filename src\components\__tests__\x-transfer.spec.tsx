import { describe, it, expect } from "vitest";
import { shallowMount, mount } from '@vue/test-utils';
import { nextTick } from "vue";
import XTransfer from '@/components/x-transfer.vue';
import XCheckbox from '@/components/x-checkbox.vue';
import XTree from '@/components/x-tree';

const baseProps = { 
  dataSource: [
    {
      title: 'title-1',
      value: 'value-1',
      checked: false,
      children: [
        {
          title: 'title-1-1',
          value: 'value-1-1',
          checked: false, // 接口获取是true => 组件左侧显示 未勾选 && 置灰
          disabled: true,
        },
        {
          title: 'title-1-2',
          value: 'value-1-2',
          checked: false,
        }
      ]
    },
    {
      title: 'title-2',
      value: 'value-2',
      checked: false,
    }
  ],
  targetSource: [
    {
      title: 'title-1-1',
      value: 'value-1-1',
      checked: false, // 接口获取是true => 组件右侧显示 未勾选 && 未置灰
      disabled: false,
    }
  ],
}

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XTransfer,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = shallowMount(XTransfer,{
      props: {
        ...baseProps,
        showSelectAll: false,
        titles: ['title01','title02'],
        locale: {
          itemUnit: "单位名称",
          searchPlaceholder: "搜索文案",
        }
      },
    });
    expect(wrapper.findAllComponents(XCheckbox).length).toBe(0); // 验证showSelectAll
    const [leftTitle,rightTitle] = wrapper.findAll('.content-top-title');
    expect(leftTitle.text()).toBe('title01');
    expect(rightTitle.text()).toBe('title02');
    const [leftNumber,rightNumber] = wrapper.findAll('.content-top-number');
    const [leftSelected,rightSelected] = wrapper.findAll('.content-bottom-selected');
    expect(leftNumber.text().includes('单位名称')).toBe(true);
    expect(rightNumber.text().includes('单位名称')).toBe(true);
    expect(leftSelected.text().includes('单位名称')).toBe(true);
    expect(rightSelected.text().includes('单位名称')).toBe(true);
    expect(wrapper.vm._locale.searchPlaceholder).toBe('搜索文案');
  });
});


// 尝试点击左侧全选，并点击转换到右侧，然后验证dataSource或targetSource
// 但尝试各种方式方法都无法侦测到变化
// 应该是dataSource和targetSource已经“脱离”props 或 无法深层侦测 的原因导致无法侦测到变化
// 后续可用emits事件来判断，但目前没有加则先不做。
describe('Events', () => {
  it('验证 点击勾选左侧全选 并 转移到右侧', () => {
    const wrapper = mount(XTransfer,{
      props: { ...baseProps }
    });
    const [leftAllCheckbox, rightAllCheckbox] = wrapper.findAll('.content-bottom-select-all input');
    leftAllCheckbox.trigger('change'); // 已证明有效
    wrapper.find('.x-transfer-center .center-right-arrow').trigger('click'); // 已证明有效
    // setTimeout(() => {
      // expect(wrapper.emitted()['update:targetSource']).toBe(false);
      // expect(wrapper.find('.x-transfer-right').findComponent(XTree).props()).toBe('')
      // expect(wrapper.find('.x-transfer-left').findComponent(XTree).props()).toBe('')
      // expect(wrapper.props().targetSource).toBe('');
      // expect(wrapper.props().dataSource).toBe('');
      // expect(wrapper.vm.rightSource).toBe('');
    // }, 0);

  }); 
})
