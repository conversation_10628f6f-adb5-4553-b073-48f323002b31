<template>
  <div class="task" ref="taskCardRef">
    <div class="task-card" v-for="(item, index) in form.taskList" :key="index">
      <div class="task-card-title">
        <div class="task-card-title-left">班次{{ index + 1 }}</div>
        <div
          class="task-card-title-right"
          v-if="form.taskList.length > 1"
          @click="delTask(index)"
        >
          ✕
        </div>
      </div>
      <div class="task-card-item">
        <div class="task-card-item-title">
          <div class="required">时间</div>
        </div>
        <div class="task-card-item-content bottom-bordered">
          <TimeRangePicker
            v-model:value="item.opDate"
            :popupContainer="taskCardRef"
            :placeholder="['开始时间', '结束时间']"
            @update:value="updateTimeRange($event, index)"
            @click="clickTimeRanger(index)"
            format="HH:mm"
            allowClear
            style="width: 240px"
          />
        </div>
      </div>
      <!-- <div class="task-card-item">
        <div class="task-card-item-title">
          <div class="left required">路线</div>
          <div class="right" @click="resetTaskRoutes(index)">
            <x-icon name="reload" />
            <span>重置</span>
          </div>
        </div>
        <div class="task-card-item-content bottom-bordered">
          <task-routes
            :dataSource="item.routeList"
            :routeList="props.routeList"
            :container="taskCardRef"
            :maxRouteLength="999"
            @update:value="updateRouteList($event, index)"
          />
        </div>
      </div> -->
      <div class="task-card-item">
        <div class="task-card-item-title">
          <div class="left required">路线模版</div>
        </div>
        <div class="task-card-item-content bottom-bordered">
          <x-select
            v-model:value="item.templateId"
            :options="formOptions.routeTemplate"
            :popupContainer="props.container"
            style="width: 500px"
          />
        </div>
      </div>
      <div class="task-card-item">
        <div class="task-card-item-title">最后停车点</div>
        <div class="task-card-item-content parking-point">
          <x-icon name="parking_point_end" width="24" height="24" />
          <x-select
            v-model:value="item.parkPosition"
            :options="props.stationList"
            :popupContainer="props.container"
            placeholder="请选择站点"
            allowClear
            style="width: 240px; margin-left: 10px"
          />
          <x-select
            v-model:value="item.cleanType"
            :options="getTaskTypeList(item.parkPosition)"
            :popupContainer="props.container"
            placeholder="请选择车位"
            allowClear
            style="width: 140px; margin-left: 10px"
          />
        </div>
      </div>
      <div class="task-card-item">
        <div class="task-card-item-title">任务执行完是/否关机</div>
        <div class="task-card-item-content">
          <x-radio
            v-model:value="item.shutDown"
            :options="formOptions.shutDown"
            :gap="16"
          />
        </div>
      </div>
    </div>
    <div class="task-button">
      <x-button
        type="linearBlue"
        text="新增班次"
        icon="button_add"
        @click="addTask"
      />
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref, watch } from "vue";
import { TimeRangePicker } from "@/components/x-time-picker";
import { machineStatusType } from "@/assets/ts/config";
import xButton from "@/components/x-button.vue";
import xSelect from "@/components/x-select.vue";
import xRadio from "@/components/x-radio.vue";
import Message from "@/components/x-message";

export type TaskInfo = {
  opDate: string[];
  executeTimeStart?: string;
  executeTimeEnd?: string;
  /** 站点类型，1：垃圾点，2：充电点，3：加水点4：停车点 */
  parkPosition: number;
  cleanType: string;
  /** 是否关机 0:否，1:是 */
  shutDown: number;
  /** 任务类型，0:每天，1-7 对应着星期一到星期天， */
  executeDay?: number;
  templateId: number;
};
interface SelectType {
  label: string;
  value: string | number;
  [x: string]: any;
}

const props = defineProps({
  container: {
    type: HTMLElement,
  },
  taskList: {
    type: Array as () => TaskInfo[],
    default: () => [],
  },
  routeTemplate: {
    type: Array as () => SelectType[],
    default: () => [],
  },
  stationList: {
    type: Array as () => SelectType[],
    default: () => [],
  },
  proAreaId: {
    type: String,
  },
});

const emits = defineEmits(["update:value"]);

const taskCardRef = ref<any>();

const getDefaultTask = () => {
  return {
    opDate: ["", ""],
    parkPosition: 0,
    cleanType: "",
    shutDown: 0,
    templateId: -1,
  } as TaskInfo;
};

const form = reactive({
  taskList: [getDefaultTask()],
});

const formOptions = reactive({
  shutDown: machineStatusType,
  routeTemplate: props.routeTemplate,
});

// 根据站点获车位
const getTaskTypeList = (value: number) => {
  const selectedSite = props.stationList.find((item) => item.value === value);
  return selectedSite
    ? selectedSite.cleanTypeList.map((item: any) => ({
        label: item.value,
        value: item.key,
      }))
    : [];
};

// 点击时间 页面滚动至当前组件顶部
const clickTimeRanger = (index: number) => {
  const taskDomLists = document.getElementsByClassName("task-card");
  taskDomLists[index].scrollIntoView({
    behavior: "smooth",
    block: "start",
  });
};

// 修改时间
const updateTimeRange = (value: string[], index: number) => {
  const isValid = isTimeRangeNotReapeat(form.taskList);
  if (!isValid) {
    Message("error", "班次时间段不能重复，请重新选择！");
    form.taskList[index].opDate = ["", ""];
  } else {
    const isDurationValid = isTimeDurationValid(form.taskList[index].opDate);
    if (!isDurationValid) {
      Message("error", "班次时间段最少半小时，请重新选择！");
      form.taskList[index].opDate = ["", ""];
    }
  }
};
// 时间转时间戳
const timeToMilliseconds = (timeStr: string) => {
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 3600000 + minutes * 60000;
};
// 校验时长是否有效
const isTimeDurationValid = (opDate: string[]) => {
  const startTime = timeToMilliseconds(opDate[0]);
  const endTime = timeToMilliseconds(opDate[1]);
  return endTime - startTime >= 1800000; // 30min
};
// 校验时间段是否重复
const isTimeRangeNotReapeat = (data: any) => {
  for (let i = 0; i < data.length; i++) {
    const { opDate } = data[i];
    const startTime = timeToMilliseconds(opDate[0]);
    const endTime = timeToMilliseconds(opDate[1]);
    for (let j = 0; j < data.length; j++) {
      if (i !== j) {
        const { opDate: compareOpDate } = data[j];
        const compareStartTime = timeToMilliseconds(compareOpDate[0]);
        const compareEndTime = timeToMilliseconds(compareOpDate[1]);
        if (
          (startTime >= compareStartTime && startTime < compareEndTime) ||
          (endTime > compareStartTime && endTime <= compareEndTime) ||
          (startTime <= compareStartTime && endTime >= compareEndTime)
        ) {
          return false;
        }
      }
    }
  }
  return true;
};

// 新增班次
const addTask = () => {
  form.taskList.push(getDefaultTask());
  emits("update:value", form.taskList);
};

// 删除任务
const delTask = (index: number) => {
  form.taskList.splice(index, 1);
  emits("update:value", form.taskList);
};

watch(
  () => form.taskList,
  (newV) => {
    form.taskList = newV;
    emits("update:value", newV);
  },
  {
    immediate: true,
    deep: true,
  }
);

watch(
  () => props.taskList,
  (newV) => (form.taskList = newV)
);
</script>

<style lang="scss" scoped>
.task {
  &-card {
    margin-top: 10px;
    padding-bottom: 15px;
    border-radius: 4px;
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    &-title {
      display: flex;
      justify-content: space-between;
      @include sc(14px, #383838) {
        font-weight: bold;
      }
      @include wh(100%, 48px) {
        line-height: 48px;
      }
      background: linear-gradient(
        180deg,
        rgba(89, 100, 251, 0.11),
        rgba(191, 211, 250, 0) 100%
      );
      border-radius: 4px;
      &-right {
        @include sc(10px, #9f9fa4);
        cursor: pointer;
      }
    }
    &-title,
    &-item {
      padding: 0 20px;
    }
    &-item {
      &-title {
        display: flex;
        justify-content: space-between;
        margin: 10px 0;
        @include sc(14px, #383838);
        .right {
          cursor: pointer;
          span {
            margin-left: 5px;
            @include sc(12px, #5964fb);
          }
        }
      }
      &-content {
        &.parking-point {
          @include ct-f(y);
          padding: 5px 0 15px 0;
          border: none;
        }
        &.bottom-bordered {
          padding-bottom: 30px;
          border-bottom: 1px solid #e5e5e5;
        }
      }
    }
  }
  &-button {
    margin-top: 10px;
  }
  .required::before {
    content: "*";
    color: red;
    margin-right: 5px;
  }
  :deep(.x-radio-checked + span) {
    color: #383838 !important;
  }
}
</style>
