import { useMainStore } from "@/stores/main";
import { getLocalStorage, setLocalStorage } from "@/assets/ts/storage";
import { router } from "@/router/router";
import Message from "@/components/x-message";
import { uuid } from "@/assets/ts/utils";
const { socket } = useMainStore();

const send = (data: Record<string, any>, type: string, timeout = 20000) =>
  new Promise((resolve, reject) => {
    // 添加超时处理
    const timer = setTimeout(() => {
      console.error(`%csend-timeout[${type}]`, "color:red;font-weight:bold;", {
        request: data,
        message: "请求超时，服务器未响应",
      });
      socket.instance?.eventMap?.delete(data.uuid);
      reject(new Error("请求超时，服务器未响应"));
    }, timeout); // 10秒超时

    socket.instance?.send(JSON.stringify(data));
    socket.instance?.eventMap?.set(data.uuid, (resData: any) => {
      clearTimeout(timer); // 清除超时定时器
      if (resData.result >= 500) {
        console.error(`%csend-err[${type}]`, "color:red;font-weight:bold;", {
          request: data,
          response: resData,
        });
        Message("error", resData.message);
        // ws快于http,所以这里也要判断
        if (resData.result >= 10000) {
          useMainStore().autoLogout();
          router.push({ name: "login" });
        }
        reject(resData);
      } else {
        console.log(`%csend[${type}]`, "color:green;font-weight:bold;", {
          request: data,
          response: resData,
        });
        resolve(resData);
      }
      socket.instance?.eventMap?.delete(data.uuid);
    });
  });

// ----------------实时相关接口

// 开启基础状态传输，用于地图基础显示
export const onBaseStatus = () =>
  send(
    {
      uuid: uuid(),
      type: 6,
      content: ["on"],
      messageType: "commonMessage",
    },
    "onBaseStatus"
  );

// 关闭基础状态传输，用于跳转到其它页面时调用
export const offBaseStatus = () =>
  send(
    {
      uuid: uuid(),
      type: 6,
      content: ["off"],
      messageType: "commonMessage",
    },
    "offBaseStatus"
  );

// 关闭清空订阅车辆详情状态传输，用于跳转到其它页面时调用
export const offAllStatus = () =>
  send(
    {
      uuid: uuid(),
      type: 7,
      messageType: "commonMessage",
    },
    "offAllStatus"
  );

export const sendToken = () =>
  send(
    {
      uuid: uuid(),
      type: 1,
      content: [getLocalStorage("token")],
      messageType: "commonMessage",
    },
    "sendToken"
  );

// 大屏 - 切换项目
export const toggleProject = (proId: string) =>
  send(
    {
      uuid: uuid(),
      type: 10,
      content: [proId],
      messageType: "commonMessage",
      calculateMode: 1,
    },
    "toggleProject"
  );

// 大屏 - 作业概览汇总 - 切换日期
export const toggleWorkViewDate = (proId: string, dateType: number) =>
  send(
    {
      uuid: uuid(),
      type: 10,
      content: [proId],
      messageType: "commonMessage",
      calculateMode: dateType, // 1当天 2昨天 3近七天 4近三十天
    },
    "toggleWorkViewDate"
  );

export const addCar = (idArr: string[]) =>
  send(
    {
      uuid: uuid(),
      type: 2,
      content: idArr,
      messageType: "commonMessage",
    },
    "addCar"
  );

export const delCar = (idArr: string[]) =>
  send(
    {
      uuid: uuid(),
      type: 4,
      content: idArr,
      messageType: "commonMessage",
    },
    "delCar"
  );

// ----------------任务相关接口
// 下发任务
export const task = (
  manufacturer: string, // 车辆厂商
  deviceId: string,
  speed?: number,
  executeTimes?: number,
  content?: string, // 任务内容
  taskType: 0 | 1 = 1 // 结束0 开始1
) => {
  const baseTask = {
    uuid: uuid(),
    messageType: "taskMessage",
    type: "task",
    taskType,
    deviceId,
  };
  if (manufacturer === "ecar") {
    return send(
      {
        ...baseTask,
        speed,
        content,
        executeTimes,
      },
      "task"
    );
  } else {
    return send(baseTask, "task");
  }
};
export const task2 = (params: {
  deviceId: string;
  /** 结束0 开始1 默认1 */
  taskType?: number;
  /** 执行次数 默认1 */
  executeTimes?: number;
  sweepMode?: number;
  cleanType?: string;
  sweepDirection?: number; //0:靠右行驶 1:靠左行驶 2:靠中行驶 3:自适应方向行驶
  stationType?: number;
  tempId?: string;
  /** 速度设置 默认2 */
  speed?: number;
  points?: {
    x: string | number;
    y: string | number;
    /** true为gps，false为笛卡尔坐标 */
    pointType: boolean;
    /** true表示本组点位可组成线，false表示本组点位可组成多边形 */
    pointGroupType: boolean;
  }[][];
  stationId?: string;
  parkId?: string;
  /** 录制路线的车端路径列表 */
  routePathList?: string[];
  /** 自定义3.0 */
  customCompleteSingleTaskList?: {
    /** 路线车端路径 */
    routePath?: string;
    /** 路线是否清扫 */
    routeClean?: boolean;
    /** 站点id */
    stationId?: string;
    /** 站点类型 */
    stationType?: number;
    /** 车位id */
    parkId?: string;
    /** 自定义绘制的区块或路线 */
    points?: {
      x?: string | number;
      y?: string | number;
      heading?: number;
      /** true为gps，false为笛卡尔坐标 */
      pointType?: boolean;
      /** true表示本组点位可组成线，false表示本组点位可组成多边形 */
      pointGroupType?: boolean;
    }[];
    routeNos?: string[]; //结构化路线编号，与points互斥
    pathPoints?: boolean; //是否是结构化点位
  }[];
}) => {
  Object.entries(params).forEach(([key, value]) => {
    // @ts-ignore
    if (!value || (Array.isArray(value) && !value.length)) delete params[key];
  });
  return send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 1,
      speed: 2,
      ...params,
    },
    "task2"
  );
};

// 大屏下发任务
export const screenTask = (deviceId: string, tempId: string, stationId: string = "") => {
  return send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 1, // 结束0 开始1
      deviceId,
      tempId,
      stationId,
      speed: 2,
    },
    "task"
  );
};

// 开机上电
export const openMachine = (
  id: string,
  manufacturer: string // 车辆厂商
) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: manufacturer === "PA" ? "PADigloSet" : "digIoSet",
      content: 1,
      deviceId: id,
    },
    "openMachine"
  );

// 关机下电
export const closeMachine = (
  id: string,
  manufacturer: string // 车辆厂商
) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: manufacturer === "PA" ? "PADigloSet" : "digIoSet",
      content: 0,
      deviceId: id,
    },
    "closeMachine"
  );

// 暂停任务
export const pauseTask = (id: string) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 2,
      deviceId: id,
    },
    "pauseTask"
  );

// 继续任务
export const continueTask = (id: string) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 3,
      deviceId: id,
    },
    "continueTask"
  );

// 取消任务
export const stopTask = (id: string) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 0,
      deviceId: id,
    },
    "stopTask"
  );

// ----------------告警相关接口
// 告警消息 启动连接
export const openAlarmList = (content: string, taskType: string, firstId: string) =>
  send(
    {
      uuid: uuid(),
      messageType: "messageMessage",
      type: 1, // 0：关闭 1：开启
      content: content, //0：全部 1：缺水 2：缺电 3：倒垃圾 4：下发指令 5:事件
      taskType: taskType, //0：未读 1：已读 2：全部
      remark: firstId || "", // 上一次messageList中第一个id 用于分页
    },
    "openAlarmList"
  );

// 告警消息 关闭连接
export const closeAlarmList = () =>
  send(
    {
      uuid: uuid(),
      messageType: "messageMessage",
      type: 0,
    },
    "closeAlarmList"
  );

// ----------------控制相关接口
// 速度设置
export const settingSpeed = (id: string, speed: number) =>
  send(
    {
      uuid: uuid(),
      messageType: "taskMessage",
      type: "task",
      taskType: 4,
      deviceId: id,
      speed: speed,
    },
    "speed"
  );

// 获取充电桩列表
export const openChargingStatus = (idArr: string[]) =>
  send(
    {
      uuid: uuid(),
      type: 8,
      content: idArr,
      messageType: "commonMessage",
    },
    "openChargingStatus"
  );

// 关闭充电桩列表
export const closeChargingStatus = () =>
  send(
    {
      uuid: uuid(),
      type: 9,
      messageType: "commonMessage",
    },
    "closeChargingStatus"
  );

/** 开启行车路线推送 */
export const onFahrtroutes = (deviceId: string) => {
  return send(
    {
      uuid: uuid(),
      messageType: "showPathMessage",
      showPath: 1,
      showPathDeviceId: deviceId,
    },
    "onFahrtroutes"
  );
};
/** 关闭行车路线推送 */
export const offFahrtroutes = (deviceId: string) => {
  return send(
    {
      uuid: uuid(),
      messageType: "showPathMessage",
      showPath: 0,
      showPathDeviceId: deviceId,
    },
    "offFahrtroutes"
  );
};
