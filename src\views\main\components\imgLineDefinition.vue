<template>
  <x-popover
    trigger="hover"
    placement="bottom"
    contentType="light"
    style="display: inline-block"
  >
    <img class="image-tip" src="@/assets/images/alarm_question.png" />
    <template #content>
      <div class="table-image-popover-hover-title">
        {{ $t("imageLineDesc") }}
      </div>
      <div
        class="table-image-popover-hover-item"
        v-for="(item, index) in lineTips.info"
        :key="index"
      >
        <span
          class="table-image-popover-hover-item-legend"
          :style="{ background: `${item.color}` }"
        >
        </span>
        <span class="table-image-popover-hover-item-definition"
          >{{ item.label }}
        </span>
      </div>
    </template>
  </x-popover>
</template>

<script lang="ts" setup>
import { reactive } from "vue";
import xPopover from "@/components/x-popover.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");
const imgLineDefType = [
  {
    label: `${$t("garbage")}/${$t("pipeline")}`,
    color: "#FF0000",
  },
  {
    label: $t("clearedGarbage"),
    color: "#00FF00",
  },
  {
    label: $t("newGarbage"),
    color: "#FFC800",
  },
  {
    label: $t("unclearGarbage"),
    color: "#FF0000",
  },
];

const lineTips = reactive({
  info: imgLineDefType,
  show: false,
});
</script>

<style lang="scss" scoped>
.image-tip {
  display: inline-block;
  margin-left: 5px;
  &:hover {
    cursor: pointer;
  }
}
.table-image-popover-hover {
  &-title {
    padding: 10px;
    @include sc(14px, #383838) {
      font-weight: bold;
    }
  }
  &-item {
    @include ct-f(y) {
      padding: 8px 4px;
    }
    &-legend {
      display: inline-block;
      @include wh(4px, 16px);
      margin: 0 10px;
    }
    &-definition {
      @include sc(14px, #5e5e5e);
      margin-right: 10px;
    }
  }
}
</style>
