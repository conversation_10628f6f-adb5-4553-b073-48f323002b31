<template>
  <svg
    class="round-percent"
    :width="wh"
    :height="wh"
    viewBox="0 0 96 96"
    fill="none"
  >
    <g>
      <g>
        <!-- 进度条（外） -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          r="46"
          stroke-width="2"
          stroke="url('#outSide')"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="strokeDasharray.outRound"
        />
      </g>
      <g>
        <!-- 基础圆环 -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          r="36"
          stroke-width="2"
          stroke="#DCDCDC"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="baseRound"
        />
      </g>
      <g>
        <!-- 进度条（内） -->
        <circle
          :cx="wh / 2"
          :cy="wh / 2"
          r="36"
          stroke-width="8"
          stroke="url(#inSide)"
          fill="none"
          stroke-linecap="round"
          :stroke-dasharray="strokeDasharray.inRound"
        />
      </g>
    </g>
    <defs>
      <linearGradient id="inSide">
        <stop :stop-color="props.offline ? '#adadad' : '#27d4a1'" />
      </linearGradient>
      <linearGradient id="outSide">
        <stop
          offset="0%"
          :stop-color="props.offline ? '#adadad' : 'rgb(72, 215, 180)'"
        />
        <stop
          offset="100%"
          :stop-color="props.offline ? '#adadad' : 'rgb(72, 215, 180, 0.05)'"
        />
      </linearGradient>
    </defs>
  </svg>
</template>
<script lang="ts" setup>
import { computed } from "vue";
const props = defineProps({
  percent: {
    type: Number,
    required: true,
  },
  offline: {
    type: Boolean,
    default: () => false,
  },
});
const wh = 96;
const inPerimeter = Math.PI * 2 * 36;
const outPerimeter = Math.PI * 2 * 46;
const baseRound = (270 / 360) * inPerimeter;
const strokeDasharray = computed(() => {
  const scale = props.percent / 100 > 1 ? 1 : props.percent / 100;
  return {
    inRound: `${scale * ((270 / 360) * inPerimeter)} ${inPerimeter}`,
    outRound: `${scale * ((270 / 360) * outPerimeter)} ${outPerimeter}`,
  };
});
</script>

<style lang="scss" scoped>
.round-percent {
  transform: rotate(135deg);

  circle {
    transition: stroke-dasharray 0.6s ease-in-out;
  }
}
</style>
