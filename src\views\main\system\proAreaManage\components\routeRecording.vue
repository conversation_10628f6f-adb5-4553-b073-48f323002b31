<template>
  <section class="route-recording">
    <div class="route-recording-button">
      <x-button
        type="paleBlue"
        :text="'添加文件'"
        icon="button_add"
        :disabled="uploadBtnDisabled"
        @click="handleAddFile"
      />
      <input
        ref="fileInputRef"
        type="file"
        :multiple="props.multiple"
        :accept="props.accept"
        :capture="false"
        style="display: none"
        @change="fileInputChange"
      />
    </div>
    <div class="route-recording-list">
      <div
        v-for="(file, i) in fileList"
        :key="file.uid"
        class="list-item"
        :class="{
          'is-active': curFileIndex === i,
          'is-disabled': file.status === 'uploading',
        }"
        @click="handleListItem(file, i)"
      >
        <div class="list-item-row1">
          <div class="list-item-row1-left">
            <XIcon
              name="csv_link"
              width="14"
              height="14"
            />
            <span class="list-item-row1-left-text">CSV</span>
          </div>
          <div
            class="list-item-row1-right"
            :title="file.name"
          >
            <div class="list-item-row1-name">{{ file.name }}</div>
            <div class="list-item-row1-time">{{ file.createdTime }}</div>
          </div>
          <div
            class="list-item-row1-del"
            @click.stop="handleDeleteItem(file, i)"
          >
            <XIcon
              name="del_x"
              width="16"
              height="16"
            />
          </div>
          <div
            class="list-item-row1-progress"
            v-if="file.status === 'uploading'"
          >
            <div
              class="list-item-row1-progress-bar"
              :style="{ width: (file.percent ?? 0) + '%' }"
            />
          </div>
        </div>
        <div class="list-item-row2">
          <div class="list-item-row2-left">
            <x-checkbox
              class="checkbox"
              text="业务类"
              :disabled="file.changeLoading"
              :checked="file.routeType === false"
              @update:checked="handleTypeChange(false, file)"
            />
            <x-checkbox
              class="checkbox"
              text="辅助类"
              :disabled="file.changeLoading"
              :checked="file.routeType === true"
              @update:checked="handleTypeChange(true, file)"
            />
            <el-button
              type="primary"
              link
              @click="handelDownload(file)"
              >下载文件</el-button
            >
          </div>
          <div class="list-item-row2-right">
            <x-switch
              :checkedValue="true"
              :unCheckedValue="false"
              :disabled="file.changeLoading"
              v-model:checked="file.routeStatus"
              @change="handleStatusChange($event, file)"
            >
              <template #checkedChildren>
                <x-icon name="switch_checked"></x-icon>
              </template>
              <template #unCheckedChildren>
                <x-icon name="switch_unChecked"></x-icon>
              </template>
            </x-switch>
          </div>
          <!-- <x-checkbox
          class="checkbox"
          :text="item.label"
          :checked="item.checkStatus"
          @update:checked="scheduleConfig[index].checkStatus = $event"
        /> -->
          <!-- <x-radio-button
              type="tab"
              :gap="5"
              v-model:value="readTab.activeIndex"
              :options="readTab.statusList"
              @change="changeReadStatus"
            /> -->
        </div>
      </div>
    </div>
    <Teleport :to="props.container">
      <div
        class="route-recording-frame"
        v-show="curFileIndex !== -1"
      >
        <XForm
          ref="formRef"
          :model="formModel"
          :rules="formRules"
        >
          <XFormItem
            label="文件名"
            name="name"
            label-flex="80px"
          >
            <span>{{ formModel.name }}</span>
          </XFormItem>
          <XFormItem
            label="车端路径"
            name="routePath"
            label-flex="80px"
          >
            <XInput v-model:value="formModel.routePath" />
          </XFormItem>
        </XForm>
      </div>
    </Teleport>
  </section>
</template>

<script lang="tsx" setup>
import { computed, nextTick, ref, watch } from "vue";
import XButton from "@/components/x-button.vue";
import XForm from "@/components/x-form.vue";
import XIcon from "@/components/x-icon.vue";
import XFormItem from "@/components/x-form-item.vue";
import XInput from "@/components/x-input.vue";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import { uuid } from "@/assets/ts/utils";
import type { AxiosRequestConfig } from "axios";
import { defaultTimeout } from "@/services/http";
import axios from "axios";
import { changeTheRecordingRouteStatus, changeTheRecordingRouteType, deleteAreaRecordingRoute } from "@/services/api";
import type { FileItem } from "@/components/x-upload/x-upload.vue";
import { polylineStyle } from "@/services/wsconfig";
import { getLocalStorage } from "@/assets/ts/storage";
import xCheckbox from "@/components/x-checkbox.vue";
import xSwitch from "@/components/x-switch.vue";
import { ElMessage } from "element-plus";
export type RouteFileItem = Partial<
  FileItem & {
    routeFileUrl: string;
    routeName: string;
    /** 车端路径 */
    routePath?: string;
    /** 路线类型，false业务类true辅助类 */
    routeType?: boolean;
    /** 开关，false关true开 */
    routeStatus?: boolean;
    /** 自录路线id */
    id: string;
    recordingGpsList: {
      longitude: number;
      latitude: number;
      x: number;
      y: number;
    }[];
    createdTime: string;
    /** 更改请求 */
    changeLoading: boolean;
    [x: string]: any;
  }
>;

const props = withDefaults(
  defineProps<{
    modelValue: RouteFileItem[];
    multiple?: boolean;
    action?: string;
    accept?: string;
    /** 文件上传最大数量 默认无穷大 ∞ */
    maxCount?: number;
    beforeUpload?: Function;
    container: HTMLDivElement;
    type: "add" | "edit" | "view";
    mapInfo: {
      map: any;
      Amap: any;
      csvRoutePolylines: any[];
      [x: string]: any;
    };
    id?: string;
    activeId?: string;
  }>(),
  {
    modelValue: () => [],
    multiple: false,
    accept: ".csv",
    maxCount: Infinity,
    action: "/sys/proArea/uploadAndParseRecordingRoute",
    beforeUpload: () => true,
  }
);
const emit = defineEmits(["update:modelValue"]);
const curFileIndex = ref<number>(-1);
const uploadBtnDisabled = computed(() => fileList.value.some((item) => item.status === "uploading"));

const handelDownload = (file: any) => {
  if (!file?.routeFileUrl) {
    ElMessage.warning("暂无可下载文件");
    return;
  }

  const link = document.createElement("a");
  link.href = file.routeFileUrl;
  link.download = "";
  link.target = "_blank";

  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

/** 点击文件上传 */
const handleAddFile = () => {
  if (curFileIndex.value == -1 || (formRef.value && formRef.value.validate())) {
    fileInputRef.value && fileInputRef.value.click();
  }
};

//#region 文件上传相关
const fileInputRef = ref<HTMLInputElement>();
const fileList = ref<RouteFileItem[]>(props.modelValue);

const fileInputChange = (e: Event) => {
  const target = e.target as HTMLInputElement;
  if (target.files && target.files.length) {
    updateFileList(Array.from(target.files));
  }
};
const updateFileList = (files: File[]) => {
  /** 超出最大数量即删除最大数量后的文件 */
  const spliceLength = files.length + fileList.value.length - props.maxCount;
  spliceLength > 0 && fileList.value.splice(0, spliceLength);

  files.forEach((file) => {
    const fileItem = {
      file,
      uid: uuid(),
      name: file.name,
      size: file.size,
      type: file.type,
      percent: 0,
      status: "uploading",
      url: "",
    } as RouteFileItem;

    /** 存在相同的文件名时，反馈替换提示 */
    const existFileIndex = fileList.value.findIndex((item) => item.name === file.name);
    if (existFileIndex !== -1) {
      xModal.confirm({
        title: "该路线文件名已存在，是否替换？",
        confirm: async () => {
          await deleteCsvRouteFile(fileList.value[existFileIndex], existFileIndex);
          fileList.value.push(fileItem);
          uploadFile(fileList.value.at(-1)!);
        },
      });
    } else {
      fileList.value.push(fileItem);
      uploadFile(fileList.value.at(-1)!);
    }
  });
  nextTick(() => (fileInputRef.value!.value = ""));
};

/** 上传自录路线文件 */
const uploadFile = (fileItem: RouteFileItem) => {
  if (!fileItem.file) return;
  const config = {
    params: { id: props.id },
    withCredentials: false,
    headers: {
      "Content-Type": "multipart/form-data",
      token: getLocalStorage("token"),
    },
    timeout: defaultTimeout,
    transformRequest: [
      function (file) {
        const form = new FormData();
        form.append("file", file);
        return form;
      },
    ],
    onUploadProgress: (progressEvent) => {
      fileItem.percent = progressEvent.total ? ((progressEvent.loaded / progressEvent.total) * 100) | 0 : 0;
    },
  } as AxiosRequestConfig;

  axios
    .post(import.meta.env.VITE_BASE_URL + props.action, fileItem.file, config)
    .then((response) => {
      // 上传成功
      if (response.status === 200) {
        const { result } = response.data;
        fileItem.status = "success";
        fileItem.url = result.routeFileUrl;
        fileItem.name = result.routeName;
        fileItem.recordingGpsList = result.recordingGpsList;
        fileItem.routeFileUrl = result.routeFileUrl;
        fileItem.routePath = result.routePath;
        fileItem.routeName = result.routeName;
        fileItem.id = result.id;
        fileItem.createdTime = result.createdTime;
        fileItem.routeStatus = true;
        fileItem.routeType = false;
        drawRecordRoute(fileItem);

        fileItem.statusVisible = true;
        setTimeout(() => {
          fileItem.statusVisible = false;
        }, 1000);

        emit("update:modelValue", fileList.value);
      }
    })
    .catch((reason) => {
      Message("error", reason?.response?.data?.message || "文件上传失败");
      deleteErrorRouteFileItem(fileItem);
    });
};
/** 删除错误文件 */
const deleteErrorRouteFileItem = (fileItem: RouteFileItem) => {
  fileList.value = fileList.value.filter((item) => item !== fileItem);
  emit("update:modelValue", fileList.value);
};
//#endregion

/** 文件列表单项点击 */
const handleListItem = (row: RouteFileItem, index: number) => {
  if (row.status === "uploading") return;
  curFileIndex.value = index;
  props.mapInfo.csvRoutePolylines.forEach((route: any) => {
    if (route.getExtData().id === row.id) {
      route.setOptions({
        strokeColor: "#5964FB",
        strokeWeight: 6,
      });
    } else {
      route.setOptions({
        ...polylineStyle,
      });
    }
  });
};
/** 文件列表单项删除 */
const handleDeleteItem = (row: RouteFileItem, index: number) => {
  if (row.status === "uploading") return;
  xModal.confirm({
    title: "确定删除该路线以及文件吗？",
    confirmType: "red",
    confirm: async () => {
      await deleteCsvRouteFile(row, index);
      Message("success", "移除成功");
    },
  });
};

/** 删除自录路线文件 */
const deleteCsvRouteFile = async (row: RouteFileItem, index: number) => {
  await deleteAreaRecordingRoute({ id: row.id as string });
  const polyline = props.mapInfo.csvRoutePolylines.find((item) => item.getExtData().id === row.id);
  if (polyline) {
    props.mapInfo.map.remove(polyline);
  }
  fileList.value.splice(index, 1);
  emit("update:modelValue", fileList.value);
  if (curFileIndex.value === index) {
    curFileIndex.value = -1;
  }
};

//#region 底部表单
const formRef = ref<InstanceType<typeof XForm>>();
const formModel = computed(() => fileList.value[curFileIndex.value] ?? {});
const formRules = {
  routePath: [["required", "请输入车端路径"]],
};
//#endregion

/** 绘制自录路线 */
const drawRecordRoute = (route: RouteFileItem) => {
  const path = route.recordingGpsList?.map(({ longitude, latitude }) => [longitude, latitude]) || [];
  const polyline = new props.mapInfo.Amap.Polyline({
    ...polylineStyle,
    path,
    extData: route,
  });
  props.mapInfo.csvRoutePolylines.push(polyline);
  props.mapInfo.map.add(polyline);
};

/** 更改自录路线状态 */
const handleStatusChange = async (e: boolean, file: RouteFileItem) => {
  try {
    file.changeLoading = true;
    await changeTheRecordingRouteStatus({
      id: file.id as string,
      status: e,
    });
  } catch (error) {
    console.error("error", e, file.routeStatus);
    file.routeStatus = !e;
  } finally {
    file.changeLoading = false;
  }
};

/** 更改自录路线类型 */
const handleTypeChange = async (e: boolean, file: RouteFileItem) => {
  try {
    file.changeLoading = true;
    await changeTheRecordingRouteType({
      id: file.id as string,
      type: e,
    });
    file.routeType = e;
  } catch (error) {
    console.error("error", error);
  } finally {
    file.changeLoading = false;
  }
};

watch(
  () => props.activeId,
  (newV) => {
    if (newV) {
      const index = fileList.value.findIndex((item) => item.id === newV);
      handleListItem(fileList.value[index], index);
      nextTick(() => formRef.value?.validate());
    }
  },
  { immediate: true }
);
</script>
<style lang="scss">
.route-recording {
  @include wh(100%);
  overflow: hidden;
  padding-top: 16px;
  display: flex;
  flex-direction: column;
  &-button {
    padding: 0 16px;
  }
  &-list {
    margin-top: 10px;
    flex: 1 0 auto;
    padding: 0 16px 60px;
    overflow-y: auto;
    @include wh(100%, 0);
    @include scrollbar(y, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
    .list-item {
      // height: 42px;
      display: flex;
      flex-direction: column;
      padding: 10px;
      border: 1px solid #e5e6e8;
      border-radius: 6px;
      margin-bottom: 10px;
      cursor: pointer;
      position: relative;
      &.is-disabled {
        cursor: not-allowed;
        background-color: #fff;
        color: inherit;
        border-color: #e5e6e8;
        .list-item-left {
          background-color: #e5e6e8;
        }
      }
      &.is-active,
      &:hover {
        border-color: transparent;
        background-color: #e8ecfd;
        color: #5964fb;
        .list-item-row1-left {
          background-color: #5964fb;
        }
      }
      &-row1 {
        display: flex;
        align-items: center;
        position: relative;
        &-left {
          width: 55px;
          height: 25px;
          border-radius: 3px;
          background-color: #e5e6e8;
          clip-path: polygon(0 0, 80% 0, 100% 40%, 100% 100%, 0 100%);
          font-size: 12px;
          font-weight: 900;
          color: #fff;
          padding: 5px;
          display: flex;
          align-items: center;
          &-text {
            margin-left: 2px;
            letter-spacing: 1px;
          }
        }
        &-right {
          margin-left: 10px;
        }
        &-name {
          width: 215px;
          text-overflow: ellipsis;
          white-space: nowrap;
          overflow: hidden;
        }
        &-time {
          font-size: 12px;
          color: #555;
        }
        &-del {
          position: absolute;
          right: 5px;
          top: 50%;
          transform: translateY(-50%);
        }
        &-progress {
          width: 100%;
          height: 2px;
          background-color: #e5e6e8;
          position: absolute;
          bottom: 0;
          left: 0;
          &-bar {
            height: 100%;
            background-color: #5964fb;
          }
        }
      }
      &-row2 {
        display: flex;
        align-items: center;
        justify-content: space-between;
        margin-top: 10px;
        &-left {
          display: flex;
          align-items: center;
          gap: 10px;
        }
        &-right {
          margin-left: 10px;
        }
      }
    }
  }

  &-frame {
    position: absolute;
    right: 366px;
    bottom: 70px;
    padding: 14px;
    background-color: #fff;
    border-radius: 4px;
    box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
    min-width: 450px;
  }
}
</style>
