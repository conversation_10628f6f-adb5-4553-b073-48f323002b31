<template>
  <section class="tree">
    <div
      :class="['tree-root', { disabled: props.item.disabled }]"
      :style="{ paddingLeft: `${(props.deep - 1) * 20}px` }"
      @click.stop="itemClickHandle"
    >
      <div
        v-if="props.checkable && props.item.checked !== undefined"
        :class="['tree-root-checkable', { mr8: !(props.item.children && props.item.children.length > 0) }]"
      >
        <x-checkbox
          :disabled="props.item.disabled"
          :indeterminate="Boolean(props.item.indeterminate)"
          :checked="props.item.checked"
          @update:checked="updateSelected"
        />
      </div>
      <div
        v-if="!props.checkable || (props.item.children && props.item.children.length > 0)"
        class="tree-root-triangle"
        @click="updateExpandedKeys"
      >
        <span
          v-if="props.item.children && props.item.children.length > 0"
          :class="[{ open: props.item.expanded }]"
        ></span>
      </div>

      <div :class="['tree-root-title', { disabled: props.item.disabled }]">
        <slot
          v-if="$slots.title"
          name="title"
          :title="props.item.title"
          :item="props.item"
        ></slot>
        <template v-else>{{ props.item.title }}</template>
      </div>
    </div>
    <div
      v-show="props.item.expanded"
      class="tree-child"
    >
      <Tree
        :treeData="props.treeData"
        :rootPath="[...props.rootPath, index]"
        :autoExpandParent="props.autoExpandParent"
        :clickHandle="props.clickHandle"
        :item="_item"
        v-for="(_item, index) in props.item.children"
        :key="index"
        :deep="props.deep + 1"
        :checkable="props.checkable"
        :multiple="props.multiple"
        :selectedItems="props.selectedItems"
        @update:selectedItems="updateselectedItems"
        :expandedKeys="props.expandedKeys"
        @update:expandedKeys="emitExpandedKeys"
        :selectedKeys="props.selectedKeys"
        @update:selectedKeys="updateSelectedKeys"
        :selectedTitles="props.selectedTitles"
        @update:selectedTitles="updateSelectedTitles"
        :checkedKeys="props.checkedKeys"
        @update:checkedKeys="updateCheckedKeys"
      >
        <template
          v-if="$slots.title"
          #title="{ title, item }"
        >
          <slot
            name="title"
            :title="title"
            :item="item"
          ></slot>
        </template>
      </Tree>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { onMounted } from "vue";
import type { PropType } from "vue";
import { treeBfsParse } from "@/assets/ts/utils";
import xCheckbox from "@/components/x-checkbox.vue";
export type Key = string | number;
export type TreeItemType = {
  title: string;
  value: string | number;
  expanded?: boolean; // 是否展开
  checked?: boolean; // 全选
  indeterminate?: boolean; // 半选
  disabled?: boolean; // 是否置灰
  children?: TreeItemType[];
  [key: string]: any;
};
const props = defineProps({
  treeData: {
    type: Array as PropType<TreeItemType[]>,
    required: true,
  },
  // parentData: {
  //   type: Object as PropType<TreeItemType>,
  //   required: true,
  // },
  autoExpandParent: {
    type: Boolean,
    default: () => false,
  },
  clickHandle: {
    // 自定义点击处理
    type: Function,
  },
  rootPath: {
    type: Array as PropType<number[]>,
    required: true,
  },
  item: {
    type: Object as PropType<TreeItemType>,
    required: true,
  },
  deep: {
    type: Number,
    required: true,
  },
  slotTitle: {
    type: HTMLElement,
  },
  selectedItems: {
    type: Array as PropType<TreeItemType[]>,
    required: true,
  },
  selectedTitles: {
    // 选中的树节点
    type: Array as PropType<Key[]>,
    required: true,
  },
  selectedKeys: {
    // 选中的树节点
    type: Array as PropType<Key[]>,
    required: true,
  },
  expandedKeys: {
    // 展开的树节点
    type: Array as PropType<Key[]>,
    required: true,
  },
  checkable: {
    // 是否添加可选框
    type: Boolean,
    required: true,
  },
  checkedKeys: {
    // 选中复选框的树节点
    type: Array as PropType<Key[]>,
    required: true,
  },
  multiple: {
    // 多选
    type: Boolean,
    required: true,
  },
});
const emits = defineEmits([
  "update:selectedItems",
  "update:selectedTitles",
  "update:selectedKeys",
  "update:expandedKeys",
  "update:checkedKeys",
]);

onMounted(() => {
  if (props.checkable) {
    // eslint-disable-next-line vue/no-mutating-props
    if (props.item.checked === undefined) props.item.checked = false;
    // eslint-disable-next-line vue/no-mutating-props
    if (props.item.expanded === undefined) props.item.expanded = props.autoExpandParent;
  }
});

const itemClickHandle = () => {
  if (props.clickHandle) {
    props.clickHandle(props.item);
  } else {
    updateSelected();
  }
};
const updateSelected = () => {
  if (props.item.disabled) return;
  if (props.checkable) {
    const updateItems: TreeItemType[] = [];
    // 子孙层级
    const _bool = !props.item.checked;
    if (props.item.children && props.item.children.length > 0) {
      treeBfsParse(props.item.children, "children", (item: TreeItemType) => {
        if (!item.disabled) {
          item.checked = _bool;
        }
        item.indeterminate = false;
        updateItems.push(item);
      });
    }

    // 自身 + 父辈层级
    let pathIndex = 1;
    let tree = props.treeData[props.rootPath[0]];
    const sortBy = [tree];
    while (pathIndex < props.rootPath.length) {
      tree = tree.children?.[props.rootPath[pathIndex]]!;
      sortBy.unshift(tree);
      pathIndex++;
    }
    updateItems.push(...sortBy);

    sortBy.forEach((item) => {
      item.checked = Boolean(
        item.children && item.children.length > 0 ? item.children?.every((v) => v.checked || v.disabled) : _bool
      );
      item.indeterminate = Boolean(
        item.children?.some((v) => !v.disabled && (v.checked || v.indeterminate)) &&
          item.children?.some((v) => !v.checked && !v.disabled)
      );
    });
    // 多选
    updateItems.forEach((item) => {
      const index = props.selectedItems.findIndex((v) => v.value === item.value);
      // 不存在 && 选中
      if (!~index && item.checked) {
        // eslint-disable-next-line vue/no-mutating-props
        props.selectedItems.push(item);
        // eslint-disable-next-line vue/no-mutating-props
        // props.selectedTitles.push(item.title);
        // eslint-disable-next-line vue/no-mutating-props
        // props.selectedKeys.push(item.value);
        // 存在 && 没选中
      } else if (~index && !item.checked) {
        // eslint-disable-next-line vue/no-mutating-props
        props.selectedItems.splice(index, 1);
        // eslint-disable-next-line vue/no-mutating-props
        // props.selectedTitles.splice(index, 1);
        // eslint-disable-next-line vue/no-mutating-props
        // props.selectedKeys.splice(index, 1);
      }
    });

    emits("update:selectedItems", props.selectedItems);
    // emits("update:selectedTitles", props.selectedTitles);
    // emits("update:selectedKeys", props.selectedKeys);
  } else {
    // 针对下拉单选
    emits("update:selectedItems", [props.item]);
    // emits("update:selectedTitles", [props.item.title]);
    // emits("update:selectedKeys", [props.item.value]);
  }
};
const updateselectedItems = (items: TreeItemType[]) => {
  emits("update:selectedItems", items);
};
const updateSelectedKeys = (keys: Key[]) => {
  emits("update:selectedKeys", keys);
};

const updateSelectedTitles = (titles: Key[]) => {
  emits("update:selectedTitles", titles);
};
const updateCheckedKeys = (keys: Key[]) => {
  emits("update:checkedKeys", keys);
};

const updateExpandedKeys = (e: Event) => {
  if (!props.item.children || props.item.children.length === 0) return;
  e.stopPropagation();
  let _expandedKeys = props.expandedKeys;
  if (_expandedKeys.includes(props.item.value)) {
    if (!props.item.expanded) {
      props.item.expanded = true;
    } else {
      _expandedKeys.splice(
        _expandedKeys.findIndex((key) => key === props.item.value),
        1
      );
      // eslint-disable-next-line vue/no-mutating-props
      props.item.expanded = false;
    }
  } else if (props.item.expanded) {
    props.item.expanded = false;
  } else {
    _expandedKeys.push(props.item.value);
    // eslint-disable-next-line vue/no-mutating-props
    props.item.expanded = true;
  }
  // emitExpandedKeys(_expandedKeys);
};
const emitExpandedKeys = (keys: Key) => {
  emits("update:expandedKeys", keys);
};
</script>

<style lang="scss" scoped>
.tree {
  width: 100%;
  &-root {
    cursor: pointer;
    &.disabled {
      cursor: not-allowed;
    }
    display: flex;
    @include wh(100%, 32px) {
      padding-left: 24px;
      border-radius: 4px;
      margin-top: 4px;
      color: rgba(0, 0, 0, 0.9);
      line-height: 32px;
    }
    &:nth-child(1) {
      margin-top: 0;
    }
    &:hover {
      background-color: rgb(217, 224, 251);
      :deep(.x-checkbox .content-inner) {
        background-color: #5964fb;
        border-color: #5964fb;
      }
      :deep(.x-checkbox .content-inner.disabled) {
        background-color: rgb(229, 229, 229);
        border-color: rgb(229, 229, 229);
      }
      :deep(.x-checkbox .content-inner::after) {
        border-right-color: #fff;
        border-bottom-color: #fff;
      }
    }
    &-checkable {
      @include ct-f;
      margin-left: 6px;
      &.mr8 {
        margin-right: 8px;
      }
    }
    &-triangle {
      @include ct-f;
      @include wh(30px, 100%);
      span {
        transition: transform 0.3s;
        @include triangle(4px, 3px, rgba(0, 0, 0, 0.6), right);
        &.open {
          transform: rotate(90deg);
        }
      }
    }

    &-title {
      width: 100%;
      @include ell;
      &.disabled {
        color: rgb(159, 159, 164);
      }
    }
  }
}
</style>
