<template>
  <div class="sidebar">
    <div class="sidebar-tabs">
      <div
        class="sidebar-tabs-scroll"
        ref="tabScrollRef"
        @wheel="onTabScrollWheel"
      >
        <div
          class="sidebar-tabs-item"
          :class="{ active: activeTab === item.value }"
          v-for="item in tabList"
          :key="item.value"
          @click="handleTabClick(item.value)"
          :ref="(el:any) => { if (el) tabRefs[idx] = el }"
        >
          <div class="sidebar-tabs-item-label">{{ item.label }}</div>
        </div>
      </div>
      <el-dropdown
        trigger="click"
        @command="handleTabClick"
      >
        <div class="sidebar-tabs-more">
          <el-tooltip
            content="更多"
            placement="bottom"
          >
            <el-icon><Expand /></el-icon>
          </el-tooltip>
        </div>

        <template #dropdown>
          <el-dropdown-menu>
            <el-dropdown-item
              v-for="item in tabList"
              :key="item.value"
              :command="item.value"
            >
              {{ item.label }}
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
    </div>

    <div class="sidebar-content">
      <el-scrollbar height="500px">
        <div
          v-if="activeTab === 'smart_order'"
          class="tab-container"
        >
          <smart-order-page ref="smartOrderPageRef" />
        </div>
        <div
          v-if="activeTab === 'traffic_light' || activeTab === 'municipal_facilities'"
          class="tab-container"
        >
          <urban-facility-page
            ref="urbanFacilityPageRef"
            :activeTab="activeTab"
          />
        </div>
        <div
          v-if="activeTab === 'inspection'"
          class="tab-container"
        >
          <inspection-page ref="inspectionPageRef" />
        </div>
        <div
          v-if="activeTab === 'pipeline' || activeTab === 'collision'"
          class="tab-container"
          :activeTab="activeTab"
        >
          <pipeline-collision-page ref="pipelineCollisionPageRef" />
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup lang="ts">
import smartOrderPage from "./parts/smart_order_page.vue";
import urbanFacilityPage from "./parts/urban_facility_page.vue";
import inspectionPage from "./parts/inspection_page.vue";
import pipelineCollisionPage from "./parts/pipeline_collision_page.vue";
import { ref, reactive, toRefs, onMounted, nextTick } from "vue";
import { Expand } from "@element-plus/icons-vue";
const smartOrderPageRef = ref();
const urbanFacilityPageRef = ref();
const inspectionPageRef = ref();
const pipelineCollisionPageRef = ref();
const activeTab = ref("smart_order");
const tabRefs = ref<HTMLElement[]>([]);
const tabScrollRef = ref<HTMLElement>();
const tabList = [
  {
    label: "智慧工单",
    value: "smart_order",
  },
  {
    label: "红绿灯",
    value: "traffic_light",
  },
  {
    label: "巡检",
    value: "inspection",
  },
  {
    label: "市政设施",
    value: "municipal_facilities",
  },
  {
    label: "管线",
    value: "pipeline",
  },
  {
    label: "碰撞",
    value: "collision",
  },
];

function onTabScrollWheel(e: WheelEvent) {
  const el = tabScrollRef.value;
  if (!el) return;
  if (el.scrollWidth > el.clientWidth) {
    e.preventDefault();
    el.scrollLeft += e.deltaY;
  }
}

function scrollTabIntoView() {
  const idx = tabList.findIndex((tab) => tab.value === activeTab.value);
  const tabEl = tabRefs.value[idx];
  const scrollEl = tabScrollRef.value;
  if (tabEl && scrollEl) {
    const tabRect = tabEl.getBoundingClientRect();
    const scrollRect = scrollEl.getBoundingClientRect();
    if (tabRect.left < scrollRect.left || tabRect.right > scrollRect.right) {
      // 让tab居中显示
      scrollEl.scrollLeft += tabRect.left - scrollRect.left - (scrollRect.width - tabRect.width) / 2;
    }
  }
}

const handleTabClick = (value: string) => {
  activeTab.value = value;
  nextTick(() => {
    scrollTabIntoView();
  });
  switch (value) {
    case "smart_order":
      if (smartOrderPageRef.value) {
        smartOrderPageRef.value.load();
      }
      break;
    case "traffic_light":
      urbanFacilityPageRef.value.load();
      break;
    case "municipal_facilities":
      urbanFacilityPageRef.value.load();
      break;
    case "pipeline":
      pipelineCollisionPageRef.value.load();
      break;
    case "collision":
      pipelineCollisionPageRef.value.load();
      break;
    case "inspection":
      inspectionPageRef.value.load();
      break;
  }
};

const load = () => {
  nextTick(() => {
    handleTabClick(activeTab.value);
  });
};

const Refresh = () => {
  if (activeTab.value === "traffic_light" || activeTab.value === "municipal_facilities") {
    urbanFacilityPageRef.value && urbanFacilityPageRef.value.load();
  }
};
defineExpose({
  load,
  Refresh,
});
</script>
<style scoped lang="scss">
.sidebar {
  width: 282px;
  height: 600px;
  position: absolute;
  left: 8px;
  top: 7px;
  box-shadow: 0px 0px 10px 0px rgba(72, 111, 245, 0.14);
  backdrop-filter: blur(30px);
  background: rgb(244, 247, 254);
  // z-index: 888;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  user-select: none;
}

.sidebar-tabs {
  display: flex;
  align-items: center;
  color: #9f9fa4;
  cursor: pointer;
  flex-shrink: 0;
  position: relative;
  min-width: 0;
  .sidebar-tabs-scroll {
    display: flex;
    overflow-x: auto;
    overflow-y: hidden;
    flex: 1 1 0%;
    min-width: 0;
    white-space: nowrap;
    padding-bottom: 12px;
    &::-webkit-scrollbar {
      height: 4px;
    }
  }
  .sidebar-tabs-item {
    position: relative;
    flex: 0 0 auto;
    padding: 0 8px;
    cursor: pointer;
    white-space: nowrap;
    box-sizing: border-box;
    padding-top: 10px;
    &:hover {
      color: #242859;
      font-weight: 700;
      &::after {
        content: "";
        display: block;
        width: 19px;
        height: 2px;
        background: #242859;
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
    &.active {
      color: #242859;
      font-weight: 700;
      &::after {
        content: "";
        display: block;
        width: 19px;
        height: 2px;
        background: #242859;
        position: absolute;
        bottom: -4px;
        left: 50%;
        transform: translateX(-50%);
      }
    }
  }
  .sidebar-tabs-more {
    flex: 0 0 auto;
    margin-left: 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    width: 29px;
    height: 33px;
    background: rgba(255, 255, 255, 1);
  }
}

.sidebar-content {
  flex: 1;
  overflow: hidden;

  .tab-container {
    width: 100%;
    height: 100%;
  }
}
</style>
