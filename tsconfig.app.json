{"extends": "@vue/tsconfig/tsconfig.web.json", "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "src/**/*.d.ts"], "files": ["./node_modules/@vuemap/amap-jsapi-types/index.d.ts"], "exclude": ["src/**/__tests__/*"], "compilerOptions": {"preserveValueImports": false, "importsNotUsedAsValues": "remove", "verbatimModuleSyntax": true, "lib": ["ESNext", "DOM"], "composite": true, "baseUrl": ".", "paths": {"@/*": ["./src/*"]}}}