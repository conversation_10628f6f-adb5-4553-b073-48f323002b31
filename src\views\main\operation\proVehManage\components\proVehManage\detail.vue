<template>
  <x-drawer
    :title="$t('viewDetails')"
    :visible="props.show"
    @update:visible="updateVisible"
    width="80%"
  >
    <x-radio-button
      type="tab"
      :gap="5"
      v-model:value="readTab.activeIndex"
      :options="readTab.statusList"
      @change="changeReadStatus"
    />
    <div
      class="content"
      ref="viewDetailRef"
    >
      <div
        class="content-body log"
        v-if="!readTab.activeIndex"
      >
        <div class="content-body-title">
          <div class="container">
            <div class="label">{{ $t("cloudSyncTime") }}：</div>
            <div class="value">{{ detail.cloudSynchroniseTime }}</div>
          </div>
          <div class="container">
            <div class="label">{{ $t("vehicleSyncTime") }}：</div>
            <div class="value">{{ detail.vehicleSynchroniseTime }}</div>
          </div>
          <div class="container">
            <div class="label">{{ $t("status") }}：</div>
            <div
              class="value"
              :class="{ active: detail.synchroniseStatus }"
            >
              {{ detail.synchroniseStatus ? $t("synchronized") : $t("unsynchronized") }}
            </div>
          </div>
        </div>
        <template
          v-for="(task, index) in detail.taskList"
          :key="index"
        >
          <TaskPreview
            :task="task"
            :showSwitchStatus="true"
            :popupContainer="viewDetailRef"
            bgType="dark"
          />
        </template>
      </div>
      <div
        class="content-body info"
        v-else
      >
        <div class="content-body-left">
          <div class="content-body-title">{{ detail.info.vehicleNo }}</div>
          <div class="content-body-subtitle">
            <div class="content-body-subtitle-item">
              {{ detail.info.userName }}
            </div>
            <div class="content-body-subtitle-item">
              {{ formatPhoneNum(detail.info.userPhoneNo) }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vehicleModel") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.carModel }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vehicleType") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.carType }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("vehicleVinNo") }}：</div>
            <div class="content-body-item-value">
              {{ formatAccount(detail.info.carVinNo) }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("simNo") }}：</div>
            <div class="content-body-item-value">
              {{ formatAccount(detail.info.simNo) }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("configList") }}：</div>
            <div class="content-body-item-value">
              <div
                class="file-item"
                v-for="(item, index) in detail.info.instrUrl"
                :key="index"
              >
                <div class="left">{{ item.fileName }}</div>
                <div class="right">
                  <div
                    class="btn"
                    @click="previewDoc(item.instrUrl)"
                  >
                    {{ $t("view") }}
                  </div>
                  <a
                    class="btn"
                    style="margin-left: 10px"
                    :href="item.instrUrl"
                    :download="item.fileName"
                    >{{ $t("download") }}</a
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="content-body-right">
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("launchTime") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.launchTime }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("areaName") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.areaName }}
            </div>
          </div>
          <div class="content-body-item">
            <div class="content-body-item-label">{{ $t("projectName") }}：</div>
            <div class="content-body-item-value">
              {{ detail.info.proName }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
  <PreviewFile
    v-model:show="fileModal.show"
    :url="fileModal.url"
  />
</template>

<script lang="tsx" setup>
import { reactive, watch, ref } from "vue";
import { getScheduleDetail } from "@/services/api";
import type { GetScheduleDetailResponse } from "@/services/type";
import { formatAccount, formatPhoneNum } from "@/assets/ts/utils";
import XDrawer from "@/components/x-drawer.vue";
import PreviewFile from "@/views/main/components/previewFile.vue";
import TaskPreview from "../taskPreview.vue";
import xRadioButton from "@/components/x-radio-button.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("proVehManage");

const viewDetailRef = ref<any>();

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: String,
    required: true,
  },
  vehicleId: {
    type: String,
    required: true,
  },
  proAreaId: String,
});

const emits = defineEmits(["update:show"]);
const updateVisible = (bool: boolean) => emits("update:show", bool);

const readTab = reactive({
  statusList: [
    {
      value: 0,
      label: $t("scheduleLog"),
    },
    {
      value: 1,
      label: $t("vehicleInfo"),
    },
  ],
  activeIndex: 0,
});
const changeReadStatus = (index: number) => {
  readTab.activeIndex = index;
};

const detail = reactive({
  cloudSynchroniseTime: "",
  vehicleSynchroniseTime: "",
  synchroniseStatus: false,
  info: {} as any,
  taskList: [] as any,
} as GetScheduleDetailResponse);

const fileModal = reactive({
  show: false,
  url: "",
});

// 回显数据
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      getScheduleList();
    }
  }
);

// 预览文件
const previewDoc = (url: any) => {
  fileModal.show = true;
  fileModal.url = url || "";
};

// 获取当前已有任务列表
const getScheduleList = async () => {
  const result = await getScheduleDetail({
    id: props.id,
    vehicleId: props.vehicleId,
  });
  const { cloudSynchroniseTime, vehicleSynchroniseTime, synchroniseStatus, vehicle, arrangement } = result;
  detail.cloudSynchroniseTime = cloudSynchroniseTime;
  detail.vehicleSynchroniseTime = vehicleSynchroniseTime;
  detail.synchroniseStatus = synchroniseStatus;
  detail.info = vehicle;
  detail.taskList =
    arrangement?.map((task: any) => {
      return {
        ...task,
        executeDayType: task.executeDays && !task.executeDays.includes("0") ? "weekly" : "daily",
        scheduleType: task.scheduleType === 6 ? 4 : task.scheduleType,
        _switchType: [4, 6].includes(task.scheduleType ?? 1) ? task.scheduleType : null,
      };
    }) || [];
};
</script>

<style lang="scss" scoped>
.content {
  border-radius: 8px;
  background: #fff;
  .content-body {
    &.log {
      padding: 20px;
      .content-body-title {
        @include ct-f {
          justify-content: space-between;
        }
        .container {
          display: flex;
          .label {
            @include sc(14px, #9f9fa4);
          }
          .value {
            @include sc(14px, #383838);
            &.active {
              color: #27d4a1;
            }
          }
        }
      }
    }
    &.info {
      background: #f4f7fe;
      display: flex;
      .content-body-left,
      .content-body-right {
        padding: 20px 30px 30px 30px;
        background: #fff;
        width: 50%;
        border-radius: 8px;
      }
      .content-body-left {
        margin-right: 10px;
      }
      .content-body-title {
        @include sc(18px, rgb(16, 22, 55)) {
          font-weight: bold;
        }
      }
      .content-body-subtitle {
        margin-bottom: 30px;
        @include fj(flex-start) {
          margin-top: 10px;
        }
        @include sc(14px, #383838);
        &-item::after {
          content: "|";
          color: #e5e5e5;
          padding: 0 12px;
        }
        &-item:last-child::after {
          display: none;
        }
      }
      .content-body-item {
        display: flex;
        margin-top: 20px;
        font-size: 14px;
        &-label {
          width: 82px;
          color: #999;
        }
        &-value {
          color: #383838;
          flex: 1;
          display: flex;
          flex-direction: column;
          .file-item {
            @include ct-f(y) {
              justify-content: space-between;
              margin-bottom: 10px;
            }
            .left {
              color: #383838;
            }
            .right {
              display: flex;
              .btn,
              .btn a {
                @include sc(12px, #5964fb);
                cursor: pointer;
              }
            }
          }
        }
      }
    }
  }
}
</style>
