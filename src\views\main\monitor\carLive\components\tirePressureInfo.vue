<template>
  <section class="tire-pressure" :class="position">
    <div class="tire-pressure-content">
      <div
        class="content-img bottom"
        v-if="position === 'FR' || position === 'RR'"
      >
        <x-icon :name="iconName" width="34" height="20" />
      </div>
      <div class="content-text">
        <span class="label" :class="colorName"></span>
        <span class="count">{{ pressureText }}</span>
        <span class="unit" style="margin-right: 10px">bar</span>
        <span class="count">{{ temperatureText }}</span>
        <span class="unit">℃</span>
      </div>
      <div
        class="content-img top"
        v-if="position === 'FL' || position === 'RL'"
      >
        <x-icon :name="iconName" width="34" height="20" />
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import xIcon from "@/components/x-icon.vue";
import { ref, watch, computed } from "vue";
import { parseTirePressure, parseTireTemperature } from "@/services/wsconfig";
const props = defineProps({
  pressure: {
    type: Number,
    require: true,
  },
  temperature: {
    type: Number,
    require: true,
  },
  // 0---下线或无数据 1---正常 2---告警
  status: {
    type: Number,
    default: 0,
  },
  position: {
    type: String,
    require: true,
  },
});

// 胎压
const pressureText = computed(() =>
  props.pressure === null || props.pressure == undefined
    ? "--"
    : parseTirePressure(props.pressure)
);

// 胎温
const temperatureText = computed(() =>
  props.temperature === null || props.temperature == undefined
    ? "--"
    : parseTireTemperature(props.temperature)
);

// 图示
const iconName = ref("");
const colorName = ref("");
watch(
  () => props.status,
  (newV) => {
    const status =
      props.pressure === null || props.pressure == undefined ? 0 : newV;
    const options = ["offline", "normal", "warn"];
    iconName.value = `tire_pressure_${options[status]}`;
    colorName.value = options[status];
  },
  {
    immediate: true,
  }
);
</script>

<style lang="scss" scoped>
.tire-pressure {
  display: inline-block;
  &.FL {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(calc(-50% + 62px), calc(-150% + 18px));
  }
  &.RL {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(calc(-50% - 110px), calc(-150% + 18px));
  }
  &.FR {
    position: absolute;
    top: calc(50% + 95px);
    left: 50%;
    transform: translate(calc(-50% + 62px), calc(-150% + 32px));
  }
  &.RR {
    position: absolute;
    top: calc(50% + 95px);
    left: 50%;
    transform: translate(calc(-50% - 110px), calc(-150% + 32px));
  }
  &-content {
    .content-text {
      margin: 5px 0;
      .label {
        display: inline-block;
        margin-right: 5px;
        @include wh(2px, 14px);
        &.offline {
          background: #9f9fa4;
        }
        &.normal {
          background: #27d4a1;
        }
        &.warn {
          background: #f41515;
        }
      }
      .count {
        @include sc(14px, #383838) {
          font-weight: bold;
        }
      }
      .unit {
        margin-left: 5px;
        @include sc(12px, #9f9fa4);
      }
    }
    .content-img {
      text-align: center;
      &.bottom {
        transform: rotate(180deg);
      }
    }
  }
}
</style>
