<template>
  <section class="x-checkbox">
    <span class="x-checkbox-content">
      <input
        :class="['content-input', { disabled: props.disabled }]"
        type="checkbox"
        :checked="props.checked"
        :value="props.text"
        :disabled="disabled"
        @click="(e) => props.stopPropagation && e.stopPropagation()"
        @change="changeHandle"
      />
      <span
        :class="[
          'content-inner',
          {
            checked: props.checked && !props.indeterminate,
            indeterminate: props.indeterminate,
            disabled: props.disabled,
          },
        ]"
      >
      </span>
    </span>
    <span v-if="props.text" class="x-checkbox-text">
      <template v-if="$slots.label">
        <slot name="label"></slot>
      </template>
      <span v-else>{{ props.text }}</span>
    </span>
  </section>
</template>
<script lang="ts" setup>
const props = defineProps({
  checked: {
    type: Boolean,
    required: true,
  },
  text: {
    type: String,
  },
  indeterminate: {
    // 控制是否半选
    type: Boolean,
    default: () => false,
  },
  disabled: {
    type: Boolean,
    default: () => false,
  },
  stopPropagation: {
    type: Boolean,
    default: () => true,
  },
});

const emits = defineEmits(["update:checked"]);

const changeHandle = (e: Event) => {
  emits("update:checked", (e.target as HTMLInputElement).checked);
};
</script>

<style lang="scss" scoped>
.x-checkbox {
  @include ct-f(y);
  height: 18px;
  &-content {
    position: relative;
    .content-input {
      position: absolute;
      top: 0;
      right: 0;
      bottom: 0;
      left: 0;
      z-index: 1;
      width: 100%;
      height: 100%;
      cursor: pointer;
      opacity: 0;
      &.disabled {
        cursor: not-allowed;
      }
    }
    .content-inner {
      @include ct-f;
      position: relative;
      left: 0;
      top: 0;
      @include wh(16px) {
        background-color: #ffffff;
        border: 1px solid #e5e5e5;
        border-radius: 2px;
      }
      &.disabled {
        background-color: rgb(229, 229, 229);
      }
      &.checked {
        &::after {
          opacity: 1;
          transform: rotate(45deg) scale(1) translate(-50%, -50%);
        }
      }
      &.indeterminate {
        &::after {
          opacity: 1;
          width: 0;
          height: 10px;
          border-radius: 20px;
          transform: rotate(90deg) scale(1) translate(-5px, -3px);
        }
      }
      &::after {
        position: absolute;
        left: 3px;
        top: 7px;
        width: 4px;
        height: 7px;
        opacity: 0;
        border-right: 2px solid #5964fb;
        border-bottom: 2px solid #5964fb;
        transition: all 0.2s cubic-bezier(0.12, 0.4, 0.29, 1.46);
        content: "";
      }
    }
  }
  &-text {
    margin-left: 4px;
    @include sc(12px, #b0b1c5) {
      line-height: 18px;
    }
  }
}
</style>
