<template>
  <section
    class="video"
    :style="`left: ${video.left}px; top: ${video.top}px;`"
    ref="videoRef"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
  >
    <div
      class="video-content"
      :style="`width: ${video.width}px; height: ${video.height}px;`"
    >
      <div id="dvr-video"></div>
    </div>
    <div
      class="video-opera"
      v-show="video.width"
    >
      <div
        class="video-opera-close"
        @click="close"
      >
        ✕
      </div>
      <div
        class="video-opera-toggle"
        @click="toggle"
      >
        <x-icon :name="`map_video_${video.toggleId === 1 ? 'four' : 'one'}`" />
        <span>{{ video.toggleId === 1 ? $t("smallScreen") : $t("fullScreen") }}</span>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import "@/plugins/byskplayer.js";
import { onMounted, ref, reactive, watch } from "vue";
import { useMainStore } from "@/stores/main";
import { liveCarVideoInfo } from "@/services/api";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

const { socket, updateActiveCarIdVideo } = useMainStore();
const close = () => updateActiveCarIdVideo(null);

let device = "";
onMounted(() => {
  video.player = new byskplayer({
    id: "dvr-video",
    baseURL: "wss://videostream.car900.com", // 视频服务
    userLevel: 1, // 用户级别 开发平台无效
    userkey: "2161e23a-4d3b-4d1a-bf0a-cafd56f9c6f8", // 客户代码
    userId: "apitest",
    isSwitchCodetypeOnFullscreen: false, // 全屏是否切换高清，默认false
    isReal: true,
    isRecord: false,
    click: () => {
      // e.stopPropagation();
      // e.preventDefault();
    },
    playstart: ({ channel }) => {
      // console.log(1111111111111, "playstart", channel);
      video.opens[channel - 1] = true;
    },
    playend: ({ channel }) => {
      // console.log(1111111111111, "playend", channel);
      video.opens[channel - 1] = false;
    },
    snapshot: () => {
      // console.log(1111111111111, "snapshot");
    },
    message: () => {
      // console.log(1111111111111, "message");
    },
  });
  video.player.poster = new URL(`@/assets/images/map_video_poster.png`, import.meta.url).href;
  const { flag, ids } = video.player.allocate(video.channels.length);
  flag === 1 && (video.ids = ids);

  watch(
    () => socket.activeCarIdVideo,
    async (newV) => {
      const carInfo = await liveCarVideoInfo(newV);
      if (newV && !carInfo?.sn) {
        device = carInfo?.deviceNo || "00000000000"; // "16200140518"
        openPlayer(video.ids[0], video.channels[0]);
        // closePlayer(video.ids[0], video.channels[0]);
        smallScreen();
        // 去除插件内部dom拖拽效果
        document.querySelectorAll(".player.num").forEach((dom) => (dom.draggable = false));
      } else {
        video.width = 0;
        video.height = 0;
        // 动画效果
        setTimeout(() => {
          video.channels.forEach((channel, index) => {
            closePlayer(video.ids[index + 1], channel);
          });
        }, 300);
      }
    }
  );
});

const oneVideoConfig = {
  id: 1,
  playerNum: 1,
  autoCloseTime: 5,
  width: 500,
  height: 280,
};
const fourVideoConfig = {
  id: 4,
  playerNum: 4,
  autoCloseTime: 5,
  width: 870,
  height: 592,
};

const video = reactive({
  player: null as any,
  ids: [],
  channels: [1, 2, 3, 4],
  opens: [false, false, false, false],
  width: 0,
  height: 0,
  left: 0,
  top: 0,
  mouseInVideoX: 0,
  mouseInVideoY: 0,
  isDragging: false,
  zoom: 1,
  toggleId: oneVideoConfig.id,
});

const videoRef = ref<any>();
const handleMouseDown = (e: MouseEvent) => {
  // 适配全局zoom缩放
  video.zoom = Number(getComputedStyle(document.querySelector(".main")!).zoom);
  const { left: vLeft, top: vTop } = videoRef.value.getBoundingClientRect();
  const clientX = e.clientX / video.zoom;
  const clientY = e.clientY / video.zoom;
  video.mouseInVideoX = clientX - vLeft;
  video.mouseInVideoY = clientY - vTop;

  video.isDragging = true;
};
const handleMouseMove = (e: MouseEvent) => {
  if (video.isDragging) {
    const clientX = e.clientX / video.zoom;
    const clientY = e.clientY / video.zoom;
    const {
      left: pLeft,
      top: pTop,
      width: pWidth,
      height: pHeight,
    } = videoRef.value.parentElement.getBoundingClientRect();
    let [left, top] = [clientX - video.mouseInVideoX - pLeft, clientY - video.mouseInVideoY - pTop];

    // 视频可拖动边界限制
    const leftMin = 0,
      topMin = 0,
      leftMax = pWidth - video.width,
      topMax = pHeight - video.height;
    if (left > leftMax) left = leftMax;
    else if (left < leftMin) left = leftMin;
    if (top > topMax) top = topMax;
    else if (top < topMin) top = topMin;

    video.left = left;
    video.top = top;

    // 鼠标超出视频边界结束拖动 (mousemove触发的局限性提供10px时效)
    const { left: vLeft, right: vRight, top: vTop, bottom: vBottom } = videoRef.value.getBoundingClientRect();
    if (clientX <= vLeft + 10 || clientX >= vRight - 10 || clientY <= vTop + 10 || clientY >= vBottom - 10) {
      handleMouseUp();
    }
  }
};
const handleMouseUp = () => {
  video.isDragging = false;
};

const openPlayer = (
  id: string,
  channel: number,
  func: Function = ({ status, channel }) => {
    status === 1 && (video.opens[channel - 1] = true);
  }
) => {
  video.player.real.open(
    id,
    {
      device, //终端号
      channel,
      protocolType: 1, // 协议类型，1：GF-1078，GB-1078; 2：GA系列
      codeTypeCtrlBtnEnabled: true, // 是否显示高标清切换按钮(GF才有高清)
      plate: socket.activeCarIdVideo, // 车牌号（可选）
      SpecialSign: 0, // 特殊协议 0=不处理  1=粤标
    },
    func
  );
};

const closePlayer = (id: string, channel: number, func: Function = () => {}) => {
  video.player.real.close(
    id,
    {
      device, //终端号
      channel,
      protocolType: 1, // 协议类型，1：GF-1078，GB-1078; 2：GA系列
    },
    func
  );
};

const toggle = () => {
  video.toggleId === oneVideoConfig.id ? bigScreen() : smallScreen();
};

const bigScreen = () => {
  const { id, playerNum, autoCloseTime, width, height } = fourVideoConfig;
  video.toggleId = id;
  video.player.setPlayerNum(playerNum);
  video.player.autoCloseTime = autoCloseTime; // 设置自动关闭视频时间(分钟)
  video.width = width;
  video.height = height;
  // 根据第一个的状态决定开关
  video.channels.slice(1).forEach((channel, index) => {
    openPlayer(video.ids[index + 1], channel);
    !video.opens[0] && closePlayer(video.ids[index + 1], channel);
  });
};

const smallScreen = () => {
  const { id, playerNum, autoCloseTime, width, height } = oneVideoConfig;
  video.toggleId = id;
  video.player.setPlayerNum(playerNum);
  video.player.autoCloseTime = autoCloseTime; // 设置自动关闭视频时间(分钟)
  video.width = width;
  video.height = height;
  // 关闭除第一个之外的视频流
  video.channels.slice(1).forEach((channel, index) => {
    closePlayer(video.ids[index + 1], channel);
  });
};
</script>

<style lang="scss" scoped>
.video {
  position: absolute;
  display: flex;
  &-content {
    cursor: move;
    transition: all 1s cubic-bezier(0.23, 1, 0.32, 1);
  }
  &-opera {
    @include wh(54px, 100%) {
      padding-top: 4px;
    }
    &-close {
      cursor: pointer;
      @include wh(24px) {
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(12px, rgba(255, 255, 255, 0.8)) {
        text-align: center;
      }
      line-height: 24px;
    }
    &-toggle {
      overflow: hidden;
      cursor: pointer;
      margin-top: 10px;
      @include wh(24px, 24px) {
        padding: 0 6px;
        border-radius: 12px;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(12px, rgba(255, 255, 255, 0.8)) {
        line-height: 24px;
      }

      transition: width 0.4s ease;
      span {
        padding-left: 4px;
      }
      &:hover {
        width: 54px;
      }
    }
  }
}
</style>
