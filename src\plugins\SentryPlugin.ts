import * as Sentry from "@sentry/vue";

export const initSentry = (app, router) => {
  Sentry.init({
    release: import.meta.env.VITE_SENTRY_RELEASE,
    environment: import.meta.env.VITE_SENTRY_SOURCE_MAP,
    app,
    dsn: "https://<EMAIL>:8443/sentry//2",
    debug: false,

    integrations: [
      new Sentry.Integrations.Breadcrumbs({
        console: false,
      }),
      // 追踪性能
      new Sentry.BrowserTracing({
        routingInstrumentation: Sentry.vueRouterInstrumentation(router),
      }),
      // 回放
      new Sentry.Replay({
        // 捕获请求和响应细节
        networkDetailAllowUrls: ["localhost:5173/dev/", "**************/prod/"],
        maskAllText: false,
      }),
    ],

    // --------错误
    sampleRate: 1.0,
    maxBreadcrumbs: 30,
    // ignoreErrors: ["404"],
    // denyUrls: ["/node_modules/.vite/deps/axios.js"],
    beforeSend(event, hint) {
      return event;
    },

    // --------追踪
    tracesSampler: (samplingContext) => 1.0,
    // ignoreTransactions: ["userManage"],
    beforeSendTransaction(event) {
      return event;
    },

    // --------回放
    replaysSessionSampleRate: 0,
    replaysOnErrorSampleRate: 1.0,
  });
};
