<template>
  <section class="area-work">
    <sub-title>区域作业排名</sub-title>
    <div class="area-work-top">
      <div class="toggle-button">
        <div
          :class="['toggle-button-time', { enable: areaWork.btnIndex === 0 }]"
          @click="toggleSelect(0)"
        >
          时长
        </div>
        <div
          :class="['toggle-button-area', { enable: areaWork.btnIndex === 1 }]"
          @click="toggleSelect(1)"
        >
          面积
        </div>
      </div>
      <div class="toggle-select">
        <x-select
          v-model:value="areaWork.selValue"
          :options="areaWork.selOptions"
        />
      </div>
    </div>
    <div
      ref="areaWorkRef"
      class="area-work-bottom"
      @mouseenter="stopAnimate"
      @mousemove="stopAnimate"
      @mouseleave="startAnimate"
      @wheel="areaWorkWheel"
    >
      <div
        class="area-work-item"
        v-for="(item, index) in rankList"
        :key="index"
      >
        <div class="area-work-item-left">
          {{ item.rank }}
        </div>
        <div class="area-work-item-right">
          <div class="area-name">{{ item.name }}</div>
          <div class="area-work-time">{{ item.data }}</div>
        </div>
      </div>
    </div>
  </section>
</template>
<script lang="ts" setup>
import { ref, reactive, onMounted, onUnmounted, computed, watch } from "vue";
import subTitle from "./subTitle.vue";
import xSelect from "./x-select.vue";
import { animateTime } from "@/assets/ts/animate";
import { getTimeRange } from "@/assets/ts/dateTime";
import { screenAreaWork } from "@/services/api";

const props = withDefaults(
  defineProps<{
    proId: string;
  }>(),
  {}
);

onMounted(() => {
  updateScroll();
  startAnimate();
});

const areaWorkRef = ref<HTMLElement>();
const areaWork = reactive({
  timer: null,
  btnIndex: 0,
  selValue: String(getTimeRange(0)),
  selOptions: [
    {
      value: String(getTimeRange(0)),
      label: "当天",
    },
    {
      value: String(getTimeRange(1)),
      label: "昨天",
    },
    {
      value: String(getTimeRange(0, 6)),
      label: "近7天",
    },
    {
      value: String(getTimeRange(0, 29)),
      label: "近30天",
    },
  ],
  rankTimeList: [],
  rankAreaList: [],
  cancelAnimate: null,
  maxScrollTop: 0,
  scrollTop: 0,
});
const animateNum = 2; // 大于等于2个才能实现无限滚动
const rankList = computed(() => {
  const reqList =
    areaWork.btnIndex === 0 ? areaWork.rankTimeList : areaWork.rankAreaList;
  return reqList.length >= animateNum
    ? [...reqList, ...reqList, ...reqList]
    : reqList;
});
onMounted(() => {
  // 切换项目
  watch(
    () => props.proId,
    (newV) => {
      if (areaWork.selValue === areaWork.selOptions[0].value) {
        updateData(newV, ...areaWork.selOptions[0].value.split(","));
      } else {
        areaWork.selValue = areaWork.selOptions[0].value;
      }
    }
  );
  // 切换日期
  watch(
    () => areaWork.selValue,
    (newV) => {
      updateData(props.proId, ...newV.split(","));
    }
  );
  // 10分钟更新一次
  areaWork.timer = setInterval(() => {
    updateData(props.proId, ...areaWork.selOptions[0].value.split(","));
  }, 10 * 60 * 1000);
});

onUnmounted(() => {
  clearInterval(areaWork.timer);
});

const updateData = (proId: string, workStart: string, workEnd: string) => {
  screenAreaWork({
    proId,
    workStart,
    workEnd,
  }).then((res) => {
    areaWork.rankTimeList = res.cleanTimeList.map(
      ({ name, statisticalData }, index) => ({
        rank: (index + 1).toString().padStart(2, "0"),
        data: statisticalData + "h",
        name,
      })
    );
    areaWork.rankAreaList = res.cleanAreaList.map(
      ({ name, statisticalData }, index) => ({
        rank: (index + 1).toString().padStart(2, "0"),
        data: statisticalData + "㎡",
        name,
      })
    );
    updateDataAnimate();
  });

  // areaWork.rankTimeList = new Array(Math.ceil(Math.random() * 10)).fill({
  //   name: "测试",
  //   data: "111111",
  //   rank: "01",
  // });

  // areaWork.rankAreaList = new Array(Math.ceil(Math.random() * 10)).fill({
  //   name: "测试",
  //   data: "111111",
  //   rank: "01",
  // });
};

const toggleSelect = (selIndex: number) => {
  areaWork.btnIndex = selIndex;
  updateDataAnimate();
};

const animate = (scroll?: number) => {
  areaWork.scrollTop += scroll || 0.5;
  if (areaWork.scrollTop > areaWork.maxScrollTop || areaWork.scrollTop < 0) {
    areaWork.scrollTop = areaWork.maxScrollTop / 2;
  }
  areaWorkRef.value.scrollTop = areaWork.scrollTop;
};
const updateScroll = () => {
  if (rankList.value.length >= animateNum) {
    areaWork.maxScrollTop =
      (areaWorkRef.value.clientHeight / animateNum) *
      (rankList.value.length - animateNum);
    areaWork.scrollTop =
      (areaWorkRef.value.clientHeight / animateNum) *
      (rankList.value.length / 3);
    areaWorkRef.value.scrollTop = areaWork.scrollTop;
  }
};
const startAnimate = () => {
  if (rankList.value.length >= animateNum) {
    areaWork.cancelAnimate = animateTime(animate);
  }
};

// 由于当选择日期后鼠标会自动进入，导致没有触发mouseenter，所以也把mousemove绑定了
const stopAnimate = () => {
  areaWork.cancelAnimate && areaWork.cancelAnimate();
};
const updateDataAnimate = () => {
  stopAnimate();
  updateScroll();
  startAnimate();
};
const areaWorkWheel = (e: Event) => {
  const wheelIsUp = e.deltaY < 0;
  wheelIsUp ? animate(-10) : animate(10);
};
</script>

<style lang="scss" scoped>
.area-work {
  @include fj {
    flex-direction: column;
  }
  @include wh(100%);
  .area-work-top {
    @include fj;
    @include wh(100%, 32px) {
      padding: 0 22px;
    }
  }
  .area-work-bottom {
    // flex: 1;
    height: 13vh;
    width: 100%;
    padding: 0 14px;
    overflow-y: hidden;
    .area-work-item {
      @include fj;
      @include wh(100%, 48%) {
        margin-top: 0.4vh;
      }
      &-left {
        @include ct-f;
        @include wh(54px, 100%) {
          @include bis("@/assets/images/screen_area_work_rank.png");
          @include sc(2vh, rgb(238, 241, 255)) {
            font-weight: 700;
          }
        }
      }
      &-right {
        @include fj {
          align-items: center;
        }
        flex: 1;
        height: 100%;
        padding: 0 26px;
        margin-right: 14px;
        @include bis("@/assets/images/screen_area_work_content.png");
        background-size: 100% 1.2vh;
        background-position: 0 calc(100% - 0.6vh);
        color: #fff;
        .area-name {
          font-size: 1.6vh;
        }
        .area-work-time {
          font-size: 1.4vh;
        }
      }
    }
  }
  .toggle-button {
    display: flex;
    @include wh(116px, 100%) {
      background-color: #414352;
      border-radius: 4px;
    }
    &-time,
    &-area {
      cursor: pointer;
      flex: 1;
      height: 100%;
      border-radius: 4px;
      @include sc(1.6vh, rgb(248, 250, 255)) {
        text-align: center;
        line-height: 32px;
      }
      &.enable {
        font-weight: 700;
        background-color: #5a76fc;
      }
    }
  }
  .toggle-select {
    width: 100px;
  }
}
</style>
