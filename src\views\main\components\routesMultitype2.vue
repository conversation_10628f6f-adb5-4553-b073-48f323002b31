<template>
  <div class="route-item-container" ref="taskContentRef">
    <div
      class="route-item"
      v-for="(item, index) in listModel"
      :key="index"
      @mouseover="toggleHover(index, true)"
      @mouseleave="toggleHover(index, false)"
    >
      <div class="route-item-content">
        <div class="route-item-content-left">
          {{ (index + 1).toString().padStart(2, "0") }}
        </div>
        <div class="route-item-content-center">
          <x-select
            v-model:value="item.id"
            :options="props.options"
            :popupContainer="props.container"
            @change="selectChange($event, index)"
            :placeholder="version === 1 ? $t('PSelectRoute') : '请选择区块'"
            style="flex: 1"
          />
          <!-- v-if="item.type === 'route'" 2.0 区块和路线混合选择时判断 -->
          <x-cascader
            v-if="version === 1"
            v-model:value="item.sweepingTypeList"
            :options="formOptions.cascader[index]"
            :popupContainer="props.container"
            :allowClear="false"
            :autoSelectFirstChild="true"
            menuWidth="300"
            @update:value="changeCleaningType($event, index)"
            style="width: 200px"
          />
        </div>
        <div class="route-item-content-right" v-if="item.hovered">
          <x-icon
            v-if="listModel.length > 1"
            class="minus-icon"
            name="minus_circle"
            width="20"
            height="20"
            @click="delRoute(index)"
          />
          <x-icon
            v-if="listModel.length < maxRouteLength"
            class="plus-icon"
            name="plus_circle"
            width="20"
            height="20"
            @click="addRoute(index)"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import { reactive, watch, ref } from "vue";
import xIcon from "@/components/x-icon.vue";
import xSelect from "@/components/x-select.vue";
import xCascader from "@/components/x-cascader.vue";
import { baseCleaningType, fullCleaningType } from "@/assets/ts/config";
import { treeBfsParse, i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

interface ListType {
  id: string;
  name?: string;
  vehRouteNo: string;
  taskType: string;
  sweepingType?: string;
  sweepingTypeList?: string[];
  cleanTypeArrays?: string[];
  type?: "route" | "block";
  label?: string;
  value?: string;
  hovered?: boolean;
}

const props = defineProps({
  list: {
    type: Array<ListType>,
    required: true,
  },
  /** 下拉框列表 */
  options: {
    type: Array<ListType>,
    required: true,
  },
  container: {
    type: HTMLElement,
  },
  // 最大路线条数
  maxRouteLength: {
    type: Number,
    default: 10,
  },
  /** 模版版本 */
  version: {
    type: Number,
    default: 1,
  },
});

const emits = defineEmits(["update:list", "delRoute", "changeRoute"]);

const taskContentRef = ref<any>();

const listModel = ref<ListType[]>([
  {
    id: "0",
    name: "",
    vehRouteNo: "",
    taskType: "",
    sweepingType: "",
    sweepingTypeList: [] as string[],
    hovered: false,
  },
]);

// 添加路线
const addRoute = (index: number) => {
  listModel.value.splice(index + 1, 0, {
    id: "0",
    vehRouteNo: "",
    taskType: "",
    sweepingType: "",
    sweepingTypeList: [] as string[],
    hovered: false,
  });
  emits("update:list", listModel.value);
};

// 删除路线
const delRoute = (index: number) => {
  listModel.value.splice(index, 1);
  emits("update:list", listModel.value);
  emits("delRoute", index);
};

// 更改路线
const selectChange = (value: string, index: number) => {
  const item = props.options.find((item) => item.value === value);
  if (item?.type === "route") {
    listModel.value[index].name = item.name;
    listModel.value[index].vehRouteNo = item.vehRouteNo;
    listModel.value[index].type = "route";
    getTaskTypeList(value, index);
  } else {
    listModel.value[index].type = "block";
    listModel.value[index].name = item?.name;
  }
  emits("changeRoute", listModel.value);
};

const formOptions = reactive({
  cascader: [] as any[][],
});

treeBfsParse(fullCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

treeBfsParse(baseCleaningType, "children", (item: any, count: number) => {
  if (item.index === undefined) {
    item.index = count;
  }

  if (item.children && item.children.length > 0) {
    item.children.forEach((ele: any, i: number) => {
      ele.index = i;
    });
  }
});

// 根据路线获取任务类型
const getTaskTypeList = (value: string, index: number) => {
  if (value) {
    const selectedRoute: any =
      props.options.find((item) => item.value === value) || {};
    formOptions.cascader[index] = selectedRoute.cleanTypeArrays?.includes(
      "edgewise"
    )
      ? fullCleaningType
      : baseCleaningType;
  } else {
    formOptions.cascader[index] = [];
  }
};

// 更改清扫类型
const changeCleaningType = (value: string, index: number) => {
  listModel.value[index].taskType = value[0];
  listModel.value[index].sweepingType = value[1] || "";
  emits("update:list", listModel.value);
};

// 将数据更新分批处理
const updateFormRouteList = (data: ListType[]) => {
  const batchSize = 10;
  let index = 0;
  const updateBatch = () => {
    const slice = data.slice(index, index + batchSize);
    listModel.value.splice(index, batchSize, ...slice);
    index += batchSize;
    if (index < data.length) {
      requestAnimationFrame(updateBatch);
    }
  };
  updateBatch();
};

watch(
  () => props.list,
  (newV) => {
    newV.forEach((item, index) => {
      getTaskTypeList(item.vehRouteNo, index);
    });
    updateFormRouteList(newV);
  },
  {
    immediate: true,
  }
);

const toggleHover = (index: number, isHovered: boolean) => {
  listModel.value[index].hovered = isHovered;
};
</script>
<style lang="scss" scoped>
.route-item-container {
  .route-item {
    padding: 10px;
    &-content {
      @include ct-f(y);
      &-left {
        @include wh(20px, 20px) {
          line-height: 20px;
          text-align: center;
        }
        @include sc(14px, #9f9fa4) {
          border: 1px solid #9f9fa4;
          border-radius: 50px;
        }
      }
      &-center {
        display: flex;
        margin-left: 10px;
        column-gap: 10px;
        width: 380px;
      }
      &-right {
        display: flex;
        margin-left: 10px;
        .minus-icon,
        .plus-icon {
          cursor: pointer;
        }
        .minus-icon {
          margin-right: 5px;
        }
      }
    }
    &:hover {
      box-shadow: 0px 0px 20px rgba(155, 162, 190, 0.2);
      border-radius: 8px;
      transition: all 0.3s ease, border 0.3s ease;
    }
  }
}
</style>
