/// <reference types="vite/client" />

declare class byskplayer {
  constructor(obj: any);
}

interface ImportMetaEnv {
  /** 请求地址前缀 */
  readonly VITE_BASE_URL: string;
  /** websocket地址 */
  readonly VITE_WS_URL: string;
  /** 视频请求地址 */
  readonly VITE_VIDEO_BASE_URL: string;
  /** 代理地址 */
  readonly VITE_APP_PROXY_URL: string;
  /** 高德地图key */
  readonly VITE_APP_AMAP_KEY: string;
  /** 高德地图安全密钥 */
  readonly VITE_APP_AMAP_SECURITY_JS_CODE: string;
  // 更多环境变量...
}

interface ImportMeta {
  readonly env: ImportMetaEnv;
}
