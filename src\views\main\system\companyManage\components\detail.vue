<template>
  <x-drawer
    :title="$t('companyDetail')"
    :visible="props.show"
    @update:visible="updateVisible"
    bodyPadding="20px 0 20px 20px"
    width="570px"
  >
    <div class="content">
      <div class="content-info-panel">
        <div class="content-info-panel-title">
          {{ detail.companyinfo.entName }}
        </div>
        <div class="content-info-panel-subtitle">
          <div class="content-info-panel-subtitle-item">
            {{ detail.companyinfo.userName }}
          </div>
          <div class="content-info-panel-subtitle-item">
            {{ detail.companyinfo.mobile }}
          </div>
        </div>
        <div class="content-info-panel-body">
          <template v-for="(item, index) in bottomInfo" :key="index">
            <div class="content-info-panel-body-item" v-if="item.value">
              <div class="content-info-panel-body-item-label">
                {{ item.label }}
              </div>
              <div class="content-info-panel-body-item-value">
                {{ item.value }}
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="content-fun">
        <div class="content-fun-title">{{ $t("functionSetting") }}</div>
        <div class="content-fun-thead">
          <div class="content-fun-thead-th">{{ $t("interface") }}</div>
          <div class="content-fun-thead-td">{{ $t("function") }}</div>
        </div>
        <div
          v-for="item in detail.companyinfo?.permitTree?.subPermList || []"
          :key="item.menuId"
          class="content-fun-tbody"
        >
          <div class="content-fun-tbody-th">{{ item.name }}</div>
          <div class="content-fun-tbody-td">
            <div
              v-for="sub in item.subPermList"
              :key="sub.menuId"
              class="content-fun-tbody-td-item"
            >
              {{ sub.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
  </x-drawer>
</template>

<script lang="ts" setup>
import { reactive, watch, computed } from "vue";
import { getCompanyDetail } from "@/services/api";
import xDrawer from "@/components/x-drawer.vue";
import type { GetCompanyDetailResponse } from "@/services/type";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("companyManage");
/**
 * 编译器宏-组件外允许传入的属性
 */
const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  id: {
    type: Number,
    required: true,
  },
});
/**
 * 编译器宏-组件内允许发出的事件
 */
const emits = defineEmits(["update:show"]);

/**
 * 显示隐藏
 */
const updateVisible = (bool: boolean) => emits("update:show", bool);

/**
 * 详情数据
 */
const detail = reactive({
  companyinfo: {} as GetCompanyDetailResponse,
});

/**
 * 依赖详情数据的计算属性
 */
const bottomInfo = computed(() => [
  {
    label: $t("parentEnt") + "：",
    value: detail.companyinfo.parentEnt,
  },
  {
    label: $t("mainAccount") + "：",
    value: detail.companyinfo.userAccount,
  },
  {
    label: $t("address") + "：",
    value: detail.companyinfo.entAddr,
  },
]);

/**
 * 监听显示
 */
watch(
  () => props.show,
  async (newV) => {
    if (newV) {
      detail.companyinfo = await getCompanyDetail(props.id);
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  margin-right: 20px;
  &-info-panel {
    background: rgb(255, 255, 255);
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(72, 111, 245, 0.14);
    padding: 24px 20px 56px 20px;
    &-title {
      @include sc(16px, #242859) {
        font-weight: 700;
      }
    }
    &-subtitle {
      @include fj(flex-start) {
        margin-top: 10px;
      }
      @include sc(14px, #383838);
      &-item::after {
        content: "|";
        color: #e5e5e5;
        padding: 0 12px;
      }
      &-item:last-child::after {
        display: none;
      }
    }
    &-body {
      padding-top: 10px;
      &-item {
        @include fj(flex-start) {
          margin-top: 20px;
        }
        &-label {
          @include sc(14px, #9f9fa4) {
            width: 5em;
          }
        }
        &-value {
          @include sc(14px, #383838) {
            flex: 1;
            overflow: hidden;
          }
        }
      }
    }
  }
  &-fun {
    padding: 20px;
    margin-top: 15px;
    border-radius: 8px;
    background: #fff;
    &-title {
      @include sc(16px, #242859);
    }
    &-thead {
      margin-top: 16px;
      line-height: 36px;
      @include fj(flex-start) {
        background: linear-gradient(
          180deg,
          rgba(89, 100, 251, 0.12),
          rgba(89, 100, 251, 0.03) 100%
        );
      }
      &-th {
        @include sc(14px, #383838) {
          width: 140px;
          padding: 0 24px;
        }
      }
      &-td {
        @include sc(14px, #383838);
        flex: 1;
        overflow: hidden;
      }
    }
    &-tbody {
      @include fj(flex-start) {
        align-items: center;
        background-color: #fafafa;
        padding: 16px 0;
        position: relative;
      }
      &::after {
        content: "";
        position: absolute;
        left: 139px;
        top: 16px;
        bottom: 16px;
        width: 1px;
        background-color: #dce3fb;
      }
      &-th {
        @include sc(14px, #383838) {
          width: 140px;
          padding: 0 24px;
        }
      }
      &-td {
        @include sc(14px, #383838);
        flex: 1;
        overflow: hidden;
        &-item {
          display: inline-block;
          vertical-align: top;
          margin-left: 24px;
          padding: 10px;
        }
      }
    }
  }
}
</style>
