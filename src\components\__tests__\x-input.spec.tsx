import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import XInput from '@/components/x-input.vue';

const baseProps = { value: 'text' } 

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XInput,{
      props: { ...baseProps }
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 props', () => {
    const wrapper = shallowMount(XInput, {
      props: {
        type: "password",
        prefix: 'prefixString',
        suffix: 'suffixString',
        placeholder: 'placeholder',
        maxlength: 66,
        disabled: true,
        ...baseProps
      }
    });
    expect(wrapper.vm.value).toBe('text');
    expect(wrapper.find('input').attributes().autocomplete).toBe('new-password');
    expect(wrapper.find('.content-prefix').find('x-icon').attributes().name).toBe('prefixString');
    expect(wrapper.find('.content-suffix').find('x-icon').attributes().name).toBe('suffixString');
    expect(wrapper.find('input').attributes().placeholder).toBe('placeholder');
    expect(wrapper.find('input').attributes().maxlength).toBe('66');
    expect(wrapper.find('input').attributes().disabled).toBeDefined();
  });
});

describe('Events', () => {
  it('触发focus后检查allowClear是否生效', async () => {
    const wrapper = shallowMount(XInput, {
      props: {
        allowClear: true,
        ...baseProps
      }
    });
    await wrapper.find('input').trigger('focus');
    expect(wrapper.find('.show-clear').find('x-icon').attributes().name).toBe('input_clear');
  });
  it('触发input后检查filter是否生效', () => {
    const wrapper = shallowMount(XInput, {
      props: {
        filter: (value) => value + 'Filter',
        ...baseProps
      }
    });
    wrapper.find('input').trigger('input');
    expect(wrapper.emitted()['update:value'][0][0]).toBe('textFilter');
  });
  it('检测foucs和blur的触发逻辑', async () => {
    const wrapper = shallowMount(XInput, {
      props: {
        ...baseProps
      }
    });

    await wrapper.find('input').trigger('focus');
    expect(wrapper.emitted().focus[0]).toBeDefined();
    expect(wrapper.vm.isFocus).toBe(true);

    await wrapper.find('input').trigger('blur');
    expect(wrapper.emitted().blur[0]).toBeDefined();
    expect(wrapper.vm.isFocus).toBe(false);
  });
});
