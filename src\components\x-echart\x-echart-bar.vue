<!-- 
<x-echart-bar 
  style="width: 500px; height: 500px" 
  :xLabels="barData.xLabels" 
  :values="barData.values" 
/>
const barData = {
  xLabels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
  values: [120, 200, 150, 80, 70, 110, 130],
}; 
-->

<template>
  <section class="x-echart-bar">
    <echart-base :options="options"></echart-base>
  </section>
</template>
<script lang="ts" setup>
import { computed } from "vue";
import type { EChartsOption } from "echarts";
import EchartBase from "./base.vue";

const props = withDefaults(
  defineProps<{
    barOptionType?: "default" | "test";
    title?: string;
    xLabels: string[];
    values: any[];
  }>(),
  {
    title: "",
    barOptionType: "default",
  }
);

const options = computed<EChartsOption>(() => {
  const baseOption = {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {},
    toolbox: {
      feature: {
        saveAsImage: {},
      },
    },
    grid: {
      left: "3%",
      right: "4%",
      bottom: "3%",
      containLabel: true,
    },
  };
  return {
    default: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: [
        {
          type: "category",
          data: props.xLabels,
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "销量",
          type: "bar",
          barWidth: "60%",
          data: props.values,
        },
      ],
    },
    test: {
      ...baseOption,
      title: {
        text: props.title,
      },
      xAxis: [
        {
          type: "category",
          data: props.xLabels,
        },
      ],
      yAxis: [
        {
          type: "value",
        },
      ],
      series: [
        {
          name: "销量",
          type: "bar",
          barWidth: "60%",
          data: props.values,
        },
      ],
    },
  }[props.barOptionType] as EChartsOption;
});
</script>

<style lang="scss" scoped>
.x-echart-bar {
  @include wh(100%);
}
</style>
