import TimePicker from "./src/time-picker.vue";
import TimeRangePicker from "./src/time-range-picker.vue";

import type { App } from "vue";

export * from "./src/type";

export { TimePicker, TimeRangePicker };
export default Object.assign(TimePicker, {
  TimePicker,
  TimeRangePicker,
  install: (app: App) => {
    app.component("XTimePicker", TimePicker);
    app.component("XTimeRangePicker", TimeRangePicker);
    return app;
  },
});
