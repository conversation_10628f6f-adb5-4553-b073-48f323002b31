<template>
  <svg
    :class="`x-icon ${props.className ?? ''}`"
    :style="svgStyle"
    aria-hidden="true"
    @mousedown="isActive = true"
    @mouseup="isActive = false"
  >
    <use :xlink:href="`#icon-${props.activeName && isActive ? props.activeName : props.name}`" />
  </svg>
</template>

<script lang="ts" setup>
import { computed, ref } from "vue";

const props = defineProps<{
  name: string;
  activeName?: string;
  className?: string;
  width?: string;
  height?: string;
  color?: string;
}>();

const isActive = ref(false);
const svgStyle = computed(() => {
  let style = "";
  if (props.width) style += `width: ${props.width};`;
  if (props.height) style += `height: ${props.height};`;
  if (props.color) style += `color: ${props.color};`;
  return style;
});
</script>

<style lang="scss" scoped>
.x-icon {
  width: 1em;
  height: 1em;
  vertical-align: -0.15em;
  fill: currentColor;
  overflow: hidden;
  transition: width 0.6s, height 0.6s;
}
</style>
