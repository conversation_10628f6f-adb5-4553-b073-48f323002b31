<template>
  <div class="car-info-page">
    <xIcon
      v-if="carInfo.inCharge || pipelineData.charge"
      class="repair-tag"
      :name="carInfo.inCharge ? 'repair-tag' : 'repair-tag-2'"
      :width="114"
      :height="109"
    ></xIcon>
    <x-radio-button
      type="tab"
      :gap="10"
      v-model:value="readTab.activeIndex"
      :options="readTab.statusList"
    />
    <car-status-part v-if="readTab.activeIndex === 'carStatus'" />
    <task-status-part
      :deviceId="deviceId"
      v-if="readTab.activeIndex === 'taskStatus'"
    />
    <remote-status-part
      v-if="readTab.activeIndex === 'remoteStatus'"
      :pipelineData="pipelineData"
    />
    <advanced-config-part v-if="readTab.activeIndex === 'advancedConfig'" />
  </div>
</template>

<script setup lang="ts">
import { inject, onMounted, reactive, Ref, ref } from "vue";
import carStatusPart from "./car_status_part.vue";
import taskStatusPart from "./task_status_part.vue";
import remoteStatusPart from "./remote_status_part.vue";
import advancedConfigPart from "./advanced_config_part.vue";
import type { CarInfo } from "../../../type";
import { getVehicleAdvancedConfig } from "@/services/api";
const props = defineProps({
  deviceId: {
    type: String,
    default: "",
  },
});
const carInfo = inject("carInfo") as Ref<CarInfo>;

const readTab = reactive({
  activeIndex: "carStatus",
  statusList: [
    { label: "车辆状态", value: "carStatus" },
    { label: "任务状态", value: "taskStatus" },
    { label: "远程状态", value: "remoteStatus" },
    { label: "高级配置", value: "advancedConfig" },
  ],
});

const pipelineData = ref({
  charge: false,
  data: [] as any[],
  id: "",
});
const init = async () => {
  const res = await getVehicleAdvancedConfig({
    deviceId: carInfo.value.deviceId,
  });
  pipelineData.value = {
    charge: res.charge === 1 ? true : false,
    data: [res.chargeStartTime, res.chargeEndTime],
    id: res.id,
  };
};
onMounted(() => {
  init();
});

const reset = () => {
  readTab.activeIndex = "carStatus";
};

defineExpose({
  reset,
});
</script>
<script lang="ts">
export default {
  name: "carInfoPage",
};
</script>
<style scoped lang="scss">
.car-info-page {
  height: 100%;
  flex: 1;
  user-select: none;
  position: relative;
  .repair-tag {
    position: absolute;
    top: -22px;
    right: -23px;
  }
}
</style>
