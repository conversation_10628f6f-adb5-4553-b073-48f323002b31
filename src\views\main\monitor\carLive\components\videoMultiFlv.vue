<template>
  <!-- <section
    ref="videoMultiFlvRef"
    class="video-multi-flv"
    :style="`left: ${opera.left}px; top: ${opera.top}px;`"
    @mousedown="handleMouseDown"
    @mousemove="handleMouseMove"
    @mouseup="handleMouseUp"
  > -->
  <section class="video-multi-flv">
    <div class="video-content" ref="videoMultiRef" :style="videoMultiStyle">
      <template v-if="windowTypeShow === 'small'">
        <div class="video-content-top">
          <video-flv
            v-if="_videoList.length"
            :url="_videoList[curIndex].url"
            :name="_videoList[curIndex].name"
            :position="_videoList[curIndex].position"
          />
        </div>
        <div class="video-content-bottom">
          <div
            :class="['bottom-item', { enabled: index === curIndex }]"
            v-for="(item, index) in _videoList"
            @click="curIndex = index"
            :key="index"
          >
            <div v-if="index === curIndex" class="bottom-item-playing">
              {{ $t("nowPlaying") }}...
            </div>
            <x-icon
              v-else
              class="bottom-item-play"
              name="video_play"
              width="18"
              height="18"
            />
            <div class="bottom-item-position">{{ item.position }}</div>
          </div>
        </div>
      </template>
      <template v-if="windowTypeShow === 'middle'">
        <div class="video-content-left">
          <video-flv
            style="height: 50%"
            v-for="(item, index) in _videoList.slice(0, 2)"
            :key="index"
            :url="item.url"
            :name="item.name"
            :position="item.position"
          />
        </div>
        <div class="video-content-right">
          <video-flv
            style="height: 50%"
            v-for="(item, index) in _videoList.slice(2)"
            :key="index"
            :url="item.url"
            :name="item.name"
            :position="item.position"
          />
        </div>
      </template>
    </div>
    <div class="video-opera" v-if="windowType != 'none'">
      <div class="video-opera-close" @click="close">✕</div>
      <div class="video-opera-fullscreen" @click="toggle('large')">
        <x-icon width="16px" height="16px" name="map_video_one" />
        <span>{{ $t("bigScreen") }}</span>
      </div>
      <div
        class="video-opera-toggle"
        @click="toggle(windowType === 'small' ? 'middle' : 'small')"
      >
        <x-icon
          width="16px"
          height="16px"
          :name="`map_video_${windowType === 'small' ? 'two' : 'four'}`"
        />
        <span>{{
          windowType === "small" ? $t("flatten") : $t("smallScreen")
        }}</span>
      </div>
    </div>
  </section>
</template>

<script lang="ts" setup>
import { computed, ref, watch, onMounted } from "vue";
import videoFlv from "./videoFlv.vue";
import type { videoInfoType } from "./videoFlv.vue";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carLive");

type windowTypeType = "none" | "small" | "middle" | "large";
const props = withDefaults(
  defineProps<{
    videoList: videoInfoType[];
  }>(),
  {}
);
const emits = defineEmits(["close"]);
const close = () => {
  toggle("none");
  emits("close");
};

// 关闭时  先动画缩小 => 后关闭视频流和隐藏dom
const windowType = ref<windowTypeType>("none"); // 用于width、height动画
const windowTypeShow = ref<windowTypeType>("none"); // 用于延迟隐藏dom
const _videoList = ref<videoInfoType[]>([]); // 用于延迟关闭视频流
const delayTime = 1000; // 延迟时间
onMounted(() => {
  watch(
    () => props.videoList,
    (newV) => {
      if (newV.length > 0) {
        _videoList.value = newV;
        if (windowType.value !== "middle") {
          toggle("small");
        }
      } else {
        toggle("none");
      }
    },
    {
      immediate: true,
    }
  );
});

const curIndex = ref(0);
const videoMultiStyle = computed(() => {
  let style = "";
  return (style += `
    width: ${
      {
        none: 0,
        small: 500,
        middle: 870,
        large: 500,
      }[windowType.value]
    }px;
    height: ${
      {
        none: 0,
        small: 338,
        middle: 591,
        large: 338,
      }[windowType.value]
    }px;`);
});

// 全屏时会自动占满屏，无需通过width和height来处理
// onMounted(() => {
//   document.addEventListener("fullscreenchange", fullscreenchangeHandle);
// });
// onBeforeUnmount(() => {
//   document.removeEventListener("fullscreenchange", fullscreenchangeHandle);
// });

// let prevWindowType: windowType = "small";
// const fullscreenchangeHandle = () => {
//   if (document.fullscreenElement) {
//     // console.log("进入全屏模式");
//   } else {
//     // console.log("退出全屏模式");
//     // windowType.value = prevWindowType;
//   }
// };

const videoMultiRef = ref<any>();
const toggle = (type: windowTypeType) => {
  ({
    none: () => {
      if (windowType.value === "none") return;
      windowType.value = "none";
      setTimeout(() => {
        windowTypeShow.value = "none";
        _videoList.value = [];
      }, delayTime);
    },
    small: () => {
      if (windowType.value === "small") return;
      windowType.value = "small";
      windowTypeShow.value = "small";
    },
    middle: () => {
      if (windowType.value === "middle") return;
      windowType.value = "middle";
      windowTypeShow.value = "middle";
    },
    large: () => {
      // 判断不同浏览器的全屏方法
      if (videoMultiRef.value.requestFullscreen) {
        videoMultiRef.value.requestFullscreen();
      } else if (videoMultiRef.value.mozRequestFullScreen) {
        /* Firefox */
        videoMultiRef.value.mozRequestFullScreen();
      } else if (videoMultiRef.value.webkitRequestFullscreen) {
        /* Chrome, Safari 和 Opera */
        videoMultiRef.value.webkitRequestFullscreen();
      } else if (videoMultiRef.value.msRequestFullscreen) {
        /* IE/Edge */
        videoMultiRef.value.msRequestFullscreen();
      }
      // 中屏切大屏需要切换回小屏样式
      // windowType.value = "small";
      // nextTick(() => {
      // 退出全屏时会受到transition的影响，导致瞬时变小
      videoMultiRef.value.style.transition = "none";
      // });
      // prevWindowType = windowType.value;
      // windowType.value = "large";
    },
  }[type]());
};

// 拖拽相关
// const videoMultiFlvRef = ref<any>();
// const opera = reactive({
//   zoom: 1,
//   isDragging: false,
//   left: 0,
//   top: 0,
// });
// const handleMouseDown = (e: MouseEvent) => {
//   console.log(11111111, "mousedown", e);
//   // 适配全局zoom缩放
//   opera.zoom = Number(getComputedStyle(document.querySelector(".main")!).zoom);
//   const { left: vLeft, top: vTop } =
//     videoMultiFlvRef.value.getBoundingClientRect();
//   const clientX = e.clientX / opera.zoom;
//   const clientY = e.clientY / opera.zoom;
//   opera.mouseInVideoX = clientX - vLeft;
//   opera.mouseInVideoY = clientY - vTop;

//   opera.isDragging = true;
// };
// const handleMouseMove = (e: MouseEvent) => {
//   if (opera.isDragging) {
//     console.log(11111111, "mousemove", e);
//     const clientX = e.clientX / opera.zoom;
//     const clientY = e.clientY / opera.zoom;
//     const {
//       left: pLeft,
//       top: pTop,
//       width: pWidth,
//       height: pHeight,
//     } = videoMultiFlvRef.value.parentElement.getBoundingClientRect();
//     let [left, top] = [
//       clientX - opera.mouseInVideoX - pLeft,
//       clientY - opera.mouseInVideoY - pTop,
//     ];

//     // 视频可拖动边界限制
//     const { width, height } = videoMultiFlvRef.value.getBoundingClientRect();
//     const leftMin = 0,
//       topMin = 0,
//       leftMax = pWidth - width,
//       topMax = pHeight - height;
//     if (left > leftMax) left = leftMax;
//     else if (left < leftMin) left = leftMin;
//     if (top > topMax) top = topMax;
//     else if (top < topMin) top = topMin;

//     opera.left = left;
//     opera.top = top;

//     // 鼠标超出视频边界结束拖动 (mousemove触发的局限性提供10px时效)
//     const {
//       left: vLeft,
//       right: vRight,
//       top: vTop,
//       bottom: vBottom,
//     } = videoMultiFlvRef.value.getBoundingClientRect();
//     if (
//       clientX <= vLeft + 10 ||
//       clientX >= vRight - 10 ||
//       clientY <= vTop + 10 ||
//       clientY >= vBottom - 10
//     ) {
//       handleMouseUp();
//     }
//   }
// };
// const handleMouseUp = () => {
//   console.log(11111111, "mouseup");
//   opera.isDragging = false;
// };
</script>

<style lang="scss" scoped>
.video-multi-flv {
  display: flex;
  .video-content {
    overflow: hidden;
    border-radius: 8px;
    background-color: #fff;
    transition: width 0.4s ease, height 0.4s ease;
    &-top {
      height: 83%;
    }
    &-bottom {
      display: flex;
      width: 100%;
      height: 17%;
      margin-top: 2px;

      .bottom-item {
        cursor: pointer;
        position: relative;
        @include ct-f;
        @include wh(100%) {
          margin-right: 2px;
        }
        &:last-child {
          margin-right: 0;
        }
        @include bis("@/assets/images/video_control_bg.png");
        &-playing {
          color: rgb(250, 252, 255);
          font-size: 12px;
        }
        &-position {
          position: absolute;
          top: 0.4%;
          right: 6%;
          color: #fff;
          font-size: 12px;
        }
        &.enabled {
          cursor: default;
          background-image: url("@/assets/images/video_control_bg_enable.png");
        }
      }
    }
    // &-left {
    //   display: inline-block;
    //   @include wh(calc(60% - 2px), 100%) {
    //     margin-right: 2px;
    //   }
    // }
    // &-right {
    //   display: inline-block;
    //   @include wh(40%, 100%);
    // }
    &-left {
      display: inline-block;
      @include wh(calc(50% - 1px), 100%) {
        margin-right: 2px;
      }
    }
    &-right {
      display: inline-block;
      @include wh(calc(50% - 1px), 100%);
    }
  }
  .video-opera {
    width: 56px;
    padding-left: 1px;
    padding-top: 4px;
    &-close {
      cursor: pointer;
      @include wh(24px) {
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(14px, rgba(255, 255, 255, 0.8)) {
        text-align: center;
      }
      line-height: 24px;
    }
    &-fullscreen,
    &-toggle {
      overflow: hidden;
      cursor: pointer;
      margin-top: 4px;
      @include wh(24px, 24px) {
        padding: 1px 4px 0 4px;
        border-radius: 12px;
        background-color: rgba(0, 0, 0, 0.3);
      }
      @include sc(12px, rgba(255, 255, 255, 0.8)) {
        line-height: 24px;
      }
      transition: width 0.4s ease;
      span {
        padding-left: 4px;
      }
      &:hover {
        width: 54px;
      }
    }
  }
}
</style>
