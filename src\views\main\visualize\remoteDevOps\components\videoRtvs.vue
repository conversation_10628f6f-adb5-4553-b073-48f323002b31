<template>
  <div class="remote-wrapper">
    <section
      class="video-rtvs"
      :style="{
        width: `${rtvsDivAttr.width}px`,
        height: `${rtvsDivAttr.height + 20}px`,
      }"
      ref="rtvsRef"
      v-show="socket.activeCarIdVideo"
    >
      <div
        class="video-rtvs-header"
        @mousedown="onMouseDown"
      ></div>
      <div
        class="video-rtvs-player"
        ref="playerRef"
      />
      <div
        class="video-rtvs-opera"
        v-show="socket.activeCarIdVideo"
      >
        <div
          class="video-rtvs-opera-close"
          @click="close"
        >
          ✕
        </div>
        <div
          class="video-rtvs-opera-toggle"
          @click="switchScreen"
        >
          <x-icon :name="`map_video_${playerCount === 1 ? 'four' : 'one'}`" />
          <span>{{ playerCount === 1 ? "小屏" : "大屏" }}</span>
        </div>
      </div>
    </section>
  </div>
</template>

<script lang="ts" setup>
import { liveCarVideoInfo } from "@/services/api";
import XMessage from "@/components/x-message";
import { useMainStore } from "@/stores/main";
import { computed, defineComponent, onMounted, reactive, ref, watch } from "vue";

const props = defineProps({
  left: {
    type: String,
    default: "300px",
  },
  top: {
    type: String,
    default: "60px",
  },
});

/** rtvs初始化配置 后期不可改 */
const videoPlayerOptions = {
  /** 集群管理地址 */
  clusterHost: "iot.chengshizhiguang.com",
  /** 集群管理端口 */
  clusterPort: "17000",
  /** 协议版本 0: 808-2013, 1: 808-2019, 2: GB28181 */
  protocol: 0,
  /** 是否使用cdn */
  isUseCdn: false,
  /** 初始化分屏数量 */
  MaxUcNum: 4,
  /** 单个视频显示宽度 父级盒子如有宽度即可不填 */
  // videoWidth: 500,
  /** 单个视频显示高度 父级盒子如有宽度即可不填 */
  // videoHeight: 280,
  /** 超时警告时间 默认4.5分钟通知 */
  timeoutWarningMsec: 270000,
  /** 超时时间 默认5分钟 */
  timeoutCloseMsec: 300000,
};

const layoutOpts = {
  1: {
    width: 500,
    height: 280,
  },
  4: {
    width: 1000,
    height: 560,
  },
};

const { socket, updateActiveCarIdVideo } = useMainStore();
const rtvsRef = ref<HTMLElement | null>(null);
const playerRef = ref<HTMLDivElement>();
const playerCount = ref<keyof typeof layoutOpts>(1);
const playerFull = ref<boolean>(false);
const playerType = ref<"realtime" | "history">("realtime");
const realtimeOptions = reactive<_RealtimeVideoOptions>({});
const playbackOptions = reactive<_PlaybackVideoOptions>({});

const rtvsDivAttr = computed(() => {
  const width = socket.activeCarIdVideo ? layoutOpts[playerCount.value].width : 0;
  const height = socket.activeCarIdVideo ? layoutOpts[playerCount.value].height : 0;

  console.log("rtvsDivAttr", width, height);
  return {
    width,
    height,
  };
});

let rtvs: any;

// 拖拽相关变量
// 偏移量（点击处相对 box 左上角的差值）
let offsetX = 0;
let offsetY = 0;

const onMouseDown = (e: MouseEvent) => {
  const el = rtvsRef.value;
  if (!el) return;

  offsetX = e.clientX - el.offsetLeft;
  offsetY = e.clientY - el.offsetTop;

  document.addEventListener("mousemove", onMouseMove);
  document.addEventListener("mouseup", onMouseUp);

  // 阻止默认行为，防止拖动过程中选中文本
  e.preventDefault();
};

const onMouseMove = (e: MouseEvent) => {
  const el = rtvsRef.value;
  if (!el) return;

  // 计算新位置
  const left = e.clientX - offsetX;
  const top = e.clientY - offsetY;

  // 防止拖出屏幕
  const maxX = window.innerWidth - el.clientWidth;
  const maxY = window.innerHeight - el.clientHeight;

  el.style.left = `${Math.max(0, Math.min(left, maxX))}px`;
  el.style.top = `${Math.max(0, Math.min(top, maxY))}px`;
};

const onMouseUp = () => {
  document.removeEventListener("mousemove", onMouseMove);
  document.removeEventListener("mouseup", onMouseUp);
};

/** 初始化 */
const rtvsInit = () => {
  // @ts-ignore
  rtvs = CvNetVideo.Init(playerRef.value, playerCount.value, {
    ...videoPlayerOptions,
    callback: rtvsInitCallback,
    events: {
      /** 超时通知 仅通知，如需关闭需用户在此事件响应中自行调用关闭接口 */
      timeoutClose: () => {
        XMessage("error", "长时间无操作，已自动关闭！");
        stopVideo();
      },
      timeoutWarning: () => {
        XMessage("error", "视频连接超时!!!");
        stopVideo();
      },
      /** 分屏双击回调， 返回值为 true表示取消双击放大 */
      onUcDbclick: () => true,
      /** 设备断开传输视频事件 */
      onStop: onVideoStop,
      onDevDisconnect: (id: number) => {
        console.log("设备断开传输视频事件", id);
        stopVideo(id);
      },
      onWsClose: (id: number) => {
        console.log("Websocket通道关闭事件", id);
        stopVideo(id);
      },
      onServerNotice: (type: string, id: number) => {
        console.log("服务端通知事件", type, id);
      },
      onEndByServer: (cause: number, id: number) => {
        XMessage("error", `视频${id} 播放结束，原因：${cause}`);
        stopVideo(id);
      },
      onPlayBackCacheTime: (ms: any, id: number) => {
        // console.log("回放缓存时间回调", ms, id);
      },
    },
  });
};

const rtvsInitCallback = () => {
  customControlsInit();
  const videoDivList = playerRef.value?.querySelectorAll(".video-box-body");
  videoDivList?.forEach((el: Element & { index?: number }, index) => {
    if (index < playerCount.value) {
      // 修复点击进度条无法继续播放的问题
      const playbackBarBackground = el.querySelector(".playbackbarbackgroud");
      playbackBarBackground?.addEventListener("click", () => {
        const pauseBtn = el.querySelector(".playpausebtn") as HTMLDivElement;
        pauseBtn.click();
      });

      // 修复关闭全屏时产生的视图大小与原来的视图大小不一致的问题
      const fullScreenBtn = el.querySelector(".fullscreenbtn");
      fullScreenBtn?.addEventListener("click", () => {
        if (playerFull.value) rtvs.Resize(rtvsDivAttr.value.width, rtvsDivAttr.value.height);
        playerFull.value = !playerFull.value;
      });
    }
  });
};

/** 初始化/更新 自定义内容 */
const customControlsInit = () => {
  // 隐藏右键菜单
  const menuDiv = playerRef.value?.querySelector(".video-menu-new");
  menuDiv?.classList.add("hide");

  // 针对每个窗口添加控件
  const videoDivList = playerRef.value?.querySelectorAll(".video-box-body");
  videoDivList?.forEach((el: Element & { index?: number }, index) => {
    if (index < playerCount.value) {
      // 对每个视频： 创建播放按钮或更新播放按钮
      let playBtnDiv = el.querySelector(".video-play-btn");
      if (playBtnDiv) {
        // 判断是否播放状态
        rtvs.IsPlaying(el.index) ? playBtnDiv.classList.add("hide") : playBtnDiv.classList.remove("hide");
      } else {
        // 显示可播放的视频窗口
        playBtnDiv = document.createElement("div");
        playBtnDiv.setAttribute("class", "video-play-btn");
        playBtnDiv.setAttribute("id", "video-play-btn-" + el.index);
        // 播放按钮事件
        playBtnDiv.addEventListener("click", () => {
          if (playerType.value === "realtime") {
            realtimeVideo(el.index);
          } else {
            // playbackVideo(el.index);
          }
        });
        el.appendChild(playBtnDiv);
      }

      // 对每个视频：添加描述或更新描述
      let descSpan = el.querySelector(".video-desc") as HTMLSpanElement;
      const descStr = `视频${el.index} @${
        (playerType.value === "realtime" ? realtimeOptions.simNumber : playbackOptions.simNumber) || "未知"
      }  ${socket.activeCarIdVideo || "未知"}`;
      if (descSpan) {
        descSpan.innerText = descStr;
      } else {
        descSpan = document.createElement("span");
        descSpan.setAttribute("class", "video-desc");
        descSpan.setAttribute("id", "video-desc-" + el.index);
        descSpan.innerText = descStr;
        el.appendChild(descSpan);
      }
    }
  });
};

/**
 * @desc 停止视频时事件
 * @param {Number} id 表示第几个分屏 从1开始 -1表示对讲通道
 * @param {any} ucVideo UCVideo对象
 */
const onVideoStop = (id: number) => {
  const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
  playBtnDiv?.classList.remove("hide");
};

/** 停止指定视频 默认停止全部 */
const stopVideo = (id = -1) => {
  rtvs.Stop(id);
  if (id === -1) {
    const playBtnDivList = playerRef.value?.querySelectorAll(".video-play-btn");
    playBtnDivList?.forEach((el) => {
      window.getComputedStyle(el).display === "none" && el.classList.remove("hide");
    });
  } else {
    const playBtnDiv = playerRef.value?.querySelector(`#video-play-btn-${id}`);
    playBtnDiv?.classList.remove("hide");
  }
};

/** 关闭弹窗 */
const close = () => {
  updateActiveCarIdVideo(null);
  stopVideo();
};

/** 切换分屏 */
const switchScreen = async () => {
  if (playerCount.value === 1) {
    playerCount.value = 4;
  } else {
    playerCount.value = 1;
    await new Promise((resolve) => setTimeout(resolve, 600)); // 解决大尺寸切换为小尺寸时的动画副作用
  }
  resizePlayer(rtvsDivAttr.value.width, rtvsDivAttr.value.height);
  setLayoutByScreens(playerCount.value);
  customControlsInit();
  for (let i = 2; i <= 4; i++) {
    const isPlaying = rtvs.IsPlaying(i);
    playerCount.value === 4 ? !isPlaying && realtimeVideo(i) : isPlaying && stopVideo(i);
  }
};

/** 重新设置播放控件整体所占用大小 */
const resizePlayer = (width: number, height: number) => {
  rtvs.Resize(width, height);
};

/** 设置分屏数量,默认支持1, 2, 4, 6, 8, 9, 10, 13, 16; 如果无效默认选择4分屏,可结合CustomScreens自定义分屏 */
const setLayoutByScreens = (num: number) => {
  rtvs.LayoutByScreens(num);
};

/** 实时播放 */
const realtimeVideo = (videoId = 0) => {
  if (!realtimeOptions.simNumber) return XMessage("error", "未找到视频信息");
  const playBtnDiv = playerRef.value?.querySelector("#video-play-btn-" + videoId);
  playBtnDiv?.classList.add("hide");
  const options: _RealtimeVideoOptions = {
    simNumber: undefined,
    channelId: videoId,
    streamType: 0,
    hasAudio: false,
    videoId,
    config: {},
    callback: undefined,
    playMode: 0,
    ...realtimeOptions,
  };
  rtvs.StartRealTimeVideo(
    options.simNumber,
    options.channelId,
    options.streamType,
    options.hasAudio,
    options.videoId,
    options.config,
    options.callback,
    options.playMode
  );
};

onMounted(() => {
  rtvsInit();

  // 设置初始位置
  if (rtvsRef.value) {
    rtvsRef.value.style.left = props.left;
    rtvsRef.value.style.top = props.top;
  }

  watch(
    () => socket.activeCarIdVideo,
    async (newV) => {
      if (newV) {
        // 重置到默认位置
        if (rtvsRef.value) {
          rtvsRef.value.style.left = props.left;
          rtvsRef.value.style.top = props.top;
        }

        resizePlayer(rtvsDivAttr.value.width, rtvsDivAttr.value.height);
        const carInfo = await liveCarVideoInfo(newV);
        if (carInfo) {
          realtimeOptions.simNumber = carInfo.dvrId || carInfo.deviceNo;
          customControlsInit();
          realtimeVideo(1);
        } else {
          XMessage("error", "未找到视频信息");
        }
      }
    }
  );
});
</script>
<script lang="ts">
export default defineComponent({
  name: "video-rtvs",
});
</script>
<style lang="scss">
.remote-wrapper {
  .video-rtvs {
    position: absolute;
    display: flex;
    flex-direction: column;
    background: #ffffff80;
    backdrop-filter: blur(4px);
    border-radius: 4px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.2);
    overflow: visible;
    z-index: 1001;
    user-select: none;
    transition: width 0.5s, height 0.5s;
    will-change: left, top; /* 性能优化，减少reflow */

    &-header {
      height: 20px;
      width: 100%;
      background: rgba(0, 0, 0, 0.2);
      cursor: move;
      flex-shrink: 0; /* 防止被挤压 */
    }

    &-player {
      width: 100%;
      flex: 1; /* 占用剩余空间 */
      height: calc(100% - 20px); /* 减去头部高度 */
      overflow: hidden;
      .video-container {
        background-color: transparent;
        .video-select,
        .video-box-body {
          border: none !important;
        }
        .video-box-body {
          .video-body {
            background: #516f8a80;
          }
        }
        .video-play-btn {
          position: absolute;
          top: calc(50% - 22px);
          left: calc(50% - 22px);
          width: 44px;
          height: 44px;
          cursor: pointer;
          background-image: url("https://szjieya-mes.oss-cn-shenzhen.aliyuncs.com/mes/web/resource/jaya/video-monitor/monitor-play.png");
          background-repeat: round;
          background-size: cover;

          &:hover {
            background-image: url("https://szjieya-mes.oss-cn-shenzhen.aliyuncs.com/mes/web/resource/jaya/video-monitor/monitor-play-active.png");
          }
        }
      }
      .video-desc {
        position: absolute;
        bottom: 5px;
        right: 10px;
        font-size: 12px;
        color: #fff;
      }
      .control-btn {
        &.stretchbtn {
          display: none;
        }
        &.fullscreenbtn {
          left: calc(100% - 25px);
        }
      }
      .hide {
        display: none;
      }
    }
    &-opera {
      position: absolute;
      left: calc(100% + 5px);
      top: 20px;
      z-index: 1001;
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      &-close {
        cursor: pointer;
        @include wh(24px) {
          border-radius: 50%;
          background-color: rgba(0, 0, 0, 0.3);
        }
        @include sc(12px, rgba(255, 255, 255, 0.8)) {
          text-align: center;
        }
        line-height: 24px;
      }
      &-toggle {
        overflow: hidden;
        cursor: pointer;
        margin-top: 10px;
        @include wh(24px, 24px) {
          padding: 0 6px;
          border-radius: 12px;
          background-color: rgba(0, 0, 0, 0.3);
        }
        @include sc(12px, rgba(255, 255, 255, 0.8)) {
          line-height: 24px;
        }

        transition: width 0.4s ease;
        span {
          padding-left: 4px;
        }
        &:hover {
          width: 54px;
        }
      }
    }
    // 解决初始化没有宽高的问题
    .video-div-rotate {
      width: 100% !important;
      height: 100% !important;
    }
  }
}
</style>
