<template>
  <x-modal
    :title="$t('filePreview')"
    :visible="props.show"
    :bodyStyle="{ padding: 0 }"
    :maskClosable="false"
    @update:visible="updateVisible"
    width="754px"
    height="574px"
  >
    <div class="content">
      <vue-office-excel
        :src="viewData.url"
        v-if="viewData.type === 'xlsx' || viewData.type === 'xls'"
      />
      <vue-office-docx
        :src="viewData.url"
        v-else-if="viewData.type === 'docx'"
      />
      <iframe
        :src="viewData.url"
        width="100%"
        height="100%"
        frameborder="0"
        v-else-if="viewData.type === 'pdf'"
      ></iframe>
      <div v-else class="no-result">
        <p>{{ $t("notPreviewPleaseDownload") }}</p>
      </div>
    </div>
  </x-modal>
</template>

<script lang="ts" setup>
import { reactive, watch } from "vue";
import xModal from "@/components/x-modal";
import VueOfficeDocx from "@vue-office/docx";
import "@vue-office/docx/lib/index.css";
import VueOfficeExcel from "@vue-office/excel";
import "@vue-office/excel/lib/index.css";
import { parseFilePath } from "@/components/x-upload/file-path";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("mainComps");

const props = defineProps({
  show: {
    type: Boolean,
    required: true,
  },
  url: {
    type: String,
    required: true,
  },
});

const emits = defineEmits(["confirm", "update:show"]);

const updateVisible = (bool: boolean) => {
  emits("update:show", bool);
};

const viewData = reactive({
  show: false,
  type: "",
  url: "",
});

watch(
  () => props.show,
  (newV) => {
    if (newV) {
      viewData.type = parseFilePath(props.url)?.suffix || "";
      viewData.url = props.url;
    }
  }
);
</script>

<style lang="scss" scoped>
.content {
  padding: 0;
  height: 480px;
  .no-result {
    @include ct-f(both);
    @include wh(100%);
    @include sc(14px, #555);
  }
}
// word适应
:deep(.docx-wrapper .docx) {
  width: auto !important;
}
// 滚动条
:deep(.vue-office-docx),
:deep(.x-spreadsheet-scrollbar) {
  @include scrollbar(both, 4px, rgb(233, 234, 248), rgba(89, 100, 251, 0.7));
}
</style>
