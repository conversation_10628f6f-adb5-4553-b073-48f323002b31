<template>
  <section class="x-tree">
    <Tree
      :treeData="props.treeData"
      :rootPath="[index]"
      :autoExpandParent="props.autoExpandParent"
      :clickHandle="props.clickHandle"
      :item="item"
      :key="index"
      :deep="1"
      :checkable="props.checkable"
      :multiple="props.multiple"
      :selectedItems="props.selectedItems"
      @update:selectedItems="updateselectedItems"
      :expandedKeys="props.expandedKeys"
      @update:expandedKeys="updateExpandedKeys"
      :selectedKeys="_selectedKeys"
      @update:selectedKeys="updateSelectedKeys"
      :selectedTitles="_selectedTitles"
      @update:selectedTitles="updateSelectedTitles"
      :checkedKeys="props.checkedKeys"
      @update:checkedKeys="updateCheckedKeys"
      v-for="(item, index) in props.treeData"
    >
      <template
        v-if="$slots.title"
        #title="{ title, item }"
      >
        <slot
          name="title"
          :title="title"
          :item="item"
        ></slot>
      </template>
    </Tree>
  </section>
</template>
<script lang="ts" setup>
import { reactive, watch } from "vue";
import type { PropType } from "vue";
import { treeBfsParse } from "@/assets/ts/utils";
import Tree from "./tree.vue";
import type { TreeItemType, Key } from "./tree.vue";

const props = defineProps({
  treeData: {
    type: Array as PropType<TreeItemType[]>,
    default: () => [],
  },
  autoExpandParent: {
    type: Boolean,
    default: () => false,
  },
  clickHandle: {
    // 自定义点击处理
    type: Function,
  },
  selectedItems: {
    type: Array as PropType<TreeItemType[]>,
    default: () => reactive([]),
  },
  selectedTitles: {
    // 选中的树节点
    type: Array as PropType<Key[]>,
    default: () => reactive([]),
  },
  selectedKeys: {
    // 选中的树节点
    type: Array as PropType<Key[]>,
    default: () => reactive([]),
  },
  expandedKeys: {
    // 展开的树节点
    type: Array as PropType<Key[]>,
    default: () => reactive([]),
  },
  checkable: {
    // 是否添加可选框
    type: Boolean,
    default: () => false,
  },
  checkedKeys: {
    // 选中复选框的树节点
    type: Array as PropType<Key[]>,
    default: () => reactive([]),
  },
  multiple: {
    // 多选
    type: Boolean,
    default: () => false,
  },
});

const emits = defineEmits([
  "update:selectedItems",
  "update:selectedKeys",
  "update:expandedKeys",
  "update:selectedTitles",
  "update:checkedKeys",
]);

watch(
  () => props.treeData,
  () => {
    // 修复props.selectedItems对应不上props.treeData的问题
    treeBfsParse(props.treeData, "children", (item: TreeItemType) => {
      if (
        (!item.children || item.children.length === 0) &&
        item.checked &&
        !props.selectedItems.some((v) => v.id === item.id)
      ) {
        props.selectedItems.push(item);
      }
    });
  }
);

const updateselectedItems = (items: TreeItemType[]) => {
  emits("update:selectedItems", items);
};

const _selectedTitles = reactive(props.selectedTitles || []);
const updateSelectedTitles = (titles: Key[]) => {
  if (props.checkable) {
    // 多选
    _selectedTitles.splice(0, _selectedTitles.length, ...titles);
    emits("update:selectedTitles", _selectedTitles);
  } else {
    // 针对下拉单选
    emits("update:selectedTitles", titles);
  }
};

const _selectedKeys = reactive(props.selectedKeys || []);
const updateSelectedKeys = (keys: Key[]) => {
  if (props.checkable) {
    // 多选
    _selectedKeys.splice(0, _selectedKeys.length, ...keys);
    emits("update:selectedKeys", _selectedKeys);
  } else {
    // 针对下拉单选
    emits("update:selectedKeys", keys);
  }
};

const updateExpandedKeys = (keys: Key[]) => {
  emits("update:expandedKeys", keys);
};
const updateCheckedKeys = (keys: Key[]) => {
  emits("update:checkedKeys", keys);
};
</script>

<style lang="scss" scoped>
.x-tree {
  width: 100%;
}
</style>
