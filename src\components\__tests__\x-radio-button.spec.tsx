import { describe, it, expect } from "vitest";
import { shallowMount } from '@vue/test-utils';
import XRadioButton from '@/components/x-radio-button.vue';

const baseProps = { 
  value:'value1',
  options: [
    { label: 'label1', value: 'value1', disabled: true },
    { label: 'label2', value: 'value2' },
    { label: 'label3', value: 'value3' },
  ],
} 

describe('Snapshot', () => {
  it('正确渲染 默认快照', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    expect(wrapper.html()).toMatchSnapshot();
  });
});

describe('Props Render', () => {
  it('正确渲染 所有选项', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    expect(wrapper.findAll('.x-radio-button-list-default li').length).toBe(3);
  });

  it('正确渲染 所有选项名', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-button-list-default li');
    radios.forEach((radio, index) => {
      const option = baseProps.options[index];
      expect(radio.find('.x-radio-button-label').text()).toBe(option.label);
    });
  });

  it('正确渲染 选中/非选中样式', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-button-list-default li');
    expect(radios[0].classes()).toContain('x-radio-button-checked');
    expect(radios[1].classes()).not.toContain('x-radio-button-checked');
  });

  it('正确计算 单选器间距', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: {
        gap: 20,
        ...baseProps
      }
    });
    const radios = wrapper.findAll('.x-radio-wrap li');
    for (const radio of radios) {
      expect(radio.attributes().style).toBe('padding: 0 20px;');
    };
  });

  it('正确渲染 插槽', () => {
    const text = 'Test Content';
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps,
      slots: {
        content: text,
      },
    });
    const radios = wrapper.findAll('.x-radio-button-list-default li');
    radios.forEach((radio) => {
      expect(radio.text()).toBe(text);
    });
  });
});

describe('Events Click', () => {
  it('点击选项时应触发update和change事件并传值', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-button-list-default li');
    radios[1].trigger('click');
    expect(wrapper.emitted()['update:value'][0][0]).toBe('value2');
    expect(wrapper.emitted().change[0][0]).toBe('value2');
  });

  it('禁用单个子单选器时应阻止该单选器点击事件', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: baseProps
    });
    const radios = wrapper.findAll('.x-radio-button-list-default li');
    radios[0].trigger('click');
    expect(wrapper.emitted().change).not.toBeDefined();
  });

  it('禁用所有子单选器时应阻止所有单选器点击事件', () => {
    const wrapper = shallowMount(XRadioButton, {
      props: { 
        disabled: true, 
        ...baseProps 
      }
    });
    wrapper.trigger('click');
    expect(wrapper.emitted().change).not.toBeDefined();
  });
})
