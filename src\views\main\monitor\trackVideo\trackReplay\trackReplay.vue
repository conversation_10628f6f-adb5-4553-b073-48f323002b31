<template>
  <section
    class="track-replay"
    ref="trackReplayRef"
  >
    <!-- 地图容器 -->
    <div id="container"></div>
    <!-- 搜索面板 -->
    <searchPanel
      @search="searchTrack"
      :deviceId="props.id"
      :popupContainer="trackReplayRef"
    />
    <!-- 进度播放器 -->
    <trackProgress
      v-show="trackInfo.navg"
      :value="trackInfo.trackPercent"
      :speedCount="trackInfo.speedCount"
      :playStatus="trackInfo.naviStatus"
      :goDisabled="true"
      :backDisabled="true"
      @togglePlay="changeMovingStatus"
      @changeProgress="changeProgress"
      @changeSpeed="changeSpeed"
      @rePlay="restartAnimation"
    />
  </section>
</template>
<script lang="ts" setup>
import { reactive, ref, onMounted, watch, createVNode, render } from "vue";
import {
  trackKeyPointStyle,
  trackEndPointStyle,
  trackStartPointStyle,
  trackPathLineStyle,
  trackPathLineHoverStyle,
  trackPathLinePassedStyle,
} from "@/services/wsconfig";
import AMapLoader from "@amap/amap-jsapi-loader";
import searchPanel from "./components/searchPanel.vue";
import trackProgress from "./components/trackProgress.vue";
import carMarker from "./components/carMarker.vue";
import pointMarker from "./components/pointMarker.vue";
import InfoWindow from "./components/infoWindow.vue";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
import { lazyAMapApiLoader } from "@/plugins/lazyAMapApiLoader";
const $t = i18nSimpleKey("trackReplay");

onMounted(() => {
  initMap();
});
const props = defineProps({
  id: {
    type: String,
    default: "",
  },
});

const trackReplayRef = ref<any>();

const mapInfo: any = reactive({
  map: null,
  Amap: null,
  infoWindow: null,
  carMarker: null,
  startPointMarker: null,
  endPointMarker: null,
});

const trackInfo: any = reactive({
  // 轨迹点
  trackArr: [],
  // 轨迹点
  trackInfoArr: [],
  // 加载轨迹点
  showTrack: false,
  // 轨迹巡航路线
  pathSimplifierIns: null,
  // 当前节点
  currentPosition: [],
  // 当前节点索引
  pathIndex: 0,
  // 倍速
  speedCount: 1,
  // 速度 km/h
  moveSpeed: 15,
  // 行驶状态 0:未开始  1:行驶中  2:暂停
  naviStatus: 0,
  // 进度条百分比
  trackPercent: 0,
  // 路径总长 m
  totalDistance: 0,
});

// 初始化地图
const initMap = () => {
  lazyAMapApiLoader({ AMapUI: { plugins: [] } }).then((AMap) => {
    mapInfo.map = new AMap.Map("container", {
      zooms: [2, 20],
      mapStyle: "amap://styles/c06d186366f663bf08fd26481ff16d06",
    });
    mapInfo.Amap = AMap;
    const scale = new AMap.Scale({
      position: {
        left: "10px",
        bottom: "50px",
      },
    });
    mapInfo.map.addControl(scale);
  });
};

/**
 * 浮窗处理
 */
// 创建信息窗体
const renderInfoWindow = (infoWindow: typeof mapInfo.Amap.InfoWindow, infoWindowDiv: HTMLElement, info: any) => {
  render(
    createVNode(InfoWindow, {
      window: infoWindow,
      map: mapInfo.map,
      info: info,
    }),
    infoWindowDiv
  );
};
// 实时更新信息窗体位置
watch(
  () => trackInfo.currentPosition,
  (newV) => {
    if (newV) {
      if (trackInfo.navg && mapInfo.infoWindow) {
        const openInfoWindow = mapInfo.infoWindow.getIsOpen();
        if (openInfoWindow) {
          const { infoWindowDiv } = mapInfo.infoWindow.getExtData();
          renderInfoWindow(mapInfo.infoWindow, infoWindowDiv, trackInfo.trackInfoArr[trackInfo.pathIndex]);
          mapInfo.infoWindow.open(mapInfo.map, newV);
        }
      }
    }
  }
);

/**
 * 轨迹处理
 */
// 查询轨迹
const searchTrack = (lineArr: any, showTrack: boolean) => {
  if (lineArr.length <= 1) {
    Message("error", $t("currentTimeNoVehicle"));
    resetMap();
    return;
  }
  resetMap();
  trackInfo.trackArr = lineArr.map((item: any) => [item.longitude, item.latitude]) || [];
  trackInfo.trackInfoArr = lineArr || [];
  trackInfo.showTrack = Boolean(showTrack);
  trackInfo.currentPosition = trackInfo.trackArr[0] || [];
  // @ts-ignore
  // eslint-disable-next-line no-undef
  trackInfo.totalDistance = AMap.GeometryUtil.distanceOfLine(trackInfo.trackArr);
  initTrack();
  addCarMarker();
  addPointMarker();
};
const resetMap = () => {
  if (mapInfo.carMarker) {
    mapInfo.map.remove(mapInfo.carMarker);
    mapInfo.carMarker = null;
  }
  if (mapInfo.startPointMarker) {
    mapInfo.map.remove(mapInfo.startPointMarker);
    mapInfo.startPointMarker = null;
  }
  if (mapInfo.endPointMarker) {
    mapInfo.map.remove(mapInfo.endPointMarker);
    mapInfo.endPointMarker = null;
  }
  if (trackInfo.pathSimplifierIns) {
    trackInfo.pathSimplifierIns.setData([]);
    trackInfo.naviStatus = 0;
  }
  if (mapInfo.infoWindow) {
    mapInfo.infoWindow.close();
  }
  trackInfo.trackPercent = 0;
  trackInfo.speedCount = 1;
};
// 初始化轨迹
const initTrack = () => {
  // @ts-ignore
  // eslint-disable-next-line no-undef
  AMapUI.load(["ui/misc/PathSimplifier"], (PathSimplifier: any) => {
    if (!PathSimplifier.supportCanvas) {
      alert(`${$t("currentEnvNotSupport")} Canvas！`);
      return;
    }
    trackInfo.pathSimplifierIns = new PathSimplifier({
      zIndex: 100,
      autoSetFitView: true,
      clickToSelectPath: false,
      map: mapInfo.map,
      getPath: (pathData: any) => {
        return pathData.path;
      },
      getHoverTitle: function (pathData: any, pathIndex: number, pointIndex: number) {
        if (pointIndex >= 0) {
          // 鼠标悬停轨迹节点
          const { addr, dataTime, latitude, longitude } = trackInfo.trackInfoArr[pointIndex];
          return `
            ${$t("node")}：${pointIndex + 1}/${pathData.path.length}；<br>
            ${$t("coordinate")}：${longitude}，${latitude}；<br>
            ${$t("time")}：${dataTime};<br>
            ${$t("addresse")}：${addr}；
          `;
        }
        // 鼠标悬停节点之间连线
        return "";
      },
      renderOptions: {
        // 轨迹点（地图视野内的轨迹节点的总数量小于该阈值，则绘制全部的节点）
        renderAllPointsIfNumberBelow: trackInfo.showTrack ? 86400 : -1,
        // 轨迹点样式
        keyPointStyle: trackKeyPointStyle,
        // 轨迹线样式
        pathLineStyle: trackPathLineStyle,
        // 轨迹线hover样式
        pathLineHoverStyle: trackPathLineHoverStyle,
        // 起点样式
        startPointStyle: trackStartPointStyle,
        // 终点样式
        endPointStyle: trackEndPointStyle,
      },
    });
    trackInfo.pathSimplifierIns.setData([
      {
        name: `${$t("route")}0`,
        path: trackInfo.trackArr,
      },
    ]);
    trackInfo.navg = trackInfo.pathSimplifierIns.createPathNavigator(0, {
      loop: false,
      speed: trackInfo.moveSpeed,
      pathNavigatorStyle: {
        content: "none",
        // 经过轨迹线样式
        pathLinePassedStyle: trackPathLinePassedStyle,
      },
    });
    // 解决未开始状态不允许拖动进度的问题
    restartAnimation();
    pauseAnimation();
    trackInfo.navg.on("move", function () {
      // @ts-ignore
      const that = this;
      // 处理进度条进度
      const movedDistance = that.getMovedDistance();
      const percent = (movedDistance / trackInfo.totalDistance) * 100 || 0;
      trackInfo.trackPercent = percent;
      // 结束时切换播放器状态
      const idx = that.getCursor().idx;
      trackInfo.pathIndex = idx;
      if (idx === trackInfo.navg.getPathEndIdx()) {
        trackInfo.naviStatus = 0;
      }
      // 监听实时位置
      const position = trackInfo.navg.getPosition();
      trackInfo.currentPosition = position;
      // 实时更新车辆位置
      mapInfo.carMarker.setPosition(position);
      addCarMarker();
    });
  });
};

/**
 * 车辆巡航器
 */
const renderCarMarker = (marker: typeof mapInfo.Amap.Marker, markerDiv: HTMLElement, info: any, focus?: boolean) => {
  render(
    createVNode(carMarker, {
      status: {
        deviceId: info.deviceId,
      },
      direction: info.direction,
      focus,
    }),
    markerDiv
  );
};
const addCarMarker = () => {
  if (mapInfo.carMarker) {
    const { markerDiv } = mapInfo.carMarker.getExtData();
    renderCarMarker(mapInfo.carMarker, markerDiv, trackInfo.trackInfoArr[trackInfo.pathIndex]);
  } else {
    const markerDiv = document.createElement("div");
    mapInfo.carMarker = new mapInfo.Amap.Marker({
      content: markerDiv,
      position: trackInfo.trackArr[0],
      anchor: "center",
      offset: new mapInfo.Amap.Pixel(-1, -5),
      extData: {
        markerDiv: markerDiv,
      },
    });
    const startInfo = trackInfo.trackInfoArr[0];
    renderCarMarker(mapInfo.carMarker, markerDiv, startInfo, false);
    mapInfo.map.add(mapInfo.carMarker);
    mapInfo.carMarker.on("mouseover", () => {
      const infoWindowDiv = document.createElement("div");
      const curInfo = trackInfo.trackInfoArr[trackInfo.pathIndex];
      const infoWindow = new mapInfo.Amap.InfoWindow({
        isCustom: true,
        autoMove: true,
        content: infoWindowDiv,
        position: trackInfo.currentPosition,
        offset: new mapInfo.Amap.Pixel(-3, -46),
        extData: {
          infoWindowDiv: infoWindowDiv,
        },
      });
      renderInfoWindow(infoWindow, infoWindowDiv, curInfo);
      infoWindow.open(mapInfo.map, trackInfo.currentPosition);
      mapInfo.infoWindow = infoWindow;
    });
  }
};

/**
 * 起点标记
 */
const renderPointMarker = (markerDiv: HTMLElement, type1: string) => {
  render(createVNode(pointMarker, { type: type1 }), markerDiv);
};
const addPointMarker = () => {
  const startMarkerDiv = document.createElement("div");
  mapInfo.startPointMarker = new mapInfo.Amap.Marker({
    zIndex: 10,
    content: startMarkerDiv,
    position: trackInfo.currentPosition,
    anchor: "center",
  });
  renderPointMarker(startMarkerDiv, "start");

  const endMarkerDiv = document.createElement("div");
  const lastPoint = trackInfo.trackArr[trackInfo.trackArr.length - 1];
  mapInfo.endPointMarker = new mapInfo.Amap.Marker({
    zIndex: 10,
    content: endMarkerDiv,
    position: lastPoint,
    anchor: "center",
  });
  renderPointMarker(endMarkerDiv, "end");

  mapInfo.map.add([mapInfo.startPointMarker, mapInfo.endPointMarker]);
};

/**
 * 播放器操作
 */
// 重新开始
const restartAnimation = () => {
  trackInfo.navg.start();
  trackInfo.naviStatus = 1;
};
// 暂停动画
const pauseAnimation = () => {
  trackInfo.navg.pause();
  trackInfo.naviStatus = 2;
};
// 继续动画
const resumeAnimation = () => {
  trackInfo.navg.resume();
  trackInfo.naviStatus = 1;
};
// 停止动画
const stopAnimation = () => {
  trackInfo.navg.stop();
};
// 暂停/继续动画
const changeMovingStatus = () => {
  if (trackInfo.naviStatus === 0 || trackInfo.pathIndex === trackInfo.navg.getPathEndIdx()) {
    restartAnimation();
  } else if (trackInfo.naviStatus === 1) {
    pauseAnimation();
  } else {
    resumeAnimation();
  }
};
// 倍速控制
const changeSpeed = (e: number) => {
  trackInfo.speedCount = e;
  trackInfo.navg.setSpeed(trackInfo.moveSpeed * e);
};
// 手动调整进度条进度
const changeProgress = (percent: number) => {
  stopAnimation();
  restartAnimation();
  pauseAnimation();
  trackInfo.trackPercent = percent;
  const movedDistance = (percent / 100) * trackInfo.totalDistance || 0;
  trackInfo.navg.moveByDistance(movedDistance);
  trackInfo.pathSimplifierIns.renderLater();
};
</script>

<style lang="scss" scoped>
.track-replay {
  position: relative;
  @include wh(100%);
  #container {
    @include wh(100%);
  }
}
</style>
