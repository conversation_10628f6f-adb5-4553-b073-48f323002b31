<template>
  <section class="page-table-layout" ref="pageTableLayoutRef">
    <div class="page-table-layout-top">
      <div class="top-title">{{ $t("carInfoManage") }}</div>
      <div class="top-add-button">
        <x-button
          v-if="permitList.includes('sys:vehicleInfo:save')"
          type="paleBlue"
          :text="$t('add')"
          icon="button_add"
          @click="addReactive.show = true"
        />
        <Add v-model:show="addReactive.show" @confirm="addReactive.confirm" />
      </div>
    </div>
    <div class="page-table-layout-middle">
      <div class="middle-left">
        <div class="middle-left-items">
          <div class="middle-left-item" style="width: 320px">
            <x-select
              v-model:value="searchForm.entName"
              :options="formOptions.companyOptions"
              :popupContainer="pageTableLayoutRef"
              :placeholder="$t('PEnterEnt')"
              showSearch
            />
          </div>
          <div class="middle-left-item" style="width: 112px">
            <x-select
              :value="searchForm.searchType"
              @update:value="onChangeSearchType"
              :options="formOptions.searchType"
              :popupContainer="pageTableLayoutRef"
            />
          </div>
          <div class="middle-left-item">
            <x-input
              :value="searchForm.searchValue"
              @update:value="onChangeSearchValue"
              :placeholder="searchPlaceholder"
              suffix="input_search"
              style="width: 240px"
            />
          </div>
          <div class="middle-left-item" style="width: 112px">
            <x-select
              v-model:value="searchForm.vehicleType"
              :options="formOptions.vehicleType"
              :popupContainer="pageTableLayoutRef"
            />
          </div>
          <div class="middle-left-item" style="width: 112px">
            <x-select
              v-model:value="searchForm.vehicleModelId"
              :options="formOptions.vehicleModelId"
              :popupContainer="pageTableLayoutRef"
            />
          </div>
        </div>
        <div class="middle-left-items">
          <div class="middle-left-item" style="width: 380px; display: flex">
            <div class="middle-left-item-label">{{ $t("opDate") }}</div>
            <div class="middle-left-item-value">
              <DateRangePicker
                v-model:value="searchForm.opDate"
                :popupContainer="pageTableLayoutRef"
                :placeholder="[$t('startDate'), $t('endDate')]"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="middle-right">
        <x-button
          v-if="permitList.includes('sys:vehicleInfo:list')"
          @click="reSearch"
          type="blue"
          :text="$t('search')"
          style="margin-right: 12px"
        />
        <x-button @click="resetSearchForm" type="green" :text="$t('reset')" />
      </div>
    </div>
    <div class="page-table-layout-bottom">
      <x-table
        :cols="table.cols"
        :dataSource="table.dataSource"
        :pagination="table.pagination"
        :loading="table.loading"
        @change="tableChange"
      >
        <template #entName="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.entName }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.entName }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #vin="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.vin }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.vin }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #simId="{ record }">
          <x-popover
            trigger="hover"
            placement="bottom"
            class="table-company-popover"
            :container="pageTableLayoutRef"
          >
            {{ record.simId }}
            <template #content>
              <div class="user-manage-table-company-hover-popover">
                {{ record.simId }}
              </div>
            </template>
          </x-popover>
        </template>
        <template #opera="{ record }">
          <div class="table-opera">
            <div class="table-opera-text">
              <span
                v-if="permitList.includes('sys:vehicleInfo:update')"
                @click="openEdit(record)"
              >
                {{ $t("edit") }}
              </span>
              <span
                v-if="permitList.includes('sys:vehicleInfo:info')"
                @click="openDetail(record.id)"
              >
                {{ $t("detail") }}
              </span>
              <span
                v-if="permitList.includes('sys:vehicleInfo:delete')"
                style="color: #e24562"
                @click="handleDelete(record)"
              >
                {{ $t("delete") }}
              </span>
            </div>
          </div>
        </template>
      </x-table>
      <Edit
        v-model:show="editReactive.show"
        :id="editReactive.id"
        :entId="editReactive.entId"
        @confirm="editReactive.confirm"
      />
      <Detail v-model:show="detailReactive.show" :id="detailReactive.id" />
    </div>
  </section>
</template>
<script lang="tsx" setup>
import type { PageSizeType } from "@/components/types";
import type { VehicleListRequest } from "@/services/type";
import { ref, reactive, watch, computed } from "vue";
import {
  compList,
  vehicleList,
  delVehicle,
  getConfigList,
  vehicleModelList,
} from "@/services/api";
import { useMainStore } from "@/stores/main";
import Add from "./components/add.vue";
import Edit from "./components/edit.vue";
import Detail from "./components/detail.vue";
import xButton from "@/components/x-button.vue";
import xInput from "@/components/x-input.vue";
import xTable from "@/components/x-table.vue";
import xSelect from "@/components/x-select.vue";
import xPopover from "@/components/x-popover.vue";
import { DateRangePicker } from "@/components/x-date-picker";
import xModal from "@/components/x-modal";
import Message from "@/components/x-message";
import { i18nSimpleKey } from "@/assets/ts/utils";
const $t = i18nSimpleKey("carInfoManage");

const {
  userInfo: { permitList },
} = useMainStore();

/**
 * 引用
 */
const pageTableLayoutRef = ref<any>();

// 车辆型号
const updateVehicleModelList = async () => {
  const params = {
    page: 1,
    limit: 50,
  } as any;

  // 车辆类型与车辆型号联动
  searchForm.vehicleType && (params.vehicleType = searchForm.vehicleType);

  const { list } = await vehicleModelList(params);

  if (list) {
    formOptions.vehicleModelId = [
      { id: "", vehicleModel: $t("allModels") },
      ...list,
    ].map((item) => ({
      value: String(item.id),
      label: item.vehicleModel,
    }));
  } else {
    formOptions.vehicleModelId = [];
  }
};

/**
 * 表单组件数据支持
 */
const formOptions = reactive({
  companyOptions: [] as { label: string; value: string }[],
  vehicleType: [] as any[],
  vehicleModelId: [] as any[],
  searchType: [
    {
      value: "vehicleNo",
      label: $t("vehicleNo"),
      placeholder: $t("vehicleNo"),
    },
    {
      value: "vin",
      label: "VIN",
      placeholder: $t("vinNo"),
    },
    {
      value: "simId",
      label: $t("simNo"),
      placeholder: $t("simNo"),
    },
  ],
});

const searchPlaceholder = computed(() => {
  const searchTypeOption = formOptions.searchType.find(
    (option) => option.value === searchForm.searchType
  );
  return searchTypeOption
    ? `${$t("PEnter")}${searchTypeOption.placeholder}`
    : "";
});

/**
 * 表格-字段
 */
const table = reactive({
  cols: [
    {
      key: "orderId",
      title: $t("orderNumber"),
      width: "30",
    },
    {
      key: "entName",
      title: $t("entName"),
      width: "60",
      slots: "entName",
    },
    {
      key: "vehicleNo",
      title: $t("vehicleNo"),
      width: "40",
    },
    {
      key: "vin",
      title: "VIN",
      width: "70",
      slots: "vin",
    },
    {
      key: "simId",
      title: $t("simNo"),
      width: "70",
      slots: "simId",
    },
    {
      key: "vehicleTypeText",
      title: $t("type"),
      width: "40",
    },
    {
      key: "vehicleModeText",
      title: $t("model"),
      width: "40",
    },
    {
      key: "opDateText",
      title: $t("opDate"),
      width: "40",
    },
    {
      key: "opera",
      title: $t("opera"),
      slots: "opera",
      width: "60",
    },
  ],
  dataSource: [] as any[],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 10 as PageSizeType,
  },
  loading: true,
});
/**
 * 表格-搜索表单
 */
const searchForm = reactive({
  entName: "",
  opDate: ["", ""],
  vehicleModelId: "",
  searchType: "vehicleNo",
  searchValue: "",
  vehicleNo: "",
  simId: "",
  vin: "",
  vehicleType: 0,
});
/**
 * 表格-搜索
 */
const submitSearch = async () => {
  table.loading = true;

  const params = {
    page: table.pagination.current,
    limit: table.pagination.pageSize,
  } as VehicleListRequest;

  if (searchForm.entName) {
    params.entName = searchForm.entName;
  }

  params[searchForm.searchType] = searchForm.searchValue;

  searchForm.vehicleModelId &&
    (params.vehicleModelId = searchForm.vehicleModelId);
  searchForm.vehicleType && (params.vehicleType = searchForm.vehicleType);

  const [opDateStart, opDateEnd] = searchForm.opDate;
  opDateStart && (params.opDateStart = opDateStart);
  opDateEnd && (params.opDateEnd = opDateEnd);

  const { totalCount, list } = await vehicleList(params);
  table.pagination.total = totalCount;
  table.dataSource = [
    ...(list
      ? list.map((item, index) => {
          return {
            orderId: (index + 1).toString().padStart(3, "0"),
            opera: ref(false),
            ...item,
          };
        })
      : []),
  ];
  setTimeout(() => {
    table.loading = false;
  }, 1000);
};
/**
 * 表格-重置
 */
const resetSearchForm = () => {
  searchForm.entName = "";
  searchForm.opDate = ["", ""];
  searchForm.vehicleModelId = "";
  searchForm.searchType = "vehicleNo";
  searchForm.searchValue = "";
  searchForm.vehicleNo = "";
  searchForm.simId = "";
  searchForm.vehicleType = 0;
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-重新搜索
 */
const reSearch = () => {
  table.pagination["current"] = 1;
  submitSearch();
};
/**
 * 表格-分页
 */
const tableChange = (key: "current" | "pageSize", value: PageSizeType) => {
  key === "pageSize" && (table.pagination["current"] = 1);
  table.pagination[key] = value;
  submitSearch();
};
/**
 * 表格-首次加载
 */
(async () => {
  submitSearch();
  formOptions.companyOptions = (await compList()).map(({ entName }) => ({
    label: entName!,
    value: entName!,
  }));
  // 车辆类型
  const { operConfigList } = await getConfigList({
    configList: ["vehicle_type"],
  });
  formOptions.vehicleType = [
    { k: 0, val: $t("allTypes") },
    ...operConfigList,
  ].map((item) => ({ value: Number(item.k), label: item.val }));
  // 车辆型号
  updateVehicleModelList();
})();

/**
 * 相关操作
 */
// 新增
const addReactive = reactive({
  show: false,
  confirm: () => submitSearch(),
});
// 编辑
const editReactive = reactive({
  show: false,
  id: "",
  entId: "",
  confirm: () => submitSearch(),
});
const openEdit = (record: any) => {
  editReactive.id = record.id;
  editReactive.entId = String(record.entId);
  editReactive.show = true;
};
// 详情
const detailReactive = reactive({
  show: false,
  id: "",
});
const openDetail = (id: string) => {
  detailReactive.id = id;
  detailReactive.show = true;
};
// 删除
const handleDelete = (record: any) => {
  xModal.confirm({
    title: $t("sureToDelCarInfo"),
    content: (
      <div style="font-size:14px;color:#383838;">
        {$t("willUnbindCarAndSim")}
      </div>
    ),
    confirm() {
      return delVehicle([record.id]).then(() => {
        Message("success", $t("deleteSuccess"));
        submitSearch();
      });
    },
  });
};
// 切换搜索类型
const onChangeSearchType = (value: string) => {
  if (searchForm.searchType === value) return;
  searchForm.searchValue = "";
  searchForm.searchType = value;
};
const onChangeSearchValue = (value: string) => {
  searchForm.searchValue = value;
};
watch(
  () => searchForm.vehicleType,
  () => {
    // 车辆型号
    searchForm.vehicleModelId = "";
    updateVehicleModelList();
  }
);
</script>

<style lang="scss">
.company-manage-table-opera-click-popover {
  &-item {
    cursor: pointer;
    @include wh(90px, 40px);
    font-size: 13px;
    line-height: 40px;
    text-align: center;
    &:hover {
      background-color: #242859;
    }
    &:first-child {
      border-radius: 4px 4px 0 0;
    }
    &:last-child {
      border-radius: 0 0 4px 4px;
    }
  }
}
</style>

<style lang="scss" scoped>
.user-manage-table-company-hover-popover {
  height: 32px;
  padding: 0 6px;
  line-height: 32px;
}
.page-table-layout {
  display: flex;
  flex-direction: column;
  @include wh(100%) {
    padding: 12px 24px 24px 24px;
  }

  &-top {
    @include fj {
      align-items: center;
    }
    @include wh(100%, 36px);
    .top-title {
      @include sc(18px, rgb(36, 40, 89)) {
        line-height: 36px;
        font-weight: bold;
      }
    }
  }
  &-middle {
    @include fj;
    @include wh(100%, auto) {
      margin-top: 20px;
    }
    .middle-right {
      display: flex;
    }
    .middle-left-items {
      display: flex;
      margin-top: 14px;
      &:first-child {
        margin-top: 0;
      }
    }
    .middle-left-item {
      margin-left: 16px;
      &:first-child {
        margin-left: 0;
      }
      &-label {
        @include sc(14px, #5e5e5e) {
          line-height: 20px;
          padding: 6px 8px 6px 0;
        }
      }
      &-value {
        flex: 1;
      }
    }
  }
  &-bottom {
    margin-top: 16px;
    flex: 1;
    width: 100%;
    overflow: hidden;
    .table-company-popover {
      @include ell;
    }
    .table-opera {
      display: flex;
      &-text {
        span {
          cursor: pointer;
          color: #4277fe;
          margin-right: 16px;
        }
      }
      &-more {
        cursor: pointer;
        @include fj {
          align-items: center;
        }
        @include wh(24px) {
          padding: 0 4px;
          border-radius: 4px;
        }
        &.enable {
          background-color: #f7f9fe;
        }
        span {
          @include wh(3px) {
            display: block;
            border-radius: 50%;
            background-color: #4277fe;
          }
        }
      }
    }
  }
}
</style>
